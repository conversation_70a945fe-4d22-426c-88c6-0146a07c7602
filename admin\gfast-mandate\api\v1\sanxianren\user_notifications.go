/*
* @desc:用户通知相关API
* @company:山东傲英网络科技股份有限公司
* @Author: wolfman
* @Date:   2024/12/19
 */

package sanxianren

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
)

// GetNotificationsReq 获取通知列表请求
type GetNotificationsReq struct {
	g.Meta         `path:"/user/notifications" tags:"三线人/用户通知" method:"get" summary:"获取通知列表"`
	NotificationType string `json:"notificationType" description:"通知类型：system-系统通知，submission-投稿通知，interaction-互动通知，all-全部"`
	IsRead         *int   `json:"isRead" description:"是否已读：1-已读，0-未读，null-全部"`
	commonApi.PageReq
	commonApi.Author
}

// GetNotificationsRes 获取通知列表响应
type GetNotificationsRes struct {
	g.Meta `mime:"application/json"`
	commonApi.ListRes
	List         []NotificationInfo `json:"list" description:"通知列表"`
	UnreadCount  int                `json:"unreadCount" description:"未读数量"`
}

// NotificationInfo 通知信息
type NotificationInfo struct {
	Id               uint64 `json:"id" description:"通知ID"`
	NotificationType string `json:"notificationType" description:"通知类型"`
	Title            string `json:"title" description:"通知标题"`
	Content          string `json:"content" description:"通知内容"`
	IsRead           int    `json:"isRead" description:"是否已读"`
	RelatedType      string `json:"relatedType" description:"关联类型"`
	RelatedId        uint64 `json:"relatedId" description:"关联ID"`
	CreatedAt        string `json:"createdAt" description:"创建时间"`
}

// MarkNotificationReadReq 标记通知已读请求
type MarkNotificationReadReq struct {
	g.Meta         `path:"/user/notifications/read" tags:"三线人/用户通知" method:"put" summary:"标记通知已读"`
	NotificationId uint64 `json:"notificationId" description:"通知ID，0表示全部标记为已读"`
	commonApi.Author
}

// MarkNotificationReadRes 标记通知已读响应
type MarkNotificationReadRes struct {
	g.Meta `mime:"application/json"`
}

// GetNotificationStatsReq 获取通知统计请求
type GetNotificationStatsReq struct {
	g.Meta `path:"/user/notifications/stats" tags:"三线人/用户通知" method:"get" summary:"获取通知统计"`
	commonApi.Author
}

// GetNotificationStatsRes 获取通知统计响应
type GetNotificationStatsRes struct {
	g.Meta      `mime:"application/json"`
	Total       int `json:"total" description:"总数"`
	Unread      int `json:"unread" description:"未读数"`
	System      int `json:"system" description:"系统通知数"`
	Submission  int `json:"submission" description:"投稿通知数"`
	Interaction int `json:"interaction" description:"互动通知数"`
}
