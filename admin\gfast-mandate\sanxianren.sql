/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 80012
 Source Host           : localhost:3306
 Source Schema         : sanxian1

 Target Server Type    : MySQL
 Target Server Version : 80012
 File Encoding         : 65001

 Date: 31/08/2025 16:32:33
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for ai_processing_tasks
-- ----------------------------
DROP TABLE IF EXISTS `ai_processing_tasks`;
CREATE TABLE `ai_processing_tasks`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_uuid` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '任务UUID',
  `submission_uuid` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '稿件UUID',
  `processing_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '处理类型：factory/people/story',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending' COMMENT '任务状态：pending/processing/completed/failed',
  `options` json NULL COMMENT '处理选项',
  `original_content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '原始内容',
  `cleaned_content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '清洗后内容',
  `extracted_data` json NULL COMMENT '提取的数据',
  `wiki_format` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT 'Wikipedia格式内容',
  `suggested_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '建议标题',
  `keywords` json NULL COMMENT '关键词',
  `processing_log` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '处理日志',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '错误信息',
  `started_at` datetime NULL DEFAULT NULL COMMENT '开始时间',
  `completed_at` datetime NULL DEFAULT NULL COMMENT '完成时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_task_uuid`(`task_uuid`) USING BTREE,
  INDEX `idx_submission_uuid`(`submission_uuid`) USING BTREE,
  INDEX `idx_processing_type`(`processing_type`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'AI处理任务表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for casbin_rule
-- ----------------------------
DROP TABLE IF EXISTS `casbin_rule`;
CREATE TABLE `casbin_rule`  (
  `ptype` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `v0` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `v1` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `v2` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `v3` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `v4` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `v5` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for cleaning_logs
-- ----------------------------
DROP TABLE IF EXISTS `cleaning_logs`;
CREATE TABLE `cleaning_logs`  (
  `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '日志ID',
  `template_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模板ID',
  `prompt_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '提示词ID',
  `original_data` json NOT NULL COMMENT '原始输入数据JSON',
  `cleaned_data` json NULL COMMENT '清洗后的输出数据JSON',
  `processing_log` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '处理日志',
  `status` tinyint(1) NOT NULL COMMENT '处理状态：1-成功，0-失败',
  `process_time` int(11) NULL DEFAULT NULL COMMENT '处理时间（毫秒）',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '错误信息',
  `related_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关联业务ID',
  `related_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关联业务类型',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_template_id`(`template_id`) USING BTREE,
  INDEX `idx_prompt_id`(`prompt_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE,
  CONSTRAINT `fk_cleaning_logs_prompt` FOREIGN KEY (`prompt_id`) REFERENCES `cleaning_prompts` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_cleaning_logs_template` FOREIGN KEY (`template_id`) REFERENCES `cleaning_templates` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '数据清洗日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for cleaning_prompts
-- ----------------------------
DROP TABLE IF EXISTS `cleaning_prompts`;
CREATE TABLE `cleaning_prompts`  (
  `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '提示词ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '提示词名称',
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '提示词分类',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '提示词描述',
  `template_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '关联模板ID',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '1.0' COMMENT '版本号',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '提示词内容',
  `parameters` json NULL COMMENT '参数配置JSON',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `created_by` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人ID',
  `updated_by` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '更新人ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_template_id`(`template_id`) USING BTREE,
  INDEX `idx_category`(`category`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE,
  CONSTRAINT `fk_prompts_template` FOREIGN KEY (`template_id`) REFERENCES `cleaning_templates` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '数据清洗提示词表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for cleaning_templates
-- ----------------------------
DROP TABLE IF EXISTS `cleaning_templates`;
CREATE TABLE `cleaning_templates`  (
  `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模板ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模板名称',
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模板分类',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '模板描述',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '1.0' COMMENT '版本号',
  `fields` json NOT NULL COMMENT '字段定义JSON',
  `rules` json NULL COMMENT '验证规则JSON',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `created_by` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人ID',
  `updated_by` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '更新人ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category`(`category`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '数据清洗模板表' ROW_FORMAT = Dynamic;













-- ----------------------------
-- Table structure for factory_city_code
-- ----------------------------
DROP TABLE IF EXISTS `factory_city_code`;
CREATE TABLE `factory_city_code`  (
  `id` varchar(55) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '城市ID',
  `pid` varchar(55) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '城市父ID',
  `deep` varchar(55) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '级别',
  `name` varchar(55) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '城市名称',
  `pinyin_prefix` varchar(55) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '城市拼音头',
  `pinyin` varchar(55) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '城市拼音',
  `ext_id` varchar(55) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '完整ID',
  `ext_name` varchar(55) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '城市全称',
  `weathercode` varchar(55) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '天气预报的编码',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `id，name,code`(`id`, `name`, `weathercode`) USING BTREE COMMENT '这三个字段并列一起，必须是唯一的'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '省市区县和天气预报编码' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for factory_submissions
-- ----------------------------
DROP TABLE IF EXISTS `factory_submissions`;
CREATE TABLE `factory_submissions`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '稿件ID，主键',
  `submission_uuid` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '稿件唯一标识',
  `submitter_id` bigint(20) UNSIGNED NOT NULL COMMENT '投稿用户ID',
  `submitter_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '投稿人姓名',
  `submitter_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '投稿人手机',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '稿件标题',
  `submission_type` enum('person','factory','oral_history','heritage_site','story') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '稿件类型',
  `content_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '稿件内容（JSON格式）',
  `original_content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '原始提交内容备份',
  `status` enum('pending','ai_processing','ai_cleaned','under_review','approved','rejected','published') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending' COMMENT '处理状态',
  `ai_cleaned_content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT 'AI清洗后的内容',
  `ai_processing_log` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT 'AI处理日志',
  `review_feedback` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '审核反馈',
  `rejection_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '拒绝原因',
  `wiki_page_id` int(11) NULL DEFAULT NULL COMMENT 'MediaWiki页面ID',
  `wiki_page_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'MediaWiki页面标题',
  `wiki_namespace` int(11) NULL DEFAULT 0 COMMENT 'MediaWiki命名空间',
  `published_at` datetime NULL DEFAULT NULL COMMENT '发布时间',
  `reviewer_id` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '审核员ID',
  `reviewed_at` datetime NULL DEFAULT NULL COMMENT '审核时间',
  `view_count` int(11) NULL DEFAULT 0 COMMENT '查看次数',
  `like_count` int(11) NULL DEFAULT 0 COMMENT '点赞数',
  `comment_count` int(11) NULL DEFAULT 0 COMMENT '评论数',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime NULL DEFAULT NULL COMMENT '软删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_submission_uuid`(`submission_uuid`) USING BTREE,
  INDEX `idx_submitter_id`(`submitter_id`) USING BTREE,
  INDEX `idx_submission_type`(`submission_type`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE,
  INDEX `idx_published_at`(`published_at`) USING BTREE,
  INDEX `idx_deleted_at`(`deleted_at`) USING BTREE,
  INDEX `idx_wiki_page_id`(`wiki_page_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '稿件投稿主表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for factory_types
-- ----------------------------
DROP TABLE IF EXISTS `factory_types`;
CREATE TABLE `factory_types`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '类型ID',
  `type_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类型名称',
  `description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '类型描述',
  `sort_order` int(11) NULL DEFAULT 0 COMMENT '排序',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_type_name`(`type_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '工厂类型表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for factory_user_types
-- ----------------------------
DROP TABLE IF EXISTS `factory_user_types`;
CREATE TABLE `factory_user_types`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '类型ID',
  `type_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类型名称',
  `description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '类型描述',
  `sort_order` int(11) NULL DEFAULT 0 COMMENT '排序',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_type_name`(`type_name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '工厂类型表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for factory_verification
-- ----------------------------
DROP TABLE IF EXISTS `factory_verification`;
CREATE TABLE `factory_verification`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '认证ID',
  `verification_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '认证唯一标识符',
  `applicant_user_id` bigint(20) UNSIGNED NOT NULL COMMENT '申请人用户ID',
  `applicant_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '申请人姓名',
  `applicant_mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '申请人手机号',
  `factory_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '工厂名称',
  `factory_address` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '工厂地址',
  `position` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '申请人在工厂的职位',
  `work_years` int(11) NOT NULL DEFAULT 0 COMMENT '工作年限',
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '认证状态：0-待认证，1-已认证，2-认证失败',
  `verification_link` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '认证链接',
  `confirmed_by_user_id` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '确认认证的用户ID',
  `confirmed_by_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '确认认证的用户姓名',
  `confirmed_at` datetime NULL DEFAULT NULL COMMENT '确认认证时间',
  `expires_at` datetime NOT NULL COMMENT '认证链接过期时间',
  `created_at` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `verification_id`(`verification_id`) USING BTREE,
  INDEX `applicant_user_id`(`applicant_user_id`) USING BTREE,
  INDEX `confirmed_by_user_id`(`confirmed_by_user_id`) USING BTREE,
  INDEX `status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '工厂成员认证申请表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for factory_verification_confirm
-- ----------------------------
DROP TABLE IF EXISTS `factory_verification_confirm`;
CREATE TABLE `factory_verification_confirm`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '确认记录ID',
  `verification_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '认证唯一标识符',
  `confirmer_user_id` bigint(20) UNSIGNED NOT NULL COMMENT '确认人用户ID',
  `confirmer_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '确认人姓名',
  `confirmer_mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '确认人手机号',
  `relationship` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '与申请人的关系',
  `confirm_result` tinyint(4) NOT NULL COMMENT '确认结果：1-确认属实，0-不属实',
  `confirm_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '确认备注',
  `created_at` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `verification_id`(`verification_id`) USING BTREE,
  INDEX `confirmer_user_id`(`confirmer_user_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '工厂成员认证确认记录表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for factory_verification_register
-- ----------------------------
DROP TABLE IF EXISTS `factory_verification_register`;
CREATE TABLE `factory_verification_register`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '注册记录ID',
  `verification_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '认证唯一标识符',
  `new_user_id` bigint(20) UNSIGNED NOT NULL COMMENT '新注册用户ID',
  `new_user_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '新用户姓名',
  `new_user_mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '新用户手机号',
  `referrer_user_id` bigint(20) UNSIGNED NOT NULL COMMENT '推荐人用户ID（认证申请人）',
  `created_at` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `verification_id`(`verification_id`) USING BTREE,
  INDEX `new_user_id`(`new_user_id`) USING BTREE,
  INDEX `referrer_user_id`(`referrer_user_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '通过认证注册的用户表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for industry_categories
-- ----------------------------
DROP TABLE IF EXISTS `industry_categories`;
CREATE TABLE `industry_categories`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '行业ID',
  `category_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '行业名称',
  `description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '行业描述',
  `sort_order` int(11) NULL DEFAULT 0 COMMENT '排序',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_category_name`(`category_name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '行业分类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for original_data_backup
-- ----------------------------
DROP TABLE IF EXISTS `original_data_backup`;
CREATE TABLE `original_data_backup`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `submission_uuid` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '稿件UUID',
  `data_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '数据类型：factory/people/story',
  `original_content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '原始内容',
  `content_hash` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '内容哈希值',
  `backup_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备份文件路径',
  `file_size` bigint(20) NOT NULL DEFAULT 0 COMMENT '文件大小（字节）',
  `compression_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'none' COMMENT '压缩类型：none/gzip/zip',
  `encryption_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'none' COMMENT '加密状态：none/aes256',
  `retention_days` int(11) NOT NULL DEFAULT 365 COMMENT '保留天数',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_submission_uuid`(`submission_uuid`) USING BTREE,
  INDEX `idx_data_type`(`data_type`) USING BTREE,
  INDEX `idx_content_hash`(`content_hash`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '原始数据备份表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for plugin_sms_config
-- ----------------------------
DROP TABLE IF EXISTS `plugin_sms_config`;
CREATE TABLE `plugin_sms_config`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `sms_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '短信平台的类型',
  `remark` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '备注',
  `status` tinyint(3) NOT NULL DEFAULT 0 COMMENT '状态',
  `config` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '配置json类型',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `sms_type`(`sms_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for plugin_sms_log
-- ----------------------------
DROP TABLE IF EXISTS `plugin_sms_log`;
CREATE TABLE `plugin_sms_log`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `sms_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '短信平台的类型',
  `msg_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '短信的类型',
  `templateid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模板id',
  `mobiles` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '电话号码',
  `params` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '模板参数',
  `result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '发送结果',
  `created_at` datetime NULL DEFAULT NULL,
  `updated_at` datetime NULL DEFAULT NULL,
  `deleted_at` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 39 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for position_categories
-- ----------------------------
DROP TABLE IF EXISTS `position_categories`;
CREATE TABLE `position_categories`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `category_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '分类名称',
  `description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分类描述',
  `sort_order` int(11) NULL DEFAULT 0 COMMENT '排序',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_category_name`(`category_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '工种分类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for position_names
-- ----------------------------
DROP TABLE IF EXISTS `position_names`;
CREATE TABLE `position_names`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '工种ID',
  `position_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '工种名称',
  `category_id` int(11) NOT NULL COMMENT '所属分类ID',
  `description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '工种描述',
  `skill_levels` json NULL COMMENT '可选技能等级',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用',
  `sort_order` int(11) NULL DEFAULT 0 COMMENT '排序',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category_id`(`category_id`) USING BTREE,
  INDEX `idx_position_name`(`position_name`) USING BTREE,
  CONSTRAINT `fk_position_names_category_id` FOREIGN KEY (`category_id`) REFERENCES `position_categories` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '工种名称表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for processing_workflows
-- ----------------------------
DROP TABLE IF EXISTS `processing_workflows`;
CREATE TABLE `processing_workflows`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `workflow_uuid` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '工作流UUID',
  `submission_uuid` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '稿件UUID',
  `workflow_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '工作流类型：full/ai_only/extract_only/wiki_only',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending' COMMENT '工作流状态：pending/running/completed/failed/cancelled',
  `current_step` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '当前步骤',
  `total_steps` int(11) NOT NULL DEFAULT 0 COMMENT '总步骤数',
  `completed_steps` int(11) NOT NULL DEFAULT 0 COMMENT '已完成步骤数',
  `steps` json NULL COMMENT '工作流步骤',
  `options` json NULL COMMENT '工作流选项',
  `results` json NULL COMMENT '处理结果',
  `errors` json NULL COMMENT '错误信息',
  `estimated_time` int(11) NULL DEFAULT NULL COMMENT '预估时间（秒）',
  `started_at` datetime NULL DEFAULT NULL COMMENT '开始时间',
  `completed_at` datetime NULL DEFAULT NULL COMMENT '完成时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_workflow_uuid`(`workflow_uuid`) USING BTREE,
  INDEX `idx_submission_uuid`(`submission_uuid`) USING BTREE,
  INDEX `idx_workflow_type`(`workflow_type`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '处理工作流表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sx_activity_logs
-- ----------------------------
DROP TABLE IF EXISTS `sx_activity_logs`;
CREATE TABLE `sx_activity_logs`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '活动ID',
  `user_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户ID',
  `user_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '用户名',
  `activity_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '活动类型（login/view/like/share/comment）',
  `activity_desc` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '活动描述',
  `content_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '内容类型',
  `content_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '内容ID',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'IP地址',
  `user_agent` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '用户代理',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_activity_type`(`activity_type`) USING BTREE,
  INDEX `idx_content`(`content_type`, `content_id`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '实时活动记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sx_banners
-- ----------------------------
DROP TABLE IF EXISTS `sx_banners`;
CREATE TABLE `sx_banners`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '轮播图ID',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '轮播图标题',
  `image_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '图片URL',
  `link_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '跳转链接',
  `link_type` tinyint(4) NOT NULL DEFAULT 3 COMMENT '链接类型：1-内部页面，2-外部链接，3-无链接',
  `position` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '展示位置：home_banner-首页轮播，story_banner-故事横幅',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序权重，数字越大越靠前',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `start_time` datetime NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime NULL DEFAULT NULL COMMENT '结束时间',
  `click_count` int(11) NOT NULL DEFAULT 0 COMMENT '点击次数',
  `view_count` int(11) NOT NULL DEFAULT 0 COMMENT '展示次数',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '轮播图描述',
  `created_by` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
  `updated_by` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_position_status`(`position`, `status`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE,
  INDEX `idx_deleted_at`(`deleted_at`) USING BTREE,
  INDEX `idx_time_range`(`start_time`, `end_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '轮播图管理表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sx_categories
-- ----------------------------
DROP TABLE IF EXISTS `sx_categories`;
CREATE TABLE `sx_categories`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类名称',
  `slug` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类标识（英文）',
  `type` enum('story','factory','people','general') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'general' COMMENT '分类类型',
  `parent_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '父分类ID（0为顶级分类）',
  `level` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '分类层级',
  `path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '分类路径（用/分隔）',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序权重（数字越大越靠前）',
  `icon` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '分类图标',
  `color` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '分类颜色',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '分类描述',
  `keywords` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '关键词（用逗号分隔）',
  `content_count` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '内容数量',
  `is_hot` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否热门（0否 1是）',
  `is_recommend` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否推荐（0否 1是）',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态（0禁用 1启用）',
  `created_by` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
  `updated_by` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_type_slug`(`type`, `slug`) USING BTREE,
  INDEX `idx_type_parent`(`type`, `parent_id`) USING BTREE,
  INDEX `idx_status_hot`(`status`, `is_hot`) USING BTREE,
  INDEX `idx_status_recommend`(`status`, `is_recommend`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '分类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sx_content_categories
-- ----------------------------
DROP TABLE IF EXISTS `sx_content_categories`;
CREATE TABLE `sx_content_categories`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `content_id` bigint(20) UNSIGNED NOT NULL COMMENT '内容ID',
  `content_type` enum('story','factory','people') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '内容类型',
  `category_id` bigint(20) UNSIGNED NOT NULL COMMENT '分类ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_content_category`(`content_id`, `content_type`, `category_id`) USING BTREE,
  INDEX `idx_category_id`(`category_id`) USING BTREE,
  INDEX `idx_content_type`(`content_type`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '内容分类关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sx_content_recommendations
-- ----------------------------
DROP TABLE IF EXISTS `sx_content_recommendations`;
CREATE TABLE `sx_content_recommendations`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '推荐ID',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '推荐标题',
  `content_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '内容类型：story-故事，factory-工厂，people-人物',
  `content_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '内容ID',
  `position` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '推荐位置：home_featured-首页精选，story_hot-热门故事，factory_hot-热门工厂，people_hot-热门人物',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序权重，数字越大越靠前',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `start_time` datetime NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime NULL DEFAULT NULL COMMENT '结束时间',
  `click_count` int(11) NOT NULL DEFAULT 0 COMMENT '点击次数',
  `view_count` int(11) NOT NULL DEFAULT 0 COMMENT '展示次数',
  `reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '推荐理由',
  `tags` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '推荐标签，JSON格式',
  `weight` decimal(10, 4) NOT NULL DEFAULT 0.0000 COMMENT '推荐权重',
  `algorithm` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'manual' COMMENT '推荐算法：manual-手动，hot-热度，new-最新，recommend-智能推荐',
  `created_by` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
  `updated_by` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_content_type_id`(`content_type`, `content_id`) USING BTREE,
  INDEX `idx_position_status`(`position`, `status`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order`) USING BTREE,
  INDEX `idx_algorithm`(`algorithm`) USING BTREE,
  INDEX `idx_weight`(`weight`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE,
  INDEX `idx_deleted_at`(`deleted_at`) USING BTREE,
  INDEX `idx_time_range`(`start_time`, `end_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '内容推荐表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sx_content_statistics
-- ----------------------------
DROP TABLE IF EXISTS `sx_content_statistics`;
CREATE TABLE `sx_content_statistics`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `content_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '内容类型（story/factory/people）',
  `total_content` int(11) NOT NULL DEFAULT 0 COMMENT '总内容数',
  `new_content` int(11) NOT NULL DEFAULT 0 COMMENT '新增内容数',
  `published_content` int(11) NOT NULL DEFAULT 0 COMMENT '已发布内容数',
  `total_views` int(11) NOT NULL DEFAULT 0 COMMENT '总浏览量',
  `total_likes` int(11) NOT NULL DEFAULT 0 COMMENT '总点赞数',
  `total_shares` int(11) NOT NULL DEFAULT 0 COMMENT '总分享数',
  `total_comments` int(11) NOT NULL DEFAULT 0 COMMENT '总评论数',
  `avg_engagement` decimal(5, 2) NOT NULL DEFAULT 0.00 COMMENT '平均互动率',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_stat_date_type`(`stat_date`, `content_type`) USING BTREE,
  INDEX `idx_stat_date`(`stat_date`) USING BTREE,
  INDEX `idx_content_type`(`content_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '内容统计表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sx_content_tags
-- ----------------------------
DROP TABLE IF EXISTS `sx_content_tags`;
CREATE TABLE `sx_content_tags`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `content_id` bigint(20) UNSIGNED NOT NULL COMMENT '内容ID',
  `content_type` enum('story','factory','people') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '内容类型',
  `tag_id` bigint(20) UNSIGNED NOT NULL COMMENT '标签ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_content_tag`(`content_id`, `content_type`, `tag_id`) USING BTREE,
  INDEX `idx_tag_id`(`tag_id`) USING BTREE,
  INDEX `idx_content_type`(`content_type`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '内容标签关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sx_factory_statistics
-- ----------------------------
DROP TABLE IF EXISTS `sx_factory_statistics`;
CREATE TABLE `sx_factory_statistics`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `factory_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '工厂类型',
  `total_factories` int(11) NOT NULL DEFAULT 0 COMMENT '总工厂数',
  `active_factories` int(11) NOT NULL DEFAULT 0 COMMENT '活跃工厂数',
  `total_workers` int(11) NOT NULL DEFAULT 0 COMMENT '总工人数',
  `total_stories` int(11) NOT NULL DEFAULT 0 COMMENT '总故事数',
  `avg_rating` decimal(3, 2) NOT NULL DEFAULT 0.00 COMMENT '平均评分',
  `total_views` int(11) NOT NULL DEFAULT 0 COMMENT '总浏览量',
  `total_likes` int(11) NOT NULL DEFAULT 0 COMMENT '总点赞数',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_stat_date_type`(`stat_date`, `factory_type`) USING BTREE,
  INDEX `idx_stat_date`(`stat_date`) USING BTREE,
  INDEX `idx_factory_type`(`factory_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '工厂统计表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sx_hot_content
-- ----------------------------
DROP TABLE IF EXISTS `sx_hot_content`;
CREATE TABLE `sx_hot_content`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '热门ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `content_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '内容类型（story/factory/people）',
  `content_id` bigint(20) UNSIGNED NOT NULL COMMENT '内容ID',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '内容标题',
  `author` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '作者',
  `view_count` int(11) NOT NULL DEFAULT 0 COMMENT '浏览量',
  `like_count` int(11) NOT NULL DEFAULT 0 COMMENT '点赞数',
  `share_count` int(11) NOT NULL DEFAULT 0 COMMENT '分享数',
  `comment_count` int(11) NOT NULL DEFAULT 0 COMMENT '评论数',
  `engagement_score` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '互动得分',
  `hot_rank` int(11) NOT NULL DEFAULT 0 COMMENT '热门排名',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_stat_date_content`(`stat_date`, `content_type`, `content_id`) USING BTREE,
  INDEX `idx_stat_date`(`stat_date`) USING BTREE,
  INDEX `idx_content_type`(`content_type`) USING BTREE,
  INDEX `idx_hot_rank`(`hot_rank`) USING BTREE,
  INDEX `idx_engagement_score`(`engagement_score`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '热门内容表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sx_prompt_versions
-- ----------------------------
DROP TABLE IF EXISTS `sx_prompt_versions`;
CREATE TABLE `sx_prompt_versions`  (
  `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '版本ID',
  `prompt_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '提示词ID',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '版本号',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '提示词内容',
  `parameters` json NULL COMMENT '参数配置JSON',
  `change_log` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '变更日志',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_prompt_id`(`prompt_id`) USING BTREE,
  INDEX `idx_version`(`version`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE,
  CONSTRAINT `fk_prompt_versions_prompt` FOREIGN KEY (`prompt_id`) REFERENCES `cleaning_prompts` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '提示词版本表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sx_recommendation_configs
-- ----------------------------
DROP TABLE IF EXISTS `sx_recommendation_configs`;
CREATE TABLE `sx_recommendation_configs`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '配置键名',
  `config_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置值，JSON格式',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '配置描述',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `created_by` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
  `updated_by` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_config_key`(`config_key`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '推荐配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sx_region_statistics
-- ----------------------------
DROP TABLE IF EXISTS `sx_region_statistics`;
CREATE TABLE `sx_region_statistics`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `province` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '省份',
  `city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '城市',
  `user_count` int(11) NOT NULL DEFAULT 0 COMMENT '用户数量',
  `content_count` int(11) NOT NULL DEFAULT 0 COMMENT '内容数量',
  `view_count` int(11) NOT NULL DEFAULT 0 COMMENT '浏览量',
  `engagement_count` int(11) NOT NULL DEFAULT 0 COMMENT '互动数',
  `engagement_rate` decimal(5, 2) NOT NULL DEFAULT 0.00 COMMENT '互动率',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_stat_date_region`(`stat_date`, `province`, `city`) USING BTREE,
  INDEX `idx_stat_date`(`stat_date`) USING BTREE,
  INDEX `idx_province`(`province`) USING BTREE,
  INDEX `idx_city`(`city`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '地域统计表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sx_tags
-- ----------------------------
DROP TABLE IF EXISTS `sx_tags`;
CREATE TABLE `sx_tags`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '标签ID',
  `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标签名称',
  `slug` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标签标识（英文）',
  `type` enum('story','factory','people','general') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'general' COMMENT '标签类型',
  `color` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '标签颜色',
  `description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '标签描述',
  `content_count` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '内容数量',
  `is_hot` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否热门（0否 1是）',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态（0禁用 1启用）',
  `created_by` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
  `updated_by` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_type_slug`(`type`, `slug`) USING BTREE,
  INDEX `idx_type_status`(`type`, `status`) USING BTREE,
  INDEX `idx_status_hot`(`status`, `is_hot`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 21 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '标签表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sx_template_versions
-- ----------------------------
DROP TABLE IF EXISTS `sx_template_versions`;
CREATE TABLE `sx_template_versions`  (
  `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '版本ID',
  `template_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模板ID',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '版本号',
  `fields` json NOT NULL COMMENT '字段定义JSON',
  `rules` json NULL COMMENT '验证规则JSON',
  `change_log` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '变更日志',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_template_id`(`template_id`) USING BTREE,
  INDEX `idx_version`(`version`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE,
  CONSTRAINT `fk_template_versions_template` FOREIGN KEY (`template_id`) REFERENCES `cleaning_templates` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '模板版本表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sx_trend_data
-- ----------------------------
DROP TABLE IF EXISTS `sx_trend_data`;
CREATE TABLE `sx_trend_data`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '趋势ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `metric_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '指标类型（user/content/engagement）',
  `metric_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '指标名称',
  `metric_value` decimal(15, 2) NOT NULL DEFAULT 0.00 COMMENT '指标值',
  `change_rate` decimal(5, 2) NOT NULL DEFAULT 0.00 COMMENT '变化率',
  `trend_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '趋势类型（up/down/stable）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_stat_date_metric`(`stat_date`, `metric_type`, `metric_name`) USING BTREE,
  INDEX `idx_stat_date`(`stat_date`) USING BTREE,
  INDEX `idx_metric_type`(`metric_type`) USING BTREE,
  INDEX `idx_metric_name`(`metric_name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '趋势数据表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sx_user_badge_records
-- ----------------------------
DROP TABLE IF EXISTS `sx_user_badge_records`;
CREATE TABLE `sx_user_badge_records`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户ID',
  `badge_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '徽章ID',
  `obtain_way` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '获得方式',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '获得时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_badge`(`user_id`, `badge_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_badge_id`(`badge_id`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户徽章记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sx_user_badges
-- ----------------------------
DROP TABLE IF EXISTS `sx_user_badges`;
CREATE TABLE `sx_user_badges`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '徽章ID',
  `badge_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '徽章名称',
  `badge_icon` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '徽章图标',
  `badge_color` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '徽章颜色',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '徽章描述',
  `condition` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '获得条件',
  `badge_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '徽章类型：1-等级徽章，2-成就徽章，3-活动徽章',
  `level` int(11) NOT NULL DEFAULT 0 COMMENT '徽章等级',
  `score` int(11) NOT NULL DEFAULT 0 COMMENT '徽章积分',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `created_by` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
  `updated_by` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_badge_type`(`badge_type`) USING BTREE,
  INDEX `idx_level`(`level`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_sort`(`sort`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户徽章表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sx_user_follows
-- ----------------------------
DROP TABLE IF EXISTS `sx_user_follows`;
CREATE TABLE `sx_user_follows`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '关注ID',
  `user_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户ID',
  `follow_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '被关注用户ID',
  `follow_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '关注类型：1-关注用户，2-关注工厂',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态：0-取消关注，1-已关注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '关注时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_follow`(`user_id`, `follow_id`, `follow_type`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_follow_id`(`follow_id`) USING BTREE,
  INDEX `idx_follow_type`(`follow_type`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户关注表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sx_user_levels
-- ----------------------------
DROP TABLE IF EXISTS `sx_user_levels`;
CREATE TABLE `sx_user_levels`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '等级ID',
  `level_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '等级名称',
  `level_icon` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '等级图标',
  `level_color` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '等级颜色',
  `level` int(11) NOT NULL DEFAULT 1 COMMENT '等级数值',
  `min_exp` int(11) NOT NULL DEFAULT 0 COMMENT '最小经验值',
  `max_exp` int(11) NOT NULL DEFAULT 0 COMMENT '最大经验值',
  `privileges` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '等级特权，JSON格式',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '等级描述',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `created_by` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
  `updated_by` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_level`(`level`) USING BTREE,
  INDEX `idx_min_exp`(`min_exp`) USING BTREE,
  INDEX `idx_max_exp`(`max_exp`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户等级表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sx_user_login_logs
-- ----------------------------
DROP TABLE IF EXISTS `sx_user_login_logs`;
CREATE TABLE `sx_user_login_logs`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户ID',
  `login_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '登录类型：phone-手机，email-邮箱，wechat-微信，qq-QQ',
  `ip_address` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'IP地址',
  `user_agent` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '用户代理',
  `device` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '设备信息',
  `location` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '登录地点',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '登录状态：0-失败，1-成功',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_login_type`(`login_type`) USING BTREE,
  INDEX `idx_ip_address`(`ip_address`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户登录日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sx_user_scores
-- ----------------------------
DROP TABLE IF EXISTS `sx_user_scores`;
CREATE TABLE `sx_user_scores`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户ID',
  `score_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '积分类型：1-获得，2-消费',
  `score` int(11) NOT NULL DEFAULT 0 COMMENT '积分数量',
  `source` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '积分来源',
  `source_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '来源ID',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '描述',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_score_type`(`score_type`) USING BTREE,
  INDEX `idx_source`(`source`) USING BTREE,
  INDEX `idx_source_id`(`source_id`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户积分记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sx_user_statistics
-- ----------------------------
DROP TABLE IF EXISTS `sx_user_statistics`;
CREATE TABLE `sx_user_statistics`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `total_users` int(11) NOT NULL DEFAULT 0 COMMENT '总用户数',
  `new_users` int(11) NOT NULL DEFAULT 0 COMMENT '新增用户数',
  `active_users` int(11) NOT NULL DEFAULT 0 COMMENT '活跃用户数',
  `retention_rate` decimal(5, 2) NOT NULL DEFAULT 0.00 COMMENT '留存率',
  `avg_session_time` int(11) NOT NULL DEFAULT 0 COMMENT '平均会话时长（秒）',
  `bounce_rate` decimal(5, 2) NOT NULL DEFAULT 0.00 COMMENT '跳出率',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_stat_date`(`stat_date`) USING BTREE,
  INDEX `idx_stat_date`(`stat_date`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户统计表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sx_user_verifies
-- ----------------------------
DROP TABLE IF EXISTS `sx_user_verifies`;
CREATE TABLE `sx_user_verifies`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '认证ID',
  `user_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户ID',
  `verify_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '认证类型：1-身份认证，2-工厂认证，3-专家认证',
  `real_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '真实姓名',
  `id_card` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '身份证号',
  `id_card_front` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '身份证正面',
  `id_card_back` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '身份证反面',
  `company_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '公司名称',
  `company_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '公司代码',
  `business_license` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '营业执照',
  `certificates` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '相关证书，JSON格式',
  `description` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '认证说明',
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '认证状态：0-待审核，1-审核通过，2-审核拒绝',
  `reject_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '拒绝原因',
  `verify_by` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '审核人',
  `verify_time` datetime NULL DEFAULT NULL COMMENT '审核时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_verify_type`(`verify_type`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_verify_by`(`verify_by`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户认证表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sx_users
-- ----------------------------
DROP TABLE IF EXISTS `sx_users`;
CREATE TABLE `sx_users`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '用户名',
  `nickname` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '昵称',
  `avatar` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '头像',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '手机号',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '邮箱',
  `gender` tinyint(4) NOT NULL DEFAULT 0 COMMENT '性别：0-未知，1-男，2-女',
  `birthday` date NULL DEFAULT NULL COMMENT '生日',
  `province` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '省份',
  `city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '城市',
  `address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '详细地址',
  `bio` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '个人简介',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-正常，2-待审核',
  `user_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '用户类型：1-普通用户，2-工厂用户，3-认证用户',
  `verify_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '认证状态：0-未认证，1-认证中，2-已认证，3-认证失败',
  `verify_info` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '认证信息',
  `verify_time` datetime NULL DEFAULT NULL COMMENT '认证时间',
  `last_login_time` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '最后登录IP',
  `login_count` int(11) NOT NULL DEFAULT 0 COMMENT '登录次数',
  `follow_count` int(11) NOT NULL DEFAULT 0 COMMENT '关注数',
  `fans_count` int(11) NOT NULL DEFAULT 0 COMMENT '粉丝数',
  `like_count` int(11) NOT NULL DEFAULT 0 COMMENT '获赞数',
  `story_count` int(11) NOT NULL DEFAULT 0 COMMENT '故事数',
  `comment_count` int(11) NOT NULL DEFAULT 0 COMMENT '评论数',
  `share_count` int(11) NOT NULL DEFAULT 0 COMMENT '分享数',
  `score` int(11) NOT NULL DEFAULT 0 COMMENT '积分',
  `level` int(11) NOT NULL DEFAULT 1 COMMENT '等级',
  `experience` int(11) NOT NULL DEFAULT 0 COMMENT '经验值',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_username`(`username`) USING BTREE,
  UNIQUE INDEX `uk_phone`(`phone`) USING BTREE,
  UNIQUE INDEX `uk_email`(`email`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_user_type`(`user_type`) USING BTREE,
  INDEX `idx_verify_status`(`verify_status`) USING BTREE,
  INDEX `idx_province_city`(`province`, `city`) USING BTREE,
  INDEX `idx_level`(`level`) USING BTREE,
  INDEX `idx_score`(`score`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE,
  INDEX `idx_deleted_at`(`deleted_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sxr_users
-- ----------------------------
DROP TABLE IF EXISTS `sxr_users`;
CREATE TABLE `sxr_users`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邮箱',
  `password_hash` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码哈希',
  `nickname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '昵称',
  `avatar_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '头像URL',
  `gender` tinyint(4) NULL DEFAULT NULL COMMENT '性别：0-未知，1-男，2-女',
  `birth_date` date NULL DEFAULT NULL COMMENT '出生日期',
  `location` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '所在地区',
  `bio` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '个人简介',
  `status` tinyint(4) NULL DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `last_login_at` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `username`(`username`) USING BTREE,
  UNIQUE INDEX `phone`(`phone`) USING BTREE,
  UNIQUE INDEX `email`(`email`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_attachment
-- ----------------------------
DROP TABLE IF EXISTS `sys_attachment`;
CREATE TABLE `sys_attachment`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '文件ID',
  `app_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '应用ID',
  `drive` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '上传驱动',
  `name` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件原始名',
  `kind` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '上传类型',
  `mime_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '扩展类型',
  `path` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '本地路径',
  `size` bigint(20) NULL DEFAULT 0 COMMENT '文件大小',
  `ext` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '扩展名',
  `md5` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'md5校验码',
  `created_by` bigint(20) NULL DEFAULT 0 COMMENT '上传人ID',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态',
  `created_at` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `md5`(`md5`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '附件管理' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_auth_rule
-- ----------------------------
DROP TABLE IF EXISTS `sys_auth_rule`;
CREATE TABLE `sys_auth_rule`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `pid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '父ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '规则名称',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '规则名称',
  `icon` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图标',
  `condition` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '条件',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `menu_type` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '类型 0目录 1菜单 2按钮',
  `weigh` int(10) NOT NULL DEFAULT 0 COMMENT '权重',
  `is_hide` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '显示状态',
  `path` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '路由地址',
  `component` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '组件路径',
  `is_link` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否外链 1是 0否',
  `module_type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '所属模块',
  `model_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '模型ID',
  `is_iframe` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否内嵌iframe',
  `is_cached` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否缓存',
  `redirect` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '路由重定向地址',
  `is_affix` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否固定',
  `link_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '链接地址',
  `created_at` datetime NULL DEFAULT NULL COMMENT '创建日期',
  `updated_at` datetime NULL DEFAULT NULL COMMENT '修改日期',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `name`(`name`) USING BTREE,
  INDEX `pid`(`pid`) USING BTREE,
  INDEX `weigh`(`weigh`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 172 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '菜单节点表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for sys_config
-- ----------------------------
DROP TABLE IF EXISTS `sys_config`;
CREATE TABLE `sys_config`  (
  `config_id` int(5) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '参数主键',
  `config_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '参数名称',
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '参数键名',
  `config_value` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '参数键值',
  `config_type` tinyint(1) NULL DEFAULT 0 COMMENT '系统内置（Y是 N否）',
  `create_by` int(64) UNSIGNED NULL DEFAULT 0 COMMENT '创建者',
  `update_by` int(64) UNSIGNED NULL DEFAULT 0 COMMENT '更新者',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `created_at` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`config_id`) USING BTREE,
  UNIQUE INDEX `uni_config_key`(`config_key`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for sys_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_dept`;
CREATE TABLE `sys_dept`  (
  `dept_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '部门id',
  `parent_id` bigint(20) NULL DEFAULT 0 COMMENT '父部门id',
  `ancestors` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '祖级列表',
  `dept_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '部门名称',
  `order_num` int(4) NULL DEFAULT 0 COMMENT '显示顺序',
  `leader` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '负责人',
  `phone` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系电话',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮箱',
  `status` tinyint(3) UNSIGNED NULL DEFAULT 0 COMMENT '部门状态（0正常 1停用）',
  `created_by` bigint(20) UNSIGNED NULL DEFAULT 0 COMMENT '创建人',
  `updated_by` bigint(20) NULL DEFAULT NULL COMMENT '修改人',
  `created_at` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `deleted_at` datetime NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`dept_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 203 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '部门表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for sys_dict_data
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_data`;
CREATE TABLE `sys_dict_data`  (
  `dict_code` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '字典编码',
  `dict_sort` int(4) NULL DEFAULT 0 COMMENT '字典排序',
  `dict_label` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典标签',
  `dict_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典键值',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典类型',
  `css_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '样式属性（其他样式扩展）',
  `list_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表格回显样式',
  `is_default` tinyint(1) NULL DEFAULT 0 COMMENT '是否默认（1是 0否）',
  `status` tinyint(1) NULL DEFAULT 0 COMMENT '状态（1正常 0停用）',
  `create_by` bigint(64) UNSIGNED NULL DEFAULT 0 COMMENT '创建者',
  `update_by` bigint(64) UNSIGNED NULL DEFAULT 0 COMMENT '更新者',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `created_at` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`dict_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 126 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '字典数据表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for sys_dict_type
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_type`;
CREATE TABLE `sys_dict_type`  (
  `dict_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '字典主键',
  `pid` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '父级ID',
  `dict_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典名称',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典类型',
  `status` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '状态（0正常 1停用）',
  `create_by` int(64) UNSIGNED NULL DEFAULT 0 COMMENT '创建者',
  `update_by` int(64) UNSIGNED NULL DEFAULT 0 COMMENT '更新者',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `created_at` datetime NULL DEFAULT NULL COMMENT '创建日期',
  `updated_at` datetime NULL DEFAULT NULL COMMENT '修改日期',
  PRIMARY KEY (`dict_id`) USING BTREE,
  UNIQUE INDEX `dict_type`(`dict_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 57 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '字典类型表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for sys_job
-- ----------------------------
DROP TABLE IF EXISTS `sys_job`;
CREATE TABLE `sys_job`  (
  `job_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `job_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '任务名称',
  `job_params` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '参数',
  `job_group` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'DEFAULT' COMMENT '任务组名',
  `invoke_target` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调用目标字符串',
  `cron_expression` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'cron执行表达式',
  `misfire_policy` tinyint(4) NULL DEFAULT 1 COMMENT '计划执行策略（1多次执行 2执行一次）',
  `concurrent` tinyint(4) NULL DEFAULT 1 COMMENT '是否并发执行（0允许 1禁止）',
  `status` tinyint(4) NULL DEFAULT 0 COMMENT '状态（0正常 1暂停）',
  `created_by` bigint(64) UNSIGNED NULL DEFAULT 0 COMMENT '创建者',
  `updated_by` bigint(64) UNSIGNED NULL DEFAULT 0 COMMENT '更新者',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '备注信息',
  `created_at` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`job_id`) USING BTREE,
  UNIQUE INDEX `invoke_target`(`invoke_target`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '定时任务调度表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for sys_job_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_job_log`;
CREATE TABLE `sys_job_log`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `target_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '方法名',
  `created_at` datetime NULL DEFAULT NULL COMMENT '执行日期',
  `result` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '执行结果',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1432 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '任务日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_login_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_login_log`;
CREATE TABLE `sys_login_log`  (
  `info_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '访问ID',
  `login_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '登录账号',
  `ipaddr` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '登录IP地址',
  `login_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '登录地点',
  `browser` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '浏览器类型',
  `os` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '操作系统',
  `status` tinyint(4) NULL DEFAULT 0 COMMENT '登录状态（0成功 1失败）',
  `msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '提示消息',
  `login_time` datetime NULL DEFAULT NULL COMMENT '登录时间',
  `module` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '登录模块',
  PRIMARY KEY (`info_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 38 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统访问记录' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for sys_notice
-- ----------------------------
DROP TABLE IF EXISTS `sys_notice`;
CREATE TABLE `sys_notice`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `title` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '标题',
  `type` bigint(20) NOT NULL COMMENT '类型',
  `tag` int(11) NULL DEFAULT NULL COMMENT '标签',
  `content` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '内容',
  `remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态',
  `created_by` bigint(20) NULL DEFAULT NULL COMMENT '发送人',
  `updated_by` bigint(20) NULL DEFAULT 0 COMMENT '修改人',
  `created_at` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `receiver` json NULL COMMENT '接收者（私信）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '通知公告' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_notice_read
-- ----------------------------
DROP TABLE IF EXISTS `sys_notice_read`;
CREATE TABLE `sys_notice_read`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `notice_id` bigint(20) NOT NULL COMMENT '信息id',
  `user_id` bigint(20) NOT NULL COMMENT '用户id',
  `clicks` int(11) NULL DEFAULT NULL COMMENT '点击次数',
  `updated_at` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `created_at` datetime NULL DEFAULT NULL COMMENT '阅读时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `notice_id`(`notice_id`, `user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '已读记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_oper_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_oper_log`;
CREATE TABLE `sys_oper_log`  (
  `oper_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '日志主键',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '模块标题',
  `business_type` int(2) NULL DEFAULT 0 COMMENT '业务类型（0其它 1新增 2修改 3删除）',
  `method` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '方法名称',
  `request_method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '请求方式',
  `operator_type` int(1) NULL DEFAULT 0 COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
  `oper_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '操作人员',
  `dept_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '部门名称',
  `oper_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '请求URL',
  `oper_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '主机地址',
  `oper_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '操作地点',
  `oper_param` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '请求参数',
  `error_msg` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '错误消息',
  `oper_time` datetime NULL DEFAULT NULL COMMENT '操作时间',
  PRIMARY KEY (`oper_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 902 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '操作日志记录' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for sys_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_post`;
CREATE TABLE `sys_post`  (
  `post_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '岗位ID',
  `post_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '岗位编码',
  `post_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '岗位名称',
  `post_sort` int(4) NOT NULL COMMENT '显示顺序',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '状态（0正常 1停用）',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `created_by` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
  `updated_by` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '修改人',
  `created_at` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `deleted_at` datetime NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`post_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '岗位信息表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `pid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '父级ID',
  `status` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '状态;0:禁用;1:正常',
  `list_order` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序',
  `name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '角色名称',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `data_scope` tinyint(3) UNSIGNED NOT NULL DEFAULT 3 COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
  `created_at` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `created_by` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '添加人',
  `effectiveTime` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '角色有效日期',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for sys_role_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_dept`;
CREATE TABLE `sys_role_dept`  (
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  `dept_id` bigint(20) NOT NULL COMMENT '部门ID',
  PRIMARY KEY (`role_id`, `dept_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色和部门关联表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for sys_role_scope
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_scope`;
CREATE TABLE `sys_role_scope`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `role_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '角色id',
  `menu_id` int(11) NOT NULL COMMENT 'api接口id',
  `data_scope` int(11) NOT NULL COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
  `dept_ids` json NULL COMMENT '扩展数据',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `role_id`(`role_id`, `menu_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 98 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色数据权限' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '用户名',
  `mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '中国手机不带国家代码，国际手机号格式为：国家代码-手机号',
  `user_nickname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '用户昵称',
  `birthday` int(11) NOT NULL DEFAULT 0 COMMENT '生日',
  `user_password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '登录密码;cmf_password加密',
  `user_salt` char(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '加密盐',
  `user_status` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '用户状态;0:禁用,1:正常,2:未验证',
  `user_email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户登录邮箱',
  `sex` tinyint(2) NOT NULL DEFAULT 0 COMMENT '性别;0:保密,1:男,2:女',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户头像',
  `dept_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '部门id',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `is_admin` tinyint(4) NOT NULL DEFAULT 1 COMMENT '是否后台管理员 1 是  0   否',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '联系地址',
  `describe` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT ' 描述信息',
  `last_login_ip` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '最后登录ip',
  `last_login_time` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  `created_at` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime NULL DEFAULT NULL COMMENT '删除时间',
  `open_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '微信open id',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `user_login`(`user_name`, `deleted_at`) USING BTREE,
  UNIQUE INDEX `mobile`(`mobile`, `deleted_at`) USING BTREE,
  INDEX `user_nickname`(`user_nickname`) USING BTREE,
  INDEX `open_id`(`open_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 43 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for sys_user_online
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_online`;
CREATE TABLE `sys_user_online`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `uuid` char(32) CHARACTER SET latin1 COLLATE latin1_general_ci NOT NULL DEFAULT '' COMMENT '用户标识',
  `token` varchar(255) CHARACTER SET latin1 COLLATE latin1_general_ci NOT NULL DEFAULT '' COMMENT '用户token',
  `create_time` datetime NULL DEFAULT NULL COMMENT '登录时间',
  `user_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户名',
  `ip` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '登录ip',
  `explorer` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '浏览器',
  `os` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '操作系统',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uni_token`(`token`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 22 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户在线状态表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for sys_user_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_post`;
CREATE TABLE `sys_user_post`  (
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `post_id` bigint(20) NOT NULL COMMENT '岗位ID',
  PRIMARY KEY (`user_id`, `post_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户与岗位关联表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for tools_gen_table
-- ----------------------------
DROP TABLE IF EXISTS `tools_gen_table`;
CREATE TABLE `tools_gen_table`  (
  `table_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `table_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '表名称',
  `table_comment` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '表描述',
  `class_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '实体类名称',
  `tpl_category` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'crud' COMMENT '使用的模板（crud单表操作 tree树表操作）',
  `package_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '生成包路径',
  `module_name` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '生成模块名',
  `business_name` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '生成业务名',
  `function_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '生成功能名',
  `function_author` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '生成功能作者',
  `options` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '其它生成选项',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注',
  `overwrite` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否覆盖原有文件',
  `sort_column` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '排序字段名',
  `sort_type` varchar(4) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'asc' COMMENT '排序方式 (asc顺序 desc倒序)',
  `show_detail` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否有查看详情功能',
  `excel_port` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否有导出excel功能',
  `use_snow_id` bit(1) NOT NULL DEFAULT b'0' COMMENT '主键是否雪花ID',
  `use_virtual` bit(1) NOT NULL DEFAULT b'0' COMMENT '树表是否使用虚拟表',
  `excel_imp` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否导入excel',
  `overwrite_info` json NULL COMMENT '生成覆盖的文件',
  `menu_pid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '父级菜单ID',
  PRIMARY KEY (`table_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 113 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '代码生成业务表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for tools_gen_table_column
-- ----------------------------
DROP TABLE IF EXISTS `tools_gen_table_column`;
CREATE TABLE `tools_gen_table_column`  (
  `column_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `table_id` bigint(20) NULL DEFAULT NULL COMMENT '归属表编号',
  `column_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '列名称',
  `column_comment` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '列描述',
  `column_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '列类型',
  `go_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'Go类型',
  `ts_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'TS类型',
  `go_field` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'Go字段名',
  `html_field` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'html字段名',
  `is_pk` bit(1) NULL DEFAULT b'0' COMMENT '是否主键（1是）',
  `is_increment` bit(1) NULL DEFAULT b'0' COMMENT '是否自增（1是）',
  `is_required` bit(1) NULL DEFAULT b'0' COMMENT '是否必填（1是）',
  `is_edit` bit(1) NULL DEFAULT b'0' COMMENT '是否编辑字段（1是）',
  `is_list` bit(1) NULL DEFAULT b'1' COMMENT '是否列表字段（1是）',
  `is_detail` bit(1) NULL DEFAULT b'1' COMMENT '是否详情字段',
  `is_query` bit(1) NULL DEFAULT b'0' COMMENT '是否查询字段（1是）',
  `sort_order_edit` int(11) NULL DEFAULT 999 COMMENT '插入编辑显示顺序',
  `sort_order_list` int(11) NULL DEFAULT 999 COMMENT '列表显示顺序',
  `sort_order_detail` int(11) NULL DEFAULT 999 COMMENT '详情显示顺序',
  `sort_order_query` int(11) NULL DEFAULT 999 COMMENT '查询显示顺序',
  `query_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'EQ' COMMENT '查询方式（等于、不等于、大于、小于、范围）',
  `html_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）',
  `dict_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典类型',
  `link_table_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联表名',
  `link_table_class` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联表类名',
  `link_table_module_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联表模块名',
  `link_table_business_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联表业务名',
  `link_table_package` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联表包名',
  `link_label_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联表键名',
  `link_label_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联表字段值',
  `col_span` int(11) NULL DEFAULT 12 COMMENT '详情页占列数',
  `row_span` int(11) NULL DEFAULT 1 COMMENT '详情页占行数',
  `is_row_start` bit(1) NULL DEFAULT b'0' COMMENT '详情页为行首',
  `min_width` int(11) NULL DEFAULT 100 COMMENT '表格最小宽度',
  `is_fixed` bit(1) NULL DEFAULT b'0' COMMENT '是否表格列左固定',
  `is_overflow_tooltip` bit(1) NULL DEFAULT b'0' COMMENT '是否过长自动隐藏',
  `is_cascade` bit(1) NULL DEFAULT b'0' COMMENT '是否级联查询',
  `parent_column_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '上级字段名',
  `cascade_column_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '级联查询字段',
  PRIMARY KEY (`column_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1147 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '代码生成业务表字段' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_types
-- ----------------------------
DROP TABLE IF EXISTS `user_types`;
CREATE TABLE `user_types`  (
  `id` tinyint(2) NOT NULL COMMENT '类型ID',
  `type_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类型名称',
  `description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '类型描述',
  `sort_order` int(11) NULL DEFAULT 0 COMMENT '排序',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户类型表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户名',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '密码',
  `nickname` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '昵称',
  `real_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '真实姓名',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '手机号码',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '邮箱地址',
  `avatar` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '头像URL',
  `gender` tinyint(1) NULL DEFAULT 0 COMMENT '性别：0-未知，1-男，2-女',
  `birthday` date NULL DEFAULT NULL COMMENT '出生日期',
  `user_type_id` tinyint(2) NULL DEFAULT 1 COMMENT '用户类型ID',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态：0-禁用，1-正常，2-待审核',
  `is_verified` tinyint(1) NULL DEFAULT 0 COMMENT '是否实名认证',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime NULL DEFAULT NULL COMMENT '软删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_username`(`username`) USING BTREE,
  UNIQUE INDEX `uk_phone`(`phone`) USING BTREE,
  UNIQUE INDEX `uk_email`(`email`) USING BTREE,
  INDEX `idx_user_type_id`(`user_type_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_deleted_at`(`deleted_at`) USING BTREE,
  CONSTRAINT `fk_users_user_type_id` FOREIGN KEY (`user_type_id`) REFERENCES `user_types` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户基础信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wikipedia_batch_items
-- ----------------------------
DROP TABLE IF EXISTS `wikipedia_batch_items`;
CREATE TABLE `wikipedia_batch_items`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `batch_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '批次ID',
  `submission_id` bigint(20) UNSIGNED NOT NULL COMMENT '提交记录ID',
  `wikipedia_submission_id` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT 'Wikipedia提交记录ID',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending' COMMENT '状态（pending/processing/success/failed/skipped）',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '错误信息',
  `processing_time` decimal(10, 3) NULL DEFAULT NULL COMMENT '处理时间（秒）',
  `retry_count` int(11) NOT NULL DEFAULT 0 COMMENT '重试次数',
  `result` json NULL COMMENT '处理结果（JSON对象）',
  `started_at` datetime NULL DEFAULT NULL COMMENT '开始处理时间',
  `completed_at` datetime NULL DEFAULT NULL COMMENT '完成时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_batch_id`(`batch_id`) USING BTREE,
  INDEX `idx_submission_id`(`submission_id`) USING BTREE,
  INDEX `idx_wikipedia_submission_id`(`wikipedia_submission_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  CONSTRAINT `wikipedia_batch_items_ibfk_1` FOREIGN KEY (`batch_id`) REFERENCES `wikipedia_batch_operations` (`batch_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'Wikipedia批量操作详情表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wikipedia_batch_operations
-- ----------------------------
DROP TABLE IF EXISTS `wikipedia_batch_operations`;
CREATE TABLE `wikipedia_batch_operations`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `batch_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '批次ID',
  `operation_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作类型（submit/sync/update）',
  `data_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '数据类型（factory/people/story）',
  `total_count` int(11) NOT NULL DEFAULT 0 COMMENT '总数量',
  `success_count` int(11) NOT NULL DEFAULT 0 COMMENT '成功数量',
  `failed_count` int(11) NOT NULL DEFAULT 0 COMMENT '失败数量',
  `pending_count` int(11) NOT NULL DEFAULT 0 COMMENT '待处理数量',
  `processing_count` int(11) NOT NULL DEFAULT 0 COMMENT '处理中数量',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending' COMMENT '批次状态（pending/processing/completed/failed/cancelled）',
  `progress` decimal(5, 2) NOT NULL DEFAULT 0.00 COMMENT '进度百分比',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '错误信息',
  `config` json NULL COMMENT '批次配置（JSON对象）',
  `result` json NULL COMMENT '批次结果（JSON对象）',
  `started_at` datetime NULL DEFAULT NULL COMMENT '开始时间',
  `completed_at` datetime NULL DEFAULT NULL COMMENT '完成时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_batch_id`(`batch_id`) USING BTREE,
  INDEX `idx_operation_type`(`operation_type`) USING BTREE,
  INDEX `idx_data_type`(`data_type`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'Wikipedia批量操作记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wikipedia_configs
-- ----------------------------
DROP TABLE IF EXISTS `wikipedia_configs`;
CREATE TABLE `wikipedia_configs`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置键',
  `config_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置值',
  `config_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'string' COMMENT '配置类型（string/int/float/bool/json）',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '配置描述',
  `is_encrypted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否加密存储',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_config_key`(`config_key`) USING BTREE,
  INDEX `idx_is_active`(`is_active`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'Wikipedia配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wikipedia_operation_logs
-- ----------------------------
DROP TABLE IF EXISTS `wikipedia_operation_logs`;
CREATE TABLE `wikipedia_operation_logs`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `operation_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作ID',
  `operation_type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作类型（create_page/update_page/upload_file/login等）',
  `related_id` bigint(20) NULL DEFAULT NULL COMMENT '关联记录ID',
  `related_type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关联记录类型（submission/batch/sync）',
  `wikipedia_page_id` bigint(20) NULL DEFAULT NULL COMMENT 'Wikipedia页面ID',
  `wikipedia_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Wikipedia页面标题',
  `level` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '日志级别（DEBUG/INFO/WARN/ERROR）',
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '日志消息',
  `request_data` json NULL COMMENT '请求数据（JSON对象）',
  `response_data` json NULL COMMENT '响应数据（JSON对象）',
  `error_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '错误代码',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '错误消息',
  `processing_time` decimal(10, 3) NULL DEFAULT NULL COMMENT '处理时间（秒）',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户代理',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_operation_id`(`operation_id`) USING BTREE,
  INDEX `idx_operation_type`(`operation_type`) USING BTREE,
  INDEX `idx_related_id_type`(`related_id`, `related_type`) USING BTREE,
  INDEX `idx_wikipedia_page_id`(`wikipedia_page_id`) USING BTREE,
  INDEX `idx_level`(`level`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'Wikipedia操作日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wikipedia_statistics
-- ----------------------------
DROP TABLE IF EXISTS `wikipedia_statistics`;
CREATE TABLE `wikipedia_statistics`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `data_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '数据类型（factory/people/story/all）',
  `total_submissions` int(11) NOT NULL DEFAULT 0 COMMENT '总提交数',
  `successful_submissions` int(11) NOT NULL DEFAULT 0 COMMENT '成功提交数',
  `failed_submissions` int(11) NOT NULL DEFAULT 0 COMMENT '失败提交数',
  `pending_submissions` int(11) NOT NULL DEFAULT 0 COMMENT '待处理提交数',
  `avg_processing_time` decimal(10, 3) NULL DEFAULT NULL COMMENT '平均处理时间（秒）',
  `success_rate` decimal(5, 2) NULL DEFAULT NULL COMMENT '成功率（百分比）',
  `error_rate` decimal(5, 2) NULL DEFAULT NULL COMMENT '错误率（百分比）',
  `throughput` decimal(10, 2) NULL DEFAULT NULL COMMENT '吞吐量（每小时处理数）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_stat_date_data_type`(`stat_date`, `data_type`) USING BTREE,
  INDEX `idx_stat_date`(`stat_date`) USING BTREE,
  INDEX `idx_data_type`(`data_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'Wikipedia统计表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wikipedia_submissions
-- ----------------------------
DROP TABLE IF EXISTS `wikipedia_submissions`;
CREATE TABLE `wikipedia_submissions`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `submission_uuid` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '稿件UUID',
  `task_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '任务ID',
  `wiki_page_id` bigint(20) NULL DEFAULT NULL COMMENT 'Wikipedia页面ID',
  `wiki_page_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Wikipedia页面标题',
  `wiki_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Wikipedia页面URL',
  `page_content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '页面内容',
  `namespace` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'main' COMMENT '命名空间',
  `summary` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '编辑摘要',
  `categories` json NULL COMMENT '分类',
  `tags` json NULL COMMENT '标签',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending' COMMENT '提交状态：pending/success/failed',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '错误信息',
  `submitted_at` datetime NULL DEFAULT NULL COMMENT '提交时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_submission_uuid`(`submission_uuid`) USING BTREE,
  INDEX `idx_task_id`(`task_id`) USING BTREE,
  INDEX `idx_wiki_page_id`(`wiki_page_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_submitted_at`(`submitted_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'Wikipedia提交记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wikipedia_sync_records
-- ----------------------------
DROP TABLE IF EXISTS `wikipedia_sync_records`;
CREATE TABLE `wikipedia_sync_records`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sync_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '同步ID',
  `sync_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '同步类型（page/batch）',
  `submission_id` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '提交记录ID（单页面同步时使用）',
  `batch_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '批次ID（批量同步时使用）',
  `wikipedia_page_id` bigint(20) NULL DEFAULT NULL COMMENT 'Wikipedia页面ID',
  `wikipedia_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Wikipedia页面标题',
  `wikipedia_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Wikipedia页面URL',
  `wikipedia_revision_id` bigint(20) NULL DEFAULT NULL COMMENT 'Wikipedia版本ID',
  `sync_action` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '同步动作（create/update/delete）',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending' COMMENT '同步状态（pending/processing/success/failed）',
  `progress` decimal(5, 2) NOT NULL DEFAULT 0.00 COMMENT '进度百分比',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '错误信息',
  `force_update` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否强制更新',
  `changes_detected` json NULL COMMENT '检测到的变更（JSON对象）',
  `sync_result` json NULL COMMENT '同步结果（JSON对象）',
  `started_at` datetime NULL DEFAULT NULL COMMENT '开始时间',
  `completed_at` datetime NULL DEFAULT NULL COMMENT '完成时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_sync_id`(`sync_id`) USING BTREE,
  INDEX `idx_sync_type`(`sync_type`) USING BTREE,
  INDEX `idx_submission_id`(`submission_id`) USING BTREE,
  INDEX `idx_batch_id`(`batch_id`) USING BTREE,
  INDEX `idx_wikipedia_page_id`(`wikipedia_page_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'Wikipedia同步记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- View structure for v_activity_statistics
-- ----------------------------
DROP VIEW IF EXISTS `v_activity_statistics`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_activity_statistics` AS select cast(`sx_activity_logs`.`created_at` as date) AS `activity_date`,`sx_activity_logs`.`activity_type` AS `activity_type`,count(0) AS `activity_count`,count(distinct `sx_activity_logs`.`user_id`) AS `unique_users` from `sx_activity_logs` where (`sx_activity_logs`.`created_at` >= (now() - interval 30 day)) group by cast(`sx_activity_logs`.`created_at` as date),`sx_activity_logs`.`activity_type` order by `activity_date` desc,`activity_count` desc;

-- ----------------------------
-- View structure for v_algorithm_performance
-- ----------------------------
DROP VIEW IF EXISTS `v_algorithm_performance`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_algorithm_performance` AS select `sx_content_recommendations`.`algorithm` AS `algorithm`,count(0) AS `recommendation_count`,sum(`sx_content_recommendations`.`click_count`) AS `total_clicks`,sum(`sx_content_recommendations`.`view_count`) AS `total_views`,avg(`sx_content_recommendations`.`click_count`) AS `avg_clicks`,avg(`sx_content_recommendations`.`view_count`) AS `avg_views`,round((avg((`sx_content_recommendations`.`click_count` / nullif(`sx_content_recommendations`.`view_count`,0))) * 100),2) AS `avg_click_rate`,avg(`sx_content_recommendations`.`weight`) AS `avg_weight`,max(`sx_content_recommendations`.`updated_at`) AS `last_updated` from `sx_content_recommendations` where (isnull(`sx_content_recommendations`.`deleted_at`) and (`sx_content_recommendations`.`status` = 1)) group by `sx_content_recommendations`.`algorithm` order by `avg_click_rate` desc;

-- ----------------------------
-- View structure for v_badge_statistics
-- ----------------------------
DROP VIEW IF EXISTS `v_badge_statistics`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_badge_statistics` AS select `ub`.`id` AS `id`,`ub`.`badge_name` AS `badge_name`,`ub`.`badge_icon` AS `badge_icon`,`ub`.`badge_color` AS `badge_color`,`ub`.`badge_type` AS `badge_type`,`ub`.`level` AS `level`,`ub`.`score` AS `score`,count(`ubr`.`id`) AS `obtain_count`,round(((count(`ubr`.`id`) * 100.0) / (select count(0) from `sx_users` where isnull(`sx_users`.`deleted_at`))),2) AS `obtain_rate`,max(`ubr`.`created_at`) AS `latest_obtain_time` from (`sx_user_badges` `ub` left join `sx_user_badge_records` `ubr` on((`ub`.`id` = `ubr`.`badge_id`))) where (`ub`.`status` = 1) group by `ub`.`id`,`ub`.`badge_name`,`ub`.`badge_icon`,`ub`.`badge_color`,`ub`.`badge_type`,`ub`.`level`,`ub`.`score` order by `obtain_count` desc;

-- ----------------------------
-- View structure for v_banner_statistics
-- ----------------------------
DROP VIEW IF EXISTS `v_banner_statistics`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_banner_statistics` AS select `sx_banners`.`position` AS `position`,count(0) AS `total_count`,sum((case when (`sx_banners`.`status` = 1) then 1 else 0 end)) AS `active_count`,sum((case when (`sx_banners`.`status` = 0) then 1 else 0 end)) AS `inactive_count`,sum(`sx_banners`.`click_count`) AS `total_clicks`,sum(`sx_banners`.`view_count`) AS `total_views`,avg(`sx_banners`.`click_count`) AS `avg_clicks`,avg(`sx_banners`.`view_count`) AS `avg_views`,max(`sx_banners`.`updated_at`) AS `last_updated` from `sx_banners` where isnull(`sx_banners`.`deleted_at`) group by `sx_banners`.`position`;

-- ----------------------------
-- View structure for v_category_statistics
-- ----------------------------
DROP VIEW IF EXISTS `v_category_statistics`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_category_statistics` AS select `c`.`id` AS `id`,`c`.`name` AS `name`,`c`.`type` AS `type`,`c`.`level` AS `level`,`c`.`content_count` AS `content_count`,`c`.`is_hot` AS `is_hot`,`c`.`is_recommend` AS `is_recommend`,`c`.`status` AS `status`,coalesce(`cc`.`content_count`,0) AS `actual_content_count`,`c`.`created_at` AS `created_at` from (`sx_categories` `c` left join (select `sx_content_categories`.`category_id` AS `category_id`,count(0) AS `content_count` from `sx_content_categories` group by `sx_content_categories`.`category_id`) `cc` on((`c`.`id` = `cc`.`category_id`))) where (`c`.`status` = 1);

-- ----------------------------
-- View structure for v_content_statistics_summary
-- ----------------------------
DROP VIEW IF EXISTS `v_content_statistics_summary`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_content_statistics_summary` AS select `sx_content_statistics`.`content_type` AS `content_type`,date_format(`sx_content_statistics`.`stat_date`,'%Y-%m') AS `month`,sum(`sx_content_statistics`.`new_content`) AS `total_new_content`,sum(`sx_content_statistics`.`total_views`) AS `total_views`,sum(`sx_content_statistics`.`total_likes`) AS `total_likes`,sum(`sx_content_statistics`.`total_shares`) AS `total_shares`,sum(`sx_content_statistics`.`total_comments`) AS `total_comments`,avg(`sx_content_statistics`.`avg_engagement`) AS `avg_engagement` from `sx_content_statistics` group by `sx_content_statistics`.`content_type`,date_format(`sx_content_statistics`.`stat_date`,'%Y-%m') order by `sx_content_statistics`.`content_type`,`month` desc;

-- ----------------------------
-- View structure for v_factory_type_stats
-- ----------------------------
DROP VIEW IF EXISTS `v_factory_type_stats`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_factory_type_stats` AS select `sx_factory_statistics`.`factory_type` AS `factory_type`,date_format(`sx_factory_statistics`.`stat_date`,'%Y-%m') AS `month`,avg(`sx_factory_statistics`.`total_factories`) AS `avg_factories`,avg(`sx_factory_statistics`.`active_factories`) AS `avg_active_factories`,sum(`sx_factory_statistics`.`total_workers`) AS `total_workers`,sum(`sx_factory_statistics`.`total_stories`) AS `total_stories`,avg(`sx_factory_statistics`.`avg_rating`) AS `avg_rating`,sum(`sx_factory_statistics`.`total_views`) AS `total_views`,sum(`sx_factory_statistics`.`total_likes`) AS `total_likes` from `sx_factory_statistics` group by `sx_factory_statistics`.`factory_type`,date_format(`sx_factory_statistics`.`stat_date`,'%Y-%m') order by `sx_factory_statistics`.`factory_type`,`month` desc;

-- ----------------------------
-- View structure for v_hot_banners
-- ----------------------------
DROP VIEW IF EXISTS `v_hot_banners`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_hot_banners` AS select `sx_banners`.`id` AS `id`,`sx_banners`.`title` AS `title`,`sx_banners`.`position` AS `position`,`sx_banners`.`click_count` AS `click_count`,`sx_banners`.`view_count` AS `view_count`,round(((`sx_banners`.`click_count` / nullif(`sx_banners`.`view_count`,0)) * 100),2) AS `click_rate`,`sx_banners`.`created_at` AS `created_at`,`sx_banners`.`updated_at` AS `updated_at` from `sx_banners` where (isnull(`sx_banners`.`deleted_at`) and (`sx_banners`.`status` = 1)) order by `sx_banners`.`click_count` desc,`sx_banners`.`view_count` desc limit 20;

-- ----------------------------
-- View structure for v_hot_categories
-- ----------------------------
DROP VIEW IF EXISTS `v_hot_categories`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_hot_categories` AS select `c`.`id` AS `id`,`c`.`name` AS `name`,`c`.`slug` AS `slug`,`c`.`type` AS `type`,`c`.`parent_id` AS `parent_id`,`c`.`level` AS `level`,`c`.`path` AS `path`,`c`.`sort_order` AS `sort_order`,`c`.`icon` AS `icon`,`c`.`color` AS `color`,`c`.`description` AS `description`,`c`.`keywords` AS `keywords`,`c`.`content_count` AS `content_count`,`c`.`is_hot` AS `is_hot`,`c`.`is_recommend` AS `is_recommend`,`c`.`status` AS `status`,`c`.`created_by` AS `created_by`,`c`.`updated_by` AS `updated_by`,`c`.`created_at` AS `created_at`,`c`.`updated_at` AS `updated_at`,coalesce(`cc`.`content_count`,0) AS `actual_content_count` from (`sx_categories` `c` left join (select `sx_content_categories`.`category_id` AS `category_id`,count(0) AS `content_count` from `sx_content_categories` group by `sx_content_categories`.`category_id`) `cc` on((`c`.`id` = `cc`.`category_id`))) where ((`c`.`status` = 1) and (`c`.`is_hot` = 1)) order by `c`.`sort_order` desc,coalesce(`cc`.`content_count`,0) desc;

-- ----------------------------
-- View structure for v_hot_content_ranking
-- ----------------------------
DROP VIEW IF EXISTS `v_hot_content_ranking`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_hot_content_ranking` AS select `sx_hot_content`.`content_type` AS `content_type`,`sx_hot_content`.`title` AS `title`,`sx_hot_content`.`author` AS `author`,sum(`sx_hot_content`.`view_count`) AS `total_views`,sum(`sx_hot_content`.`like_count`) AS `total_likes`,sum(`sx_hot_content`.`share_count`) AS `total_shares`,sum(`sx_hot_content`.`comment_count`) AS `total_comments`,avg(`sx_hot_content`.`engagement_score`) AS `avg_engagement_score` from `sx_hot_content` where (`sx_hot_content`.`stat_date` >= (curdate() - interval 7 day)) group by `sx_hot_content`.`content_type`,`sx_hot_content`.`content_id`,`sx_hot_content`.`title`,`sx_hot_content`.`author` order by `avg_engagement_score` desc,`total_views` desc limit 50;

-- ----------------------------
-- View structure for v_hot_recommendations
-- ----------------------------
DROP VIEW IF EXISTS `v_hot_recommendations`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_hot_recommendations` AS select `sx_content_recommendations`.`id` AS `id`,`sx_content_recommendations`.`title` AS `title`,`sx_content_recommendations`.`content_type` AS `content_type`,`sx_content_recommendations`.`content_id` AS `content_id`,`sx_content_recommendations`.`position` AS `position`,`sx_content_recommendations`.`click_count` AS `click_count`,`sx_content_recommendations`.`view_count` AS `view_count`,`sx_content_recommendations`.`weight` AS `weight`,`sx_content_recommendations`.`algorithm` AS `algorithm`,round(((`sx_content_recommendations`.`click_count` / nullif(`sx_content_recommendations`.`view_count`,0)) * 100),2) AS `click_rate`,`sx_content_recommendations`.`created_at` AS `created_at`,`sx_content_recommendations`.`updated_at` AS `updated_at` from `sx_content_recommendations` where (isnull(`sx_content_recommendations`.`deleted_at`) and (`sx_content_recommendations`.`status` = 1) and (isnull(`sx_content_recommendations`.`start_time`) or (`sx_content_recommendations`.`start_time` <= now())) and (isnull(`sx_content_recommendations`.`end_time`) or (`sx_content_recommendations`.`end_time` >= now()))) order by `sx_content_recommendations`.`weight` desc,`sx_content_recommendations`.`click_count` desc,`sx_content_recommendations`.`view_count` desc limit 50;

-- ----------------------------
-- View structure for v_hot_tags
-- ----------------------------
DROP VIEW IF EXISTS `v_hot_tags`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_hot_tags` AS select `t`.`id` AS `id`,`t`.`name` AS `name`,`t`.`slug` AS `slug`,`t`.`type` AS `type`,`t`.`color` AS `color`,`t`.`description` AS `description`,`t`.`content_count` AS `content_count`,`t`.`is_hot` AS `is_hot`,`t`.`status` AS `status`,`t`.`created_by` AS `created_by`,`t`.`updated_by` AS `updated_by`,`t`.`created_at` AS `created_at`,`t`.`updated_at` AS `updated_at`,coalesce(`ct`.`content_count`,0) AS `actual_content_count` from (`sx_tags` `t` left join (select `sx_content_tags`.`tag_id` AS `tag_id`,count(0) AS `content_count` from `sx_content_tags` group by `sx_content_tags`.`tag_id`) `ct` on((`t`.`id` = `ct`.`tag_id`))) where ((`t`.`status` = 1) and (`t`.`is_hot` = 1)) order by coalesce(`ct`.`content_count`,0) desc;

-- ----------------------------
-- View structure for v_recommendation_statistics
-- ----------------------------
DROP VIEW IF EXISTS `v_recommendation_statistics`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_recommendation_statistics` AS select `sx_content_recommendations`.`position` AS `position`,`sx_content_recommendations`.`content_type` AS `content_type`,count(0) AS `total_count`,sum((case when (`sx_content_recommendations`.`status` = 1) then 1 else 0 end)) AS `active_count`,sum((case when (`sx_content_recommendations`.`status` = 0) then 1 else 0 end)) AS `inactive_count`,sum(`sx_content_recommendations`.`click_count`) AS `total_clicks`,sum(`sx_content_recommendations`.`view_count`) AS `total_views`,avg(`sx_content_recommendations`.`click_count`) AS `avg_clicks`,avg(`sx_content_recommendations`.`view_count`) AS `avg_views`,avg(`sx_content_recommendations`.`weight`) AS `avg_weight`,max(`sx_content_recommendations`.`updated_at`) AS `last_updated` from `sx_content_recommendations` where isnull(`sx_content_recommendations`.`deleted_at`) group by `sx_content_recommendations`.`position`,`sx_content_recommendations`.`content_type`;

-- ----------------------------
-- View structure for v_region_ranking
-- ----------------------------
DROP VIEW IF EXISTS `v_region_ranking`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_region_ranking` AS select `sx_region_statistics`.`province` AS `province`,`sx_region_statistics`.`city` AS `city`,sum(`sx_region_statistics`.`user_count`) AS `total_users`,sum(`sx_region_statistics`.`content_count`) AS `total_content`,sum(`sx_region_statistics`.`view_count`) AS `total_views`,sum(`sx_region_statistics`.`engagement_count`) AS `total_engagement`,avg(`sx_region_statistics`.`engagement_rate`) AS `avg_engagement_rate` from `sx_region_statistics` where (`sx_region_statistics`.`stat_date` >= (curdate() - interval 30 day)) group by `sx_region_statistics`.`province`,`sx_region_statistics`.`city` order by `total_users` desc,`total_engagement` desc;

-- ----------------------------
-- View structure for v_tag_statistics
-- ----------------------------
DROP VIEW IF EXISTS `v_tag_statistics`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_tag_statistics` AS select `t`.`id` AS `id`,`t`.`name` AS `name`,`t`.`type` AS `type`,`t`.`content_count` AS `content_count`,`t`.`is_hot` AS `is_hot`,`t`.`status` AS `status`,coalesce(`ct`.`content_count`,0) AS `actual_content_count`,`t`.`created_at` AS `created_at` from (`sx_tags` `t` left join (select `sx_content_tags`.`tag_id` AS `tag_id`,count(0) AS `content_count` from `sx_content_tags` group by `sx_content_tags`.`tag_id`) `ct` on((`t`.`id` = `ct`.`tag_id`))) where (`t`.`status` = 1);

-- ----------------------------
-- View structure for v_user_activity_statistics
-- ----------------------------
DROP VIEW IF EXISTS `v_user_activity_statistics`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_user_activity_statistics` AS select `u`.`id` AS `id`,`u`.`username` AS `username`,`u`.`nickname` AS `nickname`,`u`.`avatar` AS `avatar`,`u`.`user_type` AS `user_type`,`u`.`verify_status` AS `verify_status`,`u`.`level` AS `level`,`u`.`score` AS `score`,`u`.`story_count` AS `story_count`,`u`.`comment_count` AS `comment_count`,`u`.`like_count` AS `like_count`,`u`.`share_count` AS `share_count`,`u`.`follow_count` AS `follow_count`,`u`.`fans_count` AS `fans_count`,`u`.`last_login_time` AS `last_login_time`,`u`.`login_count` AS `login_count`,((((`u`.`story_count` * 10) + (`u`.`comment_count` * 2)) + `u`.`like_count`) + (`u`.`share_count` * 5)) AS `activity_score`,(case when (`u`.`last_login_time` >= (now() - interval 1 day)) then '今日活跃' when (`u`.`last_login_time` >= (now() - interval 7 day)) then '本周活跃' when (`u`.`last_login_time` >= (now() - interval 30 day)) then '本月活跃' else '不活跃' end) AS `activity_level` from `sx_users` `u` where (isnull(`u`.`deleted_at`) and (`u`.`status` = 1)) order by ((((`u`.`story_count` * 10) + (`u`.`comment_count` * 2)) + `u`.`like_count`) + (`u`.`share_count` * 5)) desc;

-- ----------------------------
-- View structure for v_user_level_distribution
-- ----------------------------
DROP VIEW IF EXISTS `v_user_level_distribution`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_user_level_distribution` AS select `ul`.`level` AS `level`,`ul`.`level_name` AS `level_name`,`ul`.`level_icon` AS `level_icon`,`ul`.`level_color` AS `level_color`,count(`u`.`id`) AS `user_count`,round(((count(`u`.`id`) * 100.0) / (select count(0) from `sx_users` where isnull(`sx_users`.`deleted_at`))),2) AS `percentage`,avg(`u`.`score`) AS `avg_score`,avg(`u`.`experience`) AS `avg_experience` from (`sx_user_levels` `ul` left join `sx_users` `u` on(((`ul`.`level` = `u`.`level`) and isnull(`u`.`deleted_at`)))) where (`ul`.`status` = 1) group by `ul`.`id`,`ul`.`level`,`ul`.`level_name`,`ul`.`level_icon`,`ul`.`level_color` order by `ul`.`level`;

-- ----------------------------
-- View structure for v_user_region_distribution
-- ----------------------------
DROP VIEW IF EXISTS `v_user_region_distribution`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_user_region_distribution` AS select `sx_users`.`province` AS `province`,`sx_users`.`city` AS `city`,count(0) AS `user_count`,round(((count(0) * 100.0) / (select count(0) from `sx_users` where isnull(`sx_users`.`deleted_at`))),2) AS `percentage`,sum((case when (`sx_users`.`user_type` = 2) then 1 else 0 end)) AS `factory_users`,sum((case when (`sx_users`.`verify_status` = 2) then 1 else 0 end)) AS `verified_users`,avg(`sx_users`.`score`) AS `avg_score`,max(`sx_users`.`created_at`) AS `latest_register_time` from `sx_users` where (isnull(`sx_users`.`deleted_at`) and (`sx_users`.`province` <> '') and (`sx_users`.`city` <> '')) group by `sx_users`.`province`,`sx_users`.`city` order by `user_count` desc;

-- ----------------------------
-- View structure for v_user_statistics
-- ----------------------------
DROP VIEW IF EXISTS `v_user_statistics`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_user_statistics` AS select count(0) AS `total_users`,sum((case when (`sx_users`.`status` = 1) then 1 else 0 end)) AS `active_users`,sum((case when (`sx_users`.`status` = 0) then 1 else 0 end)) AS `disabled_users`,sum((case when (`sx_users`.`status` = 2) then 1 else 0 end)) AS `pending_users`,sum((case when (`sx_users`.`user_type` = 1) then 1 else 0 end)) AS `normal_users`,sum((case when (`sx_users`.`user_type` = 2) then 1 else 0 end)) AS `factory_users`,sum((case when (`sx_users`.`user_type` = 3) then 1 else 0 end)) AS `verified_users`,sum((case when (`sx_users`.`verify_status` = 2) then 1 else 0 end)) AS `certified_users`,sum((case when (cast(`sx_users`.`created_at` as date) = curdate()) then 1 else 0 end)) AS `today_new_users`,sum((case when (cast(`sx_users`.`created_at` as date) >= (curdate() - interval 7 day)) then 1 else 0 end)) AS `week_new_users`,sum((case when (cast(`sx_users`.`created_at` as date) >= (curdate() - interval 30 day)) then 1 else 0 end)) AS `month_new_users`,avg(`sx_users`.`score`) AS `avg_score`,avg(`sx_users`.`level`) AS `avg_level`,max(`sx_users`.`updated_at`) AS `last_updated` from `sx_users` where isnull(`sx_users`.`deleted_at`);

-- ----------------------------
-- View structure for v_user_statistics_summary
-- ----------------------------
DROP VIEW IF EXISTS `v_user_statistics_summary`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_user_statistics_summary` AS select date_format(`sx_user_statistics`.`stat_date`,'%Y-%m') AS `month`,sum(`sx_user_statistics`.`new_users`) AS `total_new_users`,avg(`sx_user_statistics`.`active_users`) AS `avg_active_users`,avg(`sx_user_statistics`.`retention_rate`) AS `avg_retention_rate`,avg(`sx_user_statistics`.`avg_session_time`) AS `avg_session_time`,avg(`sx_user_statistics`.`bounce_rate`) AS `avg_bounce_rate` from `sx_user_statistics` group by date_format(`sx_user_statistics`.`stat_date`,'%Y-%m') order by `month` desc;

-- ----------------------------
-- 新增APP个人中心管理功能相关数据表
-- 添加时间：2025年
-- 说明：为APP个人中心功能提供完整的数据库支持
-- ----------------------------

-- ----------------------------
-- Table structure for sx_user_collections
-- ----------------------------
DROP TABLE IF EXISTS `sx_user_collections`;
CREATE TABLE `sx_user_collections` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '收藏ID',
  `user_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户ID',
  `content_type` enum('story','factory','people') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '内容类型',
  `content_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '内容ID',
  `content_title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '内容标题',
  `content_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '内容封面图',
  `content_summary` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '内容摘要',
  `collection_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_content`(`user_id`, `content_type`, `content_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_content_type`(`content_type`) USING BTREE,
  INDEX `idx_collection_time`(`collection_time`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户收藏表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sx_user_browse_history
-- ----------------------------
DROP TABLE IF EXISTS `sx_user_browse_history`;
CREATE TABLE `sx_user_browse_history` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '历史ID',
  `user_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户ID',
  `content_type` enum('story','factory','people') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '内容类型',
  `content_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '内容ID',
  `content_title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '内容标题',
  `content_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '内容封面图',
  `browse_duration` int(11) NOT NULL DEFAULT 0 COMMENT '浏览时长（秒）',
  `browse_progress` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '浏览进度（百分比）',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'IP地址',
  `user_agent` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '用户代理',
  `last_browse_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后浏览时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_content`(`user_id`, `content_type`, `content_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_content_type`(`content_type`) USING BTREE,
  INDEX `idx_last_browse_time`(`last_browse_time`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户浏览历史表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sx_comments
-- ----------------------------
DROP TABLE IF EXISTS `sx_comments`;
CREATE TABLE `sx_comments` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '评论ID',
  `parent_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '父评论ID（0为顶级评论）',
  `user_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '评论用户ID',
  `user_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '评论用户名',
  `user_avatar` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '用户头像',
  `content_type` enum('story','factory','people') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '内容类型',
  `content_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '内容ID',
  `comment_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '评论内容',
  `like_count` int(11) NOT NULL DEFAULT 0 COMMENT '点赞数',
  `reply_count` int(11) NOT NULL DEFAULT 0 COMMENT '回复数',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态：1-正常，0-隐藏，-1-删除',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'IP地址',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_parent_id`(`parent_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_content`(`content_type`, `content_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '评论表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sx_user_likes
-- ----------------------------
DROP TABLE IF EXISTS `sx_user_likes`;
CREATE TABLE `sx_user_likes` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '点赞ID',
  `user_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户ID',
  `target_type` enum('story','factory','people','comment') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '目标类型',
  `target_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '目标ID',
  `like_status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '点赞状态：1-点赞，0-取消点赞',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_target`(`user_id`, `target_type`, `target_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_target`(`target_type`, `target_id`) USING BTREE,
  INDEX `idx_like_status`(`like_status`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '点赞记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sx_user_shares
-- ----------------------------
DROP TABLE IF EXISTS `sx_user_shares`;
CREATE TABLE `sx_user_shares` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '分享ID',
  `user_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '分享用户ID',
  `content_type` enum('story','factory','people') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '内容类型',
  `content_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '内容ID',
  `content_title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '内容标题',
  `share_platform` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '分享平台：wechat-微信，qq-QQ，weibo-微博，link-链接',
  `share_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '分享链接',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'IP地址',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_content`(`content_type`, `content_id`) USING BTREE,
  INDEX `idx_share_platform`(`share_platform`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '分享记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sx_user_notifications
-- ----------------------------
DROP TABLE IF EXISTS `sx_user_notifications`;
CREATE TABLE `sx_user_notifications` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '通知ID',
  `user_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户ID',
  `notification_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '通知类型：system-系统，like-点赞，comment-评论，follow-关注，submission-投稿',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '通知标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '通知内容',
  `related_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '关联类型',
  `related_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '关联ID',
  `action_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '操作链接',
  `is_read` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否已读：1-已读，0-未读',
  `read_time` datetime NULL DEFAULT NULL COMMENT '阅读时间',
  `priority` tinyint(4) NOT NULL DEFAULT 1 COMMENT '优先级：1-普通，2-重要，3-紧急',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_notification_type`(`notification_type`) USING BTREE,
  INDEX `idx_is_read`(`is_read`) USING BTREE,
  INDEX `idx_priority`(`priority`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户通知表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sx_products
-- ----------------------------
DROP TABLE IF EXISTS `sx_products`;
CREATE TABLE `sx_products` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '商品ID',
  `product_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品名称',
  `product_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品编码',
  `category_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '分类ID',
  `product_images` json NULL COMMENT '商品图片JSON数组',
  `product_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '商品描述',
  `price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '商品价格',
  `original_price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '原价',
  `stock_quantity` int(11) NOT NULL DEFAULT 0 COMMENT '库存数量',
  `sales_count` int(11) NOT NULL DEFAULT 0 COMMENT '销售数量',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态：1-上架，0-下架',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序权重',
  `created_by` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
  `updated_by` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_product_code`(`product_code`) USING BTREE,
  INDEX `idx_category_id`(`category_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '商品表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sx_orders
-- ----------------------------
DROP TABLE IF EXISTS `sx_orders`;
CREATE TABLE `sx_orders` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '订单号',
  `user_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户ID',
  `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '订单总金额',
  `discount_amount` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '优惠金额',
  `shipping_fee` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '运费',
  `final_amount` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '实付金额',
  `order_status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '订单状态：1-待付款，2-待发货，3-待收货，4-已完成，5-已取消',
  `payment_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '支付状态：0-未支付，1-已支付，2-已退款',
  `payment_method` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '支付方式：alipay-支付宝，wechat-微信，bank-银行卡',
  `payment_time` datetime NULL DEFAULT NULL COMMENT '支付时间',
  `shipping_address` json NULL COMMENT '收货地址JSON',
  `shipping_time` datetime NULL DEFAULT NULL COMMENT '发货时间',
  `delivery_time` datetime NULL DEFAULT NULL COMMENT '收货时间',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '订单备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_order_no`(`order_no`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_order_status`(`order_status`) USING BTREE,
  INDEX `idx_payment_status`(`payment_status`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '订单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sx_order_items
-- ----------------------------
DROP TABLE IF EXISTS `sx_order_items`;
CREATE TABLE `sx_order_items` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '订单详情ID',
  `order_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '订单ID',
  `product_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品ID',
  `product_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品名称',
  `product_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品图片',
  `product_price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '商品单价',
  `quantity` int(11) NOT NULL DEFAULT 1 COMMENT '购买数量',
  `total_price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '小计金额',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_order_id`(`order_id`) USING BTREE,
  INDEX `idx_product_id`(`product_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '订单详情表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sx_user_settings
-- ----------------------------
DROP TABLE IF EXISTS `sx_user_settings`;
CREATE TABLE `sx_user_settings` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '设置ID',
  `user_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户ID',
  `setting_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '设置键名',
  `setting_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '设置值',
  `setting_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'string' COMMENT '设置类型：string-字符串，int-整数，bool-布尔，json-JSON',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '设置描述',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_setting`(`user_id`, `setting_key`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_setting_key`(`setting_key`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户设置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sx_user_feedback
-- ----------------------------
DROP TABLE IF EXISTS `sx_user_feedback`;
CREATE TABLE `sx_user_feedback` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '反馈ID',
  `user_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户ID',
  `feedback_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '反馈类型：bug-问题反馈，suggestion-建议，complaint-投诉，other-其他',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '反馈标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '反馈内容',
  `contact_info` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '联系方式',
  `attachments` json NULL COMMENT '附件JSON数组',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '处理状态：1-待处理，2-处理中，3-已处理，4-已关闭',
  `admin_reply` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '管理员回复',
  `admin_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '处理管理员ID',
  `processed_at` datetime NULL DEFAULT NULL COMMENT '处理时间',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'IP地址',
  `user_agent` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '用户代理',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_feedback_type`(`feedback_type`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户反馈表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sx_user_submissions
-- ----------------------------
DROP TABLE IF EXISTS `sx_user_submissions`;
CREATE TABLE `sx_user_submissions` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '投稿ID',
  `user_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '投稿用户ID',
  `submission_type` enum('story','factory','people') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '投稿类型',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '投稿标题',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '投稿内容',
  `media_files` json NULL COMMENT '媒体文件JSON数组',
  `tags` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '标签，逗号分隔',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态：1-待审核，2-审核中，3-已通过，4-已拒绝，5-已发布',
  `admin_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '审核管理员ID',
  `admin_comment` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '审核意见',
  `reviewed_at` datetime NULL DEFAULT NULL COMMENT '审核时间',
  `published_at` datetime NULL DEFAULT NULL COMMENT '发布时间',
  `published_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '发布后的内容ID',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'IP地址',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_submission_type`(`submission_type`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户投稿表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sx_user_vip
-- ----------------------------
DROP TABLE IF EXISTS `sx_user_vip`;
CREATE TABLE `sx_user_vip` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'VIP记录ID',
  `user_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户ID',
  `vip_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'VIP类型：monthly-月度，yearly-年度，lifetime-终身',
  `start_date` datetime NOT NULL COMMENT 'VIP开始时间',
  `end_date` datetime NOT NULL COMMENT 'VIP结束时间',
  `is_active` tinyint(4) NOT NULL DEFAULT 1 COMMENT '是否激活：1-激活，0-未激活',
  `payment_amount` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '支付金额',
  `payment_method` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '支付方式',
  `order_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '订单号',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_vip_type`(`vip_type`) USING BTREE,
  INDEX `idx_is_active`(`is_active`) USING BTREE,
  INDEX `idx_end_date`(`end_date`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户VIP表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sx_user_badges
-- ----------------------------
DROP TABLE IF EXISTS `sx_user_badges`;
CREATE TABLE `sx_user_badges` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '徽章ID',
  `user_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户ID',
  `badge_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '徽章类型：contributor-贡献者，storyteller-故事家，collector-收藏家，active-活跃用户',
  `badge_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '徽章名称',
  `badge_icon` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '徽章图标',
  `badge_description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '徽章描述',
  `earned_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '获得时间',
  `is_displayed` tinyint(4) NOT NULL DEFAULT 1 COMMENT '是否显示：1-显示，0-隐藏',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_badge`(`user_id`, `badge_type`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_badge_type`(`badge_type`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户徽章表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- 冗余数据表清理说明
-- ----------------------------
-- 以下表建议在生产环境中删除（冗余或测试表）：
-- 1. sxr_users - 与sx_users功能重复
-- 2. users - 与sx_users功能重复
-- 3. factory_user_types - 与factory_types功能重复
--
-- 删除命令（请谨慎执行）：
-- DROP TABLE IF EXISTS `sxr_users`;
-- DROP TABLE IF EXISTS `users`;
-- DROP TABLE IF EXISTS `factory_user_types`;
--
-- 注意：demo_* 系列测试表已删除

SET FOREIGN_KEY_CHECKS = 1;
 