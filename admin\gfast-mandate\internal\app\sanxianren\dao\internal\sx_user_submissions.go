// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SxUserSubmissionsDao is the data access object for table sx_user_submissions.
type SxUserSubmissionsDao struct {
	table   string                   // table is the underlying table name of the DAO.
	group   string                   // group is the database configuration group name of current DAO.
	columns SxUserSubmissionsColumns // columns contains all the column names of Table for convenient usage.
}

// SxUserSubmissionsColumns defines and stores column names for table sx_user_submissions.
type SxUserSubmissionsColumns struct {
	Id             string // 投稿ID
	UserId         string // 投稿用户ID
	SubmissionType string // 投稿类型
	Title          string // 投稿标题
	Content        string // 投稿内容
	MediaFiles     string // 媒体文件JSON数组
	Tags           string // 标签，逗号分隔
	Status         string // 状态：1-待审核，2-审核中，3-已通过，4-已拒绝，5-已发布
	AdminId        string // 审核管理员ID
	AdminComment   string // 审核意见
	ReviewedAt     string // 审核时间
	PublishedAt    string // 发布时间
	PublishedId    string // 发布后的内容ID
	IpAddress      string // IP地址
	CreatedAt      string // 创建时间
	UpdatedAt      string // 更新时间
}

// sxUserSubmissionsColumns holds the columns for table sx_user_submissions.
var sxUserSubmissionsColumns = SxUserSubmissionsColumns{
	Id:             "id",
	UserId:         "user_id",
	SubmissionType: "submission_type",
	Title:          "title",
	Content:        "content",
	MediaFiles:     "media_files",
	Tags:           "tags",
	Status:         "status",
	AdminId:        "admin_id",
	AdminComment:   "admin_comment",
	ReviewedAt:     "reviewed_at",
	PublishedAt:    "published_at",
	PublishedId:    "published_id",
	IpAddress:      "ip_address",
	CreatedAt:      "created_at",
	UpdatedAt:      "updated_at",
}

// NewSxUserSubmissionsDao creates and returns a new DAO object for table data access.
func NewSxUserSubmissionsDao() *SxUserSubmissionsDao {
	return &SxUserSubmissionsDao{
		group:   "default",
		table:   "sx_user_submissions",
		columns: sxUserSubmissionsColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *SxUserSubmissionsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *SxUserSubmissionsDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *SxUserSubmissionsDao) Columns() SxUserSubmissionsColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *SxUserSubmissionsDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *SxUserSubmissionsDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}
