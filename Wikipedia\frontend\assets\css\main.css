/* 天南地北三线人 - 主样式文件 */

/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

/* 导航栏样式 */
.navbar {
    position: sticky;
    top: 0;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid #e4e7ed;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.navbar-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    height: 70px;
    gap: 20px;
}

.navbar-brand {
    display: flex;
    flex-direction: column;
    cursor: pointer;
    min-width: 200px;
}

.navbar-brand .logo {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #409eff;
    font-size: 18px;
    font-weight: 700;
}

.navbar-brand .brand-text {
    background: linear-gradient(45deg, #409eff, #722ed1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.navbar-brand .subtitle {
    font-size: 11px;
    color: #909399;
    margin-left: 32px;
    margin-top: 2px;
}

.navbar-nav {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 20px;
}

.navbar-nav .nav-item {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    color: #606266;
    white-space: nowrap;
}

.navbar-nav .nav-item:hover {
    background: #f5f7fa;
    color: #409eff;
    transform: translateY(-1px);
}

.navbar-nav .nav-item.active {
    background: #ecf5ff;
    color: #409eff;
    font-weight: 500;
}

.navbar-search {
    flex: 1;
    max-width: 600px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.search-input {
    flex: 1;
}

.search-input .el-input__wrapper {
    border-radius: 25px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.search-input .el-input__wrapper:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.search-button {
    border-radius: 20px;
    margin-right: 4px;
}

.mobile-menu-btn {
    display: none;
}

/* 主要内容区域 */
.main-content {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
}

.search-results-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.search-results-header h2 {
    margin: 0 0 8px 0;
    color: #303133;
    font-size: 24px;
    font-weight: 600;
}

.search-results-header p {
    margin: 0;
    color: #606266;
    font-size: 14px;
}

.category-filters {
    margin-bottom: 30px;
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.filter-tags {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.category-tag {
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    font-size: 14px;
    padding: 8px 16px;
}

.category-tag:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.category-tag .count {
    margin-left: 4px;
    opacity: 0.7;
    font-size: 12px;
}

/* 瀑布流布局 */
.waterfall-container {
    display: flex;
    gap: 20px;
    align-items: flex-start;
    min-height: 400px;
}

.waterfall-column {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* 文章卡片样式 */
.article-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    margin-bottom: 20px;
}

.article-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.featured-badge {
    position: absolute;
    top: 12px;
    right: 12px;
    background: #e6a23c;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
    z-index: 2;
}

.card-image {
    position: relative;
    width: 100%;
    height: 200px;
    overflow: hidden;
}

.card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.card-image:hover img {
    transform: scale(1.05);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    opacity: 0;
    transition: opacity 0.3s ease;
    display: flex;
    align-items: flex-end;
    justify-content: flex-end;
    padding: 12px;
}

.article-card:hover .image-overlay {
    opacity: 1;
}

.overlay-actions {
    display: flex;
    gap: 8px;
}

.card-content {
    padding: 16px;
}

.card-category {
    margin-bottom: 8px;
}

.card-title {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.card-summary {
    margin: 0 0 12px 0;
    font-size: 14px;
    color: #606266;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.card-tags {
    margin-bottom: 12px;
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    align-items: center;
}

.tag-item {
    font-size: 12px;
}

.more-tags {
    font-size: 12px;
    color: #909399;
}

.card-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    padding: 8px 0;
    border-top: 1px solid #f0f2f5;
    border-bottom: 1px solid #f0f2f5;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: #909399;
}

.card-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.author-info {
    display: flex;
    align-items: center;
    gap: 6px;
}

.author-name {
    font-size: 12px;
    color: #606266;
}

.time-info {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: #909399;
}

/* 加载状态 */
.loading-more {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #606266;
    font-size: 14px;
}

.loading-icon {
    margin-right: 8px;
    animation: spin 1s linear infinite;
}

.no-more-data {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #909399;
    font-size: 14px;
}

.no-more-data i {
    margin-right: 8px;
    color: #67c23a;
}

.empty-state {
    text-align: center;
    padding: 80px 20px;
    color: #606266;
}

.empty-icon {
    color: #c0c4cc;
    margin-bottom: 20px;
    font-size: 64px;
}

.empty-state h3 {
    margin: 0 0 12px 0;
    font-size: 20px;
    font-weight: 500;
}

.empty-state p {
    margin: 0 0 24px 0;
    font-size: 14px;
    line-height: 1.5;
}

/* 返回顶部按钮 */
.backtop-button {
    width: 40px;
    height: 40px;
    background: #409eff;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

.backtop-button:hover {
    background: #337ecc;
    transform: translateY(-2px);
}

/* 动画 */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 页脚样式 */
.site-footer {
    background: #2c3e50;
    color: #ecf0f1;
    margin-top: 60px;
    padding: 40px 0 20px 0;
}

.footer-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 30px;
}

.footer-section h4 {
    color: #3498db;
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 600;
}

.footer-section p {
    margin: 0 0 15px 0;
    color: #bdc3c7;
    line-height: 1.6;
}

.footer-links {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.footer-links a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s ease;
    font-size: 14px;
}

.footer-links a:hover {
    color: #3498db;
}

.footer-bottom {
    border-top: 1px solid #34495e;
    padding-top: 20px;
    text-align: center;
    color: #95a5a6;
    font-size: 14px;
}

.footer-bottom p {
    margin: 5px 0;
}

.footer-bottom a {
    color: #3498db;
    text-decoration: none;
}

.footer-bottom a:hover {
    text-decoration: underline;
}

/* 工具类 */
.mr-4 {
    margin-right: 4px;
}

.ml-5 {
    margin-left: 5px;
}
