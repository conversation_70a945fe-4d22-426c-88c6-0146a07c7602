/*
* @desc:用户收藏控制器
* @company:山东傲英网络科技股份有限公司
* @Author: wolfman
* @Date:   2024/12/19
 */

package controller

import (
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	v1 "github.com/tiger1103/gfast/v3/api/v1/sanxianren"
	"github.com/tiger1103/gfast/v3/internal/app/sanxianren/service"
	systemService "github.com/tiger1103/gfast/v3/internal/app/system/service"
)

// UserCollectionsController 用户收藏控制器
type UserCollectionsController struct{}

// AddCollection 添加收藏
func (c *UserCollectionsController) AddCollection(ctx context.Context, req *v1.AddCollectionReq) (res *v1.AddCollectionRes, err error) {
	// 获取当前用户ID
	userId := systemService.Context().GetUserId(ctx)
	if userId == 0 {
		return nil, gerror.New("用户未登录")
	}

	// 调用服务添加收藏
	err = service.UserCollections().AddCollection(ctx, userId, req)
	if err != nil {
		g.Log().Error(ctx, "添加收藏失败:", err)
		return nil, gerror.New(err.Error())
	}

	return &v1.AddCollectionRes{}, nil
}

// RemoveCollection 取消收藏
func (c *UserCollectionsController) RemoveCollection(ctx context.Context, req *v1.RemoveCollectionReq) (res *v1.RemoveCollectionRes, err error) {
	// 获取当前用户ID
	userId := systemService.Context().GetUserId(ctx)
	if userId == 0 {
		return nil, gerror.New("用户未登录")
	}

	// 调用服务取消收藏
	err = service.UserCollections().RemoveCollection(ctx, userId, req)
	if err != nil {
		g.Log().Error(ctx, "取消收藏失败:", err)
		return nil, gerror.New(err.Error())
	}

	return &v1.RemoveCollectionRes{}, nil
}

// GetCollections 获取收藏列表
func (c *UserCollectionsController) GetCollections(ctx context.Context, req *v1.GetCollectionsReq) (res *v1.GetCollectionsRes, err error) {
	// 获取当前用户ID
	userId := systemService.Context().GetUserId(ctx)
	if userId == 0 {
		return nil, gerror.New("用户未登录")
	}

	// 调用服务获取收藏列表
	res, err = service.UserCollections().GetCollections(ctx, userId, req)
	if err != nil {
		g.Log().Error(ctx, "获取收藏列表失败:", err)
		return nil, gerror.New(err.Error())
	}

	return res, nil
}

// CheckCollection 检查收藏状态
func (c *UserCollectionsController) CheckCollection(ctx context.Context, req *v1.CheckCollectionReq) (res *v1.CheckCollectionRes, err error) {
	// 获取当前用户ID
	userId := systemService.Context().GetUserId(ctx)
	if userId == 0 {
		return nil, gerror.New("用户未登录")
	}

	// 调用服务检查收藏状态
	res, err = service.UserCollections().CheckCollection(ctx, userId, req)
	if err != nil {
		g.Log().Error(ctx, "检查收藏状态失败:", err)
		return nil, gerror.New(err.Error())
	}

	return res, nil
}
