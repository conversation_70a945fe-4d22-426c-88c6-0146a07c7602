/*
* @desc:用户个人中心服务
* @company:山东傲英网络科技股份有限公司
* @Author: wolfman
* @Date:   2024/12/19
 */

package service

import (
	"context"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	v1 "github.com/tiger1103/gfast/v3/api/v1/sanxianren"
	"github.com/tiger1103/gfast/v3/internal/app/sanxianren/dao"
	"github.com/tiger1103/gfast/v3/internal/app/sanxianren/model/entity"
)

type userProfileService struct{}

func init() {
	RegisterUserProfile(&userProfileService{})
}

// GetUserProfile 获取用户个人资料
func (s *userProfileService) GetUserProfile(ctx context.Context, userId uint64) (*v1.UserProfileRes, error) {
	// 暂时返回默认的用户资料，后续可以根据实际需求调整
	// 这里可以从用户表或其他相关表获取用户信息

	// 获取VIP信息
	vipInfo, _ := s.GetUserVipInfo(ctx, userId)

	// 获取徽章信息
	badgesInfo, _ := s.GetUserBadges(ctx, userId)

	// 获取投稿统计
	submissionStats, _ := s.GetUserSubmissionStats(ctx, userId)

	// 获取收藏数量
	collectionCount, _ := dao.SxUserCollections.Ctx(ctx).Where("user_id", userId).Count()

	// 获取关注数量（这里可以根据实际业务逻辑调整）
	followCount := 0

	// 获取捐赠金额（这里可以根据实际业务逻辑调整）
	donateAmount := 0.0

	// 构建用户工厂信息（暂时为空）
	factories := []v1.UserFactoryInfo{}

	// 构建响应
	res := &v1.UserProfileRes{
		Id:              userId,
		Name:            "用户" + fmt.Sprintf("%d", userId),
		Avatar:          "/static/default-avatar.jpg",
		Gender:          "未知",
		BirthYear:       0,
		Phone:           "",
		FactoryCode:     "",
		Factory:         "",
		Position:        "",
		WorkYears:       0,
		Bio:             "",
		IsVip:           vipInfo != nil && vipInfo.IsVip,
		VipExpireDate:   "",
		IsAuthenticated: false,
		AuthCount:       0,
		SubmissionCount: submissionStats.Total,
		CollectionCount: int(collectionCount),
		FollowCount:     followCount,
		DonateAmount:    donateAmount,
		Badges:          []v1.UserBadgeInfo{},
		Factories:       factories,
	}

	if vipInfo != nil {
		res.VipExpireDate = vipInfo.EndDate
	}

	if badgesInfo != nil {
		res.Badges = badgesInfo.List
	}

	if submissionStats != nil {
		res.SubmissionCount = submissionStats.Total
	}

	return res, nil
}

// EditUserProfile 编辑用户个人资料
func (s *userProfileService) EditUserProfile(ctx context.Context, userId uint64, req *v1.EditUserProfileReq) error {
	// 暂时不更新数据库，后续可以根据实际需求调整
	// 这里可以添加实际的用户资料更新逻辑

	g.Log().Info(ctx, "用户资料更新请求:", req)

	// 模拟成功
	err := error(nil)
	if err != nil {
		g.Log().Error(ctx, "更新用户资料失败:", err)
		return fmt.Errorf("更新用户资料失败")
	}

	return nil
}

// GetUserSubmissions 获取用户投稿列表
func (s *userProfileService) GetUserSubmissions(ctx context.Context, userId uint64, req *v1.UserSubmissionsReq) (*v1.UserSubmissionsRes, error) {
	query := dao.SxUserSubmissions.Ctx(ctx).Where("user_id", userId)

	// 添加状态过滤
	if req.Status != "" {
		statusMap := map[string]int{
			"pending":   1, // 待审核
			"reviewing": 2, // 审核中
			"approved":  3, // 已通过
			"rejected":  4, // 已拒绝
			"published": 5, // 已发布
		}
		if status, ok := statusMap[req.Status]; ok {
			query = query.Where("status", status)
		}
	}

	// 添加类型过滤
	if req.Type != "" {
		query = query.Where("submission_type", req.Type)
	}

	// 获取总数
	total, err := query.Count()
	if err != nil {
		g.Log().Error(ctx, "获取投稿总数失败:", err)
		return nil, fmt.Errorf("获取投稿列表失败")
	}

	// 分页查询
	var submissions []entity.SxUserSubmissions
	err = query.Page(req.PageNum, req.PageSize).OrderDesc("created_at").Scan(&submissions)
	if err != nil {
		g.Log().Error(ctx, "获取投稿列表失败:", err)
		return nil, fmt.Errorf("获取投稿列表失败")
	}

	// 转换为响应格式
	list := make([]v1.UserSubmissionInfo, 0, len(submissions))
	statusTextMap := map[int]string{
		1: "待审核",
		2: "审核中",
		3: "已通过",
		4: "已拒绝",
		5: "已发布",
	}

	for _, submission := range submissions {
		item := v1.UserSubmissionInfo{
			Id:             submission.Id,
			SubmissionType: submission.SubmissionType,
			Title:          submission.Title,
			Status:         submission.Status,
			StatusText:     statusTextMap[submission.Status],
			AdminComment:   submission.AdminComment,
			CreatedAt:      submission.CreatedAt.Format("2006-01-02 15:04:05"),
		}

		if submission.ReviewedAt != nil {
			item.ReviewedAt = submission.ReviewedAt.Format("2006-01-02 15:04:05")
		}

		if submission.PublishedAt != nil {
			item.PublishedAt = submission.PublishedAt.Format("2006-01-02 15:04:05")
		}

		list = append(list, item)
	}

	res := &v1.UserSubmissionsRes{
		ListRes: commonApi.ListRes{
			CurrentPage: req.PageNum,
			Total:       int(total),
		},
		List: list,
	}

	return res, nil
}

// GetUserSubmissionStats 获取用户投稿统计
func (s *userProfileService) GetUserSubmissionStats(ctx context.Context, userId uint64) (*v1.UserSubmissionStatsRes, error) {
	// 获取各状态的投稿数量
	stats := &v1.UserSubmissionStatsRes{}

	// 总数
	total, _ := dao.SxUserSubmissions.Ctx(ctx).Where("user_id", userId).Count()
	stats.Total = int(total)

	// 待审核
	pending, _ := dao.SxUserSubmissions.Ctx(ctx).Where("user_id", userId).Where("status", 1).Count()
	stats.Pending = int(pending)

	// 审核中
	reviewing, _ := dao.SxUserSubmissions.Ctx(ctx).Where("user_id", userId).Where("status", 2).Count()
	stats.Reviewing = int(reviewing)

	// 已通过
	approved, _ := dao.SxUserSubmissions.Ctx(ctx).Where("user_id", userId).Where("status", 3).Count()
	stats.Approved = int(approved)

	// 已拒绝
	rejected, _ := dao.SxUserSubmissions.Ctx(ctx).Where("user_id", userId).Where("status", 4).Count()
	stats.Rejected = int(rejected)

	// 已发布
	published, _ := dao.SxUserSubmissions.Ctx(ctx).Where("user_id", userId).Where("status", 5).Count()
	stats.Published = int(published)

	return stats, nil
}

// GetUserVipInfo 获取用户VIP信息
func (s *userProfileService) GetUserVipInfo(ctx context.Context, userId uint64) (*v1.UserVipInfoRes, error) {
	var vip entity.SxUserVip
	err := dao.SxUserVip.Ctx(ctx).Where("user_id", userId).Where("is_active", 1).OrderDesc("end_date").Scan(&vip)
	if err != nil {
		// 用户没有VIP记录
		return &v1.UserVipInfoRes{
			IsVip: false,
		}, nil
	}

	// 检查VIP是否过期
	now := time.Now()
	isVip := vip.EndDate != nil && vip.EndDate.Time.After(now)

	res := &v1.UserVipInfoRes{
		IsVip:         isVip,
		VipType:       vip.VipType,
		StartDate:     vip.StartDate.Format("2006-01-02"),
		EndDate:       vip.EndDate.Format("2006-01-02"),
		PaymentAmount: vip.PaymentAmount,
		OrderNo:       vip.OrderNo,
	}

	return res, nil
}

// GetUserBadges 获取用户徽章列表
func (s *userProfileService) GetUserBadges(ctx context.Context, userId uint64) (*v1.UserBadgesRes, error) {
	var badges []entity.SxUserBadges
	err := dao.SxUserBadges.Ctx(ctx).Where("user_id", userId).Where("is_displayed", 1).OrderDesc("earned_at").Scan(&badges)
	if err != nil {
		g.Log().Error(ctx, "获取用户徽章失败:", err)
		return &v1.UserBadgesRes{List: []v1.UserBadgeInfo{}}, nil
	}

	list := make([]v1.UserBadgeInfo, 0, len(badges))
	for _, badge := range badges {
		item := v1.UserBadgeInfo{
			Type:        badge.BadgeType,
			Name:        badge.BadgeName,
			Icon:        badge.BadgeIcon,
			Description: badge.BadgeDescription,
		}
		list = append(list, item)
	}

	return &v1.UserBadgesRes{List: list}, nil
}
