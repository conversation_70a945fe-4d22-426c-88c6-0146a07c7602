/*
* @desc:用户个人中心路由
* @company:山东傲英网络科技股份有限公司
* @Author: wolfman
* @Date:   2024/12/19
 */

package router

import (
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/internal/app/sanxianren/controller"
)

// UserProfile 用户个人中心路由（需要登录验证）
func UserProfile(group *ghttp.RouterGroup) {
	userProfileController := &controller.UserProfileController{}
	userCollectionsController := &controller.UserCollectionsController{}
	userHistoryController := &controller.UserHistoryController{}
	userNotificationsController := &controller.UserNotificationsController{}

	// 用户个人中心路由组（需要登录验证）
	group.Bind(
		// 个人资料相关
		userProfileController.GetUserProfile,         // 获取用户个人资料
		userProfileController.EditUserProfile,        // 编辑用户个人资料
		userProfileController.GetUserSubmissions,     // 获取用户投稿列表
		userProfileController.GetUserSubmissionStats, // 获取用户投稿统计
		userProfileController.GetUserVipInfo,         // 获取用户VIP信息
		userProfileController.GetUserBadges,          // 获取用户徽章列表

		// 收藏相关
		userCollectionsController.AddCollection,    // 添加收藏
		userCollectionsController.RemoveCollection, // 取消收藏
		userCollectionsController.GetCollections,   // 获取收藏列表
		userCollectionsController.CheckCollection,  // 检查收藏状态

		// 浏览历史相关
		userHistoryController.AddHistory,   // 添加浏览历史
		userHistoryController.GetHistory,   // 获取浏览历史
		userHistoryController.ClearHistory, // 清空浏览历史

		// 通知相关
		userNotificationsController.GetNotifications,     // 获取通知列表
		userNotificationsController.MarkNotificationRead, // 标记通知已读
		userNotificationsController.GetNotificationStats, // 获取通知统计
	)
}
