/*
* @desc:OAuth认证服务
* @company:山东傲英网络科技股份有限公司
* @Author: AI Assistant
* @Date: 2025/01/22
 */

package service

import (
	"context"
	"crypto/rand"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/gogf/gf/v2/crypto/gmd5"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/sanxianren/model"
)

// IOAuthService OAuth认证服务接口
type IOAuthService interface {
	// GetAuthorizationUrl 获取授权URL
	GetAuthorizationUrl(ctx context.Context, userId uint64, returnUrl string) (string, error)

	// HandleCallback 处理OAuth回调
	HandleCallback(ctx context.Context, req *model.OAuthCallbackRequest) (*model.OAuthCallbackResponse, error)

	// ExchangeToken 交换访问令牌
	ExchangeToken(ctx context.Context, code string) (*model.OAuthTokenResponse, error)

	// RefreshToken 刷新访问令牌
	RefreshToken(ctx context.Context, refreshToken string) (*model.OAuthTokenResponse, error)

	// GetUserInfo 获取用户信息
	GetUserInfo(ctx context.Context, accessToken string) (*model.MediaWikiUserInfo, error)

	// ValidateToken 验证访问令牌
	ValidateToken(ctx context.Context, accessToken string) (bool, error)

	// RevokeToken 撤销访问令牌
	RevokeToken(ctx context.Context, accessToken string) error

	// GetSession 获取OAuth会话
	GetSession(ctx context.Context, sessionId string) (*model.OAuthSession, error)

	// SaveSession 保存OAuth会话
	SaveSession(ctx context.Context, session *model.OAuthSession) error

	// DeleteSession 删除OAuth会话
	DeleteSession(ctx context.Context, sessionId string) error

	// GetUserOAuthStatus 获取用户OAuth认证状态
	GetUserOAuthStatus(ctx context.Context, userId uint64) (isAuthenticated bool, sessionId string, userInfo *model.MediaWikiUserInfo, expiresAt string, err error)
}

// oauthService OAuth认证服务实现
type oauthService struct {
	config     *model.MediaWikiOAuthConfig
	httpClient *http.Client
}

// OAuthService OAuth认证服务实例
var OAuthService IOAuthService

func init() {
	// 初始化OAuth服务
	ctx := context.Background()
	config := &model.MediaWikiOAuthConfig{}

	// 从配置文件加载配置
	if err := g.Cfg().MustGet(ctx, "mediawiki").Struct(config); err != nil {
		g.Log().Error(ctx, "加载MediaWiki OAuth配置失败:", err)
		// 使用默认配置
		config = getDefaultOAuthConfig()
	}

	OAuthService = &oauthService{
		config: config,
		httpClient: &http.Client{
			Timeout: time.Duration(config.API.Timeout) * time.Second,
		},
	}
}

// GetAuthorizationUrl 获取授权URL
func (s *oauthService) GetAuthorizationUrl(ctx context.Context, userId uint64, returnUrl string) (string, error) {
	// 生成状态参数
	state, err := s.generateState(userId, returnUrl)
	if err != nil {
		return "", gerror.Wrap(err, "生成状态参数失败")
	}

	// 构建授权URL参数
	params := url.Values{
		"response_type": {"code"},
		"client_id":     {s.config.OAuth.ClientId},
		"redirect_uri":  {s.config.OAuth.RedirectUri},
		"scope":         {strings.Join(s.config.OAuth.Scopes, " ")},
		"state":         {state},
	}

	// 构建完整的授权URL
	authUrl := fmt.Sprintf("%s?%s", s.config.OAuth.AuthorizationUrl, params.Encode())

	g.Log().Info(ctx, "生成OAuth授权URL:", authUrl)
	return authUrl, nil
}

// HandleCallback 处理OAuth回调
func (s *oauthService) HandleCallback(ctx context.Context, req *model.OAuthCallbackRequest) (*model.OAuthCallbackResponse, error) {
	// 检查是否有错误
	if req.Error != "" {
		return &model.OAuthCallbackResponse{
			Success: false,
			Message: fmt.Sprintf("OAuth授权失败: %s - %s", req.Error, req.ErrorDescription),
		}, nil
	}

	// 验证状态参数
	stateData, err := s.parseState(req.State)
	if err != nil {
		return &model.OAuthCallbackResponse{
			Success: false,
			Message: "无效的状态参数",
		}, nil
	}

	// 交换访问令牌
	tokenResp, err := s.ExchangeToken(ctx, req.Code)
	if err != nil {
		return &model.OAuthCallbackResponse{
			Success: false,
			Message: "获取访问令牌失败: " + err.Error(),
		}, nil
	}

	// 获取用户信息
	userInfo, err := s.GetUserInfo(ctx, tokenResp.AccessToken)
	if err != nil {
		return &model.OAuthCallbackResponse{
			Success: false,
			Message: "获取用户信息失败: " + err.Error(),
		}, nil
	}

	// 创建或更新本地用户
	gfastToken, err := s.createOrUpdateUser(ctx, userInfo, stateData.UserId)
	if err != nil {
		return &model.OAuthCallbackResponse{
			Success: false,
			Message: "创建用户失败: " + err.Error(),
		}, nil
	}

	// 保存OAuth会话
	session := &model.OAuthSession{
		SessionId:    s.generateSessionId(),
		UserId:       stateData.UserId,
		AccessToken:  tokenResp.AccessToken,
		RefreshToken: tokenResp.RefreshToken,
		TokenType:    tokenResp.TokenType,
		ExpiresIn:    tokenResp.ExpiresIn,
		Scope:        tokenResp.Scope,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
		ExpiresAt:    time.Now().Add(time.Duration(tokenResp.ExpiresIn) * time.Second),
	}

	if err := s.SaveSession(ctx, session); err != nil {
		g.Log().Warning(ctx, "保存OAuth会话失败:", err)
	}

	return &model.OAuthCallbackResponse{
		Success:   true,
		Message:   "OAuth认证成功",
		Token:     gfastToken,
		UserInfo:  userInfo,
		ReturnUrl: stateData.ReturnUrl,
	}, nil
}

// ExchangeToken 交换访问令牌
func (s *oauthService) ExchangeToken(ctx context.Context, code string) (*model.OAuthTokenResponse, error) {
	// 构建令牌请求参数
	params := url.Values{
		"grant_type":    {"authorization_code"},
		"code":          {code},
		"redirect_uri":  {s.config.OAuth.RedirectUri},
		"client_id":     {s.config.OAuth.ClientId},
		"client_secret": {s.config.OAuth.ClientSecret},
	}

	// 发送令牌请求
	resp, err := s.httpClient.PostForm(s.config.OAuth.TokenUrl, params)
	if err != nil {
		return nil, gerror.Wrap(err, "发送令牌请求失败")
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, gerror.Wrap(err, "读取令牌响应失败")
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return nil, gerror.New(fmt.Sprintf("令牌请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body)))
	}

	// 解析响应
	var tokenResp model.OAuthTokenResponse
	if err := json.Unmarshal(body, &tokenResp); err != nil {
		return nil, gerror.Wrap(err, "解析令牌响应失败")
	}

	return &tokenResp, nil
}

// RefreshToken 刷新访问令牌
func (s *oauthService) RefreshToken(ctx context.Context, refreshToken string) (*model.OAuthTokenResponse, error) {
	// 构建刷新令牌请求参数
	params := url.Values{
		"grant_type":    {"refresh_token"},
		"refresh_token": {refreshToken},
		"client_id":     {s.config.OAuth.ClientId},
		"client_secret": {s.config.OAuth.ClientSecret},
	}

	// 发送刷新令牌请求
	resp, err := s.httpClient.PostForm(s.config.OAuth.TokenUrl, params)
	if err != nil {
		return nil, gerror.Wrap(err, "发送刷新令牌请求失败")
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, gerror.Wrap(err, "读取刷新令牌响应失败")
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return nil, gerror.New(fmt.Sprintf("刷新令牌请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body)))
	}

	// 解析响应
	var tokenResp model.OAuthTokenResponse
	if err := json.Unmarshal(body, &tokenResp); err != nil {
		return nil, gerror.Wrap(err, "解析刷新令牌响应失败")
	}

	return &tokenResp, nil
}

// GetUserInfo 获取用户信息
func (s *oauthService) GetUserInfo(ctx context.Context, accessToken string) (*model.MediaWikiUserInfo, error) {
	// 构建用户信息请求URL
	params := url.Values{
		"action": {"query"},
		"meta":   {"userinfo"},
		"uiprop": {"blockinfo|groups|rights|editcount|realname|email|registrationdate"},
		"format": {"json"},
	}

	userInfoUrl := fmt.Sprintf("%s?%s", s.config.OAuth.UserInfoUrl, params.Encode())

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "GET", userInfoUrl, nil)
	if err != nil {
		return nil, gerror.Wrap(err, "创建用户信息请求失败")
	}

	// 添加授权头
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", accessToken))
	req.Header.Set("User-Agent", s.config.API.UserAgent)

	// 发送请求
	resp, err := s.httpClient.Do(req)
	if err != nil {
		return nil, gerror.Wrap(err, "发送用户信息请求失败")
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, gerror.Wrap(err, "读取用户信息响应失败")
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return nil, gerror.New(fmt.Sprintf("用户信息请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body)))
	}

	// 解析响应
	var userInfoResp model.MediaWikiUserInfoResponse
	if err := json.Unmarshal(body, &userInfoResp); err != nil {
		return nil, gerror.Wrap(err, "解析用户信息响应失败")
	}

	// 检查API错误
	if userInfoResp.Error != nil {
		return nil, gerror.New(fmt.Sprintf("MediaWiki API错误: %s - %s", userInfoResp.Error.Code, userInfoResp.Error.Info))
	}

	return &userInfoResp.Query.UserInfo, nil
}

// ValidateToken 验证访问令牌
func (s *oauthService) ValidateToken(ctx context.Context, accessToken string) (bool, error) {
	// 通过获取用户信息来验证令牌
	_, err := s.GetUserInfo(ctx, accessToken)
	if err != nil {
		return false, nil // 令牌无效，但不返回错误
	}
	return true, nil
}

// RevokeToken 撤销访问令牌
func (s *oauthService) RevokeToken(ctx context.Context, accessToken string) error {
	// MediaWiki可能不支持令牌撤销，这里只是删除本地会话
	// 实际实现需要根据MediaWiki的OAuth扩展功能来决定
	g.Log().Info(ctx, "撤销访问令牌:", accessToken)
	return nil
}

// GetSession 获取OAuth会话
func (s *oauthService) GetSession(ctx context.Context, sessionId string) (*model.OAuthSession, error) {
	// 从Redis缓存中获取会话
	cacheKey := fmt.Sprintf("oauth_session:%s", sessionId)
	sessionData, err := g.Redis().Get(ctx, cacheKey)
	if err != nil {
		return nil, gerror.Wrap(err, "获取OAuth会话失败")
	}

	if sessionData.IsNil() {
		return nil, gerror.New("OAuth会话不存在")
	}

	var session model.OAuthSession
	if err := json.Unmarshal(sessionData.Bytes(), &session); err != nil {
		return nil, gerror.Wrap(err, "解析OAuth会话数据失败")
	}

	// 检查会话是否过期
	if time.Now().After(session.ExpiresAt) {
		// 会话已过期，删除缓存
		g.Redis().Del(ctx, cacheKey)
		return nil, gerror.New("OAuth会话已过期")
	}

	return &session, nil
}

// SaveSession 保存OAuth会话
func (s *oauthService) SaveSession(ctx context.Context, session *model.OAuthSession) error {
	// 序列化会话数据
	sessionData, err := json.Marshal(session)
	if err != nil {
		return gerror.Wrap(err, "序列化OAuth会话数据失败")
	}

	// 保存到Redis缓存
	cacheKey := fmt.Sprintf("oauth_session:%s", session.SessionId)
	expiration := time.Until(session.ExpiresAt)

	err = g.Redis().SetEX(ctx, cacheKey, sessionData, int64(expiration.Seconds()))
	if err != nil {
		return gerror.Wrap(err, "保存OAuth会话失败")
	}

	return nil
}

// DeleteSession 删除OAuth会话
func (s *oauthService) DeleteSession(ctx context.Context, sessionId string) error {
	cacheKey := fmt.Sprintf("oauth_session:%s", sessionId)
	_, err := g.Redis().Del(ctx, cacheKey)
	if err != nil {
		return gerror.Wrap(err, "删除OAuth会话失败")
	}
	return nil
}

// generateState 生成状态参数
func (s *oauthService) generateState(userId uint64, returnUrl string) (string, error) {
	stateData := model.OAuthState{
		UserId:    userId,
		Timestamp: time.Now().Unix(),
		Nonce:     s.generateNonce(),
		ReturnUrl: returnUrl,
	}

	// 序列化状态数据
	stateBytes, err := json.Marshal(stateData)
	if err != nil {
		return "", gerror.Wrap(err, "序列化状态数据失败")
	}

	// Base64编码
	state := base64.URLEncoding.EncodeToString(stateBytes)
	return state, nil
}

// parseState 解析状态参数
func (s *oauthService) parseState(state string) (*model.OAuthState, error) {
	// Base64解码
	stateBytes, err := base64.URLEncoding.DecodeString(state)
	if err != nil {
		return nil, gerror.Wrap(err, "Base64解码状态参数失败")
	}

	// 反序列化状态数据
	var stateData model.OAuthState
	if err := json.Unmarshal(stateBytes, &stateData); err != nil {
		return nil, gerror.Wrap(err, "反序列化状态数据失败")
	}

	// 验证时间戳（防止重放攻击）
	if time.Now().Unix()-stateData.Timestamp > 600 { // 10分钟有效期
		return nil, gerror.New("状态参数已过期")
	}

	return &stateData, nil
}

// generateNonce 生成随机数
func (s *oauthService) generateNonce() string {
	bytes := make([]byte, 16)
	rand.Read(bytes)
	return base64.URLEncoding.EncodeToString(bytes)
}

// generateSessionId 生成会话ID
func (s *oauthService) generateSessionId() string {
	return gmd5.MustEncryptString(fmt.Sprintf("%d_%s", time.Now().UnixNano(), s.generateNonce()))
}

// createOrUpdateUser 创建或更新用户
func (s *oauthService) createOrUpdateUser(ctx context.Context, userInfo *model.MediaWikiUserInfo, gfastUserId uint64) (string, error) {
	var syncResult *UserSyncResult
	var err error

	if gfastUserId > 0 {
		// 如果提供了gfast用户ID，同步用户信息
		syncResult, err = UserSyncService.SyncUserFromMediaWiki(ctx, userInfo, gfastUserId)
		if err != nil {
			return "", gerror.Wrap(err, "同步用户信息失败")
		}
	} else {
		// 如果没有提供gfast用户ID，创建新用户
		syncResult, err = UserSyncService.CreateGfastUser(ctx, userInfo)
		if err != nil {
			return "", gerror.Wrap(err, "创建用户失败")
		}
	}

	g.Log().Info(ctx, "OAuth用户同步完成:", gconv.Map(syncResult))
	return syncResult.Token, nil
}

// getDefaultOAuthConfig 获取默认OAuth配置
func getDefaultOAuthConfig() *model.MediaWikiOAuthConfig {
	return &model.MediaWikiOAuthConfig{
		OAuth: model.OAuthClientConfig{
			ClientId:            "default_client_id",
			ClientSecret:        "default_client_secret",
			AuthorizationUrl:    "https://www.sanxianren.com/wiki/Special:OAuth/authorize",
			TokenUrl:            "https://www.sanxianren.com/wiki/Special:OAuth/token",
			UserInfoUrl:         "https://www.sanxianren.com/w/api.php",
			RedirectUri:         "http://localhost:8808/api/v1/sanxianren/oauth/callback",
			Scopes:              []string{"basic", "editpage"},
			SessionTimeout:      3600,
			RefreshTokenTimeout: 2592000,
		},
		API: model.APIConfig{
			BaseUrl:    "https://www.sanxianren.com/w/api.php",
			UserAgent:  "SanxianrenBot/1.0",
			Timeout:    30,
			RetryCount: 3,
			RetryDelay: 1000,
		},
		UserSync: model.UserSyncConfig{
			AutoCreateUser: true,
			DefaultGroups:  []string{"user"},
			UsernamePrefix: "gfast_",
			PasswordLength: 16,
			FieldMapping: map[string]string{
				"username": "username",
				"email":    "email",
				"realname": "real_name",
			},
		},
	}
}

// GetUserOAuthStatus 获取用户OAuth认证状态
func (s *oauthService) GetUserOAuthStatus(ctx context.Context, userId uint64) (isAuthenticated bool, sessionId string, userInfo *model.MediaWikiUserInfo, expiresAt string, err error) {
	// 查找用户的OAuth会话
	cacheKey := fmt.Sprintf("oauth_user_session:%d", userId)
	sessionData, err := g.Redis().Get(ctx, cacheKey)
	if err != nil {
		// Redis错误，返回未认证状态
		return false, "", nil, "", nil
	}

	if sessionData.IsNil() {
		// 没有找到会话，返回未认证状态
		return false, "", nil, "", nil
	}

	// 解析会话数据
	var session model.OAuthSession
	err = json.Unmarshal(sessionData.Bytes(), &session)
	if err != nil {
		// 解析失败，删除无效会话
		g.Redis().Del(ctx, cacheKey)
		return false, "", nil, "", nil
	}

	// 检查会话是否过期
	if time.Now().After(session.ExpiresAt) {
		// 会话已过期，删除会话
		g.Redis().Del(ctx, cacheKey)
		return false, "", nil, "", nil
	}

	// 验证访问令牌是否仍然有效
	valid, err := s.ValidateToken(ctx, session.AccessToken)
	if err != nil || !valid {
		// 令牌无效，删除会话
		g.Redis().Del(ctx, cacheKey)
		return false, "", nil, "", nil
	}

	// 获取用户信息
	userInfo, userErr := s.GetUserInfo(ctx, session.AccessToken)
	if userErr != nil {
		// 获取用户信息失败，但会话仍然有效
		g.Log().Warning(ctx, "获取用户信息失败:", userErr)
		userInfo = nil
	}

	// 返回认证状态
	return true, session.SessionId, userInfo, session.ExpiresAt.Format(time.RFC3339), nil
}
