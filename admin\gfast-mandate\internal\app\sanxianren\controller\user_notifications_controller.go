/*
* @desc:用户通知控制器
* @company:山东傲英网络科技股份有限公司
* @Author: wolfman
* @Date:   2024/12/19
 */

package controller

import (
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	v1 "github.com/tiger1103/gfast/v3/api/v1/sanxianren"
	"github.com/tiger1103/gfast/v3/internal/app/sanxianren/service"
	systemService "github.com/tiger1103/gfast/v3/internal/app/system/service"
)

// UserNotificationsController 用户通知控制器
type UserNotificationsController struct{}

// GetNotifications 获取通知列表
func (c *UserNotificationsController) GetNotifications(ctx context.Context, req *v1.GetNotificationsReq) (res *v1.GetNotificationsRes, err error) {
	// 获取当前用户ID
	userId := systemService.Context().GetUserId(ctx)
	if userId == 0 {
		return nil, gerror.New("用户未登录")
	}

	// 调用服务获取通知列表
	res, err = service.UserNotifications().GetNotifications(ctx, userId, req)
	if err != nil {
		g.Log().Error(ctx, "获取通知列表失败:", err)
		return nil, gerror.New(err.Error())
	}

	return res, nil
}

// MarkNotificationRead 标记通知已读
func (c *UserNotificationsController) MarkNotificationRead(ctx context.Context, req *v1.MarkNotificationReadReq) (res *v1.MarkNotificationReadRes, err error) {
	// 获取当前用户ID
	userId := systemService.Context().GetUserId(ctx)
	if userId == 0 {
		return nil, gerror.New("用户未登录")
	}

	// 调用服务标记通知已读
	err = service.UserNotifications().MarkNotificationRead(ctx, userId, req)
	if err != nil {
		g.Log().Error(ctx, "标记通知已读失败:", err)
		return nil, gerror.New(err.Error())
	}

	return &v1.MarkNotificationReadRes{}, nil
}

// GetNotificationStats 获取通知统计
func (c *UserNotificationsController) GetNotificationStats(ctx context.Context, req *v1.GetNotificationStatsReq) (res *v1.GetNotificationStatsRes, err error) {
	// 获取当前用户ID
	userId := systemService.Context().GetUserId(ctx)
	if userId == 0 {
		return nil, gerror.New("用户未登录")
	}

	// 调用服务获取通知统计
	res, err = service.UserNotifications().GetNotificationStats(ctx, userId)
	if err != nil {
		g.Log().Error(ctx, "获取通知统计失败:", err)
		return nil, gerror.New(err.Error())
	}

	return res, nil
}
