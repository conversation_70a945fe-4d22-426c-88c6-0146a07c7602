/**
 * 瀑布流布局Hook
 * <AUTHOR> Assistant
 * @date 2025-01-22
 */

import { ref, computed, nextTick, onMounted, onUnmounted, Ref } from 'vue';

export interface WaterfallOptions {
  // 列数
  columnCount?: number;
  // 列间距
  gap?: number;
  // 最小列宽
  minColumnWidth?: number;
  // 是否自动调整列数
  autoColumnCount?: boolean;
  // 响应式断点
  breakpoints?: {
    [key: number]: number; // 屏幕宽度: 列数
  };
}

export interface WaterfallReturn {
  // 列数据
  columns: Ref<any[][]>;
  // 列宽度（CSS值）
  columnWidth: Ref<string>;
  // 实际列数
  actualColumnCount: Ref<number>;
  // 添加项目到列
  addItemsToColumns: (items: any[]) => void;
  // 清空所有列
  clearColumns: () => void;
  // 重新计算布局
  updateLayout: () => void;
  // 获取最短列的索引
  getShortestColumnIndex: () => number;
  // 获取列高度
  getColumnHeights: () => number[];
}

/**
 * 瀑布流布局Hook
 */
export function useWaterfall(
  container: Ref<HTMLElement | undefined>,
  options: WaterfallOptions = {}
): WaterfallReturn {
  const {
    columnCount = 3,
    gap = 20,
    minColumnWidth = 250,
    autoColumnCount = true,
    breakpoints = {
      1200: 4,
      992: 3,
      768: 2,
      576: 1,
    },
  } = options;

  const columns = ref<any[][]>([]);
  const actualColumnCount = ref(columnCount);
  const containerWidth = ref(0);

  // 计算列宽度
  const columnWidth = computed(() => {
    if (containerWidth.value === 0) {
      return '100%';
    }

    const totalGap = (actualColumnCount.value - 1) * gap;
    const availableWidth = containerWidth.value - totalGap;
    const width = availableWidth / actualColumnCount.value;
    
    return `${width}px`;
  });

  // 初始化列
  const initializeColumns = () => {
    columns.value = Array.from({ length: actualColumnCount.value }, () => []);
  };

  // 计算最佳列数
  const calculateColumnCount = () => {
    if (!container.value || !autoColumnCount) {
      actualColumnCount.value = columnCount;
      return;
    }

    const width = container.value.offsetWidth;
    containerWidth.value = width;

    // 根据断点计算列数
    let newColumnCount = columnCount;
    
    for (const [breakpoint, cols] of Object.entries(breakpoints).sort(([a], [b]) => Number(b) - Number(a))) {
      if (width >= Number(breakpoint)) {
        newColumnCount = cols;
        break;
      }
    }

    // 根据最小列宽计算最大可能列数
    const maxPossibleColumns = Math.floor((width + gap) / (minColumnWidth + gap));
    newColumnCount = Math.min(newColumnCount, maxPossibleColumns);
    
    // 确保至少有一列
    newColumnCount = Math.max(1, newColumnCount);

    if (newColumnCount !== actualColumnCount.value) {
      actualColumnCount.value = newColumnCount;
      redistributeItems();
    }
  };

  // 重新分配项目到新的列数
  const redistributeItems = () => {
    const allItems = columns.value.flat();
    initializeColumns();
    addItemsToColumns(allItems);
  };

  // 获取最短列的索引
  const getShortestColumnIndex = (): number => {
    if (columns.value.length === 0) return 0;

    let shortestIndex = 0;
    let shortestHeight = getColumnHeight(0);

    for (let i = 1; i < columns.value.length; i++) {
      const height = getColumnHeight(i);
      if (height < shortestHeight) {
        shortestHeight = height;
        shortestIndex = i;
      }
    }

    return shortestIndex;
  };

  // 获取列高度（估算）
  const getColumnHeight = (columnIndex: number): number => {
    if (!columns.value[columnIndex]) return 0;
    
    // 这里使用项目数量作为高度的估算
    // 在实际应用中，可能需要根据实际的DOM元素高度来计算
    return columns.value[columnIndex].length;
  };

  // 获取所有列的高度
  const getColumnHeights = (): number[] => {
    return columns.value.map((_, index) => getColumnHeight(index));
  };

  // 添加项目到列
  const addItemsToColumns = (items: any[]) => {
    if (!items || items.length === 0) return;

    items.forEach(item => {
      const shortestColumnIndex = getShortestColumnIndex();
      columns.value[shortestColumnIndex].push(item);
    });
  };

  // 清空所有列
  const clearColumns = () => {
    columns.value.forEach(column => column.splice(0));
  };

  // 更新布局
  const updateLayout = async () => {
    await nextTick();
    calculateColumnCount();
  };

  // 窗口大小变化处理
  const handleResize = () => {
    calculateColumnCount();
  };

  // 防抖处理
  let resizeTimeout: number | null = null;
  const debouncedResize = () => {
    if (resizeTimeout) {
      clearTimeout(resizeTimeout);
    }
    resizeTimeout = window.setTimeout(handleResize, 150);
  };

  // 监听容器大小变化
  let resizeObserver: ResizeObserver | null = null;
  const observeContainer = () => {
    if (!container.value || !window.ResizeObserver) {
      return;
    }

    resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width } = entry.contentRect;
        if (width !== containerWidth.value) {
          containerWidth.value = width;
          calculateColumnCount();
        }
      }
    });

    resizeObserver.observe(container.value);
  };

  // 停止观察容器
  const unobserveContainer = () => {
    if (resizeObserver) {
      resizeObserver.disconnect();
      resizeObserver = null;
    }
  };

  onMounted(() => {
    calculateColumnCount();
    initializeColumns();
    
    // 监听窗口大小变化
    window.addEventListener('resize', debouncedResize);
    
    // 监听容器大小变化
    nextTick(() => {
      observeContainer();
    });
  });

  onUnmounted(() => {
    window.removeEventListener('resize', debouncedResize);
    unobserveContainer();
    
    if (resizeTimeout) {
      clearTimeout(resizeTimeout);
    }
  });

  return {
    columns,
    columnWidth,
    actualColumnCount,
    addItemsToColumns,
    clearColumns,
    updateLayout,
    getShortestColumnIndex,
    getColumnHeights,
  };
}

/**
 * 虚拟瀑布流Hook（适用于大量数据）
 */
export function useVirtualWaterfall(
  container: Ref<HTMLElement | undefined>,
  options: WaterfallOptions & {
    itemHeight?: number; // 预估项目高度
    overscan?: number;   // 预渲染项目数量
  } = {}
): WaterfallReturn & {
  visibleItems: Ref<any[]>;
  scrollTop: Ref<number>;
} {
  const {
    itemHeight = 200,
    overscan = 5,
    ...waterfallOptions
  } = options;

  const waterfall = useWaterfall(container, waterfallOptions);
  const visibleItems = ref<any[]>([]);
  const scrollTop = ref(0);

  // 计算可见项目
  const calculateVisibleItems = () => {
    if (!container.value) return;

    const containerHeight = container.value.offsetHeight;
    const startIndex = Math.floor(scrollTop.value / itemHeight) - overscan;
    const endIndex = Math.ceil((scrollTop.value + containerHeight) / itemHeight) + overscan;

    const allItems = waterfall.columns.value.flat();
    visibleItems.value = allItems.slice(
      Math.max(0, startIndex),
      Math.min(allItems.length, endIndex)
    );
  };

  // 监听滚动
  const handleScroll = () => {
    if (container.value) {
      scrollTop.value = container.value.scrollTop;
      calculateVisibleItems();
    }
  };

  onMounted(() => {
    if (container.value) {
      container.value.addEventListener('scroll', handleScroll, { passive: true });
    }
  });

  onUnmounted(() => {
    if (container.value) {
      container.value.removeEventListener('scroll', handleScroll);
    }
  });

  return {
    ...waterfall,
    visibleItems,
    scrollTop,
  };
}

/**
 * 响应式瀑布流Hook
 */
export function useResponsiveWaterfall(
  container: Ref<HTMLElement | undefined>,
  options: WaterfallOptions = {}
): WaterfallReturn {
  const defaultBreakpoints = {
    1400: 5,
    1200: 4,
    992: 3,
    768: 2,
    576: 1,
  };

  return useWaterfall(container, {
    autoColumnCount: true,
    breakpoints: defaultBreakpoints,
    ...options,
  });
}
