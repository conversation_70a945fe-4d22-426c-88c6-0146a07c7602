// 天南地北三线人 - 主应用文件

const { createApp, ref, computed, onMounted, onUnmounted, nextTick } = Vue;
const { ElMessage, ElMessageBox } = ElementPlus;

// 创建Vue应用
const app = createApp({
    setup() {
        // 响应式数据
        const searchQuery = ref('');
        const selectedCategory = ref('all');
        const currentNav = ref('sanxianren');
        const mobileMenuVisible = ref(false);
        
        const initialLoading = ref(true);
        const loadingMore = ref(false);
        const noMoreData = ref(false);
        
        // 文章数据
        const allArticles = ref([...mockArticles]);
        const displayArticles = ref([...mockArticles]);
        const totalCount = ref(mockArticles.length);
        
        // 瀑布流数据
        const waterfallColumns = ref([[], [], [], []]);
        const columnCount = ref(4);
        
        // 计算属性
        const columnWidth = computed(() => `${100 / columnCount.value}%`);
        
        // 方法
        const goHome = () => {
            window.location.href = './index.html';
        };
        
        const handleNavClick = (nav) => {
            currentNav.value = nav.key;
            selectedCategory.value = nav.key;
            mobileMenuVisible.value = false;
            filterArticles();
        };
        
        const handleSearch = () => {
            if (searchQuery.value.trim()) {
                filterArticles();
            }
        };
        
        const selectCategory = (categoryId) => {
            selectedCategory.value = categoryId;
            filterArticles();
        };
        
        const toggleMobileMenu = () => {
            mobileMenuVisible.value = !mobileMenuVisible.value;
        };

        const handleUserCommand = (command) => {
            switch (command) {
                case 'login':
                    // 跳转到MediaWiki登录页面
                    window.location.href = './index.php?title=Special:UserLogin';
                    break;
                case 'register':
                    // 跳转到MediaWiki注册页面
                    window.location.href = './index.php?title=Special:CreateAccount';
                    break;
                case 'admin':
                    // 跳转到管理后台
                    window.location.href = './index.php?title=Special:SpecialPages';
                    break;
                case 'wiki':
                    // 跳转到传统Wiki首页
                    window.location.href = './index.php?title=Main_Page&classic=1';
                    break;
                default:
                    console.log('未知命令:', command);
            }
        };
        
        // 简单的瀑布流分布算法
        const distributeArticles = () => {
            waterfallColumns.value = distributeToColumns(displayArticles.value, columnCount.value);
        };
        
        // 过滤文章
        const filterArticles = () => {
            let filtered = [...allArticles.value];
            
            // 按搜索关键词过滤
            if (searchQuery.value) {
                const query = searchQuery.value.toLowerCase();
                filtered = filtered.filter(article => 
                    article.title.toLowerCase().includes(query) || 
                    article.summary.toLowerCase().includes(query) ||
                    article.tags.some(tag => tag.toLowerCase().includes(query))
                );
            }
            
            // 按分类过滤
            if (selectedCategory.value && selectedCategory.value !== 'all') {
                filtered = filtered.filter(article => article.category === selectedCategory.value);
            }
            
            displayArticles.value = filtered;
            totalCount.value = filtered.length;
            
            // 重新分布到瀑布流
            distributeArticles();
            
            // 更新分类计数
            updateCategoryCounts();
        };
        
        // 更新分类计数
        const updateCategoryCounts = () => {
            categories.forEach(category => {
                if (category.id === 'all') {
                    category.count = allArticles.value.length;
                } else {
                    category.count = allArticles.value.filter(article => 
                        article.category === category.id
                    ).length;
                }
            });
        };
        
        // 加载更多文章
        const loadMoreArticles = async () => {
            if (loadingMore.value || noMoreData.value) return;
            
            loadingMore.value = true;
            
            try {
                // 模拟加载延迟
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                const newArticles = generateMoreArticles(10);
                allArticles.value.push(...newArticles);
                
                // 重新过滤和分布
                filterArticles();
                
                ElMessage.success('加载了更多内容');
            } catch (error) {
                console.error('加载更多文章失败:', error);
                ElMessage.error('加载更多内容失败');
            } finally {
                loadingMore.value = false;
            }
        };
        
        // 打开文章
        const openArticle = (article) => {
            // 这里可以跳转到文章详情页面或打开模态框
            console.log('打开文章:', article);
            ElMessage.info(`正在打开文章: ${article.title}`);
            
            // 模拟跳转到MediaWiki页面
            // window.open(`../index.php?title=${encodeURIComponent(article.title)}`, '_blank');
        };
        
        // 切换收藏状态
        const toggleFavorite = (article) => {
            article.isFavorite = !article.isFavorite;
            article.favorites += article.isFavorite ? 1 : -1;
            
            ElMessage.success(article.isFavorite ? '已添加到收藏' : '已取消收藏');
            
            // 这里可以调用API保存收藏状态
        };
        
        // 分享文章
        const shareArticle = async (article) => {
            const url = `${window.location.origin}/index.php?title=${encodeURIComponent(article.title)}`;
            const success = await shareContent(article.title, article.summary, url);
            
            if (success) {
                ElMessage.success('分享成功');
            } else {
                ElMessage.error('分享失败');
            }
        };
        
        // 刷新数据
        const refreshData = () => {
            filterArticles();
        };
        
        // 窗口大小变化处理
        const handleResize = () => {
            nextTick(() => {
                const width = window.innerWidth;
                columnCount.value = calculateColumnCount(width);
                distributeArticles();
            });
        };
        
        // 滚动处理
        const handleScroll = throttle(() => {
            if (isAtBottom(200) && !loadingMore.value && !noMoreData.value) {
                loadMoreArticles();
            }
        }, 200);
        
        // 生命周期
        onMounted(async () => {
            // 模拟初始加载
            initialLoading.value = true;
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // 初始化数据
            filterArticles();
            initialLoading.value = false;
            
            // 监听窗口大小变化
            window.addEventListener('resize', handleResize);
            
            // 监听滚动事件
            window.addEventListener('scroll', handleScroll);
            
            // 初始化列数
            handleResize();
            
            // 初始化图片懒加载
            lazyLoadImages();
        });
        
        onUnmounted(() => {
            window.removeEventListener('resize', handleResize);
            window.removeEventListener('scroll', handleScroll);
        });
        
        // 返回响应式数据和方法
        return {
            // 数据
            searchQuery,
            selectedCategory,
            currentNav,
            mobileMenuVisible,
            initialLoading,
            loadingMore,
            noMoreData,
            displayArticles,
            totalCount,
            waterfallColumns,
            columnWidth,
            navigationItems,
            categories,
            
            // 方法
            goHome,
            handleNavClick,
            handleSearch,
            selectCategory,
            toggleMobileMenu,
            handleUserCommand,
            openArticle,
            toggleFavorite,
            shareArticle,
            refreshData,
            
            // 工具函数
            formatNumber,
            formatTime,
            getCategoryType,
            getCategoryIcon,
            getCategoryName,
        };
    }
});

// 使用Element Plus
app.use(ElementPlus);

// 挂载应用
app.mount('#app');

// 全局错误处理
window.addEventListener('error', (event) => {
    console.error('全局错误:', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
    console.error('未处理的Promise拒绝:', event.reason);
});

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', () => {
    console.log('天南地北三线人 - 页面加载完成');
    
    // 设置页面标题
    document.title = '天南地北三线人 - 三线建设历史百科';
    
    // 添加页面元数据
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
        metaDescription.content = '记录三线建设历史，传承三线精神。探索三线人故事、三线厂历史、口述历史和历史遗址。';
    }
});

// 页面可见性变化处理
document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'visible') {
        console.log('页面变为可见');
        // 可以在这里刷新数据或恢复某些功能
    } else {
        console.log('页面变为隐藏');
        // 可以在这里暂停某些功能以节省资源
    }
});

// 网络状态变化处理
window.addEventListener('online', () => {
    ElMessage.success('网络连接已恢复');
});

window.addEventListener('offline', () => {
    ElMessage.warning('网络连接已断开');
});

// 导出一些全局函数供其他脚本使用
window.WikipediaApp = {
    formatNumber,
    formatTime,
    getCategoryType,
    getCategoryIcon,
    getCategoryName,
    storage,
    copyToClipboard,
    shareContent,
    scrollToTop,
    isAtBottom,
    isMobile,
    isTouchDevice,
};
