/**
 * 天南地北三线人 API接口
 * <AUTHOR> Assistant
 * @date 2025-01-22
 */

import request from '/@/utils/request';

// 接口类型定义
export interface WikipediaArticle {
  id: string;
  title: string;
  summary: string;
  content?: string;
  image?: string;
  category: string;
  type: string;
  tags: string[];
  author: {
    id: string;
    name: string;
    avatar?: string;
  };
  views: number;
  edits: number;
  favorites: number;
  isFavorite: boolean;
  featured: boolean;
  quality: string;
  language: string;
  createdAt: string;
  updatedAt: string;
}

export interface SearchParams {
  query?: string;
  category?: string;
  language?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}

export interface ListResponse<T> {
  list: T[];
  total: number;
  page: number;
  limit: number;
}

/**
 * Wikipedia API类
 */
class WikipediaApi {
  /**
   * 获取文章列表
   */
  async getArticles(params: SearchParams = {}): Promise<ListResponse<WikipediaArticle>> {
    const response = await request({
      url: '/api/v1/sanxianren/wikipedia/articles',
      method: 'get',
      params,
    });
    return response.data;
  }

  /**
   * 获取三线人列表
   */
  async getPeople(params: SearchParams = {}): Promise<ListResponse<WikipediaArticle>> {
    const response = await request({
      url: '/api/v1/sanxianren/wikipedia/people',
      method: 'get',
      params: {
        ...params,
        category: 'sanxianren',
      },
    });
    return response.data;
  }

  /**
   * 获取三线厂列表
   */
  async getFactories(params: SearchParams = {}): Promise<ListResponse<WikipediaArticle>> {
    const response = await request({
      url: '/api/v1/sanxianren/wikipedia/factories',
      method: 'get',
      params: {
        ...params,
        category: 'sanxianchang',
      },
    });
    return response.data;
  }

  /**
   * 获取故事馆内容
   */
  async getStories(params: SearchParams = {}): Promise<ListResponse<WikipediaArticle>> {
    const response = await request({
      url: '/api/v1/sanxianren/wikipedia/stories',
      method: 'get',
      params: {
        ...params,
        category: 'storyhall',
      },
    });
    return response.data;
  }

  /**
   * 获取口述历史
   */
  async getOralHistory(params: SearchParams = {}): Promise<ListResponse<WikipediaArticle>> {
    const response = await request({
      url: '/api/v1/sanxianren/wikipedia/oral-history',
      method: 'get',
      params: {
        ...params,
        category: 'oralhistory',
      },
    });
    return response.data;
  }

  /**
   * 获取遗址馆内容
   */
  async getHeritage(params: SearchParams = {}): Promise<ListResponse<WikipediaArticle>> {
    const response = await request({
      url: '/api/v1/sanxianren/wikipedia/heritage',
      method: 'get',
      params: {
        ...params,
        category: 'heritage',
      },
    });
    return response.data;
  }

  /**
   * 获取文章详情
   */
  async getArticleDetail(id: string): Promise<WikipediaArticle> {
    const response = await request({
      url: `/api/v1/sanxianren/wikipedia/articles/${id}`,
      method: 'get',
    });
    return response.data;
  }

  /**
   * 搜索文章
   */
  async searchArticles(params: SearchParams): Promise<ListResponse<WikipediaArticle>> {
    const response = await request({
      url: '/api/v1/sanxianren/wikipedia/search',
      method: 'get',
      params,
    });
    return response.data;
  }

  /**
   * 获取搜索建议
   */
  async getSearchSuggestions(query: string): Promise<any[]> {
    const response = await request({
      url: '/api/v1/sanxianren/wikipedia/search/suggestions',
      method: 'get',
      params: { q: query },
    });
    return response.data;
  }

  /**
   * 获取分类统计
   */
  async getCategoryStats(): Promise<Record<string, number>> {
    const response = await request({
      url: '/api/v1/sanxianren/wikipedia/categories/stats',
      method: 'get',
    });
    return response.data;
  }

  /**
   * 收藏文章
   */
  async favoriteArticle(articleId: string): Promise<void> {
    await request({
      url: `/api/v1/sanxianren/wikipedia/articles/${articleId}/favorite`,
      method: 'post',
    });
  }

  /**
   * 取消收藏文章
   */
  async unfavoriteArticle(articleId: string): Promise<void> {
    await request({
      url: `/api/v1/sanxianren/wikipedia/articles/${articleId}/unfavorite`,
      method: 'post',
    });
  }

  /**
   * 获取用户收藏列表
   */
  async getFavorites(params: SearchParams = {}): Promise<ListResponse<WikipediaArticle>> {
    const response = await request({
      url: '/api/v1/sanxianren/wikipedia/favorites',
      method: 'get',
      params,
    });
    return response.data;
  }

  /**
   * 创建文章
   */
  async createArticle(data: Partial<WikipediaArticle>): Promise<WikipediaArticle> {
    const response = await request({
      url: '/api/v1/sanxianren/wikipedia/articles',
      method: 'post',
      data,
    });
    return response.data;
  }

  /**
   * 更新文章
   */
  async updateArticle(id: string, data: Partial<WikipediaArticle>): Promise<WikipediaArticle> {
    const response = await request({
      url: `/api/v1/sanxianren/wikipedia/articles/${id}`,
      method: 'put',
      data,
    });
    return response.data;
  }

  /**
   * 删除文章
   */
  async deleteArticle(id: string): Promise<void> {
    await request({
      url: `/api/v1/sanxianren/wikipedia/articles/${id}`,
      method: 'delete',
    });
  }

  /**
   * 获取热门文章
   */
  async getPopularArticles(limit: number = 10): Promise<WikipediaArticle[]> {
    const response = await request({
      url: '/api/v1/sanxianren/wikipedia/articles/popular',
      method: 'get',
      params: { limit },
    });
    return response.data;
  }

  /**
   * 获取最新文章
   */
  async getLatestArticles(limit: number = 10): Promise<WikipediaArticle[]> {
    const response = await request({
      url: '/api/v1/sanxianren/wikipedia/articles/latest',
      method: 'get',
      params: { limit },
    });
    return response.data;
  }

  /**
   * 获取特色文章
   */
  async getFeaturedArticles(limit: number = 10): Promise<WikipediaArticle[]> {
    const response = await request({
      url: '/api/v1/sanxianren/wikipedia/articles/featured',
      method: 'get',
      params: { limit },
    });
    return response.data;
  }

  /**
   * 获取相关文章
   */
  async getRelatedArticles(articleId: string, limit: number = 5): Promise<WikipediaArticle[]> {
    const response = await request({
      url: `/api/v1/sanxianren/wikipedia/articles/${articleId}/related`,
      method: 'get',
      params: { limit },
    });
    return response.data;
  }

  /**
   * 记录文章浏览
   */
  async recordView(articleId: string): Promise<void> {
    await request({
      url: `/api/v1/sanxianren/wikipedia/articles/${articleId}/view`,
      method: 'post',
    });
  }

  /**
   * 获取文章编辑历史
   */
  async getArticleHistory(articleId: string, params: SearchParams = {}): Promise<any[]> {
    const response = await request({
      url: `/api/v1/sanxianren/wikipedia/articles/${articleId}/history`,
      method: 'get',
      params,
    });
    return response.data;
  }
}

// 导出API实例
export default new WikipediaApi();

// 导出常用的API方法
export const {
  getArticles,
  getPeople,
  getFactories,
  getStories,
  getOralHistory,
  getHeritage,
  getArticleDetail,
  searchArticles,
  getSearchSuggestions,
  getCategoryStats,
  favoriteArticle,
  unfavoriteArticle,
  getFavorites,
  createArticle,
  updateArticle,
  deleteArticle,
  getPopularArticles,
  getLatestArticles,
  getFeaturedArticles,
  getRelatedArticles,
  recordView,
  getArticleHistory,
} = new WikipediaApi();
