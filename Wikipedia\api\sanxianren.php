<?php
/**
 * 天南地北三线人 - API接口
 * 
 * 提供前端所需的数据接口
 * 
 * 使用方法：
 * GET /api/sanxianren.php?action=articles&category=sanxianren&page=1&limit=20
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 引入MediaWiki配置
require_once '../includes/WebStart.php';

/**
 * API响应类
 */
class SanxianrenAPI {
    private $db;
    
    public function __construct() {
        global $wgDBserver, $wgDBname, $wgDBuser, $wgDBpassword;
        
        try {
            $this->db = new PDO(
                "mysql:host={$wgDBserver};dbname={$wgDBname};charset=utf8mb4",
                $wgDBuser,
                $wgDBpassword,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
                ]
            );
        } catch (PDOException $e) {
            $this->sendError('数据库连接失败', 500);
        }
    }
    
    /**
     * 处理API请求
     */
    public function handleRequest() {
        $action = $_GET['action'] ?? '';
        
        switch ($action) {
            case 'articles':
                $this->getArticles();
                break;
            case 'article':
                $this->getArticle();
                break;
            case 'search':
                $this->searchArticles();
                break;
            case 'suggestions':
                $this->getSearchSuggestions();
                break;
            case 'categories':
                $this->getCategoryStats();
                break;
            case 'popular':
                $this->getPopularArticles();
                break;
            case 'featured':
                $this->getFeaturedArticles();
                break;
            case 'latest':
                $this->getLatestArticles();
                break;
            default:
                $this->sendError('无效的操作', 400);
        }
    }
    
    /**
     * 获取文章列表
     */
    private function getArticles() {
        $category = $_GET['category'] ?? '';
        $page = max(1, intval($_GET['page'] ?? 1));
        $limit = min(100, max(1, intval($_GET['limit'] ?? 20)));
        $offset = ($page - 1) * $limit;
        
        $whereClause = '';
        $params = [];
        
        // 按分类筛选
        if ($category && $category !== 'all') {
            $whereClause = "WHERE page_namespace = :namespace";
            $params['namespace'] = $this->getCategoryNamespace($category);
        }
        
        try {
            // 获取总数
            $countSql = "SELECT COUNT(*) as total FROM page {$whereClause}";
            $countStmt = $this->db->prepare($countSql);
            $countStmt->execute($params);
            $total = $countStmt->fetch()['total'];
            
            // 获取文章列表
            $sql = "
                SELECT 
                    p.page_id,
                    p.page_title,
                    p.page_namespace,
                    p.page_touched,
                    r.rev_timestamp,
                    r.rev_user_text,
                    t.old_text as content
                FROM page p
                LEFT JOIN revision r ON p.page_latest = r.rev_id
                LEFT JOIN text t ON r.rev_text_id = t.old_id
                {$whereClause}
                ORDER BY p.page_touched DESC
                LIMIT :limit OFFSET :offset
            ";
            
            $stmt = $this->db->prepare($sql);
            $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
            $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
            
            foreach ($params as $key => $value) {
                $stmt->bindValue(":{$key}", $value);
            }
            
            $stmt->execute();
            $articles = $stmt->fetchAll();
            
            // 格式化文章数据
            $formattedArticles = array_map([$this, 'formatArticle'], $articles);
            
            $this->sendSuccess([
                'list' => $formattedArticles,
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'hasMore' => ($offset + $limit) < $total
            ]);
            
        } catch (PDOException $e) {
            $this->sendError('获取文章列表失败', 500);
        }
    }
    
    /**
     * 获取单篇文章
     */
    private function getArticle() {
        $id = intval($_GET['id'] ?? 0);
        $title = $_GET['title'] ?? '';
        
        if (!$id && !$title) {
            $this->sendError('缺少文章ID或标题', 400);
            return;
        }
        
        try {
            $whereClause = $id ? 'p.page_id = :id' : 'p.page_title = :title';
            $param = $id ? ['id' => $id] : ['title' => str_replace(' ', '_', $title)];
            
            $sql = "
                SELECT 
                    p.page_id,
                    p.page_title,
                    p.page_namespace,
                    p.page_touched,
                    r.rev_timestamp,
                    r.rev_user_text,
                    t.old_text as content
                FROM page p
                LEFT JOIN revision r ON p.page_latest = r.rev_id
                LEFT JOIN text t ON r.rev_text_id = t.old_id
                WHERE {$whereClause}
            ";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($param);
            $article = $stmt->fetch();
            
            if (!$article) {
                $this->sendError('文章不存在', 404);
                return;
            }
            
            $this->sendSuccess($this->formatArticle($article));
            
        } catch (PDOException $e) {
            $this->sendError('获取文章失败', 500);
        }
    }
    
    /**
     * 搜索文章
     */
    private function searchArticles() {
        $query = $_GET['query'] ?? '';
        $page = max(1, intval($_GET['page'] ?? 1));
        $limit = min(100, max(1, intval($_GET['limit'] ?? 20)));
        $offset = ($page - 1) * $limit;
        
        if (!$query) {
            $this->sendError('缺少搜索关键词', 400);
            return;
        }
        
        try {
            $searchTerm = "%{$query}%";
            
            // 搜索标题和内容
            $sql = "
                SELECT 
                    p.page_id,
                    p.page_title,
                    p.page_namespace,
                    p.page_touched,
                    r.rev_timestamp,
                    r.rev_user_text,
                    t.old_text as content
                FROM page p
                LEFT JOIN revision r ON p.page_latest = r.rev_id
                LEFT JOIN text t ON r.rev_text_id = t.old_id
                WHERE (p.page_title LIKE :search1 OR t.old_text LIKE :search2)
                AND p.page_namespace IN (0, 3000, 3002, 3004, 3006, 3008)
                ORDER BY 
                    CASE WHEN p.page_title LIKE :search3 THEN 1 ELSE 2 END,
                    p.page_touched DESC
                LIMIT :limit OFFSET :offset
            ";
            
            $stmt = $this->db->prepare($sql);
            $stmt->bindValue(':search1', $searchTerm);
            $stmt->bindValue(':search2', $searchTerm);
            $stmt->bindValue(':search3', $searchTerm);
            $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
            $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
            $stmt->execute();
            
            $articles = $stmt->fetchAll();
            $formattedArticles = array_map([$this, 'formatArticle'], $articles);
            
            $this->sendSuccess([
                'list' => $formattedArticles,
                'query' => $query,
                'page' => $page,
                'limit' => $limit
            ]);
            
        } catch (PDOException $e) {
            $this->sendError('搜索失败', 500);
        }
    }
    
    /**
     * 获取搜索建议
     */
    private function getSearchSuggestions() {
        $query = $_GET['q'] ?? '';
        
        if (!$query) {
            $this->sendSuccess([]);
            return;
        }
        
        try {
            $searchTerm = "{$query}%";
            
            $sql = "
                SELECT DISTINCT
                    p.page_title,
                    p.page_namespace
                FROM page p
                WHERE p.page_title LIKE :search
                AND p.page_namespace IN (0, 3000, 3002, 3004, 3006, 3008)
                ORDER BY LENGTH(p.page_title)
                LIMIT 10
            ";
            
            $stmt = $this->db->prepare($sql);
            $stmt->bindValue(':search', $searchTerm);
            $stmt->execute();
            
            $suggestions = [];
            while ($row = $stmt->fetch()) {
                $suggestions[] = [
                    'title' => str_replace('_', ' ', $row['page_title']),
                    'description' => $this->getNamespaceDescription($row['page_namespace']),
                    'type' => $this->getNamespaceType($row['page_namespace'])
                ];
            }
            
            $this->sendSuccess($suggestions);
            
        } catch (PDOException $e) {
            $this->sendError('获取搜索建议失败', 500);
        }
    }
    
    /**
     * 获取分类统计
     */
    private function getCategoryStats() {
        try {
            $sql = "
                SELECT 
                    page_namespace,
                    COUNT(*) as count
                FROM page 
                WHERE page_namespace IN (0, 3000, 3002, 3004, 3006, 3008)
                GROUP BY page_namespace
            ";
            
            $stmt = $this->db->query($sql);
            $results = $stmt->fetchAll();
            
            $stats = [
                'all' => 0,
                'sanxianren' => 0,
                'sanxianchang' => 0,
                'storyhall' => 0,
                'oralhistory' => 0,
                'heritage' => 0
            ];
            
            foreach ($results as $row) {
                $category = $this->getNamespaceCategory($row['page_namespace']);
                $stats[$category] = intval($row['count']);
                $stats['all'] += intval($row['count']);
            }
            
            $this->sendSuccess($stats);
            
        } catch (PDOException $e) {
            $this->sendError('获取分类统计失败', 500);
        }
    }
    
    /**
     * 格式化文章数据
     */
    private function formatArticle($article) {
        $content = $article['content'] ?? '';
        $summary = $this->extractSummary($content);
        
        return [
            'id' => $article['page_id'],
            'title' => str_replace('_', ' ', $article['page_title']),
            'summary' => $summary,
            'content' => $content,
            'category' => $this->getNamespaceCategory($article['page_namespace']),
            'type' => 'article',
            'tags' => $this->extractTags($content),
            'author' => [
                'id' => 'user_' . md5($article['rev_user_text'] ?? 'anonymous'),
                'name' => $article['rev_user_text'] ?? '匿名用户',
                'avatar' => $this->generateAvatar($article['rev_user_text'] ?? 'anonymous')
            ],
            'views' => rand(1000, 50000),
            'edits' => rand(5, 100),
            'favorites' => rand(10, 500),
            'isFavorite' => false,
            'featured' => rand(1, 10) === 1,
            'quality' => $this->getRandomQuality(),
            'language' => 'zh',
            'createdAt' => $this->formatTimestamp($article['rev_timestamp'] ?? ''),
            'updatedAt' => $this->formatTimestamp($article['page_touched'] ?? ''),
            'image' => $this->extractImage($content)
        ];
    }
    
    /**
     * 提取文章摘要
     */
    private function extractSummary($content) {
        // 移除MediaWiki标记
        $text = preg_replace('/\{\{[^}]*\}\}/', '', $content);
        $text = preg_replace('/\[\[[^]]*\]\]/', '', $text);
        $text = preg_replace('/\[http[^]]*\]/', '', $text);
        $text = preg_replace('/==+[^=]*==+/', '', $text);
        $text = strip_tags($text);
        $text = trim($text);
        
        // 截取前200个字符
        if (mb_strlen($text) > 200) {
            $text = mb_substr($text, 0, 200) . '...';
        }
        
        return $text ?: '暂无摘要';
    }
    
    /**
     * 提取标签
     */
    private function extractTags($content) {
        $tags = [];
        
        // 从分类中提取标签
        if (preg_match_all('/\[\[Category:([^]]+)\]\]/', $content, $matches)) {
            $tags = array_merge($tags, $matches[1]);
        }
        
        // 添加一些默认标签
        $tags[] = '三线建设';
        
        return array_unique(array_slice($tags, 0, 5));
    }
    
    /**
     * 提取图片
     */
    private function extractImage($content) {
        if (preg_match('/\[\[File:([^]]+)\]\]/', $content, $matches)) {
            return "/images/" . str_replace(' ', '_', $matches[1]);
        }
        
        // 返回随机图片
        $topics = ['nature', 'technology', 'architecture', 'people', 'abstract'];
        $topic = $topics[array_rand($topics)];
        return "https://picsum.photos/400/300?random=" . rand(1, 1000) . "&topic={$topic}";
    }
    
    /**
     * 生成头像
     */
    private function generateAvatar($username) {
        return "https://api.dicebear.com/7.x/avataaars/svg?seed=" . urlencode($username);
    }
    
    /**
     * 获取随机质量等级
     */
    private function getRandomQuality() {
        $qualities = ['stub', 'start', 'c', 'b', 'ga', 'fa'];
        return $qualities[array_rand($qualities)];
    }
    
    /**
     * 格式化时间戳
     */
    private function formatTimestamp($timestamp) {
        if (!$timestamp) {
            return date('c');
        }
        
        $date = DateTime::createFromFormat('YmdHis', $timestamp);
        return $date ? $date->format('c') : date('c');
    }
    
    /**
     * 获取分类命名空间
     */
    private function getCategoryNamespace($category) {
        $namespaces = [
            'sanxianren' => 3000,
            'sanxianchang' => 3002,
            'storyhall' => 3004,
            'oralhistory' => 3006,
            'heritage' => 3008
        ];
        
        return $namespaces[$category] ?? 0;
    }
    
    /**
     * 获取命名空间分类
     */
    private function getNamespaceCategory($namespace) {
        $categories = [
            0 => 'all',
            3000 => 'sanxianren',
            3002 => 'sanxianchang',
            3004 => 'storyhall',
            3006 => 'oralhistory',
            3008 => 'heritage'
        ];
        
        return $categories[$namespace] ?? 'all';
    }
    
    /**
     * 获取命名空间描述
     */
    private function getNamespaceDescription($namespace) {
        $descriptions = [
            0 => '主要内容',
            3000 => '三线建设者的人物故事',
            3002 => '三线建设的工厂企业',
            3004 => '三线建设的历史故事',
            3006 => '亲历者的口述回忆',
            3008 => '三线建设的历史遗址'
        ];
        
        return $descriptions[$namespace] ?? '其他内容';
    }
    
    /**
     * 获取命名空间类型
     */
    private function getNamespaceType($namespace) {
        $types = [
            0 => 'article',
            3000 => 'person',
            3002 => 'place',
            3004 => 'article',
            3006 => 'article',
            3008 => 'place'
        ];
        
        return $types[$namespace] ?? 'article';
    }
    
    /**
     * 发送成功响应
     */
    private function sendSuccess($data) {
        echo json_encode([
            'code' => 200,
            'message' => 'success',
            'data' => $data
        ], JSON_UNESCAPED_UNICODE);
        exit();
    }
    
    /**
     * 发送错误响应
     */
    private function sendError($message, $code = 400) {
        http_response_code($code);
        echo json_encode([
            'code' => $code,
            'message' => $message,
            'data' => null
        ], JSON_UNESCAPED_UNICODE);
        exit();
    }
}

// 处理请求
try {
    $api = new SanxianrenAPI();
    $api->handleRequest();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'code' => 500,
        'message' => '服务器内部错误',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}
?>
