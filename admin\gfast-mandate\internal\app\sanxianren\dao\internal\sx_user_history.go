// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SxUserHistoryDao is the data access object for table sx_user_history.
type SxUserHistoryDao struct {
	table   string               // table is the underlying table name of the DAO.
	group   string               // group is the database configuration group name of current DAO.
	columns SxUserHistoryColumns // columns contains all the column names of Table for convenient usage.
}

// SxUserHistoryColumns defines and stores column names for table sx_user_history.
type SxUserHistoryColumns struct {
	Id          string // 历史记录ID
	UserId      string // 用户ID
	ContentType string // 内容类型
	ContentId   string // 内容ID
	Title       string // 内容标题
	Summary     string // 内容摘要
	CoverImage  string // 封面图片
	ViewedAt    string // 浏览时间
	CreatedAt   string // 创建时间
}

// sxUserHistoryColumns holds the columns for table sx_user_history.
var sxUserHistoryColumns = SxUserHistoryColumns{
	Id:          "id",
	UserId:      "user_id",
	ContentType: "content_type",
	ContentId:   "content_id",
	Title:       "title",
	Summary:     "summary",
	CoverImage:  "cover_image",
	ViewedAt:    "viewed_at",
	CreatedAt:   "created_at",
}

// NewSxUserHistoryDao creates and returns a new DAO object for table data access.
func NewSxUserHistoryDao() *SxUserHistoryDao {
	return &SxUserHistoryDao{
		group:   "default",
		table:   "sx_user_history",
		columns: sxUserHistoryColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *SxUserHistoryDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *SxUserHistoryDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *SxUserHistoryDao) Columns() SxUserHistoryColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *SxUserHistoryDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *SxUserHistoryDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}
