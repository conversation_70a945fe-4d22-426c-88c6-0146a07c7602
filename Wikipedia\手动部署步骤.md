# 📋 手动部署Form-Template-Category系统

## 🎯 部署概述

您需要将18个.wiki文件的内容复制到MediaWiki系统中。这个过程需要逐个创建页面并粘贴内容。

## 📝 部署清单

### ✅ 第一步：部署模板文件（8个）

#### 1. Template:三线工厂
- 访问：`https://www.sanxianren.com/index.php?title=Template:三线工厂&action=edit`
- 复制 `Template_三线工厂.wiki` 文件内容并粘贴
- 编辑摘要：`创建三线工厂模板`
- 点击"保存页面"

#### 2. Template:三线人员
- 访问：`https://www.sanxianren.com/index.php?title=Template:三线人员&action=edit`
- 复制 `Template_三线人员.wiki` 文件内容并粘贴
- 保存页面

#### 3. Template:三线建设背景
- 访问：`https://www.sanxianren.com/index.php?title=Template:三线建设背景&action=edit`
- 复制 `Template_三线建设背景.wiki` 文件内容并粘贴
- 保存页面

#### 4. Template:三线遗址
- 访问：`https://www.sanxianren.com/index.php?title=Template:三线遗址&action=edit`
- 复制 `Template_三线遗址.wiki` 文件内容并粘贴
- 保存页面

#### 5. Template:三线故事馆
- 访问：`https://www.sanxianren.com/index.php?title=Template:三线故事馆&action=edit`
- 复制 `Template_三线故事馆.wiki` 文件内容并粘贴
- 保存页面

#### 6. Template:口述历史
- 访问：`https://www.sanxianren.com/index.php?title=Template:口述历史&action=edit`
- 复制 `Template_口述历史.wiki` 文件内容并粘贴
- 保存页面

#### 7. Template:用户信息框
- 访问：`https://www.sanxianren.com/index.php?title=Template:用户信息框&action=edit`
- 复制 `Template_用户信息框.wiki` 文件内容并粘贴
- 保存页面

#### 8. Template:重要事件
- 访问：`https://www.sanxianren.com/index.php?title=Template:重要事件&action=edit`
- 复制 `Template_重要事件.wiki` 文件内容并粘贴
- 保存页面

### ✅ 第二步：部署表单文件（6个）

#### 1. Form:三线工厂
- 访问：`https://www.sanxianren.com/index.php?title=Form:三线工厂&action=edit`
- 复制 `Form_三线工厂.wiki` 文件内容并粘贴
- 编辑摘要：`创建三线工厂表单`
- 保存页面

#### 2. Form:三线人员
- 访问：`https://www.sanxianren.com/index.php?title=Form:三线人员&action=edit`
- 复制 `Form_三线人员.wiki` 文件内容并粘贴
- 保存页面

#### 3. Form:三线建设背景
- 访问：`https://www.sanxianren.com/index.php?title=Form:三线建设背景&action=edit`
- 复制 `Form_三线建设背景.wiki` 文件内容并粘贴
- 保存页面

#### 4. Form:三线遗址
- 访问：`https://www.sanxianren.com/index.php?title=Form:三线遗址&action=edit`
- 复制 `Form_三线遗址.wiki` 文件内容并粘贴
- 保存页面

#### 5. Form:三线故事馆
- 访问：`https://www.sanxianren.com/index.php?title=Form:三线故事馆&action=edit`
- 复制 `Form_三线故事馆.wiki` 文件内容并粘贴
- 保存页面

#### 6. Form:口述历史
- 访问：`https://www.sanxianren.com/index.php?title=Form:口述历史&action=edit`
- 复制 `Form_口述历史.wiki` 文件内容并粘贴
- 保存页面

### ✅ 第三步：部署分类文件（6个）

#### 1. Category:三线工厂
- 访问：`https://www.sanxianren.com/index.php?title=Category:三线工厂&action=edit`
- 复制 `Category_三线工厂.wiki` 文件内容并粘贴
- 编辑摘要：`创建三线工厂分类`
- 保存页面

#### 2. Category:三线人员
- 访问：`https://www.sanxianren.com/index.php?title=Category:三线人员&action=edit`
- 复制 `Category_三线人员.wiki` 文件内容并粘贴
- 保存页面

#### 3. Category:三线建设背景
- 访问：`https://www.sanxianren.com/index.php?title=Category:三线建设背景&action=edit`
- 复制 `Category_三线建设背景.wiki` 文件内容并粘贴
- 保存页面

#### 4. Category:三线遗址
- 访问：`https://www.sanxianren.com/index.php?title=Category:三线遗址&action=edit`
- 复制 `Category_三线遗址.wiki` 文件内容并粘贴
- 保存页面

#### 5. Category:三线故事馆
- 访问：`https://www.sanxianren.com/index.php?title=Category:三线故事馆&action=edit`
- 复制 `Category_三线故事馆.wiki` 文件内容并粘贴
- 保存页面

#### 6. Category:口述历史
- 访问：`https://www.sanxianren.com/index.php?title=Category:口述历史&action=edit`
- 复制 `Category_口述历史.wiki` 文件内容并粘贴
- 保存页面

## 🔧 部署后配置

### 1. 检查扩展
确保以下扩展已安装并启用：
- **PageForms** - 用于表单功能
- **Cargo** - 用于数据存储（可选但推荐）

访问：`https://www.sanxianren.com/index.php?title=Special:Version` 检查

### 2. 测试表单
部署完成后，测试表单是否正常工作：
- 访问：`https://www.sanxianren.com/index.php?title=Form:三线工厂`
- 尝试创建一个测试页面

### 3. 检查模板
确保模板正确显示：
- 创建一个测试页面，使用模板语法
- 检查格式是否正确

## 🎯 使用指南

### 创建工厂档案
1. 访问：`https://www.sanxianren.com/index.php?title=Form:三线工厂`
2. 输入工厂代号（如：976）
3. 点击"创建工厂档案"
4. 填写表单信息
5. 保存页面

### 创建人员档案
1. 访问：`https://www.sanxianren.com/index.php?title=Form:三线人员`
2. 输入人员姓名
3. 填写个人信息
4. 保存页面

### 浏览内容
- 工厂列表：`https://www.sanxianren.com/index.php?title=Category:三线工厂`
- 人员列表：`https://www.sanxianren.com/index.php?title=Category:三线人员`
- 其他分类：依此类推

## 🚨 常见问题

### 1. 表单不显示
**问题**：访问Form页面显示源代码而不是表单
**解决**：检查PageForms扩展是否正确安装

### 2. 模板格式错误
**问题**：页面显示模板代码而不是格式化内容
**解决**：检查Template页面内容是否正确粘贴

### 3. 分类页面空白
**问题**：分类页面没有显示任何内容
**解决**：确保已创建使用该分类的页面

### 4. 权限问题
**问题**：无法编辑或创建页面
**解决**：确保有足够的用户权限

## 📊 部署检查清单

### 模板文件 (8个)
- [ ] Template:三线工厂
- [ ] Template:三线人员
- [ ] Template:三线建设背景
- [ ] Template:三线遗址
- [ ] Template:三线故事馆
- [ ] Template:口述历史
- [ ] Template:用户信息框
- [ ] Template:重要事件

### 表单文件 (6个)
- [ ] Form:三线工厂
- [ ] Form:三线人员
- [ ] Form:三线建设背景
- [ ] Form:三线遗址
- [ ] Form:三线故事馆
- [ ] Form:口述历史

### 分类文件 (6个)
- [ ] Category:三线工厂
- [ ] Category:三线人员
- [ ] Category:三线建设背景
- [ ] Category:三线遗址
- [ ] Category:三线故事馆
- [ ] Category:口述历史

### 功能测试
- [ ] 表单可以正常显示
- [ ] 可以创建新页面
- [ ] 模板格式正确显示
- [ ] 分类自动归档
- [ ] 编辑功能正常

## 🎉 部署完成

完成所有步骤后，您将拥有一个功能完整的内容管理系统：

1. **用户友好的表单界面** - 简化数据录入
2. **美观的页面模板** - 统一的视觉风格
3. **自动分类管理** - 内容自动归档
4. **响应式设计** - 适配各种设备
5. **完整的功能集** - 涵盖所有内容类型

现在您可以开始录入三线建设的珍贵历史资料了！🚀

## 📞 需要帮助？

如果在部署过程中遇到问题：
1. 检查本文档的常见问题部分
2. 确认所有文件都已正确上传
3. 验证MediaWiki扩展是否正常
4. 联系技术支持获取帮助

祝您部署顺利！🎊
