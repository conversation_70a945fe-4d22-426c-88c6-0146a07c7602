/**
 * Wikipedia数据管理Store
 * <AUTHOR> Assistant
 * @date 2025-01-22
 */

import { defineStore } from 'pinia';
import { ElMessage } from 'element-plus';
import request from '/@/utils/request';

// 词条接口定义
export interface WikipediaArticle {
  id: string;
  title: string;
  summary: string;
  content?: string;
  image?: string;
  category: string;
  type: string;
  tags: string[];
  author: {
    id: string;
    name: string;
    avatar?: string;
  };
  views: number;
  edits: number;
  favorites: number;
  isFavorite: boolean;
  featured: boolean;
  quality: string;
  language: string;
  createdAt: string;
  updatedAt: string;
}

// 搜索建议接口
export interface SearchSuggestion {
  title: string;
  description: string;
  type: string;
  url: string;
}

// 搜索参数接口
export interface SearchParams {
  query?: string;
  category?: string;
  language?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Store状态接口
export interface WikipediaState {
  // 文章列表
  articles: WikipediaArticle[];
  // 总数
  totalCount: number;
  // 当前页
  currentPage: number;
  // 每页数量
  pageSize: number;
  // 是否还有更多数据
  hasMore: boolean;
  // 加载状态
  loading: boolean;
  // 错误信息
  error: string;
  // 搜索历史
  searchHistory: string[];
  // 收藏列表
  favorites: string[];
  // 缓存的文章详情
  articleCache: Record<string, WikipediaArticle>;
  // 最后更新时间
  lastUpdated: number;
}

/**
 * Wikipedia Store
 */
export const useWikipediaStore = defineStore('wikipedia', {
  state: (): WikipediaState => ({
    articles: [],
    totalCount: 0,
    currentPage: 1,
    pageSize: 20,
    hasMore: true,
    loading: false,
    error: '',
    searchHistory: [],
    favorites: [],
    articleCache: {},
    lastUpdated: 0,
  }),

  getters: {
    /**
     * 获取特色文章
     */
    featuredArticles: (state) => state.articles.filter(article => article.featured),

    /**
     * 按分类分组的文章
     */
    articlesByCategory: (state) => {
      const grouped: Record<string, WikipediaArticle[]> = {};
      state.articles.forEach(article => {
        if (!grouped[article.category]) {
          grouped[article.category] = [];
        }
        grouped[article.category].push(article);
      });
      return grouped;
    },

    /**
     * 最近更新的文章
     */
    recentArticles: (state) => {
      return [...state.articles]
        .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
        .slice(0, 10);
    },

    /**
     * 热门文章（按浏览量排序）
     */
    popularArticles: (state) => {
      return [...state.articles]
        .sort((a, b) => b.views - a.views)
        .slice(0, 10);
    },

    /**
     * 收藏的文章
     */
    favoriteArticles: (state) => {
      return state.articles.filter(article => state.favorites.includes(article.id));
    },
  },

  actions: {
    /**
     * 获取文章列表
     */
    async fetchArticles(params: SearchParams = {}): Promise<void> {
      this.loading = true;
      this.error = '';

      try {
        const response = await request({
          url: '/api/v1/sanxianren/wikipedia/articles',
          method: 'get',
          params: {
            page: 1,
            limit: this.pageSize,
            ...params,
          },
        });

        this.articles = response.data.list || [];
        this.totalCount = response.data.total || 0;
        this.currentPage = params.page || 1;
        this.hasMore = this.articles.length < this.totalCount;
        this.lastUpdated = Date.now();

        // 缓存文章详情
        this.articles.forEach(article => {
          this.articleCache[article.id] = article;
        });

        // 保存搜索历史
        if (params.query && !this.searchHistory.includes(params.query)) {
          this.searchHistory.unshift(params.query);
          this.searchHistory = this.searchHistory.slice(0, 10); // 只保留最近10条
        }

      } catch (error: any) {
        this.error = error.message || '获取文章列表失败';
        console.error('获取文章列表失败:', error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    /**
     * 加载更多文章
     */
    async loadMoreArticles(params: SearchParams = {}): Promise<boolean> {
      if (!this.hasMore || this.loading) {
        return false;
      }

      this.loading = true;

      try {
        const nextPage = this.currentPage + 1;
        const response = await request({
          url: '/api/v1/sanxianren/wikipedia/articles',
          method: 'get',
          params: {
            page: nextPage,
            limit: this.pageSize,
            ...params,
          },
        });

        const newArticles = response.data.list || [];
        
        if (newArticles.length > 0) {
          this.articles.push(...newArticles);
          this.currentPage = nextPage;
          this.hasMore = this.articles.length < this.totalCount;

          // 缓存新文章
          newArticles.forEach((article: WikipediaArticle) => {
            this.articleCache[article.id] = article;
          });

          return true;
        } else {
          this.hasMore = false;
          return false;
        }

      } catch (error: any) {
        this.error = error.message || '加载更多文章失败';
        console.error('加载更多文章失败:', error);
        return false;
      } finally {
        this.loading = false;
      }
    },

    /**
     * 获取新加载的文章（用于瀑布流）
     */
    getNewArticles(): WikipediaArticle[] {
      const startIndex = (this.currentPage - 1) * this.pageSize;
      return this.articles.slice(startIndex);
    },

    /**
     * 获取文章详情
     */
    async getArticleDetail(id: string): Promise<WikipediaArticle | null> {
      // 先从缓存中查找
      if (this.articleCache[id]) {
        return this.articleCache[id];
      }

      try {
        const response = await request({
          url: `/api/v1/sanxianren/wikipedia/articles/${id}`,
          method: 'get',
        });

        const article = response.data;
        if (article) {
          this.articleCache[id] = article;
          
          // 更新列表中的文章
          const index = this.articles.findIndex(a => a.id === id);
          if (index !== -1) {
            this.articles[index] = article;
          }
        }

        return article;
      } catch (error: any) {
        console.error('获取文章详情失败:', error);
        return null;
      }
    },

    /**
     * 获取搜索建议
     */
    async getSearchSuggestions(query: string): Promise<SearchSuggestion[]> {
      if (!query.trim()) {
        return [];
      }

      try {
        const response = await request({
          url: '/api/v1/sanxianren/wikipedia/search/suggestions',
          method: 'get',
          params: { q: query },
        });

        return response.data || [];
      } catch (error: any) {
        console.error('获取搜索建议失败:', error);
        return [];
      }
    },

    /**
     * 切换收藏状态
     */
    async toggleFavorite(articleId: string): Promise<void> {
      try {
        const isFavorite = this.favorites.includes(articleId);
        
        if (isFavorite) {
          // 取消收藏
          await request({
            url: `/api/v1/sanxianren/wikipedia/articles/${articleId}/unfavorite`,
            method: 'post',
          });
          
          this.favorites = this.favorites.filter(id => id !== articleId);
        } else {
          // 添加收藏
          await request({
            url: `/api/v1/sanxianren/wikipedia/articles/${articleId}/favorite`,
            method: 'post',
          });
          
          this.favorites.push(articleId);
        }

        // 更新文章的收藏状态
        const article = this.articles.find(a => a.id === articleId);
        if (article) {
          article.isFavorite = !isFavorite;
          article.favorites += isFavorite ? -1 : 1;
        }

        // 更新缓存
        if (this.articleCache[articleId]) {
          this.articleCache[articleId].isFavorite = !isFavorite;
          this.articleCache[articleId].favorites += isFavorite ? -1 : 1;
        }

      } catch (error: any) {
        console.error('切换收藏状态失败:', error);
        throw error;
      }
    },

    /**
     * 获取收藏列表
     */
    async fetchFavorites(): Promise<void> {
      try {
        const response = await request({
          url: '/api/v1/sanxianren/wikipedia/favorites',
          method: 'get',
        });

        this.favorites = response.data.map((item: any) => item.article_id) || [];
      } catch (error: any) {
        console.error('获取收藏列表失败:', error);
      }
    },

    /**
     * 创建文章
     */
    async createArticle(articleData: Partial<WikipediaArticle>): Promise<WikipediaArticle> {
      try {
        const response = await request({
          url: '/api/v1/sanxianren/wikipedia/articles',
          method: 'post',
          data: articleData,
        });

        const newArticle = response.data;
        this.articles.unshift(newArticle);
        this.articleCache[newArticle.id] = newArticle;
        this.totalCount += 1;

        ElMessage.success('文章创建成功');
        return newArticle;
      } catch (error: any) {
        console.error('创建文章失败:', error);
        ElMessage.error('创建文章失败');
        throw error;
      }
    },

    /**
     * 更新文章
     */
    async updateArticle(id: string, articleData: Partial<WikipediaArticle>): Promise<WikipediaArticle> {
      try {
        const response = await request({
          url: `/api/v1/sanxianren/wikipedia/articles/${id}`,
          method: 'put',
          data: articleData,
        });

        const updatedArticle = response.data;
        
        // 更新列表中的文章
        const index = this.articles.findIndex(a => a.id === id);
        if (index !== -1) {
          this.articles[index] = updatedArticle;
        }

        // 更新缓存
        this.articleCache[id] = updatedArticle;

        ElMessage.success('文章更新成功');
        return updatedArticle;
      } catch (error: any) {
        console.error('更新文章失败:', error);
        ElMessage.error('更新文章失败');
        throw error;
      }
    },

    /**
     * 删除文章
     */
    async deleteArticle(id: string): Promise<void> {
      try {
        await request({
          url: `/api/v1/sanxianren/wikipedia/articles/${id}`,
          method: 'delete',
        });

        // 从列表中移除
        this.articles = this.articles.filter(a => a.id !== id);
        
        // 从缓存中移除
        delete this.articleCache[id];
        
        // 从收藏中移除
        this.favorites = this.favorites.filter(fId => fId !== id);
        
        this.totalCount -= 1;

        ElMessage.success('文章删除成功');
      } catch (error: any) {
        console.error('删除文章失败:', error);
        ElMessage.error('删除文章失败');
        throw error;
      }
    },

    /**
     * 清空数据
     */
    clearData(): void {
      this.articles = [];
      this.totalCount = 0;
      this.currentPage = 1;
      this.hasMore = true;
      this.error = '';
    },

    /**
     * 重置状态
     */
    reset(): void {
      this.clearData();
      this.searchHistory = [];
      this.articleCache = {};
      this.lastUpdated = 0;
    },

    /**
     * 清除搜索历史
     */
    clearSearchHistory(): void {
      this.searchHistory = [];
    },

    /**
     * 从本地存储加载数据
     */
    loadFromStorage(): void {
      try {
        const stored = localStorage.getItem('wikipedia_store');
        if (stored) {
          const data = JSON.parse(stored);
          this.searchHistory = data.searchHistory || [];
          this.favorites = data.favorites || [];
        }
      } catch (error) {
        console.error('从本地存储加载数据失败:', error);
      }
    },

    /**
     * 保存到本地存储
     */
    saveToStorage(): void {
      try {
        const data = {
          searchHistory: this.searchHistory,
          favorites: this.favorites,
        };
        localStorage.setItem('wikipedia_store', JSON.stringify(data));
      } catch (error) {
        console.error('保存到本地存储失败:', error);
      }
    },
  },
});
