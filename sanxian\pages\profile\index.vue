<template>
	<view class="profile-page">
		<!-- 页面头部 -->
		<PageHeader title="个人中心" />
		
		<!-- 个人信息区域 -->
		<view class="user-info-section">
			<view class="user-info" v-if="userInfo.isLogin">
				<view class="user-avatar-wrapper">
				  <image class="user-avatar" :src="userInfo.avatar" mode="aspectFill" @click="goToEditProfile"></image>
				</view>
				<view class="user-details">
					<view class="user-name-section">
						<text class="user-name">{{userInfo.name}}</text>
						<view class="auth-badge" v-if="userInfo.isAuthenticated">
							<text class="auth-text">已认证</text>
						</view>
					</view>
					<!-- 会员徽章区域 -->
					<view class="member-badges" v-if="userInfo.badges && userInfo.badges.length > 0">
						<view class="badge-item" v-for="badge in userInfo.badges" :key="badge.type" :class="badge.type">
							<text class="badge-icon">{{badge.icon}}</text>
							<text class="badge-text">{{badge.name}}</text>
						</view>
					</view>
					<view class="user-factories">
						<text class="factory-label">工作经历：</text>
						<scroll-view class="factory-scroll" scroll-x="true" show-scrollbar="false">
							<view class="factory-list">
								<view class="factory-tag" v-for="factory in userInfo.factories" :key="factory.id">
									<text class="factory-name">{{factory.name}}</text>
								</view>
							</view>
						</scroll-view>
					</view>
					<view class="user-stats">
						<view class="stat-item">
							<text class="stat-number">{{userInfo.submissionCount}}</text>
							<text class="stat-label">投稿</text>
						</view>
						<view class="stat-item">
							<text class="stat-number">{{userInfo.collectionCount}}</text>
							<text class="stat-label">收藏</text>
						</view>
						<view class="stat-item">
							<text class="stat-number">{{userInfo.followCount}}</text>
							<text class="stat-label">关注</text>
						</view>
					</view>
				</view>
			</view>
			<!-- 未登录状态 -->
			<view class="login-prompt" v-else @click="goToLogin">
				<view class="login-icon">👤</view>
				<text class="login-text">点击登录</text>
				<text class="login-desc">登录后享受更多功能</text>
			</view>
		</view>

		<!-- 主内容区域 -->
		<view class="content">
			
			<!-- 捐赠支持 -->
			<view class="section">
				<view class="donate-banner" @click="goToSupport">
					<view class="donate-content">
						<view class="donate-left">
							<text class="donate-icon">❤️</text>
							<view class="donate-info">
								<text class="donate-title">支持传承</text>
								<text class="donate-desc">为三线文化传承贡献力量</text>
							</view>
						</view>
						<view class="donate-right">
							<text class="donate-action">立即捐赠</text>
						</view>
					</view>
					<view class="donate-stats" v-if="userInfo.donateAmount > 0">
						<text class="donate-total">累计捐赠：¥{{userInfo.donateAmount}}</text>
					</view>
				</view>
			</view>
			
			<!-- 我的投稿 -->
			<view class="section">
				<view class="section-header">
					<view class="header-left">
						<text class="section-title">📝 我的投稿</text>
						<text class="section-subtitle">管理我的投稿内容</text>
					</view>
					<view class="more-link" @click="viewAllSubmissions">
						<text class="more-text">查看全部</text>
						<text class="more-arrow">></text>
					</view>
				</view>
				<view class="submission-stats">
					<view class="stat-card pending" @click="viewSubmissions('pending')">
						<text class="stat-count">{{submissionStats.pending}}</text>
						<text class="stat-status">待审核</text>
					</view>
					<view class="stat-card approved" @click="viewSubmissions('approved')">
						<text class="stat-count">{{submissionStats.approved}}</text>
						<text class="stat-status">已通过</text>
					</view>
					<view class="stat-card rejected" @click="viewSubmissions('rejected')">
						<text class="stat-count">{{submissionStats.rejected}}</text>
						<text class="stat-status">已拒绝</text>
					</view>
				</view>
			</view>

			<!-- 通知中心 -->
			<view class="section">
				<view class="section-header">
					<text class="section-title">🔔 通知中心</text>
				</view>
				<NotificationCenter />
			</view>

			<!-- 收藏与历史 -->
			<view class="section">
				<view class="section-header">
					<text class="section-title">💖 收藏与历史</text>
				</view>
				<view class="menu-list">
					<MenuItem icon="⭐" text="我的收藏" action="viewCollections" :count="userInfo.collectionCount" @menu-click="handleMenuClick" />
					<MenuItem icon="📖" text="浏览历史" action="viewHistory" @menu-click="handleMenuClick" />
				</view>
			</view>

			
			<!-- 文创商城与订单 -->
			<view class="section">
				<view class="section-header">
					<text class="section-title">🛍️ 商城与订单</text>
				</view>
				<view class="menu-list">
					<MenuItem icon="🏪" text="文创商城" action="goToShop" @menu-click="handleMenuClick" />
					<MenuItem icon="📦" text="我的订单" action="viewOrders" :count="orderStats.total > 0 ? orderStats.total : null" @menu-click="handleMenuClick" />
				</view>
				<view class="order-quick-stats">
					<view class="quick-stat" @click="viewOrders('pending')">
						<text class="quick-icon">⏳</text>
						<text class="quick-count">{{orderStats.pending}}</text>
						<text class="quick-label">待付款</text>
					</view>
					<view class="quick-stat" @click="viewOrders('shipping')">
						<text class="quick-icon">🚚</text>
						<text class="quick-count">{{orderStats.shipping}}</text>
						<text class="quick-label">待收货</text>
					</view>
					<view class="quick-stat" @click="viewOrders('completed')">
						<text class="quick-icon">✅</text>
						<text class="quick-count">{{orderStats.completed}}</text>
						<text class="quick-label">已完成</text>
					</view>
					<view class="quick-stat" @click="viewOrders('refund')">
						<text class="quick-icon">🔄</text>
						<text class="quick-count">{{orderStats.refund}}</text>
						<text class="quick-label">退款</text>
					</view>
				</view>
			</view>

			

			<!-- 身份认证 -->
			<view class="section" v-if="userInfo.isLogin">
				<view class="section-header">
					<text class="section-title">🔐 身份认证</text>
				</view>
				<view class="auth-card" @click="goToAuth">
					<view class="auth-content">
						<view class="auth-left">
							<text class="auth-status" v-if="!userInfo.isAuthenticated">未认证</text>
							<text class="auth-status verified" v-else>已认证</text>
							<text class="auth-desc" v-if="!userInfo.isAuthenticated">需要5个人证明身份</text>
							<text class="auth-desc" v-else>认证人数：{{userInfo.authCount}}/5</text>
						</view>
						<view class="auth-right">
							<text class="auth-action" v-if="!userInfo.isAuthenticated">去认证</text>
							<text class="auth-action" v-else>查看详情</text>
						</view>
					</view>
					<view class="auth-progress" v-if="userInfo.authCount > 0">
						<view class="progress-bar">
							<view class="progress-fill" :style="{width: (userInfo.authCount / 5 * 100) + '%'}"></view>
						</view>
						<text class="progress-text">{{userInfo.authCount}}/5</text>
					</view>
				</view>
			</view>

			<!-- 账号管理 -->
			<view class="section">
				<view class="section-header">
					<text class="section-title">⚙️ 账号管理</text>
				</view>
				<view class="menu-list">
					<MenuItem v-if="userInfo.isLogin" icon="👤" text="编辑资料" action="editProfile" @menu-click="handleMenuClick" />
					<MenuItem v-if="userInfo.isLogin" icon="🔧" text="账号设置" action="accountSettings" @menu-click="handleMenuClick" />
					<MenuItem v-if="userInfo.isLogin" icon="🔒" text="隐私设置" action="privacySettings" @menu-click="handleMenuClick" />
					<MenuItem v-if="!userInfo.isLogin" icon="🔑" text="登录/注册" action="goToLogin" @menu-click="handleMenuClick" />
					<MenuItem v-if="userInfo.isLogin" icon="🚪" text="退出登录" action="logout" type="logout" @menu-click="handleMenuClick" />
				</view>
			</view>

            <!-- 会员中心 -->
			<view class="section">
				<view class="vip-banner" @click="goToVipCenter">
					<view class="vip-content">
						<view class="vip-left">
							<text class="vip-title">👑 会员中心</text>
							<text class="vip-desc" v-if="!userInfo.isVip">开通VIP享受专属权益</text>
							<text class="vip-desc" v-else>VIP会员 · 到期时间：{{userInfo.vipExpireDate}}</text>
						</view>
						<view class="vip-right">
							<text class="vip-action" v-if="!userInfo.isVip">立即开通</text>
							<text class="vip-action" v-else>管理会员</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 其他功能 -->
			<view class="section">
				<view class="section-header">
					<text class="section-title">📱 其他功能</text>
				</view>
				<view class="menu-list">
					<MenuItem icon="📞" text="联系我们" action="contactUs" @menu-click="handleMenuClick" />
					<MenuItem icon="💬" text="意见反馈" action="feedback" @menu-click="handleMenuClick" />
					<MenuItem icon="ℹ️" text="关于我们" action="aboutApp" @menu-click="handleMenuClick" />
				</view>
			</view>
		</view>
		
		<!-- 底部导航菜单 -->
		<!-- 底部导航栏 -->
		<BottomNav currentPage="profile" />
	</view>
</template>

<script>
	import BottomNav from '@/components/BottomNav.vue'
	import PageHeader from '@/components/PageHeader.vue'
	import MenuItem from '@/components/MenuItem.vue'
	import NotificationCenter from '@/components/common/NotificationCenter.vue'
	import {
	getFactoryPeopleDetail,
	getSubmissionStats,
	getOrderStats,
	getUserProfile,
	getUserSubmissionStats,
	getUserVipInfo,
	getUserBadges,
	getNotificationStats
} from '@/utils/apiUtils.js'
	import userStore from '@/utils/userStore.js'

	export default {
		components: {
			BottomNav,
			PageHeader,
			MenuItem,
			NotificationCenter
		},
		// 页面加载时获取最新用户数据
		onLoad() {
			this.updateUserInfo();
			// 监听用户状态变化
			userStore.addListener(this.updateUserInfo);
		},
		// 页面卸载时移除监听
		onUnload() {
			userStore.removeListener(this.updateUserInfo);
		},
		data() {
			return {
				// 用户信息 - 从userStore获取实际数据
				userInfo: userStore.userInfo || {
					isLogin: false,
					name: '',
					avatar: '/static/sanxianren.jpg',
					isVip: false,
					vipExpireDate: '',
					isAuthenticated: false,
					authCount: 0,
					submissionCount: 0,
					collectionCount: 0,
					followCount: 0,
					donateAmount: 0,
					factories: [],
					// 会员徽章数据
					badges: [
						{ type: 'vip', name: 'VIP会员', icon: '👑', active: true },
						{ type: 'author', name: '作者', icon: '✍️', active: true },
						{ type: 'donor', name: '捐赠者', icon: '💝', active: true },
						{ type: 'volunteer', name: '志愿者', icon: '🤝', active: false },
						{ type: 'promoter', name: '推广者', icon: '📢', active: false }
					].filter(badge => badge.active)
				},
				// 投稿统计
				submissionStats: {
					pending: 3,
					approved: 12,
					rejected: 0
				},
				// 订单统计
				orderStats: {
					total: 5,
					pending: 1,
					shipping: 2,
					completed: 2,
					refund: 0
				}
			}
		},
		methods: {
			/**
			 * 更新用户信息
			 * 从userStore获取最新的用户数据并更新页面
			 */
			async updateUserInfo() {
				console.log('开始更新用户信息...');

				// 检查用户是否已登录
				if (userStore.isLoggedIn && userStore.userInfo) {
					try {
						// 使用新的用户个人资料API
						const profileResult = await getUserProfile();
						console.log('用户个人资料结果:', profileResult);

						if (profileResult.success && profileResult.data) {
							const profileData = profileResult.data;

							// 获取投稿统计
							const submissionStatsResult = await getUserSubmissionStats();
							console.log('投稿统计结果:', submissionStatsResult);

							// 获取VIP信息
							const vipInfoResult = await getUserVipInfo();
							console.log('VIP信息结果:', vipInfoResult);

							// 获取徽章信息
							const badgesResult = await getUserBadges();
							console.log('徽章信息结果:', badgesResult);

							// 获取通知统计
							const notificationStatsResult = await getNotificationStats();
							console.log('通知统计结果:', notificationStatsResult);

							// 更新用户信息
							this.userInfo = {
								isLogin: true,
								id: profileData.id,
								name: profileData.name,
								avatar: profileData.avatar || '/static/sanxianren.jpg',
								isVip: profileData.isVip,
								vipExpireDate: profileData.vipExpireDate,
								isAuthenticated: profileData.isAuthenticated,
								authCount: profileData.authCount,
								submissionCount: profileData.submissionCount,
								collectionCount: profileData.collectionCount,
								followCount: profileData.followCount,
								donateAmount: profileData.donateAmount,
								factories: profileData.factories || [],
								badges: profileData.badges || []
							};

							// 更新投稿统计
							if (submissionStatsResult.success && submissionStatsResult.data) {
								this.submissionStats = submissionStatsResult.data;
							}

							// 更新通知统计（可以用于显示未读通知数量）
							if (notificationStatsResult.success && notificationStatsResult.data) {
								this.notificationStats = notificationStatsResult.data;
								// 更新用户信息中的未读通知数量
								this.userInfo.unreadNotifications = notificationStatsResult.data.unread || 0;
							}

						} else {
							console.error('获取用户个人资料失败:', profileResult.error);
							// 回退到userStore数据
							this.updateUserInfoFromStore();
						}
					} catch (error) {
						console.error('更新用户信息时发生错误:', error);
						// 回退到userStore数据
						this.updateUserInfoFromStore();
					}

					console.log('用户信息已更新:', this.userInfo);
				} else {
					// 用户未登录，设置默认值
					this.userInfo = {
						isLogin: false,
						name: '',
						avatar: '/static/sanxianren.jpg',
						isVip: false,
						vipExpireDate: '',
						isAuthenticated: false,
						authCount: 0,
						submissionCount: 0,
						collectionCount: 0,
						followCount: 0,
						donateAmount: 0,
						factories: [],
						badges: []
					};
					console.log('用户未登录，使用默认信息');
				}
			},

			/**
			 * 从userStore更新用户信息（回退方法）
			 */
			updateUserInfoFromStore() {
				// 更新用户信息
				this.userInfo = userStore.userInfo;

				// 确保必要的字段存在
				if (!this.userInfo.badges) {
					this.userInfo.badges = [
						{ type: 'vip', name: 'VIP会员', icon: '👑', active: this.userInfo.isVip },
						{ type: 'author', name: '作者', icon: '✍️', active: this.userInfo.submissionCount > 0 },
						{ type: 'donor', name: '捐赠者', icon: '💝', active: this.userInfo.donateAmount > 0 },
						{ type: 'volunteer', name: '志愿者', icon: '🤝', active: false },
						{ type: 'promoter', name: '推广者', icon: '📢', active: false }
					].filter(badge => badge.active);
				}

				// 如果没有工厂信息，设置为空数组
				if (!this.userInfo.factories) {
					this.userInfo.factories = [];
				}
			},
			
			/**
			 * 处理菜单点击事件
			 * @param {Object} event - 事件对象，包含action等信息
			 */
			handleMenuClick(event) {
				const action = event.action || event;
				console.log('菜单点击事件:', action);
				if (this[action] && typeof this[action] === 'function') {
					this[action]();
				} else {
					console.warn('未找到对应的方法:', action);
				}
			},
			
			/**
			 * 跳转到登录页面
			 */
			goToLogin() {
				console.log('跳转到登录页面');
				uni.navigateTo({
					url: '/pages/auth/login'
				});
			},
			
			/**
			 * 跳转到编辑个人资料页面（已登录用户点击头像）
			 */
			goToEditProfile() {
				console.log('跳转到编辑个人资料页面');
				uni.navigateTo({
					url: '/pages/profile/edit'
				});
			},
			
			/**
			 * 查看所有投稿
			 */
			viewAllSubmissions() {
				console.log('查看所有投稿');
				// uni.navigateTo({
				// 	url: '/pages/submission/list'
				// });
			},
			
			/**
			 * 按状态查看投稿
			 * @param {string} status - 投稿状态 (pending/approved/rejected)
			 */
			viewSubmissions(status) {
				console.log('查看投稿状态:', status);
				// uni.navigateTo({
				// 	url: `/pages/submission/list?status=${status}`
				// });
			},
			
			/**
			 * 查看收藏
			 */
			viewCollections() {
				console.log('查看收藏');
				// uni.navigateTo({
				// 	url: '/pages/collection/index'
				// });
			},
			
			/**
			 * 查看浏览历史
			 */
			viewHistory() {
				console.log('查看浏览历史');
				// uni.navigateTo({
				// 	url: '/pages/history/index'
				// });
			},
			
			/**
			 * 跳转到VIP中心
			 */
			goToVipCenter() {
				console.log('跳转到VIP中心');
				// uni.navigateTo({
				// 	url: '/pages/vip/index'
				// });
			},
			
			/**
			 * 跳转到文创商城
			 */
			goToShop() {
				console.log('跳转到文创商城');
				// uni.navigateTo({
				// 	url: '/pages/shop/index'
				// });
			},
			
			/**
			 * 查看订单
			 * @param {string} status - 订单状态 (pending/shipping/completed/refund)
			 */
			viewOrders(status = 'all') {
				console.log('查看订单:', status);
				// uni.navigateTo({
				// 	url: `/pages/order/list?status=${status}`
				// });
			},
			
			/**
			 * 跳转到支持页面
			 */
			goToSupport() {
				console.log('跳转到支持页面');
				uni.navigateTo({
					url: '/pages/support/index'
				});
			},
			
			/**
			 * 跳转到身份认证页面
			 */
			goToAuth() {
				console.log('跳转到身份认证页面');
				uni.navigateTo({
					url: '/pages/auth/verification'
				});
			},
			
			/**
			 * 查看我的收藏
			 */
			viewCollections() {
				console.log('查看我的收藏');
				uni.navigateTo({
					url: '/pages/profile/collections'
				});
			},
			
			/**
			 * 查看浏览历史
			 */
			viewHistory() {
				console.log('查看浏览历史');
				uni.navigateTo({
					url: '/pages/profile/history'
				});
			},
			
			/**
			 * 编辑个人资料
			 */
			editProfile() {
				console.log('编辑个人资料');
				uni.navigateTo({
					url: '/pages/profile/edit'
				});
			},
			
			/**
			 * 账号设置
			 */
			accountSettings() {
				console.log('账号设置');
				// uni.navigateTo({
				// 	url: '/pages/settings/account'
				// });
			},
			
			/**
			 * 隐私设置
			 */
			privacySettings() {
				console.log('隐私设置');
				// uni.navigateTo({
				// 	url: '/pages/settings/privacy'
				// });
			},
			
			/**
			 * 退出登录
			 * 函数说明：统一调用 userStore.userLogout 清理会话（token、用户信息、权限、缓存），
			 * 并提示用户退出成功，随后重启到首页，避免页面堆栈残留。
			 */
			logout() {
				uni.showModal({
					title: '确认退出',
					content: '确定要退出登录吗？',
					success: (res) => {
						if (res.confirm) {
							// 调用统一登出逻辑，确保清理新旧键
							if (userStore && typeof userStore.userLogout === 'function') {
								userStore.userLogout();
							}
							// 更新本地展示状态
							this.userInfo.isLogin = false;
							this.userInfo.name = '';
							this.userInfo.avatar = '';
							this.userInfo.isVip = false;

							uni.showToast({
								title: '退出成功',
								icon: 'success',
								duration: 800
							});

							// 重启到首页，避免返回到需鉴权的历史页面
							setTimeout(() => {
								uni.reLaunch({ url: '/pages/index/index' });
							}, 800);
						} else {
							// 用户取消
						}
					}
				});
			},
			
			/**
			 * 联系我们
			 */
			contactUs() {
				console.log('联系我们');
				// uni.navigateTo({
				// 	url: '/pages/contact/index'
				// });
			},
			
			/**
			 * 意见反馈
			 */
			feedback() {
				console.log('意见反馈');
				// uni.navigateTo({
				// 	url: '/pages/feedback/index'
				// });
			},
			
			/**
			 * 关于我们
			 */
			aboutApp() {
				console.log('关于我们');
				// uni.navigateTo({
				// 	url: '/pages/about/index'
				// });
			},

			/**
			 * 更新用户徽章
			 * @param {string} badgeType - 徽章类型 (vip, author, donor, volunteer, promoter)
			 * @param {boolean} active - 是否激活徽章
			 */
			updateBadge(badgeType, active) {
				const allBadges = [
					{ type: 'vip', name: 'VIP会员', icon: '👑', active: false },
					{ type: 'author', name: '作者', icon: '✍️', active: false },
					{ type: 'donor', name: '捐赠者', icon: '💝', active: false },
					{ type: 'volunteer', name: '志愿者', icon: '🤝', active: false },
					{ type: 'promoter', name: '推广者', icon: '📢', active: false }
				];

				// 更新当前用户的徽章状态
				allBadges.forEach(badge => {
					if (badge.type === badgeType) {
						badge.active = active;
					}
					// 检查用户是否已有该徽章
					const existingBadge = this.userInfo.badges.find(b => b.type === badge.type);
					if (existingBadge) {
						badge.active = true;
					}
				});

				// 更新用户徽章列表
				this.userInfo.badges = allBadges.filter(badge => badge.active);
			},

			/**
			 * 检查并自动获得徽章
			 */
			checkAndAwardBadges() {
				// 作者徽章：投稿数量 >= 5
				if (this.userInfo.submissionCount >= 5) {
					this.updateBadge('author', true);
				}

				// 捐赠者徽章：捐赠金额 > 0
				if (this.userInfo.donateAmount > 0) {
					this.updateBadge('donor', true);
				}

				// VIP徽章：根据VIP状态
				if (this.userInfo.isVip) {
					this.updateBadge('vip', true);
				}
			},

			/**
			 * 页面加载时统一获取个人中心数据
			 * 职责：
			 * 1) 校验登录状态与 token；未登录或无 token 时不调用需要鉴权的接口
			 * 2) 调用 getFactoryPeopleDetail 获取用户基本信息（无需鉴权）
			 * 3) 在已登录且有 token 的情况下调用 getSubmissionStats 与 getOrderStats 获取统计数据
			 * 说明：request 层已统一处理 401（清理 token 并跳转登录），此处无需重复提示
			 */
			async fetchProfileData() {
				try {
					// 从 userStore 获取登录状态与 token，更可靠且包含迁移逻辑
					const isLoggedIn = typeof userStore.getLoginStatus === 'function' ? userStore.getLoginStatus() : false;
					const token = typeof userStore.getToken === 'function' ? userStore.getToken() : null;
					// 兼容历史数据：若 userStore 无数据，则退回到旧存储键
					const storedUser = (typeof userStore.getUserInfo === 'function' ? userStore.getUserInfo() : null) || uni.getStorageSync('userInfo') || {};
					const id = storedUser && storedUser.id;

					if (!id || !isLoggedIn || !token) {
						// 未登录或 token 不存在：不发起需要鉴权的请求，直接显示未登录态
						this.userInfo.isLogin = false;
						// 清空统计，避免展示过期数据
						this.submissionStats = { pending: 0, approved: 0, rejected: 0 };
						this.orderStats = { total: 0, pending: 0, shipping: 0, completed: 0, refund: 0 };
						return;
					}

					// 1) 获取工厂人员信息（无需鉴权）
					const userRes = await getFactoryPeopleDetail(id);
					if (userRes && userRes.success && userRes.data) {
						this.userInfo = {
							isLogin: true,
							name: userRes.data.name,
							avatar: userRes.data.avatar,
							isVip: userRes.data.isVip,
							vipExpireDate: userRes.data.vipExpireDate,
							isAuthenticated: userRes.data.isAuthenticated,
							authCount: userRes.data.authCount,
							submissionCount: userRes.data.story_count,
							collectionCount: userRes.data.collection_count,
							followCount: userRes.data.follow_count,
							donateAmount: userRes.data.donateAmount,
							factories: userRes.data.factories || [],
							badges: userRes.data.badges || []
						}
					}

					// 2) 获取受保护的统计数据（需鉴权）
					const [submissionRes, orderRes] = await Promise.allSettled([
						getSubmissionStats(),
						getOrderStats()
					]);

					if (submissionRes.status === 'fulfilled' && submissionRes.value && submissionRes.value.success && submissionRes.value.data) {
						this.submissionStats = {
							pending: submissionRes.value.data.pending,
							approved: submissionRes.value.data.approved,
							rejected: submissionRes.value.data.rejected
						}
					} else {
						// 兜底置零，避免页面残留旧数据
						this.submissionStats = { pending: 0, approved: 0, rejected: 0 };
					}

					if (orderRes.status === 'fulfilled' && orderRes.value && orderRes.value.success && orderRes.value.data) {
						this.orderStats = {
							total: orderRes.value.data.total,
							pending: orderRes.value.data.pending,
							shipping: orderRes.value.data.shipping,
							completed: orderRes.value.data.completed,
							refund: orderRes.value.data.refund
						}
					} else {
						this.orderStats = { total: 0, pending: 0, shipping: 0, completed: 0, refund: 0 };
					}
				} catch (error) {
					// 若为 401，request 已统一处理（清 token + 跳登录），此处直接返回
					if (error && (error.code === 401 || error.status === 401)) return;
					console.error('获取个人中心数据失败:', error);
				}
			},
		},
		/**
		 * 页面加载时执行
		 */
		onLoad() {
		  // 页面加载时获取用户相关数据
		  this.fetchProfileData()
		},
		
	}
		  
</script>

<style>
.profile-page {
  min-height: 100vh;
  background: #f5f5f5;
}
.user-info-section {
  background: #fff;
  border-radius: 20rpx;
  margin: 24rpx 24rpx 0 24rpx;
  padding: 32rpx 24rpx 24rpx 24rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.06);
}
.user-info {
  display: flex;
  align-items: flex-start;
}
.user-avatar-wrapper {
  flex-shrink: 0;
  flex-grow: 0;
  width: 80px;
  height: 80px;
  max-width: 20vw;
  max-height: 20vw;
  min-width: 40px;
  min-height: 40px;
  border-radius: 50%;
  overflow: hidden;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
}
.user-avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #fff;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  background: #f5f5f5;
  display: block;
  margin: 0 auto;
}
.user-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}
.user-name-section {
  display: flex;
  align-items: center;
  gap: 12rpx;
}
.user-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #222;
}
.auth-badge {
  background: #e6f7ff;
  color: #1890ff;
  border-radius: 8rpx;
  padding: 4rpx 12rpx;
  font-size: 20rpx;
  margin-left: 8rpx;
}
.member-badges {
  display: flex;
  gap: 12rpx;
  margin: 8rpx 0;
}
.badge-item {
  display: flex;
  align-items: center;
  background: #fdf6ec;
  border-radius: 8rpx;
  padding: 4rpx 10rpx;
  font-size: 20rpx;
  color: #faad14;
}
.badge-item.vip {
  background: #fffbe6;
  color: #faad14;
}
.badge-item.author {
  background: #e6f7ff;
  color: #1890ff;
}
.badge-item.donor {
  background: #fff0f6;
  color: #eb2f96;
}
.badge-item.volunteer {
  background: #f6ffed;
  color: #52c41a;
}
.badge-item.promoter {
  background: #f0f5ff;
  color: #2f54eb;
}
.user-factories {
  margin: 8rpx 0 0 0;
  display: flex;
  align-items: center;
  font-size: 22rpx;
  color: #666;
}
.factory-label {
  margin-right: 8rpx;
  font-size: 28rpx;
  font-weight: 700;
  color: #333;
  white-space: nowrap;
}
.factory-scroll {
  width: 70vw;
  overflow-x: auto;
}
.factory-list {
  display: flex;
  gap: 10rpx;
}
.factory-tag {
  background: #e6f7ff;
  color: #1890ff;
  border-radius: 8rpx;
  padding: 4rpx 12rpx;
  font-size: 20rpx;
}
.user-stats {
  display: flex;
  gap: 32rpx;
  margin-top: 12rpx;
}
.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.stat-number {
  font-size: 32rpx;
  font-weight: 600;
  color: #1890ff;
}
.stat-label {
  font-size: 20rpx;
  color: #888;
}
.login-prompt {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
  color: #888;
  cursor: pointer;
}
.login-icon {
  font-size: 60rpx;
  margin-bottom: 12rpx;
}
.login-text {
  font-size: 28rpx;
  font-weight: 600;
}
.login-desc {
  font-size: 20rpx;
  color: #bbb;
}
.content {
  margin: 24rpx;
  padding-bottom: 120rpx; /* 适配底栏，防止内容被遮挡，可根据底栏高度调整 */
}
.section {
  background: #fff;
  border-radius: 20rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.06);
  padding: 24rpx;
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #222;
}
.section-subtitle {
  font-size: 20rpx;
  color: #888;
  margin-left: 8rpx;
}
.more-link {
  display: flex;
  align-items: center;
  color: #1890ff;
  font-size: 22rpx;
  cursor: pointer;
}
.more-arrow {
  margin-left: 4rpx;
}
.submission-stats {
  display: flex;
  gap: 24rpx;
  margin-top: 12rpx;
}
.stat-card {
  flex: 1;
  background: #f5f5f5;
  border-radius: 12rpx;
  padding: 20rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 22rpx;
  cursor: pointer;
  transition: background 0.2s;
}
.stat-card.pending {
  background: #fffbe6;
  color: #faad14;
}
.stat-card.approved {
  background: #f6ffed;
  color: #52c41a;
}
.stat-card.rejected {
  background: #fff0f6;
  color: #eb2f96;
}
.stat-count {
  font-size: 28rpx;
  font-weight: 600;
}
.stat-status {
  font-size: 20rpx;
  color: #888;
}
.menu-list {
  display: flex;
  gap: 32rpx;
  margin-top: 12rpx;
}
.order-quick-stats {
  display: flex;
  gap: 24rpx;
  margin-top: 16rpx;
}
.quick-stat {
  flex: 1;
  background: #f5f5f5;
  border-radius: 12rpx;
  padding: 16rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 20rpx;
  cursor: pointer;
}
.quick-icon {
  font-size: 32rpx;
  margin-bottom: 4rpx;
}
.quick-count {
  font-size: 24rpx;
  font-weight: 600;
}
.quick-label {
  font-size: 18rpx;
  color: #888;
}
.auth-card {
  background: #f5f5f5;
  border-radius: 12rpx;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  cursor: pointer;
}
.auth-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.auth-status {
  font-size: 22rpx;
  font-weight: 600;
  color: #faad14;
}
.auth-status.verified {
  color: #52c41a;
}
.auth-desc {
  font-size: 18rpx;
  color: #888;
  margin-left: 8rpx;
}
.auth-action {
  font-size: 22rpx;
  color: #1890ff;
  margin-left: 12rpx;
}
.auth-progress {
  margin-top: 8rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
}
.progress-bar {
  flex: 1;
  height: 12rpx;
  background: #e6f7ff;
  border-radius: 8rpx;
  overflow: hidden;
}
.progress-fill {
  height: 100%;
  background: #1890ff;
  border-radius: 8rpx;
  transition: width 0.3s;
}
.progress-text {
  font-size: 18rpx;
  color: #888;
}
</style>
		
	