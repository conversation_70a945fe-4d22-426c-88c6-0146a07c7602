#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天南地北三线人 - 模板系统自动部署脚本
自动将Form、Template、Category文件上传到MediaWiki

使用方法：
python 部署模板系统.py

需要安装：pip install requests
"""

import os
import requests
import json
import time
from urllib.parse import urljoin

class MediaWikiUploader:
    def __init__(self, base_url, username=None, password=None):
        """
        初始化MediaWiki上传器
        
        Args:
            base_url: MediaWiki站点URL，如 https://www.sanxianren.com
            username: 用户名（可选，如果需要登录）
            password: 密码（可选，如果需要登录）
        """
        self.base_url = base_url.rstrip('/')
        self.api_url = urljoin(self.base_url, '/api.php')
        self.session = requests.Session()
        self.username = username
        self.password = password
        self.logged_in = False
        
    def login(self):
        """登录到MediaWiki"""
        if not self.username or not self.password:
            print("⚠️  未提供用户名密码，将以匿名用户身份上传")
            return True
            
        # 获取登录token
        login_token_response = self.session.get(self.api_url, params={
            'action': 'query',
            'meta': 'tokens',
            'type': 'login',
            'format': 'json'
        })
        
        if login_token_response.status_code != 200:
            print(f"❌ 获取登录token失败: {login_token_response.status_code}")
            return False
            
        login_token = login_token_response.json()['query']['tokens']['logintoken']
        
        # 执行登录
        login_response = self.session.post(self.api_url, data={
            'action': 'login',
            'lgname': self.username,
            'lgpassword': self.password,
            'lgtoken': login_token,
            'format': 'json'
        })
        
        if login_response.status_code == 200:
            result = login_response.json()
            if result['login']['result'] == 'Success':
                print(f"✅ 登录成功: {self.username}")
                self.logged_in = True
                return True
            else:
                print(f"❌ 登录失败: {result['login']['result']}")
                return False
        else:
            print(f"❌ 登录请求失败: {login_response.status_code}")
            return False
    
    def get_edit_token(self):
        """获取编辑token"""
        response = self.session.get(self.api_url, params={
            'action': 'query',
            'meta': 'tokens',
            'format': 'json'
        })
        
        if response.status_code == 200:
            return response.json()['query']['tokens']['csrftoken']
        else:
            print(f"❌ 获取编辑token失败: {response.status_code}")
            return None
    
    def upload_page(self, title, content, summary="自动部署模板系统"):
        """
        上传页面内容
        
        Args:
            title: 页面标题
            content: 页面内容
            summary: 编辑摘要
        """
        edit_token = self.get_edit_token()
        if not edit_token:
            return False
            
        response = self.session.post(self.api_url, data={
            'action': 'edit',
            'title': title,
            'text': content,
            'summary': summary,
            'token': edit_token,
            'format': 'json'
        })
        
        if response.status_code == 200:
            result = response.json()
            if 'edit' in result and result['edit']['result'] == 'Success':
                print(f"✅ 成功上传: {title}")
                return True
            else:
                print(f"❌ 上传失败: {title} - {result}")
                return False
        else:
            print(f"❌ 上传请求失败: {title} - {response.status_code}")
            return False

def read_wiki_file(filepath):
    """读取.wiki文件内容"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        print(f"❌ 读取文件失败: {filepath} - {e}")
        return None

def main():
    print("🚀 天南地北三线人 - 模板系统自动部署")
    print("=" * 50)
    
    # 配置
    BASE_URL = "https://www.sanxianren.com"

    # 登录配置 - 请填写您的MediaWiki管理员账号
    print("🔐 请输入您的MediaWiki登录信息：")
    USERNAME = input("用户名: ").strip()
    if USERNAME:
        import getpass
        PASSWORD = getpass.getpass("密码: ")
    else:
        print("⚠️  未提供登录信息，将尝试匿名上传")
        USERNAME = None
        PASSWORD = None
    
    # 初始化上传器
    uploader = MediaWikiUploader(BASE_URL, USERNAME, PASSWORD)
    
    # 登录（如果提供了用户名密码）
    if not uploader.login():
        print("❌ 登录失败，退出部署")
        return
    
    # 定义要上传的文件映射
    files_to_upload = {
        # 模板文件
        "Template:三线工厂": "Template_三线工厂.wiki",
        "Template:三线人员": "Template_三线人员.wiki",
        "Template:三线建设背景": "Template_三线建设背景.wiki",
        "Template:三线遗址": "Template_三线遗址.wiki",
        "Template:三线故事馆": "Template_三线故事馆.wiki",
        "Template:口述历史": "Template_口述历史.wiki",
        "Template:用户信息框": "Template_用户信息框.wiki",
        "Template:重要事件": "Template_重要事件.wiki",
        
        # 表单文件
        "Form:三线工厂": "Form_三线工厂.wiki",
        "Form:三线人员": "Form_三线人员.wiki",
        "Form:三线建设背景": "Form_三线建设背景.wiki",
        "Form:三线遗址": "Form_三线遗址.wiki",
        "Form:三线故事馆": "Form_三线故事馆.wiki",
        "Form:口述历史": "Form_口述历史.wiki",
        
        # 分类文件
        "Category:三线工厂": "Category_三线工厂.wiki",
        "Category:三线人员": "Category_三线人员.wiki",
        "Category:三线建设背景": "Category_三线建设背景.wiki",
        "Category:三线遗址": "Category_三线遗址.wiki",
        "Category:三线故事馆": "Category_三线故事馆.wiki",
        "Category:口述历史": "Category_口述历史.wiki",
    }
    
    # 统计
    success_count = 0
    fail_count = 0
    
    print(f"📋 准备上传 {len(files_to_upload)} 个文件...")
    print()
    
    # 逐个上传文件
    for page_title, filename in files_to_upload.items():
        print(f"📤 正在上传: {page_title}")
        
        # 检查文件是否存在
        if not os.path.exists(filename):
            print(f"⚠️  文件不存在: {filename}")
            fail_count += 1
            continue
        
        # 读取文件内容
        content = read_wiki_file(filename)
        if content is None:
            fail_count += 1
            continue
        
        # 上传页面
        if uploader.upload_page(page_title, content):
            success_count += 1
        else:
            fail_count += 1
        
        # 避免请求过于频繁
        time.sleep(1)
        print()
    
    # 输出结果
    print("=" * 50)
    print(f"📊 部署完成统计:")
    print(f"✅ 成功: {success_count} 个文件")
    print(f"❌ 失败: {fail_count} 个文件")
    print(f"📈 成功率: {success_count/(success_count+fail_count)*100:.1f}%")
    
    if success_count > 0:
        print()
        print("🎉 部署成功！您现在可以：")
        print(f"1. 访问表单页面开始创建内容：{BASE_URL}/index.php?title=Form:三线工厂")
        print(f"2. 查看分类页面浏览内容：{BASE_URL}/index.php?title=Category:三线工厂")
        print(f"3. 访问现代化首页：{BASE_URL}/modern.php")
        
    if fail_count > 0:
        print()
        print("⚠️  部分文件上传失败，请检查：")
        print("1. 网络连接是否正常")
        print("2. MediaWiki是否正常运行")
        print("3. 是否有编辑权限")
        print("4. 文件路径是否正确")

if __name__ == "__main__":
    main()
