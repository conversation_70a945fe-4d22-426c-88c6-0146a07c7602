# gfast中台 - MediaWiki OAuth前端集成指南

## 概述

本指南详细说明如何在前端项目中集成gfast中台与MediaWiki的OAuth认证功能。提供了多种前端技术栈的实现方案。

## 🚀 快速开始

### 1. 基础JavaScript集成

最简单的集成方式，适用于任何前端项目：

```html
<!-- 引入OAuth管理器 -->
<script src="frontend_oauth_integration.js"></script>

<script>
// 创建OAuth管理器实例
const oauth = new OAuthManager({
    baseUrl: '/api/v1/sanxianren',
    redirectPath: '/admin/oauth/success'
});

// 发起OAuth认证
async function startOAuth() {
    await oauth.startOAuth();
}

// 监听认证成功事件
window.addEventListener('oauth:success', (e) => {
    console.log('OAuth认证成功:', e.detail);
});
</script>
```

### 2. Vue.js集成

使用提供的Vue组件：

```vue
<template>
  <div>
    <OAuthIntegration 
      :show-config="true" 
      :show-logs="true" 
    />
  </div>
</template>

<script>
import OAuthIntegration from './OAuthIntegration.vue'

export default {
  components: {
    OAuthIntegration
  }
}
</script>
```

### 3. React集成

使用提供的React组件：

```jsx
import React from 'react';
import OAuthIntegration from './OAuthIntegration.jsx';

function App() {
  return (
    <div>
      <OAuthIntegration 
        showConfig={true} 
        showLogs={true} 
      />
    </div>
  );
}

export default App;
```

## 📁 文件结构

```
frontend/
├── oauth/
│   ├── frontend_oauth_integration.js  # 核心OAuth管理器
│   ├── OAuthIntegration.vue          # Vue组件
│   ├── OAuthIntegration.jsx          # React组件
│   └── oauth_demo.html               # 演示页面
└── styles/
    └── oauth.css                     # 样式文件
```

## 🔧 核心功能

### OAuth管理器 (OAuthManager)

核心JavaScript类，提供以下功能：

#### 初始化
```javascript
const oauth = new OAuthManager({
    baseUrl: '/api/v1/sanxianren',      // API基础URL
    tokenKey: 'gfast_token',            // gfast令牌存储键
    oauthTokenKey: 'oauth_token',       // OAuth令牌存储键
    redirectPath: '/admin/oauth/success' // 认证成功后重定向路径
});
```

#### 主要方法

1. **发起OAuth认证**
```javascript
await oauth.startOAuth(returnUrl);
```

2. **获取OAuth状态**
```javascript
const status = await oauth.getOAuthStatus();
```

3. **刷新OAuth令牌**
```javascript
const newToken = await oauth.refreshOAuthToken(refreshToken);
```

4. **撤销OAuth认证**
```javascript
await oauth.revokeOAuth();
```

#### 事件监听

```javascript
// 认证成功
window.addEventListener('oauth:success', (e) => {
    console.log('用户信息:', e.detail.userInfo);
    console.log('令牌:', e.detail.token);
});

// 认证注销
window.addEventListener('oauth:logout', () => {
    console.log('OAuth认证已注销');
});
```

## 🎨 UI组件

### Vue组件特性

- ✅ 响应式状态管理
- ✅ Element Plus UI组件
- ✅ 完整的错误处理
- ✅ 用户信息展示
- ✅ 操作日志记录

```vue
<OAuthIntegration 
  :show-config="true"   <!-- 显示OAuth配置信息 -->
  :show-logs="true"     <!-- 显示操作日志 -->
/>
```

### React组件特性

- ✅ Hooks状态管理
- ✅ Ant Design UI组件
- ✅ TypeScript支持
- ✅ 完整的错误处理
- ✅ 用户信息展示

```jsx
<OAuthIntegration 
  showConfig={true}   // 显示OAuth配置信息
  showLogs={true}     // 显示操作日志
/>
```

## 🔐 安全考虑

### 1. 令牌管理

```javascript
// 安全存储令牌
localStorage.setItem('gfast_token', token);

// 自动清理过期令牌
if (isTokenExpired(token)) {
    localStorage.removeItem('gfast_token');
}
```

### 2. CSRF防护

OAuth状态参数自动包含CSRF保护：

```javascript
// 状态参数包含时间戳和随机数
const state = {
    userId: 12345,
    timestamp: Date.now(),
    nonce: generateNonce(),
    returnUrl: '/admin'
};
```

### 3. URL清理

认证完成后自动清理敏感URL参数：

```javascript
// 清理OAuth回调参数
oauth.cleanUrl(); // 移除code、state等参数
```

## 🌐 API接口

### 1. 发起OAuth认证

```http
POST /api/v1/sanxianren/oauth/authorize
Authorization: Bearer {gfast_token}
Content-Type: application/json

{
  "return_url": "http://localhost:5173/admin/oauth/success"
}
```

### 2. 处理OAuth回调

```http
GET /api/v1/sanxianren/oauth/callback?code={code}&state={state}
```

### 3. 获取OAuth状态

```http
GET /api/v1/sanxianren/oauth/status
Authorization: Bearer {gfast_token}
```

### 4. 刷新OAuth令牌

```http
POST /api/v1/sanxianren/oauth/refresh
Content-Type: application/json

{
  "refresh_token": "{refresh_token}"
}
```

## 🎯 使用场景

### 1. 管理后台集成

在gfast管理后台中添加MediaWiki账户关联功能：

```javascript
// 在用户设置页面添加OAuth按钮
<button onclick="oauth.startOAuth()">
    关联MediaWiki账户
</button>
```

### 2. 单点登录(SSO)

实现gfast与MediaWiki的单点登录：

```javascript
// 检查OAuth状态，自动跳转
const status = await oauth.getOAuthStatus();
if (!status.is_authenticated) {
    await oauth.startOAuth();
}
```

### 3. 内容编辑集成

在内容编辑器中集成MediaWiki功能：

```javascript
// 检查是否有MediaWiki权限
if (oauth.isAuthenticated && oauth.userInfo.rights.includes('editpage')) {
    // 显示MediaWiki编辑功能
    showMediaWikiEditor();
}
```

## 🔧 自定义配置

### 1. 自定义样式

```css
/* 覆盖默认样式 */
.oauth-integration {
    --primary-color: #1890ff;
    --success-color: #52c41a;
    --warning-color: #faad14;
    --error-color: #ff4d4f;
}
```

### 2. 自定义消息提示

```javascript
const oauth = new OAuthManager({
    // 自定义消息显示函数
    showMessage: (message, type) => {
        // 使用你的消息组件
        MyNotification.show(message, type);
    }
});
```

### 3. 自定义事件处理

```javascript
// 自定义认证成功处理
window.addEventListener('oauth:success', (e) => {
    // 保存用户信息到Vuex/Redux
    store.commit('setOAuthUser', e.detail.userInfo);
    
    // 更新路由权限
    updateRoutePermissions(e.detail.userInfo.rights);
    
    // 显示欢迎消息
    showWelcomeMessage(e.detail.userInfo.name);
});
```

## 🐛 故障排除

### 1. 常见问题

#### 认证失败
```javascript
// 检查gfast令牌是否有效
const token = localStorage.getItem('gfast_token');
if (!token) {
    console.error('请先登录gfast系统');
}
```

#### 回调处理失败
```javascript
// 检查URL参数
const urlParams = new URLSearchParams(window.location.search);
const error = urlParams.get('error');
if (error) {
    console.error('OAuth错误:', error);
}
```

#### 跨域问题
```javascript
// 确保API请求包含正确的头部
fetch('/api/v1/sanxianren/oauth/status', {
    headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
    }
});
```

### 2. 调试模式

```javascript
// 启用调试模式
const oauth = new OAuthManager({
    debug: true, // 输出详细日志
    baseUrl: '/api/v1/sanxianren'
});
```

## 📱 移动端适配

### 1. 响应式设计

```css
@media (max-width: 768px) {
    .oauth-integration {
        padding: 10px;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
}
```

### 2. 触摸优化

```css
.btn {
    min-height: 44px; /* 触摸友好的最小高度 */
    padding: 12px 20px;
}
```

## 🚀 部署建议

### 1. 生产环境配置

```javascript
const oauth = new OAuthManager({
    baseUrl: process.env.NODE_ENV === 'production' 
        ? 'https://api.sanxianren.com/api/v1/sanxianren'
        : '/api/v1/sanxianren',
    redirectPath: '/admin/oauth/success'
});
```

### 2. CDN部署

```html
<!-- 从CDN加载OAuth管理器 -->
<script src="https://cdn.sanxianren.com/js/oauth-manager.min.js"></script>
```

## 📚 更多资源

- [OAuth 2.0 规范](https://tools.ietf.org/html/rfc6749)
- [MediaWiki OAuth扩展文档](https://www.mediawiki.org/wiki/Extension:OAuth)
- [gfast框架文档](https://github.com/tiger1103/gfast)

## 🤝 技术支持

如有问题或建议，请：
1. 查看故障排除部分
2. 检查浏览器控制台错误
3. 联系开发团队获取支持
