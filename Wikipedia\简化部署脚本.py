#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天南地北三线人 - 简化部署脚本
生成部署URL列表，方便手动复制粘贴

使用方法：
python 简化部署脚本.py
"""

import os

def generate_deployment_urls():
    """生成部署URL列表"""
    
    base_url = "https://www.sanxianren.com"
    
    # 定义要部署的文件映射
    files_to_deploy = {
        # 模板文件
        "Template:三线工厂": "Template_三线工厂.wiki",
        "Template:三线人员": "Template_三线人员.wiki", 
        "Template:三线建设背景": "Template_三线建设背景.wiki",
        "Template:三线遗址": "Template_三线遗址.wiki",
        "Template:三线故事馆": "Template_三线故事馆.wiki",
        "Template:口述历史": "Template_口述历史.wiki",
        "Template:用户信息框": "Template_用户信息框.wiki",
        "Template:重要事件": "Template_重要事件.wiki",
        
        # 表单文件
        "Form:三线工厂": "Form_三线工厂.wiki",
        "Form:三线人员": "Form_三线人员.wiki",
        "Form:三线建设背景": "Form_三线建设背景.wiki", 
        "Form:三线遗址": "Form_三线遗址.wiki",
        "Form:三线故事馆": "Form_三线故事馆.wiki",
        "Form:口述历史": "Form_口述历史.wiki",
        
        # 分类文件
        "Category:三线工厂": "Category_三线工厂.wiki",
        "Category:三线人员": "Category_三线人员.wiki",
        "Category:三线建设背景": "Category_三线建设背景.wiki",
        "Category:三线遗址": "Category_三线遗址.wiki", 
        "Category:三线故事馆": "Category_三线故事馆.wiki",
        "Category:口述历史": "Category_口述历史.wiki",
    }
    
    print("🚀 天南地北三线人 - 部署URL生成器")
    print("=" * 60)
    print()
    
    # 检查文件存在性
    missing_files = []
    for page_title, filename in files_to_deploy.items():
        if not os.path.exists(filename):
            missing_files.append(filename)
    
    if missing_files:
        print("❌ 以下文件不存在：")
        for file in missing_files:
            print(f"   - {file}")
        print()
        print("请确保所有.wiki文件都在当前目录中")
        return
    
    print("✅ 所有文件检查完毕，开始生成部署链接...")
    print()
    
    # 按类型分组显示
    template_files = {k: v for k, v in files_to_deploy.items() if k.startswith("Template:")}
    form_files = {k: v for k, v in files_to_deploy.items() if k.startswith("Form:")}
    category_files = {k: v for k, v in files_to_deploy.items() if k.startswith("Category:")}
    
    # 生成模板部署链接
    print("📋 第一步：部署模板文件（8个）")
    print("-" * 40)
    for i, (page_title, filename) in enumerate(template_files.items(), 1):
        url = f"{base_url}/index.php?title={page_title.replace(':', '%3A')}&action=edit"
        print(f"{i}. {page_title}")
        print(f"   URL: {url}")
        print(f"   文件: {filename}")
        print()
    
    # 生成表单部署链接
    print("📝 第二步：部署表单文件（6个）")
    print("-" * 40)
    for i, (page_title, filename) in enumerate(form_files.items(), 1):
        url = f"{base_url}/index.php?title={page_title.replace(':', '%3A')}&action=edit"
        print(f"{i}. {page_title}")
        print(f"   URL: {url}")
        print(f"   文件: {filename}")
        print()
    
    # 生成分类部署链接
    print("📂 第三步：部署分类文件（6个）")
    print("-" * 40)
    for i, (page_title, filename) in enumerate(category_files.items(), 1):
        url = f"{base_url}/index.php?title={page_title.replace(':', '%3A')}&action=edit"
        print(f"{i}. {page_title}")
        print(f"   URL: {url}")
        print(f"   文件: {filename}")
        print()
    
    # 生成HTML文件方便复制
    generate_html_guide(base_url, files_to_deploy)
    
    print("🎯 部署说明：")
    print("1. 点击上面的URL链接")
    print("2. 复制对应.wiki文件的全部内容")
    print("3. 粘贴到MediaWiki编辑框中")
    print("4. 点击'保存页面'")
    print("5. 重复以上步骤完成所有文件部署")
    print()
    print("💡 提示：建议先部署Template文件，再部署Form文件，最后部署Category文件")
    print("📄 详细的HTML指南已生成：deployment_guide.html")

def generate_html_guide(base_url, files_to_deploy):
    """生成HTML部署指南"""
    
    html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>天南地北三线人 - 部署指南</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }}
        .container {{ max-width: 1200px; margin: 0 auto; }}
        .section {{ margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }}
        .template {{ background-color: #e8f5e8; }}
        .form {{ background-color: #e8f0ff; }}
        .category {{ background-color: #fff8e8; }}
        .item {{ margin-bottom: 15px; padding: 10px; background: white; border-radius: 4px; }}
        .url {{ color: #0066cc; text-decoration: none; word-break: break-all; }}
        .url:hover {{ text-decoration: underline; }}
        .filename {{ color: #666; font-style: italic; }}
        .step {{ font-weight: bold; color: #333; }}
        .instructions {{ background: #f0f8ff; padding: 15px; border-radius: 4px; margin-top: 20px; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 天南地北三线人 - 部署指南</h1>
        
        <div class="instructions">
            <h3>📋 部署步骤：</h3>
            <ol>
                <li>点击下面的链接打开MediaWiki编辑页面</li>
                <li>复制对应.wiki文件的全部内容</li>
                <li>粘贴到编辑框中</li>
                <li>在编辑摘要中写入描述（如：创建三线工厂模板）</li>
                <li>点击"保存页面"</li>
            </ol>
            <p><strong>💡 建议顺序：</strong>先部署Template，再部署Form，最后部署Category</p>
        </div>
        
        <div class="section template">
            <h2>📋 第一步：模板文件（8个）</h2>
"""
    
    # 添加模板文件
    template_files = {k: v for k, v in files_to_deploy.items() if k.startswith("Template:")}
    for i, (page_title, filename) in enumerate(template_files.items(), 1):
        url = f"{base_url}/index.php?title={page_title.replace(':', '%3A')}&action=edit"
        html_content += f"""
            <div class="item">
                <div class="step">{i}. {page_title}</div>
                <div><a href="{url}" target="_blank" class="url">{url}</a></div>
                <div class="filename">文件: {filename}</div>
            </div>
"""
    
    # 添加表单文件
    html_content += """
        </div>
        
        <div class="section form">
            <h2>📝 第二步：表单文件（6个）</h2>
"""
    
    form_files = {k: v for k, v in files_to_deploy.items() if k.startswith("Form:")}
    for i, (page_title, filename) in enumerate(form_files.items(), 1):
        url = f"{base_url}/index.php?title={page_title.replace(':', '%3A')}&action=edit"
        html_content += f"""
            <div class="item">
                <div class="step">{i}. {page_title}</div>
                <div><a href="{url}" target="_blank" class="url">{url}</a></div>
                <div class="filename">文件: {filename}</div>
            </div>
"""
    
    # 添加分类文件
    html_content += """
        </div>
        
        <div class="section category">
            <h2>📂 第三步：分类文件（6个）</h2>
"""
    
    category_files = {k: v for k, v in files_to_deploy.items() if k.startswith("Category:")}
    for i, (page_title, filename) in enumerate(category_files.items(), 1):
        url = f"{base_url}/index.php?title={page_title.replace(':', '%3A')}&action=edit"
        html_content += f"""
            <div class="item">
                <div class="step">{i}. {page_title}</div>
                <div><a href="{url}" target="_blank" class="url">{url}</a></div>
                <div class="filename">文件: {filename}</div>
            </div>
"""
    
    html_content += """
        </div>
        
        <div class="instructions">
            <h3>🎯 部署完成后：</h3>
            <ul>
                <li>测试表单：<a href="{}/index.php?title=Form:三线工厂" target="_blank">Form:三线工厂</a></li>
                <li>查看分类：<a href="{}/index.php?title=Category:三线工厂" target="_blank">Category:三线工厂</a></li>
                <li>现代化首页：<a href="{}/modern.php" target="_blank">现代化首页</a></li>
            </ul>
        </div>
    </div>
</body>
</html>
""".format(base_url, base_url, base_url)
    
    # 保存HTML文件
    with open("deployment_guide.html", "w", encoding="utf-8") as f:
        f.write(html_content)

if __name__ == "__main__":
    generate_deployment_urls()
