#!/bin/bash

# 天南地北三线人 - Wikipedia现代化首页安装脚本
# 作者: AI Assistant
# 日期: 2025-01-22

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_success() {
    print_message $GREEN "✅ $1"
}

print_warning() {
    print_message $YELLOW "⚠️  $1"
}

print_error() {
    print_message $RED "❌ $1"
}

print_info() {
    print_message $BLUE "ℹ️  $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        print_warning "检测到您正在以root用户运行此脚本"
        read -p "是否继续? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# 检查系统要求
check_requirements() {
    print_info "检查系统要求..."
    
    # 检查PHP
    if ! command -v php &> /dev/null; then
        print_error "PHP未安装，请先安装PHP 7.4或更高版本"
        exit 1
    fi
    
    local php_version=$(php -v | head -n1 | cut -d' ' -f2 | cut -d'.' -f1,2)
    print_success "PHP版本: $php_version"
    
    # 检查Web服务器
    if command -v apache2 &> /dev/null; then
        print_success "检测到Apache Web服务器"
        WEB_SERVER="apache"
    elif command -v nginx &> /dev/null; then
        print_success "检测到Nginx Web服务器"
        WEB_SERVER="nginx"
    else
        print_warning "未检测到Apache或Nginx，请确保已安装Web服务器"
    fi
    
    # 检查MySQL/MariaDB
    if command -v mysql &> /dev/null || command -v mariadb &> /dev/null; then
        print_success "检测到MySQL/MariaDB数据库"
    else
        print_warning "未检测到MySQL/MariaDB，请确保已安装数据库服务器"
    fi
}

# 获取安装路径
get_install_path() {
    print_info "配置安装路径..."
    
    # 默认路径
    if [[ "$WEB_SERVER" == "apache" ]]; then
        DEFAULT_PATH="/var/www/html"
    else
        DEFAULT_PATH="/usr/share/nginx/html"
    fi
    
    read -p "请输入MediaWiki安装路径 [$DEFAULT_PATH]: " INSTALL_PATH
    INSTALL_PATH=${INSTALL_PATH:-$DEFAULT_PATH}
    
    if [[ ! -d "$INSTALL_PATH" ]]; then
        print_error "路径 $INSTALL_PATH 不存在"
        exit 1
    fi
    
    WIKI_PATH="$INSTALL_PATH/wikipedia"
    print_success "安装路径: $WIKI_PATH"
}

# 创建目录结构
create_directories() {
    print_info "创建目录结构..."
    
    # 创建必要的目录
    mkdir -p "$WIKI_PATH/frontend/assets/css"
    mkdir -p "$WIKI_PATH/frontend/assets/js"
    mkdir -p "$WIKI_PATH/frontend/assets/images"
    mkdir -p "$WIKI_PATH/api"
    
    print_success "目录结构创建完成"
}

# 复制文件
copy_files() {
    print_info "复制项目文件..."
    
    # 复制前端文件
    cp -r frontend/* "$WIKI_PATH/frontend/"
    
    # 复制API文件
    cp api/sanxianren.php "$WIKI_PATH/api/"
    
    # 复制配置文件
    cp LocalSettings.custom.php "$WIKI_PATH/"
    
    # 复制文档
    cp SANXIANREN_README.md "$WIKI_PATH/"
    
    print_success "文件复制完成"
}

# 设置文件权限
set_permissions() {
    print_info "设置文件权限..."
    
    # 设置目录权限
    find "$WIKI_PATH" -type d -exec chmod 755 {} \;
    
    # 设置文件权限
    find "$WIKI_PATH" -type f -exec chmod 644 {} \;
    
    # API文件需要执行权限
    chmod 755 "$WIKI_PATH/api/sanxianren.php"
    
    # 如果是Apache，设置所有者
    if [[ "$WEB_SERVER" == "apache" ]]; then
        chown -R www-data:www-data "$WIKI_PATH" 2>/dev/null || true
    elif [[ "$WEB_SERVER" == "nginx" ]]; then
        chown -R nginx:nginx "$WIKI_PATH" 2>/dev/null || true
    fi
    
    print_success "文件权限设置完成"
}

# 配置Web服务器
configure_webserver() {
    print_info "配置Web服务器..."
    
    read -p "请输入您的域名 (例如: sanxianren.wiki): " DOMAIN_NAME
    
    if [[ "$WEB_SERVER" == "apache" ]]; then
        configure_apache
    elif [[ "$WEB_SERVER" == "nginx" ]]; then
        configure_nginx
    fi
}

# 配置Apache
configure_apache() {
    local config_file="/etc/apache2/sites-available/sanxianren.conf"
    
    cat > "$config_file" << EOF
<VirtualHost *:80>
    ServerName $DOMAIN_NAME
    DocumentRoot $WIKI_PATH
    
    # 重定向根目录到现代化首页
    RedirectMatch ^/$ /frontend/index.html
    
    # 启用重写模块
    RewriteEngine On
    
    # API重写规则
    RewriteRule ^api/v1/sanxianren/(.*)$ /api/sanxianren.php?action=\$1 [QSA,L]
    
    # 静态文件缓存
    <LocationMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg)$">
        ExpiresActive On
        ExpiresDefault "access plus 1 month"
    </LocationMatch>
    
    # 错误和访问日志
    ErrorLog \${APACHE_LOG_DIR}/sanxianren_error.log
    CustomLog \${APACHE_LOG_DIR}/sanxianren_access.log combined
</VirtualHost>
EOF
    
    # 启用站点
    a2ensite sanxianren.conf
    a2enmod rewrite
    a2enmod expires
    
    print_success "Apache配置完成"
    print_info "请运行 'systemctl reload apache2' 重新加载配置"
}

# 配置Nginx
configure_nginx() {
    local config_file="/etc/nginx/sites-available/sanxianren"
    
    cat > "$config_file" << EOF
server {
    listen 80;
    server_name $DOMAIN_NAME;
    root $WIKI_PATH;
    index index.php index.html;
    
    # 重定向根目录到现代化首页
    location = / {
        return 301 /frontend/index.html;
    }
    
    # 前端静态文件
    location /frontend/ {
        try_files \$uri \$uri/ =404;
        expires 1M;
        add_header Cache-Control "public, immutable";
    }
    
    # API接口
    location ~ ^/api/v1/sanxianren/(.*)$ {
        try_files \$uri /api/sanxianren.php?action=\$1;
    }
    
    # MediaWiki PHP处理
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME \$document_root\$fastcgi_script_name;
    }
    
    # 错误和访问日志
    error_log /var/log/nginx/sanxianren_error.log;
    access_log /var/log/nginx/sanxianren_access.log;
}
EOF
    
    # 启用站点
    ln -sf "$config_file" "/etc/nginx/sites-enabled/"
    
    print_success "Nginx配置完成"
    print_info "请运行 'systemctl reload nginx' 重新加载配置"
}

# 生成MediaWiki配置提示
generate_mediawiki_config() {
    print_info "生成MediaWiki配置提示..."
    
    cat << EOF

📋 MediaWiki配置步骤:

1. 在您的 LocalSettings.php 文件末尾添加以下内容:

   require_once '$WIKI_PATH/LocalSettings.custom.php';

2. 或者将 LocalSettings.custom.php 的内容直接复制到 LocalSettings.php 中

3. 重新启动Web服务器:
   - Apache: sudo systemctl reload apache2
   - Nginx: sudo systemctl reload nginx

4. 访问您的站点:
   - 现代化首页: http://$DOMAIN_NAME/frontend/index.html
   - 传统Wiki: http://$DOMAIN_NAME/index.php

EOF
}

# 运行安装后测试
run_tests() {
    print_info "运行安装后测试..."
    
    # 检查文件是否存在
    if [[ -f "$WIKI_PATH/frontend/index.html" ]]; then
        print_success "前端文件安装成功"
    else
        print_error "前端文件安装失败"
    fi
    
    if [[ -f "$WIKI_PATH/api/sanxianren.php" ]]; then
        print_success "API文件安装成功"
    else
        print_error "API文件安装失败"
    fi
    
    # 检查权限
    if [[ -r "$WIKI_PATH/frontend/index.html" ]]; then
        print_success "文件权限设置正确"
    else
        print_warning "文件权限可能有问题"
    fi
}

# 显示完成信息
show_completion() {
    print_success "🎉 天南地北三线人 Wikipedia现代化首页安装完成!"
    
    cat << EOF

🌟 安装摘要:
   - 安装路径: $WIKI_PATH
   - 域名: $DOMAIN_NAME
   - Web服务器: $WEB_SERVER

📖 下一步:
   1. 配置MediaWiki (参考上面的配置步骤)
   2. 重启Web服务器
   3. 访问您的现代化首页

📚 文档:
   - 详细文档: $WIKI_PATH/SANXIANREN_README.md
   - 配置文件: $WIKI_PATH/LocalSettings.custom.php

🔗 访问地址:
   - 现代化首页: http://$DOMAIN_NAME/frontend/index.html
   - API接口: http://$DOMAIN_NAME/api/sanxianren.php

如有问题，请查看文档或联系技术支持。

EOF
}

# 主函数
main() {
    print_info "开始安装天南地北三线人 Wikipedia现代化首页..."
    
    check_root
    check_requirements
    get_install_path
    create_directories
    copy_files
    set_permissions
    configure_webserver
    generate_mediawiki_config
    run_tests
    show_completion
}

# 运行主函数
main "$@"
