-- OAuth认证相关数据表
-- 创建时间：2025-01-22
-- 说明：用于gfast中台与MediaWiki OAuth认证的数据表结构

-- 1. OAuth用户映射表
CREATE TABLE `oauth_user_mapping` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `gfast_user_id` bigint(20) UNSIGNED NOT NULL COMMENT 'gfast用户ID',
  `mediawiki_user_id` int(11) NOT NULL COMMENT 'MediaWiki用户ID',
  `mediawiki_name` varchar(255) NOT NULL COMMENT 'MediaWiki用户名',
  `sync_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '同步状态：1-已同步，2-同步失败',
  `last_sync_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后同步时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_gfast_user_id` (`gfast_user_id`),
  UNIQUE KEY `uk_mediawiki_user_id` (`mediawiki_user_id`),
  KEY `idx_mediawiki_name` (`mediawiki_name`),
  KEY `idx_sync_status` (`sync_status`),
  KEY `idx_last_sync_at` (`last_sync_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='OAuth用户映射表';

-- 2. OAuth会话表（可选，如果需要持久化会话）
CREATE TABLE `oauth_sessions` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `session_id` varchar(64) NOT NULL COMMENT '会话ID',
  `user_id` bigint(20) UNSIGNED NOT NULL COMMENT '用户ID',
  `access_token` text NOT NULL COMMENT '访问令牌',
  `refresh_token` text COMMENT '刷新令牌',
  `token_type` varchar(32) NOT NULL DEFAULT 'Bearer' COMMENT '令牌类型',
  `expires_in` int(11) NOT NULL COMMENT '过期时间(秒)',
  `scope` varchar(255) COMMENT '作用域',
  `state` varchar(255) COMMENT '状态参数',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `expires_at` datetime NOT NULL COMMENT '过期时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_session_id` (`session_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='OAuth会话表';

-- 3. OAuth应用配置表（可选，如果需要支持多个OAuth应用）
CREATE TABLE `oauth_applications` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '应用名称',
  `client_id` varchar(255) NOT NULL COMMENT 'OAuth客户端ID',
  `client_secret` varchar(255) NOT NULL COMMENT 'OAuth客户端密钥',
  `authorization_url` varchar(500) NOT NULL COMMENT '授权URL',
  `token_url` varchar(500) NOT NULL COMMENT '令牌URL',
  `user_info_url` varchar(500) NOT NULL COMMENT '用户信息URL',
  `redirect_uri` varchar(500) NOT NULL COMMENT '回调URL',
  `scopes` text COMMENT 'OAuth作用域（JSON格式）',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_client_id` (`client_id`),
  KEY `idx_name` (`name`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='OAuth应用配置表';

-- 4. OAuth授权日志表
CREATE TABLE `oauth_auth_logs` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) UNSIGNED COMMENT '用户ID',
  `session_id` varchar(64) COMMENT '会话ID',
  `action` varchar(50) NOT NULL COMMENT '操作类型：authorize,callback,refresh,revoke',
  `status` varchar(20) NOT NULL COMMENT '状态：success,failed,error',
  `error_code` varchar(50) COMMENT '错误代码',
  `error_message` text COMMENT '错误消息',
  `ip_address` varchar(45) COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理',
  `request_data` text COMMENT '请求数据（JSON格式）',
  `response_data` text COMMENT '响应数据（JSON格式）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_action` (`action`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='OAuth授权日志表';

-- 插入默认的MediaWiki OAuth应用配置
INSERT INTO `oauth_applications` (
  `name`, 
  `client_id`, 
  `client_secret`, 
  `authorization_url`, 
  `token_url`, 
  `user_info_url`, 
  `redirect_uri`, 
  `scopes`, 
  `status`
) VALUES (
  'MediaWiki三线人百科',
  'your_mediawiki_oauth_client_id',
  'your_mediawiki_oauth_client_secret',
  'https://www.sanxianren.com/wiki/Special:OAuth/authorize',
  'https://www.sanxianren.com/wiki/Special:OAuth/token',
  'https://www.sanxianren.com/w/api.php',
  'http://localhost:8808/api/v1/sanxianren/oauth/callback',
  '["basic", "editpage", "createpage", "uploadfile"]',
  1
);

-- 创建索引优化查询性能
-- oauth_user_mapping表的复合索引
CREATE INDEX `idx_gfast_mediawiki` ON `oauth_user_mapping` (`gfast_user_id`, `mediawiki_user_id`);
CREATE INDEX `idx_sync_status_time` ON `oauth_user_mapping` (`sync_status`, `last_sync_at`);

-- oauth_sessions表的复合索引
CREATE INDEX `idx_user_expires` ON `oauth_sessions` (`user_id`, `expires_at`);

-- oauth_auth_logs表的复合索引
CREATE INDEX `idx_user_action_time` ON `oauth_auth_logs` (`user_id`, `action`, `created_at`);
CREATE INDEX `idx_status_time` ON `oauth_auth_logs` (`status`, `created_at`);

-- 添加外键约束（可选，根据实际需求决定是否启用）
-- ALTER TABLE `oauth_user_mapping` ADD CONSTRAINT `fk_oauth_mapping_user` 
--   FOREIGN KEY (`gfast_user_id`) REFERENCES `sx_users` (`id`) ON DELETE CASCADE;

-- ALTER TABLE `oauth_sessions` ADD CONSTRAINT `fk_oauth_session_user` 
--   FOREIGN KEY (`user_id`) REFERENCES `sx_users` (`id`) ON DELETE CASCADE;

-- ALTER TABLE `oauth_auth_logs` ADD CONSTRAINT `fk_oauth_log_user` 
--   FOREIGN KEY (`user_id`) REFERENCES `sx_users` (`id`) ON DELETE SET NULL;

-- 创建视图，方便查询用户OAuth状态
CREATE VIEW `v_user_oauth_status` AS
SELECT
  u.id AS user_id,
  u.username,
  u.email,
  u.nickname,
  m.mediawiki_user_id,
  m.mediawiki_name,
  m.sync_status,
  m.last_sync_at,
  CASE
    WHEN m.id IS NOT NULL THEN 1
    ELSE 0
  END AS is_oauth_linked,
  CASE
    WHEN s.id IS NOT NULL AND s.expires_at > NOW() THEN 1
    ELSE 0
  END AS has_active_session
FROM sx_users u
LEFT JOIN oauth_user_mapping m ON u.id = m.gfast_user_id
LEFT JOIN oauth_sessions s ON u.id = s.user_id AND s.expires_at > NOW();

-- 创建存储过程，清理过期的OAuth会话
DELIMITER //
CREATE PROCEDURE `CleanExpiredOAuthSessions`()
BEGIN
  DECLARE done INT DEFAULT FALSE;
  DECLARE session_count INT DEFAULT 0;
  
  -- 删除过期的会话记录
  DELETE FROM oauth_sessions WHERE expires_at < NOW();
  
  -- 获取删除的记录数
  SET session_count = ROW_COUNT();
  
  -- 记录清理日志
  INSERT INTO oauth_auth_logs (
    action, 
    status, 
    error_message, 
    created_at
  ) VALUES (
    'cleanup', 
    'success', 
    CONCAT('清理了 ', session_count, ' 个过期会话'), 
    NOW()
  );
  
END //
DELIMITER ;

-- 创建定时任务清理过期会话（需要MySQL事件调度器支持）
-- SET GLOBAL event_scheduler = ON;
-- CREATE EVENT IF NOT EXISTS `evt_cleanup_oauth_sessions`
-- ON SCHEDULE EVERY 1 HOUR
-- DO
--   CALL CleanExpiredOAuthSessions();

-- 创建触发器，记录用户映射变更日志
DELIMITER //
CREATE TRIGGER `tr_oauth_mapping_log` 
AFTER UPDATE ON `oauth_user_mapping`
FOR EACH ROW
BEGIN
  IF OLD.sync_status != NEW.sync_status THEN
    INSERT INTO oauth_auth_logs (
      user_id,
      action,
      status,
      error_message,
      created_at
    ) VALUES (
      NEW.gfast_user_id,
      'sync_status_change',
      CASE NEW.sync_status WHEN 1 THEN 'success' ELSE 'failed' END,
      CONCAT('同步状态从 ', OLD.sync_status, ' 变更为 ', NEW.sync_status),
      NOW()
    );
  END IF;
END //
DELIMITER ;
