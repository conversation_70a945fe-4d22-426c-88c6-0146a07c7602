2025-09-06T00:00:05.282+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 15 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T00:00:05.303+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T00:00:05.307+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T00:00:05.308+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-06T00:00:05.364+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 54 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 00:00:05','在线用户定时更新，执行成功') 
2025-09-06T00:10:05.384+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 17 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T00:10:05.385+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T00:10:05.390+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T00:10:05.391+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-06T00:10:05.416+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 24 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 00:10:05','在线用户定时更新，执行成功') 
2025-09-06T00:20:05.881+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 14 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T00:20:05.883+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T00:20:05.886+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T00:20:05.887+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-06T00:20:05.972+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 84 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 00:20:05','在线用户定时更新，执行成功') 
2025-09-06T00:30:05.982+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 15 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T00:30:05.987+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T00:30:05.991+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T00:30:05.992+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-06T00:30:06.010+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 17 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 00:30:05','在线用户定时更新，执行成功') 
2025-09-06T00:40:05.983+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 16 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T00:40:05.985+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T00:40:05.989+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T00:40:05.989+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-06T00:40:06.024+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 34 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 00:40:05','在线用户定时更新，执行成功') 
2025-09-06T00:50:05.685+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 18 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T00:50:05.687+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T00:50:05.691+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T00:50:05.692+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-06T00:50:05.764+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 71 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 00:50:05','在线用户定时更新，执行成功') 
2025-09-06T01:00:05.677+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  9 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T01:00:05.679+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T01:00:05.682+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T01:00:05.682+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-06T01:00:05.714+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 31 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 01:00:05','在线用户定时更新，执行成功') 
2025-09-06T01:10:05.979+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 12 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T01:10:05.981+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T01:10:05.985+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T01:10:05.986+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-06T01:10:06.022+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 36 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 01:10:05','在线用户定时更新，执行成功') 
2025-09-06T01:50:05.524+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [197 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T01:50:05.573+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 16 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T01:50:05.635+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T01:50:05.640+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-06T01:50:05.771+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [126 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 01:50:05','在线用户定时更新，执行成功') 
2025-09-06T02:00:05.426+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 46 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T02:00:05.432+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T02:00:05.437+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T02:00:05.440+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  2 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-06T02:00:05.554+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [113 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 02:00:05','在线用户定时更新，执行成功') 
2025-09-06T02:04:16.549+08:00 [DEBU] {9834e510027562183fd1b849a1874a04} [146 ms] [default] [sanxian1] [rows:80 ] SELECT `id`,`pid`,`name`,`title`,`icon`,`condition`,`remark`,`menu_type`,`weigh`,`is_hide`,`is_cached`,`is_affix`,`path`,`redirect`,`component`,`is_iframe`,`is_link`,`link_url` FROM `sys_auth_rule` ORDER BY `weigh` desc,`id` asc
2025-09-06T02:04:16.658+08:00 [DEBU] {e451971d0275621842d1b849f1156af4} [ 78 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/menu/list','GET',1,'demo','财务部门','/api/v1/system/menu/list?title=&component=','::1','内网IP','{"component":"","title":""}','2025-09-06 02:04:16') 
2025-09-06T02:04:16.664+08:00 [DEBU] {d440a2150275621841d1b849fa7c4c9c} [184 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/getDictData','GET',1,'demo','财务部门','/api/v1/system/dict/data/getDictData?dictType=sys_show_hide&defaultValue=','::1','内网IP','{"defaultValue":"","dictType":"sys_show_hide"}','2025-09-06 02:04:16') 
2025-09-06T02:04:46.667+08:00 [DEBU] {cc972c1c097562184bd1b84978d02f80} [ 45 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`user_name`,`mobile`,`user_nickname`,`user_password`,`user_salt`,`user_status`,`is_admin`,`avatar`,`dept_id` FROM `sys_user` WHERE (`user_name`='demo') AND `deleted_at` IS NULL LIMIT 1
2025-09-06T02:04:46.770+08:00 [DEBU] {cc972c1c097562184bd1b84978d02f80} [ 96 ms] [default] [sanxian1] [rows:1  ] UPDATE `sys_user` SET `last_login_ip`='::1',`last_login_time`='2025-09-06 02:04:46' WHERE `id`=31
2025-09-06T02:04:46.825+08:00 [DEBU] {c0d52b27097562184dd1b849e7e57944} [ 22 ms] [default] [sanxian1] [rows:0  ] SELECT `id` FROM `sys_user_online` WHERE `token`='7ZUSfVIf2HyYjcv86SKPPs29v003ECPEScsdYsYYqO0UZxl82Rru4U8rKfP4TZcLHGWZOl69kvQO9jYvmzhHa0mLD5pBkXi2PvDmwW4v+2xp7IORyAwxv8mTDBqISqKVWQTZ2dY5bL/8twFOp4Il6g==' LIMIT 1
2025-09-06T02:04:46.840+08:00 [DEBU] {c0d52b27097562184dd1b849e7e57944} [ 13 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_user_online`(`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os`) VALUES('b56ec4732bd74a02e0cf9f3d241e2051','7ZUSfVIf2HyYjcv86SKPPs29v003ECPEScsdYsYYqO0UZxl82Rru4U8rKfP4TZcLHGWZOl69kvQO9jYvmzhHa0mLD5pBkXi2PvDmwW4v+2xp7IORyAwxv8mTDBqISqKVWQTZ2dY5bL/8twFOp4Il6g==','2025-09-06 02:04:46','demo','::1','Edge','Windows 10') 
2025-09-06T02:04:46.847+08:00 [DEBU] {68f67825097562184cd1b849383caa6d} [ 51 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_login_log`(`login_name`,`ipaddr`,`login_location`,`browser`,`os`,`status`,`msg`,`login_time`,`module`) VALUES('demo','::1','内网IP','Edge','Windows 10',1,'登录成功','2025-09-06 02:04:46','系统后台') 
2025-09-06T02:10:05.612+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [193 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T02:10:05.656+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 24 ms] [default] [sanxian1] [rows:2  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T02:10:05.730+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T02:10:05.732+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-06T02:10:05.884+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [138 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 02:10:05','在线用户定时更新，执行成功') 
2025-09-06T02:20:06.370+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [330 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T02:20:06.437+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 31 ms] [default] [sanxian1] [rows:2  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T02:20:06.505+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T02:20:06.512+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  6 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-06T02:20:06.669+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [139 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 02:20:06','在线用户定时更新，执行成功') 
2025-09-06T02:30:05.295+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [168 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T02:30:05.338+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 15 ms] [default] [sanxian1] [rows:2  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T02:30:05.379+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T02:30:05.381+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-06T02:30:05.486+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [100 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 02:30:05','在线用户定时更新，执行成功') 
2025-09-06T02:40:05.237+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 61 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T02:40:05.251+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  4 ms] [default] [sanxian1] [rows:2  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T02:40:05.278+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T02:40:05.280+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  2 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-06T02:40:05.370+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 89 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 02:40:05','在线用户定时更新，执行成功') 
2025-09-06T02:50:05.481+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 14 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T02:50:05.483+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:2  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T02:50:05.486+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T02:50:05.496+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 10 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-06T02:50:05.587+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 90 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 02:50:05','在线用户定时更新，执行成功') 
2025-09-06T03:00:05.577+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  4 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T03:00:05.580+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:2  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T03:00:05.584+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T03:00:05.585+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-06T03:00:05.675+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 89 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 03:00:05','在线用户定时更新，执行成功') 
2025-09-06T03:10:05.490+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 23 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T03:10:05.498+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  2 ms] [default] [sanxian1] [rows:2  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T03:10:05.502+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T03:10:05.504+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-06T03:10:05.544+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 39 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 03:10:05','在线用户定时更新，执行成功') 
2025-09-06T03:20:05.081+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 12 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T03:20:05.083+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:2  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T03:20:05.086+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T03:20:05.087+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-06T03:20:05.173+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 85 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 03:20:05','在线用户定时更新，执行成功') 
2025-09-06T09:50:05.591+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 24 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T09:50:05.596+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  2 ms] [default] [sanxian1] [rows:2  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T09:50:05.734+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [118 ms] [default] [sanxian1] [rows:1  ] DELETE FROM `sys_user_online` WHERE `token`='7ZUSfVIf2HyYjcv86SKPPs29v003ECPEScsdYsYYqO0UZxl82Rru4U8rKfP4TZcLHGWZOl69kvQO9jYvmzhHa0mLD5pBkXi2PvDmwW4v+2xp7IORyAwxv8mTDBqISqKVWQTZ2dY5bL/8twFOp4Il6g=='
2025-09-06T09:50:05.775+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 26 ms] [default] [sanxian1] [rows:1  ] DELETE FROM `sys_user_online` WHERE `token`='7ZUSfVIf2HyYjcv86SKPPs29v003ECPEScsdYsYYqO0UZxl82Rru4U8rKfP4TZcL97Q8WCgB+icNWiGQhBZootw+ZSCgen6z2Hfvi//ounRCGGyGl7NkcSL/i+6ulyuBUtzMXnYo2mzjBxTbEkaXww=='
2025-09-06T09:50:05.777+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T09:50:05.788+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 10 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-06T09:50:05.819+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 29 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 09:50:05','在线用户定时更新，执行成功') 
2025-09-06T10:00:06.154+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [244 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T10:00:06.193+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 19 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T10:00:06.287+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 82 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 10:00:06','在线用户定时更新，执行成功') 
2025-09-06T10:10:06.016+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [207 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T10:10:06.045+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 15 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T10:10:06.122+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 64 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 10:10:06','在线用户定时更新，执行成功') 
2025-09-06T10:20:05.400+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 22 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T10:20:05.404+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  2 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T10:20:05.500+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 95 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 10:20:05','在线用户定时更新，执行成功') 
2025-09-06T10:30:05.192+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 25 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T10:30:05.194+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T10:30:05.229+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 33 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 10:30:05','在线用户定时更新，执行成功') 
2025-09-06T10:40:05.224+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 51 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T10:40:05.233+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  4 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T10:40:05.255+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 20 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 10:40:05','在线用户定时更新，执行成功') 
2025-09-06T10:50:05.192+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 22 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T10:50:05.197+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  3 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T10:50:05.277+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 77 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 10:50:05','在线用户定时更新，执行成功') 
2025-09-06T11:00:05.282+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 15 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T11:00:05.285+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T11:00:05.337+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 51 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 11:00:05','在线用户定时更新，执行成功') 
2025-09-06T11:10:05.381+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 14 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T11:10:05.383+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T11:10:05.402+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 18 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 11:10:05','在线用户定时更新，执行成功') 
2025-09-06T11:20:05.389+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 13 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T11:20:05.390+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T11:20:05.413+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 21 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 11:20:05','在线用户定时更新，执行成功') 
2025-09-06T11:30:05.372+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  5 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T11:30:05.375+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  2 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T11:30:05.475+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [100 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 11:30:05','在线用户定时更新，执行成功') 
2025-09-06T11:40:05.582+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  9 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T11:40:05.585+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T11:40:05.669+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 83 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 11:40:05','在线用户定时更新，执行成功') 
2025-09-06T11:50:05.279+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 10 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T11:50:05.281+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T11:50:05.311+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 29 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 11:50:05','在线用户定时更新，执行成功') 
2025-09-06T12:00:05.770+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  3 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T12:00:05.772+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T12:00:05.809+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 35 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 12:00:05','在线用户定时更新，执行成功') 
2025-09-06T12:10:05.283+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 16 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T12:10:05.286+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T12:10:05.374+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 87 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 12:10:05','在线用户定时更新，执行成功') 
2025-09-06T12:20:05.077+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 10 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T12:20:05.079+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T12:20:05.148+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 68 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 12:20:05','在线用户定时更新，执行成功') 
2025-09-06T12:30:05.282+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 15 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T12:30:05.283+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T12:30:05.360+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 76 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 12:30:05','在线用户定时更新，执行成功') 
2025-09-06T12:40:05.780+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [181 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T12:40:05.813+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 11 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T12:40:05.886+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 61 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 12:40:05','在线用户定时更新，执行成功') 
2025-09-06T12:50:05.371+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  4 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T12:50:05.372+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T12:50:05.391+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 18 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 12:50:05','在线用户定时更新，执行成功') 
2025-09-06T13:00:05.670+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  3 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T13:00:05.672+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T13:00:05.757+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 84 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 13:00:05','在线用户定时更新，执行成功') 
2025-09-06T13:10:05.672+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  5 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T13:10:05.674+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T13:10:05.749+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 73 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 13:10:05','在线用户定时更新，执行成功') 
2025-09-06T13:20:05.175+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  8 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T13:20:05.178+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T13:20:05.261+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 81 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 13:20:05','在线用户定时更新，执行成功') 
2025-09-06T15:30:06.134+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  9 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T15:30:06.144+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T15:30:06.259+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [111 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 15:30:06','在线用户定时更新，执行成功') 
2025-09-06T15:40:05.773+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  6 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T15:40:05.775+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T15:40:05.842+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 64 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 15:40:05','在线用户定时更新，执行成功') 
2025-09-06T15:50:05.174+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  7 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T15:50:05.175+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T15:50:05.253+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 76 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 15:50:05','在线用户定时更新，执行成功') 
2025-09-06T15:52:53.047+08:00 [DEBU] {648560a039a2621808d2b849284faa7b} [ 14 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`user_name`,`mobile`,`user_nickname`,`user_password`,`user_salt`,`user_status`,`is_admin`,`avatar`,`dept_id` FROM `sys_user` WHERE (`user_name`='demo') AND `deleted_at` IS NULL LIMIT 1
2025-09-06T15:52:53.110+08:00 [DEBU] {648560a039a2621808d2b849284faa7b} [ 59 ms] [default] [sanxian1] [rows:1  ] UPDATE `sys_user` SET `last_login_ip`='::1',`last_login_time`='2025-09-06 15:52:53' WHERE `id`=31
2025-09-06T15:52:53.136+08:00 [DEBU] {4c5e26a639a2621809d2b849a267458b} [ 11 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_login_log`(`login_name`,`ipaddr`,`login_location`,`browser`,`os`,`status`,`msg`,`login_time`,`module`) VALUES('demo','::1','内网IP','Edge','Windows 10',1,'登录成功','2025-09-06 15:52:53','系统后台') 
2025-09-06T15:52:53.150+08:00 [DEBU] {4cf0f1a739a262180ad2b849a6fdb3ee} [  5 ms] [default] [sanxian1] [rows:0  ] SELECT `id` FROM `sys_user_online` WHERE `token`='7ZUSfVIf2HyYjcv86SKPPs29v003ECPEScsdYsYYqO0UZxl82Rru4U8rKfP4TZcLAfLggnr3heUdvvR/Hv9zQaWNKA4iwDOcYt/a1E2eGqRNPACmVCqQ3Rd5OOE+qK9PT4LriskdgpMm1sRhQxc64Q==' LIMIT 1
2025-09-06T15:52:53.157+08:00 [DEBU] {4cf0f1a739a262180ad2b849a6fdb3ee} [  4 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_user_online`(`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os`) VALUES('4d6a01e32ee3507169c5feb3026fadae','7ZUSfVIf2HyYjcv86SKPPs29v003ECPEScsdYsYYqO0UZxl82Rru4U8rKfP4TZcLAfLggnr3heUdvvR/Hv9zQaWNKA4iwDOcYt/a1E2eGqRNPACmVCqQ3Rd5OOE+qK9PT4LriskdgpMm1sRhQxc64Q==','2025-09-06 15:52:53','demo','::1','Edge','Windows 10') 
2025-09-06T16:00:06.056+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [188 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T16:00:06.078+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 19 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T16:00:06.082+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T16:00:06.084+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-06T16:00:06.132+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 46 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 16:00:06','在线用户定时更新，执行成功') 
2025-09-06T16:10:05.676+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  9 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T16:10:05.683+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T16:10:05.687+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T16:10:05.688+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-06T16:10:05.777+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 88 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 16:10:05','在线用户定时更新，执行成功') 
2025-09-06T16:20:05.985+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 18 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T16:20:05.990+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  4 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T16:20:05.993+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T16:20:05.995+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  2 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-06T16:20:06.060+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 64 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 16:20:05','在线用户定时更新，执行成功') 
2025-09-06T16:30:05.786+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 19 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T16:30:05.790+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  3 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T16:30:05.794+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T16:30:05.795+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-06T16:30:05.878+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 82 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 16:30:05','在线用户定时更新，执行成功') 
2025-09-06T16:40:05.185+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 18 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T16:40:05.187+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T16:40:05.192+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T16:40:05.195+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  3 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-06T16:40:05.272+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 76 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 16:40:05','在线用户定时更新，执行成功') 
2025-09-06T16:50:05.385+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 18 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T16:50:05.390+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T16:50:05.394+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T16:50:05.397+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  3 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-06T16:50:05.506+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [109 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 16:50:05','在线用户定时更新，执行成功') 
2025-09-06T16:51:54.875+08:00 [DEBU] {542f663d72a562181c7af12825f75479} [171 ms] [default] [sanxian1] [rows:111] SHOW TABLES
2025-09-06T16:51:54.953+08:00 [DEBU] {542f663d72a562181c7af12825f75479} [ 75 ms] [default] [sanxian1] [rows:14 ] SHOW FULL COLUMNS FROM `sys_job`
2025-09-06T16:51:54.969+08:00 [DEBU] {542f663d72a562181c7af12825f75479} [ 13 ms] [default] [sanxian1] [rows:1  ] SELECT `job_id`,`job_name`,`job_params`,`job_group`,`invoke_target`,`cron_expression`,`misfire_policy`,`concurrent`,`status`,`created_by`,`updated_by`,`remark`,`created_at`,`updated_at` FROM `sys_job` WHERE `status`=0
2025-09-06T16:51:54.975+08:00 [DEBU] {542f663d72a562181c7af12825f75479} [  5 ms] [default] [sanxian1] [rows:0  ] UPDATE `sys_job` SET `status`=0 WHERE `job_id`=8
2025-09-06T16:51:54.987+08:00 [DEBU] {7c08d24d72a562181e7af1282f596761} [  9 ms] [default] [sanxian1] [rows:5  ] SHOW FULL COLUMNS FROM `plugin_sms_config`
2025-09-06T16:51:55.002+08:00 [DEBU] {7c08d24d72a562181e7af1282f596761} [  9 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`sms_type`,`remark`,`status`,`config` FROM `plugin_sms_config` WHERE `status`=1 LIMIT 1
2025-09-06T16:51:55.017+08:00 [DEBU] {8050f74f72a562181f7af128a90c3efe} [  3 ms] [default] [sanxian1] [rows:1  ] SELECT * FROM `plugin_sms_config` WHERE `sms_type`='yunxin' LIMIT 1
2025-09-06T16:53:58.438+08:00 [DEBU] {88fd70098fa562185343587d748cd81d} [ 52 ms] [default] [sanxian1] [rows:111] SHOW TABLES
2025-09-06T16:53:58.451+08:00 [DEBU] {88fd70098fa562185343587d748cd81d} [ 11 ms] [default] [sanxian1] [rows:14 ] SHOW FULL COLUMNS FROM `sys_job`
2025-09-06T16:53:58.464+08:00 [DEBU] {88fd70098fa562185343587d748cd81d} [ 10 ms] [default] [sanxian1] [rows:1  ] SELECT `job_id`,`job_name`,`job_params`,`job_group`,`invoke_target`,`cron_expression`,`misfire_policy`,`concurrent`,`status`,`created_by`,`updated_by`,`remark`,`created_at`,`updated_at` FROM `sys_job` WHERE `status`=0
2025-09-06T16:53:58.468+08:00 [DEBU] {88fd70098fa562185343587d748cd81d} [  1 ms] [default] [sanxian1] [rows:0  ] UPDATE `sys_job` SET `status`=0 WHERE `job_id`=8
2025-09-06T16:53:58.473+08:00 [DEBU] {00c0720e8fa562185543587d63a446ec} [  4 ms] [default] [sanxian1] [rows:5  ] SHOW FULL COLUMNS FROM `plugin_sms_config`
2025-09-06T16:53:58.484+08:00 [DEBU] {00c0720e8fa562185543587d63a446ec} [ 10 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`sms_type`,`remark`,`status`,`config` FROM `plugin_sms_config` WHERE `status`=1 LIMIT 1
2025-09-06T16:53:58.487+08:00 [DEBU] {1cca7d0f8fa562185643587d173b20ad} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT * FROM `plugin_sms_config` WHERE `sms_type`='yunxin' LIMIT 1
2025-09-06T16:56:29.750+08:00 [DEBU] {a48a2245b2a562188b734f78ad9a31d0} [ 39 ms] [default] [sanxian1] [rows:111] SHOW TABLES
2025-09-06T16:56:29.760+08:00 [DEBU] {a48a2245b2a562188b734f78ad9a31d0} [  7 ms] [default] [sanxian1] [rows:14 ] SHOW FULL COLUMNS FROM `sys_job`
2025-09-06T16:56:29.768+08:00 [DEBU] {a48a2245b2a562188b734f78ad9a31d0} [  5 ms] [default] [sanxian1] [rows:1  ] SELECT `job_id`,`job_name`,`job_params`,`job_group`,`invoke_target`,`cron_expression`,`misfire_policy`,`concurrent`,`status`,`created_by`,`updated_by`,`remark`,`created_at`,`updated_at` FROM `sys_job` WHERE `status`=0
2025-09-06T16:56:29.771+08:00 [DEBU] {a48a2245b2a562188b734f78ad9a31d0} [  1 ms] [default] [sanxian1] [rows:0  ] UPDATE `sys_job` SET `status`=0 WHERE `job_id`=8
2025-09-06T16:56:29.777+08:00 [DEBU] {64c9d948b2a562188d734f7865dc269e} [  5 ms] [default] [sanxian1] [rows:5  ] SHOW FULL COLUMNS FROM `plugin_sms_config`
2025-09-06T16:56:29.780+08:00 [DEBU] {64c9d948b2a562188d734f7865dc269e} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`sms_type`,`remark`,`status`,`config` FROM `plugin_sms_config` WHERE `status`=1 LIMIT 1
2025-09-06T16:56:29.783+08:00 [DEBU] {0ccc6a49b2a562188e734f785ae6329d} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT * FROM `plugin_sms_config` WHERE `sms_type`='yunxin' LIMIT 1
2025-09-06T16:58:06.374+08:00 [DEBU] {b4752ec3c8a56218c56ffa17dcaf8d6d} [ 59 ms] [default] [sanxian1] [rows:111] SHOW TABLES
2025-09-06T16:58:06.396+08:00 [DEBU] {b4752ec3c8a56218c56ffa17dcaf8d6d} [ 20 ms] [default] [sanxian1] [rows:14 ] SHOW FULL COLUMNS FROM `sys_job`
2025-09-06T16:58:06.404+08:00 [DEBU] {b4752ec3c8a56218c56ffa17dcaf8d6d} [  6 ms] [default] [sanxian1] [rows:1  ] SELECT `job_id`,`job_name`,`job_params`,`job_group`,`invoke_target`,`cron_expression`,`misfire_policy`,`concurrent`,`status`,`created_by`,`updated_by`,`remark`,`created_at`,`updated_at` FROM `sys_job` WHERE `status`=0
2025-09-06T16:58:06.406+08:00 [DEBU] {b4752ec3c8a56218c56ffa17dcaf8d6d} [  1 ms] [default] [sanxian1] [rows:0  ] UPDATE `sys_job` SET `status`=0 WHERE `job_id`=8
2025-09-06T16:58:06.413+08:00 [DEBU] {3c88bbc8c8a56218c76ffa178e1fbea0} [  5 ms] [default] [sanxian1] [rows:5  ] SHOW FULL COLUMNS FROM `plugin_sms_config`
2025-09-06T16:58:06.416+08:00 [DEBU] {3c88bbc8c8a56218c76ffa178e1fbea0} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`sms_type`,`remark`,`status`,`config` FROM `plugin_sms_config` WHERE `status`=1 LIMIT 1
2025-09-06T16:58:06.418+08:00 [DEBU] {b05a5bc9c8a56218c86ffa17dfb1327c} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT * FROM `plugin_sms_config` WHERE `sms_type`='yunxin' LIMIT 1
2025-09-06T16:59:01.866+08:00 [DEBU] {485562afd5a562182bdd9a685412f5b1} [ 49 ms] [default] [sanxian1] [rows:111] SHOW TABLES
2025-09-06T16:59:01.921+08:00 [DEBU] {485562afd5a562182bdd9a685412f5b1} [ 53 ms] [default] [sanxian1] [rows:14 ] SHOW FULL COLUMNS FROM `sys_job`
2025-09-06T16:59:01.992+08:00 [DEBU] {485562afd5a562182bdd9a685412f5b1} [  6 ms] [default] [sanxian1] [rows:1  ] SELECT `job_id`,`job_name`,`job_params`,`job_group`,`invoke_target`,`cron_expression`,`misfire_policy`,`concurrent`,`status`,`created_by`,`updated_by`,`remark`,`created_at`,`updated_at` FROM `sys_job` WHERE `status`=0
2025-09-06T16:59:01.997+08:00 [DEBU] {485562afd5a562182bdd9a685412f5b1} [  3 ms] [default] [sanxian1] [rows:0  ] UPDATE `sys_job` SET `status`=0 WHERE `job_id`=8
2025-09-06T16:59:02.009+08:00 [DEBU] {dc8e43bad5a562182ddd9a68aa155914} [  9 ms] [default] [sanxian1] [rows:5  ] SHOW FULL COLUMNS FROM `plugin_sms_config`
2025-09-06T16:59:02.031+08:00 [DEBU] {dc8e43bad5a562182ddd9a68aa155914} [  6 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`sms_type`,`remark`,`status`,`config` FROM `plugin_sms_config` WHERE `status`=1 LIMIT 1
2025-09-06T16:59:02.041+08:00 [DEBU] {682fb5bcd5a562182edd9a68c9a134ce} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT * FROM `plugin_sms_config` WHERE `sms_type`='yunxin' LIMIT 1
2025-09-06T17:00:23.146+08:00 [DEBU] {d49f419be8a56218a7392d7fe58bc255} [ 61 ms] [default] [sanxian1] [rows:111] SHOW TABLES
2025-09-06T17:00:23.163+08:00 [DEBU] {d49f419be8a56218a7392d7fe58bc255} [ 14 ms] [default] [sanxian1] [rows:14 ] SHOW FULL COLUMNS FROM `sys_job`
2025-09-06T17:00:23.184+08:00 [DEBU] {d49f419be8a56218a7392d7fe58bc255} [ 19 ms] [default] [sanxian1] [rows:1  ] SELECT `job_id`,`job_name`,`job_params`,`job_group`,`invoke_target`,`cron_expression`,`misfire_policy`,`concurrent`,`status`,`created_by`,`updated_by`,`remark`,`created_at`,`updated_at` FROM `sys_job` WHERE `status`=0
2025-09-06T17:00:23.186+08:00 [DEBU] {d49f419be8a56218a7392d7fe58bc255} [  0 ms] [default] [sanxian1] [rows:0  ] UPDATE `sys_job` SET `status`=0 WHERE `job_id`=8
2025-09-06T17:00:23.191+08:00 [DEBU] {74377ca1e8a56218a9392d7f59fb5097} [  3 ms] [default] [sanxian1] [rows:5  ] SHOW FULL COLUMNS FROM `plugin_sms_config`
2025-09-06T17:00:23.195+08:00 [DEBU] {74377ca1e8a56218a9392d7f59fb5097} [  3 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`sms_type`,`remark`,`status`,`config` FROM `plugin_sms_config` WHERE `status`=1 LIMIT 1
2025-09-06T17:00:23.197+08:00 [DEBU] {9cf1fda1e8a56218aa392d7f62900bf5} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT * FROM `plugin_sms_config` WHERE `sms_type`='yunxin' LIMIT 1
2025-09-06T17:02:03.930+08:00 [DEBU] {e492dc0d00a66218ae392d7f9dcac108} [135 ms] [default] [sanxian1] [rows:14 ] SHOW FULL COLUMNS FROM `sys_oper_log`
2025-09-06T17:02:04.063+08:00 [DEBU] {680efb0d00a66218af392d7fa1fc5530} [130 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/sanxianren/oauth/status','GET',1,'demo','财务部门','/api/v1/sanxianren/oauth/status','::1','内网IP','{}','2025-09-06 17:02:03') 
2025-09-06T17:02:04.090+08:00 [DEBU] {e492dc0d00a66218ae392d7f9dcac108} [157 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/sanxianren/oauth/status','GET',1,'demo','财务部门','/api/v1/sanxianren/oauth/status','::1','内网IP','{}','2025-09-06 17:02:03') 
2025-09-06T17:02:29.778+08:00 [DEBU] {b8deef1506a66218b3392d7f7d1212fb} [ 21 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/sanxianren/oauth/status','GET',1,'demo','财务部门','/api/v1/sanxianren/oauth/status','::1','内网IP','{}','2025-09-06 17:02:29') 
2025-09-06T17:02:29.790+08:00 [DEBU] {e400481506a66218b2392d7fba1567ff} [ 45 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/sanxianren/oauth/status','GET',1,'demo','财务部门','/api/v1/sanxianren/oauth/status','::1','内网IP','{}','2025-09-06 17:02:29') 
2025-09-06T17:02:36.307+08:00 [DEBU] {40cf7a9d07a66218b5392d7f1915e624} [ 41 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/sanxianren/oauth/status','GET',1,'demo','财务部门','/api/v1/sanxianren/oauth/status','::1','内网IP','{}','2025-09-06 17:02:36') 
2025-09-06T17:02:36.312+08:00 [DEBU] {5c0aa19d07a66218b6392d7fad195503} [ 44 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/sanxianren/oauth/status','GET',1,'demo','财务部门','/api/v1/sanxianren/oauth/status','::1','内网IP','{}','2025-09-06 17:02:36') 
2025-09-06T17:02:44.039+08:00 [DEBU] {e4719c6a09a66218bc392d7f86ab58b5} [ 37 ms] [default] [sanxian1] [rows:22 ] SHOW FULL COLUMNS FROM `sys_user`
2025-09-06T17:02:44.046+08:00 [DEBU] {e4719c6a09a66218bc392d7f86ab58b5} [  6 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`user_name`,`mobile`,`user_nickname`,`user_password`,`user_salt`,`user_status`,`is_admin`,`avatar`,`dept_id` FROM `sys_user` WHERE (`user_name`='demo') AND `deleted_at` IS NULL LIMIT 1
2025-09-06T17:02:44.134+08:00 [DEBU] {e4719c6a09a66218bc392d7f86ab58b5} [ 83 ms] [default] [sanxian1] [rows:1  ] UPDATE `sys_user` SET `last_login_ip`='::1',`last_login_time`='2025-09-06 17:02:44' WHERE `id`=31
2025-09-06T17:02:44.143+08:00 [DEBU] {4cc1a07209a66218bd392d7f37fb24a7} [  2 ms] [default] [sanxian1] [rows:10 ] SHOW FULL COLUMNS FROM `sys_login_log`
2025-09-06T17:02:44.150+08:00 [DEBU] {e4719c6a09a66218bc392d7f86ab58b5} [  5 ms] [default] [sanxian1] [rows:7  ] SHOW FULL COLUMNS FROM `casbin_rule`
2025-09-06T17:02:44.169+08:00 [DEBU] {4cc1a07209a66218bd392d7f37fb24a7} [ 18 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_login_log`(`login_name`,`ipaddr`,`login_location`,`browser`,`os`,`status`,`msg`,`login_time`,`module`) VALUES('demo','::1','内网IP','Edge','Windows 10',1,'登录成功','2025-09-06 17:02:44','系统后台') 
2025-09-06T17:02:44.171+08:00 [DEBU] {e4719c6a09a66218bc392d7f86ab58b5} [ 20 ms] [default] [sanxian1] [rows:65 ] SELECT `ptype`,`v0`,`v1`,`v2`,`v3`,`v4`,`v5` FROM `casbin_rule`
2025-09-06T17:02:44.180+08:00 [DEBU] {24c80a7509a66218be392d7fc7270b85} [  4 ms] [default] [sanxian1] [rows:8  ] SHOW FULL COLUMNS FROM `sys_user_online`
2025-09-06T17:02:44.185+08:00 [DEBU] {24c80a7509a66218be392d7fc7270b85} [  3 ms] [default] [sanxian1] [rows:0  ] SELECT `id` FROM `sys_user_online` WHERE `token`='7ZUSfVIf2HyYjcv86SKPPs29v003ECPEScsdYsYYqO0UZxl82Rru4U8rKfP4TZcLsd5Bl8ycwFU0DOJCfe0fabCTOsNFJ8dVBZakOhxqleWNvK1qsWej8e21BX9eN+CN8NHAKJ9AW2lW/gAGRZimoQ==' LIMIT 1
2025-09-06T17:02:44.195+08:00 [DEBU] {24c80a7509a66218be392d7fc7270b85} [  9 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_user_online`(`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os`) VALUES('3804d6acb60625bc394eaefb7461b6f8','7ZUSfVIf2HyYjcv86SKPPs29v003ECPEScsdYsYYqO0UZxl82Rru4U8rKfP4TZcLsd5Bl8ycwFU0DOJCfe0fabCTOsNFJ8dVBZakOhxqleWNvK1qsWej8e21BX9eN+CN8NHAKJ9AW2lW/gAGRZimoQ==','2025-09-06 17:02:44','demo','::1','Edge','Windows 10') 
2025-09-06T17:03:31.426+08:00 [DEBU] {e4e5dc7314a66218c2392d7f96707939} [ 24 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/sanxianren/oauth/status','GET',1,'demo','财务部门','/api/v1/sanxianren/oauth/status','::1','内网IP','{}','2025-09-06 17:03:31') 
2025-09-06T17:03:31.429+08:00 [DEBU] {8cf0c57314a66218c1392d7fb3ce8ed3} [ 28 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/sanxianren/oauth/status','GET',1,'demo','财务部门','/api/v1/sanxianren/oauth/status','::1','内网IP','{}','2025-09-06 17:03:31') 
2025-09-06T17:03:39.946+08:00 [DEBU] {2cc63d7016a66218c7392d7f021a75b5} [ 15 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`user_name`,`mobile`,`user_nickname`,`user_password`,`user_salt`,`user_status`,`is_admin`,`avatar`,`dept_id` FROM `sys_user` WHERE (`user_name`='demo') AND `deleted_at` IS NULL LIMIT 1
2025-09-06T17:03:39.987+08:00 [DEBU] {2cc63d7016a66218c7392d7f021a75b5} [ 39 ms] [default] [sanxian1] [rows:1  ] UPDATE `sys_user` SET `last_login_ip`='::1',`last_login_time`='2025-09-06 17:03:39' WHERE `id`=31
2025-09-06T17:03:39.995+08:00 [DEBU] {5022ba7316a66218c8392d7f4e67f7cd} [  5 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_login_log`(`login_name`,`ipaddr`,`login_location`,`browser`,`os`,`status`,`msg`,`login_time`,`module`) VALUES('demo','::1','内网IP','Edge','Windows 10',1,'登录成功','2025-09-06 17:03:39','系统后台') 
2025-09-06T17:03:40.000+08:00 [DEBU] {8078067416a66218c9392d7fec39853b} [  2 ms] [default] [sanxian1] [rows:0  ] SELECT `id` FROM `sys_user_online` WHERE `token`='7ZUSfVIf2HyYjcv86SKPPs29v003ECPEScsdYsYYqO0UZxl82Rru4U8rKfP4TZcLwqOnfrKQxvvewV/ZMoKHoGtusWh8HG3/ZjaLpOWMh8XYD/z3cTrc5T3ZdRegZsoDm56U/pRYRfTlTowK5o8U3g==' LIMIT 1
2025-09-06T17:03:40.007+08:00 [DEBU] {8078067416a66218c9392d7fec39853b} [  5 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_user_online`(`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os`) VALUES('c35ea436693f5abc9a0ddade70131b18','7ZUSfVIf2HyYjcv86SKPPs29v003ECPEScsdYsYYqO0UZxl82Rru4U8rKfP4TZcLwqOnfrKQxvvewV/ZMoKHoGtusWh8HG3/ZjaLpOWMh8XYD/z3cTrc5T3ZdRegZsoDm56U/pRYRfTlTowK5o8U3g==','2025-09-06 17:03:39','demo','::1','Edge','Windows 10') 
2025-09-06T17:04:42.966+08:00 [DEBU] {c438831b25a66218cd392d7fbd7e4c4b} [ 29 ms] [default] [sanxian1] [rows:14 ] SHOW FULL COLUMNS FROM `sys_dict_data`
2025-09-06T17:04:42.970+08:00 [DEBU] {385da91b25a66218ce392d7f9bd25f08} [  2 ms] [default] [sanxian1] [rows:10 ] SHOW FULL COLUMNS FROM `sys_dict_type`
2025-09-06T17:04:42.973+08:00 [DEBU] {c438831b25a66218cd392d7fbd7e4c4b} [  5 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data`
2025-09-06T17:04:42.976+08:00 [DEBU] {c438831b25a66218cd392d7fbd7e4c4b} [  1 ms] [default] [sanxian1] [rows:10 ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-09-06T17:04:42.977+08:00 [DEBU] {385da91b25a66218ce392d7f9bd25f08} [  5 ms] [default] [sanxian1] [rows:29 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-09-06T17:04:43.064+08:00 [DEBU] {70c8851e25a66218d0392d7ffed4503a} [ 79 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-09-06 17:04:42') 
2025-09-06T17:04:43.067+08:00 [DEBU] {d431681e25a66218cf392d7f0df80b6d} [ 85 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":""}','2025-09-06 17:04:42') 
2025-09-06T17:04:46.779+08:00 [DEBU] {3c96860026a66218d3392d7f416b9b81} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT `dict_name`,`remark` FROM `sys_dict_type` WHERE (`dict_type`='sys_yes_no') AND (`status`=1) LIMIT 1
2025-09-06T17:04:46.795+08:00 [DEBU] {b8edad0026a66218d4392d7f2353b9ba} [ 16 ms] [default] [sanxian1] [rows:10 ] SHOW FULL COLUMNS FROM `sys_config`
2025-09-06T17:04:46.801+08:00 [DEBU] {3c96860026a66218d3392d7f416b9b81} [  4 ms] [default] [sanxian1] [rows:2  ] SELECT `dict_value`,`dict_label`,`is_default`,`remark` FROM `sys_dict_data` WHERE (`dict_type`='sys_yes_no') AND (`status`=1) ORDER BY `dict_sort` asc,`dict_code` asc
2025-09-06T17:04:46.804+08:00 [DEBU] {b8edad0026a66218d4392d7f2353b9ba} [  7 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_config`
2025-09-06T17:04:46.807+08:00 [DEBU] {b8edad0026a66218d4392d7f2353b9ba} [  1 ms] [default] [sanxian1] [rows:5  ] SELECT `config_id`,`config_name`,`config_key`,`config_value`,`config_type`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_config` ORDER BY `config_id` asc LIMIT 0,10
2025-09-06T17:04:46.822+08:00 [DEBU] {60c2480226a66218d5392d7f76ce10ec} [ 16 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/getDictData','GET',1,'demo','财务部门','/api/v1/system/dict/data/getDictData?dictType=sys_yes_no&defaultValue=','::1','内网IP','{"defaultValue":"","dictType":"sys_yes_no"}','2025-09-06 17:04:46') 
2025-09-06T17:04:46.824+08:00 [DEBU] {fc06950226a66218d6392d7fb4feea52} [ 13 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('参数管理','/api/v1/system/config/list','GET',1,'demo','财务部门','/api/v1/system/config/list?pageNum=1&pageSize=10&configName=&configKey=&configType=','::1','内网IP','{"configKey":"","configName":"","configType":"","pageNum":"1","pageSize":"10"}','2025-09-06 17:04:46') 
2025-09-06T17:04:50.930+08:00 [DEBU] {18ac0af826a66218d8392d7fae79d285} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT `dict_name`,`remark` FROM `sys_dict_type` WHERE (`dict_type`='sys_normal_disable') AND (`status`=1) LIMIT 1
2025-09-06T17:04:50.935+08:00 [DEBU] {18ac0af826a66218d8392d7fae79d285} [  1 ms] [default] [sanxian1] [rows:2  ] SELECT `dict_value`,`dict_label`,`is_default`,`remark` FROM `sys_dict_data` WHERE (`dict_type`='sys_normal_disable') AND (`status`=1) ORDER BY `dict_sort` asc,`dict_code` asc
2025-09-06T17:04:50.938+08:00 [DEBU] {089a5ef826a66218da392d7f764dba99} [  4 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `plugin_sms_config`
2025-09-06T17:04:50.941+08:00 [DEBU] {089a5ef826a66218da392d7f764dba99} [  1 ms] [default] [sanxian1] [rows:4  ] SELECT `id`,`sms_type`,`remark`,`status` FROM `plugin_sms_config` ORDER BY `id` asc LIMIT 0,10
2025-09-06T17:04:50.966+08:00 [DEBU] {8c44a3f826a66218db392d7f4c4ac587} [ 27 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/getDictData','GET',1,'demo','财务部门','/api/v1/system/dict/data/getDictData?dictType=sys_normal_disable&defaultValue=','::1','内网IP','{"defaultValue":"","dictType":"sys_normal_disable"}','2025-09-06 17:04:50') 
2025-09-06T17:04:51.030+08:00 [DEBU] {d0d40ffe26a66218dc392d7fbfda8cdc} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `plugin_sms_config`
2025-09-06T17:04:51.031+08:00 [DEBU] {d0d40ffe26a66218dc392d7fbfda8cdc} [  0 ms] [default] [sanxian1] [rows:4  ] SELECT `id`,`sms_type`,`remark`,`status` FROM `plugin_sms_config` ORDER BY `id` asc LIMIT 0,10
2025-09-06T17:04:53.251+08:00 [DEBU] {10e7988127a66218de392d7f6ed66283} [ 15 ms] [default] [sanxian1] [rows:10 ] SHOW FULL COLUMNS FROM `plugin_sms_log`
2025-09-06T17:04:53.261+08:00 [DEBU] {10e7988127a66218de392d7f6ed66283} [  8 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `plugin_sms_log` WHERE `deleted_at` IS NULL
2025-09-06T17:04:53.264+08:00 [DEBU] {10e7988127a66218de392d7f6ed66283} [  1 ms] [default] [sanxian1] [rows:10 ] SELECT `id`,`sms_type`,`msg_type`,`templateid`,`mobiles`,`params`,`created_at` FROM `plugin_sms_log` WHERE `deleted_at` IS NULL ORDER BY `id` desc LIMIT 0,10
2025-09-06T17:04:53.313+08:00 [DEBU] {449a208627a66218df392d7f604d6a4e} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `plugin_sms_log` WHERE `deleted_at` IS NULL
2025-09-06T17:04:53.316+08:00 [DEBU] {449a208627a66218df392d7f604d6a4e} [  1 ms] [default] [sanxian1] [rows:10 ] SELECT `id`,`sms_type`,`msg_type`,`templateid`,`mobiles`,`params`,`created_at` FROM `plugin_sms_log` WHERE `deleted_at` IS NULL ORDER BY `id` desc LIMIT 0,10
2025-09-06T17:04:56.233+08:00 [DEBU] {109ed93328a66218e3392d7f35e20e37} [  3 ms] [default] [sanxian1] [rows:22 ] SHOW FULL COLUMNS FROM `sys_auth_rule`
2025-09-06T17:04:56.243+08:00 [DEBU] {109ed93328a66218e3392d7f35e20e37} [  8 ms] [default] [sanxian1] [rows:80 ] SELECT `id`,`pid`,`name`,`title`,`icon`,`condition`,`remark`,`menu_type`,`weigh`,`is_hide`,`is_cached`,`is_affix`,`path`,`redirect`,`component`,`is_iframe`,`is_link`,`link_url` FROM `sys_auth_rule` ORDER BY `weigh` desc,`id` asc
2025-09-06T17:04:56.250+08:00 [DEBU] {bcc1ff3328a66218e4392d7f546e13fb} [ 20 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/getDictData','GET',1,'demo','财务部门','/api/v1/system/dict/data/getDictData?dictType=sys_show_hide&defaultValue=','::1','内网IP','{"defaultValue":"","dictType":"sys_show_hide"}','2025-09-06 17:04:56') 
2025-09-06T17:04:56.261+08:00 [DEBU] {082f403528a66218e5392d7f17a28925} [  6 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/menu/list','GET',1,'demo','财务部门','/api/v1/system/menu/list?title=&component=','::1','内网IP','{"component":"","title":""}','2025-09-06 17:04:56') 
2025-09-06T17:10:05.387+08:00 [DEBU] {240d56a1e8a56218a8392d7fec4c8397} [ 11 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T17:10:05.389+08:00 [DEBU] {240d56a1e8a56218a8392d7fec4c8397} [  1 ms] [default] [sanxian1] [rows:3  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T17:10:05.486+08:00 [DEBU] {240d56a1e8a56218a8392d7fec4c8397} [ 92 ms] [default] [sanxian1] [rows:1  ] DELETE FROM `sys_user_online` WHERE `token`='7ZUSfVIf2HyYjcv86SKPPs29v003ECPEScsdYsYYqO0UZxl82Rru4U8rKfP4TZcLsd5Bl8ycwFU0DOJCfe0fabCTOsNFJ8dVBZakOhxqleWNvK1qsWej8e21BX9eN+CN8NHAKJ9AW2lW/gAGRZimoQ=='
2025-09-06T17:10:05.492+08:00 [DEBU] {240d56a1e8a56218a8392d7fec4c8397} [  5 ms] [default] [sanxian1] [rows:1  ] DELETE FROM `sys_user_online` WHERE `token`='7ZUSfVIf2HyYjcv86SKPPs29v003ECPEScsdYsYYqO0UZxl82Rru4U8rKfP4TZcLAfLggnr3heUdvvR/Hv9zQaWNKA4iwDOcYt/a1E2eGqRNPACmVCqQ3Rd5OOE+qK9PT4LriskdgpMm1sRhQxc64Q=='
2025-09-06T17:10:05.494+08:00 [DEBU] {240d56a1e8a56218a8392d7fec4c8397} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T17:10:05.495+08:00 [DEBU] {240d56a1e8a56218a8392d7fec4c8397} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-06T17:10:05.513+08:00 [DEBU] {240d56a1e8a56218a8392d7fec4c8397} [ 16 ms] [default] [sanxian1] [rows:4  ] SHOW FULL COLUMNS FROM `sys_job_log`
2025-09-06T17:10:05.523+08:00 [DEBU] {240d56a1e8a56218a8392d7fec4c8397} [  8 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 17:10:05','在线用户定时更新，执行成功') 
2025-09-06T17:20:05.687+08:00 [DEBU] {240d56a1e8a56218a8392d7fec4c8397} [ 14 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T17:20:05.691+08:00 [DEBU] {240d56a1e8a56218a8392d7fec4c8397} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T17:20:05.695+08:00 [DEBU] {240d56a1e8a56218a8392d7fec4c8397} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T17:20:05.696+08:00 [DEBU] {240d56a1e8a56218a8392d7fec4c8397} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-06T17:20:05.770+08:00 [DEBU] {240d56a1e8a56218a8392d7fec4c8397} [ 72 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 17:20:05','在线用户定时更新，执行成功') 
2025-09-06T18:50:06.262+08:00 [DEBU] {440ebc4ee5ab6218b86025088d5aba4c} [276 ms] [default] [sanxian1] [rows:104] SHOW TABLES
2025-09-06T18:50:06.508+08:00 [DEBU] {440ebc4ee5ab6218b86025088d5aba4c} [243 ms] [default] [sanxian1] [rows:14 ] SHOW FULL COLUMNS FROM `sys_job`
2025-09-06T18:50:06.542+08:00 [DEBU] {440ebc4ee5ab6218b86025088d5aba4c} [ 21 ms] [default] [sanxian1] [rows:1  ] SELECT `job_id`,`job_name`,`job_params`,`job_group`,`invoke_target`,`cron_expression`,`misfire_policy`,`concurrent`,`status`,`created_by`,`updated_by`,`remark`,`created_at`,`updated_at` FROM `sys_job` WHERE `status`=0
2025-09-06T18:50:08.127+08:00 [DEBU] {440ebc4ee5ab6218b86025088d5aba4c} [1583 ms] [default] [sanxian1] [rows:0  ] UPDATE `sys_job` SET `status`=0 WHERE `job_id`=8
2025-09-06T18:50:08.658+08:00 [DEBU] {d4858ecee5ab6218ba60250895dfd19b} [529 ms] [default] [sanxian1] [rows:5  ] SHOW FULL COLUMNS FROM `plugin_sms_config`
2025-09-06T18:50:08.836+08:00 [DEBU] {d4858ecee5ab6218ba60250895dfd19b} [ 56 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`sms_type`,`remark`,`status`,`config` FROM `plugin_sms_config` WHERE `status`=1 LIMIT 1
2025-09-06T18:50:08.855+08:00 [DEBU] {6067c9f8e5ab6218bb6025086a13ef1e} [ 18 ms] [default] [sanxian1] [rows:1  ] SELECT * FROM `plugin_sms_config` WHERE `sms_type`='yunxin' LIMIT 1
2025-09-06T19:00:16.585+08:00 [DEBU] {8cb20d70e5ab6218b9602508213e9996} [7343 ms] [default] [sanxian1] [rows:8  ] SHOW FULL COLUMNS FROM `sys_user_online`
2025-09-06T19:00:16.842+08:00 [DEBU] {8cb20d70e5ab6218b9602508213e9996} [143 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T19:00:18.270+08:00 [DEBU] {8cb20d70e5ab6218b9602508213e9996} [ 24 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T19:00:19.431+08:00 [DEBU] {8cb20d70e5ab6218b9602508213e9996} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T19:00:19.517+08:00 [DEBU] {8cb20d70e5ab6218b9602508213e9996} [  8 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-06T19:00:19.832+08:00 [DEBU] {8cb20d70e5ab6218b9602508213e9996} [ 64 ms] [default] [sanxian1] [rows:4  ] SHOW FULL COLUMNS FROM `sys_job_log`
2025-09-06T19:00:20.649+08:00 [DEBU] {8cb20d70e5ab6218b9602508213e9996} [246 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 19:00:19','在线用户定时更新，执行成功') 
2025-09-06T19:10:15.794+08:00 [DEBU] {8cb20d70e5ab6218b9602508213e9996} [605 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T19:10:16.023+08:00 [DEBU] {8cb20d70e5ab6218b9602508213e9996} [ 57 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T19:10:16.238+08:00 [DEBU] {8cb20d70e5ab6218b9602508213e9996} [ 16 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T19:10:16.277+08:00 [DEBU] {8cb20d70e5ab6218b9602508213e9996} [  8 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-06T19:10:16.419+08:00 [DEBU] {8cb20d70e5ab6218b9602508213e9996} [108 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 19:10:16','在线用户定时更新，执行成功') 
2025-09-06T19:20:05.977+08:00 [DEBU] {8cb20d70e5ab6218b9602508213e9996} [ 94 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T19:20:05.982+08:00 [DEBU] {8cb20d70e5ab6218b9602508213e9996} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T19:20:05.986+08:00 [DEBU] {8cb20d70e5ab6218b9602508213e9996} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T19:20:05.991+08:00 [DEBU] {8cb20d70e5ab6218b9602508213e9996} [  3 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-06T19:20:06.078+08:00 [DEBU] {8cb20d70e5ab6218b9602508213e9996} [ 85 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 19:20:05','在线用户定时更新，执行成功') 
2025-09-06T19:29:05.103+08:00 [DEBU] {3409d4fd03ae6218c9507f65983c81f5} [8308 ms] [default] [sanxian1] [rows:104] SHOW TABLES
2025-09-06T19:29:05.163+08:00 [DEBU] {3409d4fd03ae6218c9507f65983c81f5} [ 58 ms] [default] [sanxian1] [rows:14 ] SHOW FULL COLUMNS FROM `sys_job`
2025-09-06T19:29:05.208+08:00 [DEBU] {3409d4fd03ae6218c9507f65983c81f5} [ 42 ms] [default] [sanxian1] [rows:1  ] SELECT `job_id`,`job_name`,`job_params`,`job_group`,`invoke_target`,`cron_expression`,`misfire_policy`,`concurrent`,`status`,`created_by`,`updated_by`,`remark`,`created_at`,`updated_at` FROM `sys_job` WHERE `status`=0
2025-09-06T19:29:05.216+08:00 [DEBU] {3409d4fd03ae6218c9507f65983c81f5} [  6 ms] [default] [sanxian1] [rows:0  ] UPDATE `sys_job` SET `status`=0 WHERE `job_id`=8
2025-09-06T19:29:05.228+08:00 [DEBU] {34d7e0f305ae6218cb507f65a6a05cfe} [ 11 ms] [default] [sanxian1] [rows:5  ] SHOW FULL COLUMNS FROM `plugin_sms_config`
2025-09-06T19:29:05.251+08:00 [DEBU] {34d7e0f305ae6218cb507f65a6a05cfe} [ 21 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`sms_type`,`remark`,`status`,`config` FROM `plugin_sms_config` WHERE `status`=1 LIMIT 1
2025-09-06T19:29:05.255+08:00 [DEBU] {4c86fef505ae6218cc507f65f85480d3} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT * FROM `plugin_sms_config` WHERE `sms_type`='yunxin' LIMIT 1
2025-09-06T19:30:06.733+08:00 [DEBU] {80c966f305ae6218ca507f65c4aad9a9} [ 57 ms] [default] [sanxian1] [rows:8  ] SHOW FULL COLUMNS FROM `sys_user_online`
2025-09-06T19:30:06.776+08:00 [DEBU] {80c966f305ae6218ca507f65c4aad9a9} [ 16 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T19:30:06.836+08:00 [DEBU] {80c966f305ae6218ca507f65c4aad9a9} [ 43 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T19:30:10.636+08:00 [DEBU] {80c966f305ae6218ca507f65c4aad9a9} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T19:30:10.755+08:00 [DEBU] {80c966f305ae6218ca507f65c4aad9a9} [ 52 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-06T19:30:11.971+08:00 [DEBU] {80c966f305ae6218ca507f65c4aad9a9} [ 22 ms] [default] [sanxian1] [rows:4  ] SHOW FULL COLUMNS FROM `sys_job_log`
2025-09-06T19:30:12.560+08:00 [DEBU] {80c966f305ae6218ca507f65c4aad9a9} [192 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 19:30:11','在线用户定时更新，执行成功') 
2025-09-06T19:40:05.610+08:00 [DEBU] {80c966f305ae6218ca507f65c4aad9a9} [ 29 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T19:40:05.613+08:00 [DEBU] {80c966f305ae6218ca507f65c4aad9a9} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T19:40:05.616+08:00 [DEBU] {80c966f305ae6218ca507f65c4aad9a9} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T19:40:05.618+08:00 [DEBU] {80c966f305ae6218ca507f65c4aad9a9} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-06T19:40:05.657+08:00 [DEBU] {80c966f305ae6218ca507f65c4aad9a9} [ 38 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 19:40:05','在线用户定时更新，执行成功') 
2025-09-06T19:50:05.901+08:00 [DEBU] {80c966f305ae6218ca507f65c4aad9a9} [ 20 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T19:50:05.906+08:00 [DEBU] {80c966f305ae6218ca507f65c4aad9a9} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-06T19:50:05.910+08:00 [DEBU] {80c966f305ae6218ca507f65c4aad9a9} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-06T19:50:05.913+08:00 [DEBU] {80c966f305ae6218ca507f65c4aad9a9} [  2 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-06T19:50:05.954+08:00 [DEBU] {80c966f305ae6218ca507f65c4aad9a9} [ 40 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-06 19:50:05','在线用户定时更新，执行成功') 
