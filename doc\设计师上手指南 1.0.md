# 天南地北三线人 — 设计师上手指南（v1.0）

**对象**：新加入的产品/视觉/交互设计师
**目标**：1）1 天内能理解项目与用户；2）3 天内能独立输出一个页面方案；3）1 周内按规范高效交付并与前端对齐。

---

## 0. 我们在做什么（30 秒）

把“三线建设”的**人—厂—地—证**做成可检索、可引用、可传承的数字记忆库：

* **PC** 用 MediaWiki 承载百科词条；
* **App（UniApp）** 负责**投稿**、**浏览**、**地图**、**故事馆**；
* **GFast 中台** 负责账号、AI 清洗、审核、商业化与对接 Wiki。

设计目标：**可用、可信、长者友好、证据优先、情感适度**。

---

## 1. 设计任务优先级（Onboarding Sprint）

1. **MVP 必做（第 1 周）**

   * 首页「找人找厂」搜索框 + 快捷入口
   * 人物卡片（照片+简介）瀑布流列表
   * 人物详情（证据链、来源、修订记录链接）
   * 地图页（遗址点标注 + 点开弹层）
   * 投稿引导页 + 基础投稿表单
2. **第 2–4 周**

   * 志愿者审核台（对比：原文 / AI 草稿 / 终稿）
   * 口述历史播放器（音/视频 + 转写）
   * 纪念册导出（封面、目录、人物页模板）
   * 会员/捐赠入口与收据样式

---

## 2. 用户与场景（基于现状）

* **老同志/家属**（55–85 岁）：需要**大字号、语音输入、清晰引导**；
* **志愿者**：高效标注、对比校对；
* **研究者/机构**：强调**来源、引用、导出**；
* **普通用户**：沉浸式浏览、可收藏分享。

关键场景：

* 找人找厂（直达）；
* 投稿（最少阻力）；
* 证据链（可信）；
* 地图（空间化记忆）；
* 审核（高效、可追溯）。

---

## 3. 设计系统（Design System）

> 所有变量均落地为 **Design Tokens**，与前端共享（`tokens.json`）。

### 3.1 基础 Tokens

| 类别 | Token               | 值（默认）                       | 说明                             |
| -- | ------------------- | --------------------------- | ------------------------------ |
| 色彩 | `color.bg`          | `#0F1220`                   | 背景深色                           |
|    | `color.card`        | `#151A2E`                   | 卡片底                            |
|    | `color.text`        | `#EEF2FF`                   | 正文                             |
|    | `color.muted`       | `#A6B0CF`                   | 次要文本                           |
|    | `color.brand`       | `#5B8CFF`                   | 品牌主色                           |
|    | `color.brandAlt`    | `#23D5AB`                   | 品牌渐变辅色                         |
| 字体 | `font.family.cn`    | 系统中文无衬线                     | `PingFang/HarmonyOS/MiSans` 优先 |
|    | `font.size.base`    | 16px                        | H5 基础字号                        |
|    | `font.size.large`   | 18–20px                     | 老年友好标题                         |
| 间距 | `space.xs/s/m/l/xl` | 4/8/12/16/24                | 全局间距刻度                         |
| 圆角 | `radius.card`       | 16px                        | 卡片圆角                           |
| 阴影 | `shadow.card`       | 0 10px 30px rgba(0,0,0,.25) | 卡片投影                           |

> 深/浅色：浅色方案将 `bg`/`text` 值反转并微调对比度，优先保障可读性（见 7.1）。

### 3.2 栅格与断点

* 断点：`sm 360` / `md 640` / `lg 1024` / `xl 1280`（与单页示例一致）
* 列表：CSS Columns 或 Masonry；卡片宽 280–320，间距 16
* 触控面积：最小 44×44 px

### 3.3 组件库（优先实现）

* **SearchBar**（双字段：工厂代号/姓名，语音入口）
* **PersonCard**（照片、姓名、工厂代号、简介）
* **Sheet/Modal**（地图点弹层、投稿前提示）
* **Stepper**（投稿三步）
* **EvidenceList**（来源条目、时间戳、文件类型）
* **Player**（音/视频 + 转写切换）
* **Toast/Result**（提交成功、审核中、失败）

组件状态：`default/hover/active/disabled/loading/error/empty`。

---

## 4. 关键页面规格（Spec）

### 4.1 首页 / 搜索

* 头部 **Hero**：一句话价值 + 搜索框；
* 搜索条件：工厂代号（必选其一）/ 姓名；
* 无条件进入：随机/热门人物卡片瀑布流；
* 辅助入口：投稿、地图、故事馆、关于我们。

### 4.2 人物列表（瀑布流）

* 卡片内信息层级：**照片 > 姓名 > 工厂代号 > 简介**；
* 图片比例 `4:3`，懒加载；
* 空状态：提示“换关键词 / 看随机推荐”。

### 4.3 人物详情

* 顶部：头像/合影、姓名、工厂代号、时间轴（出生—入厂—调动—退休）；
* 主体：**故事正文**、**证据链**（来源、扫描件、口述音频、审校人）、**修订信息**（链接至 Wiki）；
* 底部：相关人物/同厂同年；收藏/分享/打赏；
* 可访问性：文章字号 18–20px；行高 1.6；段落 12–16px 间距。

### 4.4 地图页

* 默认定位至全国视图；按缩放级别聚合点（Cluster）；
* 点位弹层：遗址名、所属工厂、1–3 张图、查看故事按钮；
* 工具：城市/工厂筛选、定位、路线（外链高德）。

### 4.5 投稿引导 + 表单

* 3 步：准备资料 → 拍照/语音 → 提交并查看进度；
* 字段最小集：姓名、工厂代号、入厂年份（可不精确）、岗位、照片/文件、联系方式（仅审核使用）；
* 许可勾选：**CC BY-SA 4.0** + 肖像/隐私说明；
* 结果页：展示“投稿编号 + 预计审核时长 + 修改入口”。

### 4.6 审核台（志愿者）

* 三栏对比：原文 / AI 建议 / 终稿；
* 快捷操作：来源标注、实体纠错、时间规范化；
* 批注与版本对比；
* 历史：操作留痕/回滚。

---

## 5. 文案与内容（Content Design）

* **语调**：尊重、克制、事实优先；避免夸饰。对老同志与家属使用“您”。
* **核心用语规范**：

  * 人物 = “三线人/建设者”；
  * 工厂代号示例：“504”“067”；
  * 证据链条目：`来源/时间/类型/供稿人`；
* **按钮与状态**：

  * 投稿按钮：`现在就投稿`；
  * 成功：`提交成功，等待审核`；
  * 空：`没有找到结果，试试换个关键词`；
* **可读性**：中文版面采用**真空格/半角标点**；长文加小标题与引用块。

---

## 6. 无障碍与长者友好（A11y）

* 字体最小 16px，正文建议 18px；
* 高对比度：文字与背景对比度 ≥ WCAG AA（建议 ≥ 5:1）；
* 语音输入与朗读支持；
* 点击区≥44×44px；
* 表单分段、字段提示明确；
* 错误用语清晰（不使用专业术语指责用户）。

---

## 7. 主题与图形（Brand/UI）

* **品牌元素**：工厂编号、蓝图线、经纬网格、钢印质感（克制使用）；
* **插图**：几何与线路风格，避免“过度怀旧滤镜”；
* **深浅色**：

  * 深色为默认；
  * 浅色需重新校准对比度与投影（避免灰蒙蒙）。

---

## 8. 与前端协作（Handoff）

* 设计工具：Figma（团队库 `ThreeLine Design System`）
* 命名规范：

  * 组件 `C/` 前缀：`C/PersonCard`、`C/SearchBar`；
  * 页面 `P/`：`P/Home`、`P/Map`、`P/PersonDetail`；
* 切图：只导出确需位图的插图/占位图，照片走真实数据；
* 交付物：

  1. 线框（用户流 + IA）
  2. 视觉稿（移动端优先）
  3. 组件标注（尺寸/间距/状态）
  4. Tokens（JSON）
* 开发对齐：

  * Breakpoints、栅格与间距表对齐；
  * 状态与交互（Hover/Active/Empty/Loading/Error）明示；
  * 可复用的卡片/表单优先。

---

## 9. 质量清单（设计自检）

* [ ] 搜索框键盘类型正确（数字/姓名）
* [ ] 列表空状态有引导（随机/热门入口）
* [ ] 图片懒加载并有占位图
* [ ] 文字对比度达标（AA）
* [ ] 点击区≥44×44px
* [ ] 表单错误提示清晰、逐项定位
* [ ] 许可勾选在提交前可见且易懂
* [ ] 页面在 `sm/md/lg` 均已适配
* [ ] 深/浅色样式一致且不偏灰
* [ ] 复杂流程提供“返回/保存草稿”

---

## 10. 资源与模版

* Figma 团队库：`ThreeLine Design System`（请求权限：@PM）
* UI Kit：`/UI-Kit`（组件 + Tokens）
* 图标集：RemixIcon（可增补）
* 地图样式：高德自定义样式 ID：`{待填}`
* 版权图片：自采/供稿优先，避免素材网站“怀旧模板图”

---

## 11. 快速起步（你今天就能做）

1. 在 Figma 复制模板：`P/PersonList` 与 `C/PersonCard`；
2. 根据品牌 Tokens 调整你的页面（最多两种主色用法）；
3. 画出“搜索 → 列表 → 详情”的 3 张关键状态；
4. 与前端确认断点和组件属性；
5. 发起评审：对齐文案、无障碍、性能（首屏像素与图片体积）。

---

## 12. FAQ（常见问题）

* **Q：老同志不用智能手机怎么办？**
  A：家属代填 + 线下采集日 + 志愿者上门。App 端需保留“打印/下载投稿指南”。
* **Q：要做多复杂的视觉？**
  A：克制。优先可读性与结构，避免大片贴图；品牌感通过编号/线稿体现即可。
* **Q：地图点很多怎么办？**
  A：聚合（Cluster）+ 条件筛选 + 渐进加载；弹层内容限制在 3 段信息内。

---

> 本指南与《技术说明文档 v1.0》配套，随产品迭代更新。若发现规范与实现不一致，以 Tokens 与组件库为准。
