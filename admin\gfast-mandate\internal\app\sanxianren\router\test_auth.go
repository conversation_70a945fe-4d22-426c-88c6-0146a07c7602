/*
* @desc:测试认证路由
* @company:山东傲英网络科技股份有限公司
* @Author: wolfman
* @Date:   2024/12/19
 */

package router

import (
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/internal/app/sanxianren/controller"
)

// TestAuth 测试认证路由（无需登录验证）
func TestAuth(group *ghttp.RouterGroup) {
	testAuthController := &controller.TestAuthController{}

	// 测试认证路由组（无需登录验证）
	group.Bind(
		testAuthController.GetTestToken,    // 获取测试token
		testAuthController.ValidateToken,   // 验证token状态
	)
}

// TestAuthWithLogin 测试认证路由（需要登录验证）
func TestAuthWithLogin(group *ghttp.RouterGroup) {
	testAuthController := &controller.TestAuthController{}

	// 测试认证路由组（需要登录验证）
	group.Bind(
		testAuthController.GetCurrentUser,  // 获取当前用户信息
	)
}
