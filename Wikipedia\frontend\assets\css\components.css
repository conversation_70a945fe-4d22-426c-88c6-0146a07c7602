/* 组件样式文件 */

/* 移动端抽屉菜单 */
.mobile-menu-drawer .el-drawer__header {
    padding: 20px;
    border-bottom: 1px solid #f0f2f5;
}

.mobile-menu-header .logo {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #409eff;
    font-size: 18px;
    font-weight: 600;
}

.mobile-menu-content {
    padding: 20px;
}

.mobile-nav-section {
    margin-bottom: 24px;
    padding-bottom: 20px;
    border-bottom: 1px solid #f0f2f5;
}

.mobile-nav-section .section-title {
    font-size: 14px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 12px;
}

.mobile-nav-items .nav-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 4px;
}

.mobile-nav-items .nav-item:hover {
    background: #f5f7fa;
}

.mobile-nav-items .nav-item.active {
    background: #ecf5ff;
    color: #409eff;
}

.mobile-nav-items .nav-content {
    flex: 1;
}

.mobile-nav-items .nav-title {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #303133;
}

.mobile-nav-items .nav-desc {
    display: block;
    font-size: 12px;
    color: #909399;
    margin-top: 2px;
}

/* 分类相关样式 */
.card-history {
    border-left: 4px solid #e6a23c;
}

.card-sanxianren {
    border-left: 4px solid #67c23a;
}

.card-sanxianchang {
    border-left: 4px solid #409eff;
}

.card-storyhall {
    border-left: 4px solid #f56c6c;
}

.card-oralhistory {
    border-left: 4px solid #909399;
}

.card-heritage {
    border-left: 4px solid #e6a23c;
}

/* Element Plus 组件自定义样式 */
.el-tag.category-tag {
    border: none;
    font-weight: 500;
}

.el-tag.category-tag i {
    margin-right: 4px;
}

.el-button.search-button {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.el-input.search-input .el-input__wrapper {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

/* 卡片悬浮效果 */
.article-card {
    position: relative;
    overflow: hidden;
}

.article-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
    z-index: 1;
}

.article-card:hover::before {
    left: 100%;
}

/* 加载动画 */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f2f5 25%, #e4e7ed 50%, #f0f2f5 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* 图片懒加载占位符 */
.image-placeholder {
    width: 100%;
    height: 200px;
    background: #f5f7fa;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #c0c4cc;
    font-size: 32px;
}

/* 搜索建议样式 */
.search-suggestion {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 0;
}

.suggestion-icon {
    color: #409eff;
}

.suggestion-content {
    flex: 1;
}

.suggestion-title {
    font-size: 14px;
    font-weight: 500;
    color: #303133;
}

.suggestion-desc {
    font-size: 12px;
    color: #909399;
    margin-top: 2px;
}

/* 质量评级星星 */
.quality-stars {
    display: flex;
    gap: 2px;
}

.star-filled {
    color: #e6a23c;
}

.star-empty {
    color: #f0f2f5;
}

/* 特殊效果 */
.gradient-text {
    background: linear-gradient(45deg, #409eff, #722ed1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.glass-effect {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: #c0c4cc;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #909399;
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
    transition: all 0.3s ease;
}

.slide-up-enter-from {
    transform: translateY(20px);
    opacity: 0;
}

.slide-up-leave-to {
    transform: translateY(-20px);
    opacity: 0;
}

/* 工具提示样式 */
.el-tooltip__popper {
    max-width: 300px;
}

/* 标签云效果 */
.tag-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin: 16px 0;
}

.tag-cloud .el-tag {
    cursor: pointer;
    transition: all 0.3s ease;
}

.tag-cloud .el-tag:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 统计数字动画 */
.stat-number {
    font-weight: 600;
    font-size: 18px;
    color: #409eff;
    transition: all 0.3s ease;
}

.stat-number:hover {
    transform: scale(1.1);
}

/* 卡片网格布局 */
.card-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

/* 响应式图片 */
.responsive-image {
    width: 100%;
    height: auto;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.responsive-image:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 文本截断 */
.text-ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.text-ellipsis-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.text-ellipsis-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 阴影效果 */
.shadow-sm {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.shadow-md {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.shadow-lg {
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}

.shadow-xl {
    box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);
}

/* 边框圆角 */
.rounded-sm {
    border-radius: 4px;
}

.rounded-md {
    border-radius: 8px;
}

.rounded-lg {
    border-radius: 12px;
}

.rounded-xl {
    border-radius: 16px;
}

.rounded-full {
    border-radius: 50%;
}
