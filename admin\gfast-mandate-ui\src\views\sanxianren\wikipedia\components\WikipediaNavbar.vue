<template>
  <div class="wikipedia-navbar">
    <div class="navbar-container">
      <!-- Logo和标题 -->
      <div class="navbar-brand">
        <div class="logo" @click="goHome">
          <SvgIcon name="iconfont icon-wikipedia" :size="32" />
          <span class="brand-text">天南地北三线人</span>
        </div>
        <div class="subtitle">记录三线建设历史，传承三线精神</div>
      </div>

      <!-- 主导航菜单 -->
      <div class="navbar-nav">
        <div
          v-for="nav in navigationItems"
          :key="nav.key"
          class="nav-item"
          :class="{ active: currentNav === nav.key }"
          @click="handleNavClick(nav)"
        >
          <SvgIcon :name="nav.icon" :size="18" />
          <span>{{ nav.label }}</span>
        </div>
      </div>

      <!-- 搜索栏 -->
      <div class="navbar-search">
        <el-autocomplete
          v-model="searchInput"
          :fetch-suggestions="fetchSearchSuggestions"
          :trigger-on-focus="false"
          placeholder="搜索三线人、三线厂、故事..."
          class="search-input"
          size="large"
          clearable
          @select="handleSearchSelect"
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <SvgIcon name="ele-Search" :size="18" />
          </template>
          <template #suffix>
            <el-button
              type="primary"
              :icon="Search"
              @click="handleSearch"
              class="search-button"
            >
              搜索
            </el-button>
          </template>
          <template #default="{ item }">
            <div class="search-suggestion">
              <SvgIcon :name="getSuggestionIcon(item.type)" :size="16" class="suggestion-icon" />
              <div class="suggestion-content">
                <div class="suggestion-title">{{ item.title }}</div>
                <div class="suggestion-desc">{{ item.description }}</div>
              </div>
              <el-tag :type="getSuggestionTagType(item.type)" size="small">
                {{ getSuggestionTypeText(item.type) }}
              </el-tag>
            </div>
          </template>
        </el-autocomplete>
      </div>

      <!-- 导航菜单 -->
      <div class="navbar-menu">
        <!-- 语言切换 -->
        <el-dropdown @command="handleLanguageChange" class="language-dropdown">
          <el-button text class="nav-button">
            <SvgIcon name="ele-Globe" :size="18" />
            <span class="nav-text">{{ currentLanguage.name }}</span>
            <SvgIcon name="ele-ArrowDown" :size="14" />
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item 
                v-for="lang in languages" 
                :key="lang.code"
                :command="lang.code"
                :class="{ active: currentLanguage.code === lang.code }"
              >
                <span class="lang-flag">{{ lang.flag }}</span>
                {{ lang.name }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <!-- 创建词条 -->
        <el-button 
          type="primary" 
          @click="createArticle"
          class="create-btn"
        >
          <SvgIcon name="ele-Plus" :size="16" />
          <span class="nav-text">创建词条</span>
        </el-button>

        <!-- 用户菜单 -->
        <el-dropdown @command="handleUserAction" class="user-dropdown">
          <div class="user-avatar">
            <el-avatar :size="36" :src="userInfo?.avatar">
              <SvgIcon name="ele-User" :size="20" />
            </el-avatar>
            <div v-if="userInfo" class="user-info">
              <div class="username">{{ userInfo.username }}</div>
              <div class="user-role">{{ userInfo.role }}</div>
            </div>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">
                <SvgIcon name="ele-User" :size="16" />
                个人资料
              </el-dropdown-item>
              <el-dropdown-item command="contributions">
                <SvgIcon name="ele-Edit" :size="16" />
                我的贡献
              </el-dropdown-item>
              <el-dropdown-item command="favorites">
                <SvgIcon name="ele-Star" :size="16" />
                收藏夹
              </el-dropdown-item>
              <el-dropdown-item command="watchlist">
                <SvgIcon name="ele-View" :size="16" />
                监视列表
              </el-dropdown-item>
              <el-dropdown-item divided command="settings">
                <SvgIcon name="ele-Setting" :size="16" />
                设置
              </el-dropdown-item>
              <el-dropdown-item command="logout">
                <SvgIcon name="ele-SwitchButton" :size="16" />
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <!-- 移动端菜单按钮 -->
        <el-button 
          text 
          @click="toggleMobileMenu"
          class="mobile-menu-btn"
        >
          <SvgIcon name="ele-Menu" :size="20" />
        </el-button>
      </div>
    </div>

    <!-- 移动端抽屉菜单 -->
    <el-drawer
      v-model="mobileMenuVisible"
      direction="rtl"
      size="280px"
      :show-close="false"
      class="mobile-menu-drawer"
    >
      <template #header>
        <div class="mobile-menu-header">
          <div class="logo">
            <SvgIcon name="iconfont icon-wikipedia" :size="24" />
            <span>Wikipedia</span>
          </div>
        </div>
      </template>

      <div class="mobile-menu-content">
        <!-- 用户信息 -->
        <div v-if="userInfo" class="mobile-user-info">
          <el-avatar :size="48" :src="userInfo.avatar">
            <SvgIcon name="ele-User" :size="24" />
          </el-avatar>
          <div class="user-details">
            <div class="username">{{ userInfo.username }}</div>
            <div class="user-role">{{ userInfo.role }}</div>
          </div>
        </div>

        <!-- 主导航菜单 -->
        <div class="mobile-nav-section">
          <div class="section-title">主要栏目</div>
          <div class="mobile-nav-items">
            <div
              v-for="nav in navigationItems"
              :key="nav.key"
              class="nav-item"
              :class="{ active: currentNav === nav.key }"
              @click="handleNavClick(nav)"
            >
              <SvgIcon :name="nav.icon" :size="18" />
              <div class="nav-content">
                <span class="nav-title">{{ nav.label }}</span>
                <span class="nav-desc">{{ nav.description }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 功能菜单 -->
        <div class="mobile-menu-items">
          <div class="menu-item" @click="createArticle">
            <SvgIcon name="ele-Plus" :size="18" />
            <span>创建内容</span>
          </div>
          <div class="menu-item" @click="handleUserAction('profile')">
            <SvgIcon name="ele-User" :size="18" />
            <span>个人资料</span>
          </div>
          <div class="menu-item" @click="handleUserAction('favorites')">
            <SvgIcon name="ele-Star" :size="18" />
            <span>我的收藏</span>
          </div>
          <div class="menu-item" @click="handleUserAction('settings')">
            <SvgIcon name="ele-Setting" :size="18" />
            <span>设置</span>
          </div>
          <div class="menu-item logout" @click="handleUserAction('logout')">
            <SvgIcon name="ele-SwitchButton" :size="18" />
            <span>退出登录</span>
          </div>
        </div>

        <!-- 语言切换 -->
        <div class="mobile-language-section">
          <div class="section-title">语言</div>
          <div class="language-grid">
            <div 
              v-for="lang in languages" 
              :key="lang.code"
              class="language-item"
              :class="{ active: currentLanguage.code === lang.code }"
              @click="handleLanguageChange(lang.code)"
            >
              <span class="lang-flag">{{ lang.flag }}</span>
              <span class="lang-name">{{ lang.name }}</span>
            </div>
          </div>
        </div>
      </div>
    </el-drawer>

    <!-- 高级搜索对话框 -->
    <el-dialog
      v-model="advancedSearchVisible"
      title="高级搜索"
      width="600px"
      :before-close="closeAdvancedSearch"
    >
      <WikipediaAdvancedSearch
        @search="handleAdvancedSearch"
        @close="closeAdvancedSearch"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="WikipediaNavbar">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import SvgIcon from '/@/components/svgIcon/index.vue';

// Emits
const emit = defineEmits<{
  search: [query: string];
  categoryChange: [category: string];
  languageChange: [language: string];
}>();

// Router
const router = useRouter();

// 模拟用户信息（实际项目中应该从store获取）
const userInfo = ref({
  username: '管理员',
  role: '系统管理员',
  avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=admin'
});

// 响应式数据
const searchInput = ref('');
const mobileMenuVisible = ref(false);
const advancedSearchVisible = ref(false);
const currentNav = ref('sanxianren');

// 导航项配置
const navigationItems = ref([
  {
    key: 'sanxianren',
    label: '三线人',
    icon: 'ele-User',
    path: '/sanxianren/wikipedia/people',
    description: '三线建设者的人物故事'
  },
  {
    key: 'sanxianchang',
    label: '三线厂',
    icon: 'ele-OfficeBuilding',
    path: '/sanxianren/wikipedia/factories',
    description: '三线建设的工厂企业'
  },
  {
    key: 'storyhall',
    label: '故事馆',
    icon: 'ele-Reading',
    path: '/sanxianren/wikipedia/stories',
    description: '三线建设的历史故事'
  },
  {
    key: 'oralhistory',
    label: '口述历史',
    icon: 'ele-Microphone',
    path: '/sanxianren/wikipedia/oral-history',
    description: '亲历者的口述回忆'
  },
  {
    key: 'heritage',
    label: '遗址馆',
    icon: 'ele-Monument',
    path: '/sanxianren/wikipedia/heritage',
    description: '三线建设的历史遗址'
  }
]);

// 语言配置
const languages = ref([
  { code: 'zh', name: '中文', flag: '🇨🇳' },
  { code: 'en', name: 'English', flag: '🇺🇸' },
  { code: 'ja', name: '日本語', flag: '🇯🇵' },
  { code: 'ko', name: '한국어', flag: '🇰🇷' },
  { code: 'fr', name: 'Français', flag: '🇫🇷' },
  { code: 'de', name: 'Deutsch', flag: '🇩🇪' },
]);

const currentLanguage = ref(languages.value[0]);

// 方法
const goHome = () => {
  router.push('/sanxianren/wikipedia');
};

const handleNavClick = (nav: any) => {
  currentNav.value = nav.key;
  router.push(nav.path);
  emit('categoryChange', nav.key);
  mobileMenuVisible.value = false;
};

const handleSearch = () => {
  if (searchInput.value.trim()) {
    emit('search', searchInput.value.trim());
  }
};

const handleSearchSelect = (item: any) => {
  searchInput.value = item.title;
  handleSearch();
};

const fetchSearchSuggestions = async (queryString: string, callback: Function) => {
  if (!queryString) {
    callback([]);
    return;
  }

  try {
    // 模拟搜索建议数据
    const suggestions = [
      { title: '三线建设', description: '中国1960-1970年代的重大战略', type: 'concept' },
      { title: '攀枝花钢铁基地', description: '三线建设重要工业项目', type: 'place' },
      { title: '王进喜', description: '铁人精神代表人物', type: 'person' },
      { title: '贵州航空工业', description: '三线建设航空制造业发展', type: 'article' },
    ].filter(item => item.title.includes(queryString) || item.description.includes(queryString));

    callback(suggestions);
  } catch (error) {
    console.error('获取搜索建议失败:', error);
    callback([]);
  }
};

const getSuggestionIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    article: 'ele-Document',
    person: 'ele-User',
    place: 'ele-Location',
    event: 'ele-Calendar',
    concept: 'ele-Lightbulb',
  };
  return iconMap[type] || 'ele-Document';
};

const getSuggestionTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    article: 'primary',
    person: 'success',
    place: 'warning',
    event: 'info',
    concept: 'danger',
  };
  return typeMap[type] || 'primary';
};

const getSuggestionTypeText = (type: string) => {
  const textMap: Record<string, string> = {
    article: '词条',
    person: '人物',
    place: '地点',
    event: '事件',
    concept: '概念',
  };
  return textMap[type] || '词条';
};

const showAdvancedSearch = () => {
  advancedSearchVisible.value = true;
};

const closeAdvancedSearch = () => {
  advancedSearchVisible.value = false;
};

const handleAdvancedSearch = (searchParams: any) => {
  // 处理高级搜索
  emit('search', searchParams.query);
  closeAdvancedSearch();
};

const handleLanguageChange = (langCode: string) => {
  const language = languages.value.find(lang => lang.code === langCode);
  if (language) {
    currentLanguage.value = language;
    emit('languageChange', langCode);
    ElMessage.success(`已切换到${language.name}`);
  }
  mobileMenuVisible.value = false;
};

const createArticle = () => {
  router.push('/sanxianren/wikipedia/create');
  mobileMenuVisible.value = false;
};

const handleUserAction = (action: string) => {
  switch (action) {
    case 'profile':
      router.push('/personal');
      break;
    case 'contributions':
      router.push('/sanxianren/wikipedia/contributions');
      break;
    case 'favorites':
      router.push('/sanxianren/wikipedia/favorites');
      break;
    case 'watchlist':
      router.push('/sanxianren/wikipedia/watchlist');
      break;
    case 'settings':
      router.push('/sanxianren/wikipedia/settings');
      break;
    case 'logout':
      // 模拟退出登录
      ElMessage.success('已退出登录');
      router.push('/login');
      break;
  }
  mobileMenuVisible.value = false;
};

const toggleMobileMenu = () => {
  mobileMenuVisible.value = !mobileMenuVisible.value;
};

// 生命周期
onMounted(() => {
  // 初始化当前语言
  const savedLang = localStorage.getItem('wikipedia_language');
  if (savedLang) {
    const language = languages.value.find(lang => lang.code === savedLang);
    if (language) {
      currentLanguage.value = language;
    }
  }
});
</script>

<style scoped lang="scss">
.wikipedia-navbar {
  position: sticky;
  top: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--el-border-color-light);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

  .navbar-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    height: 70px;
    gap: 20px;
  }

  .navbar-brand {
    display: flex;
    flex-direction: column;
    cursor: pointer;
    min-width: 200px;

    .logo {
      display: flex;
      align-items: center;
      gap: 8px;
      color: var(--el-color-primary);
      font-size: 18px;
      font-weight: 700;

      .brand-text {
        background: linear-gradient(45deg, #1890ff, #722ed1);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
    }

    .subtitle {
      font-size: 11px;
      color: var(--el-text-color-secondary);
      margin-left: 40px;
      margin-top: 2px;
    }
  }

  .navbar-nav {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 20px;

    .nav-item {
      display: flex;
      align-items: center;
      gap: 6px;
      padding: 8px 16px;
      border-radius: 20px;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 14px;
      color: var(--el-text-color-regular);
      white-space: nowrap;

      &:hover {
        background: var(--el-fill-color-light);
        color: var(--el-color-primary);
        transform: translateY(-1px);
      }

      &.active {
        background: var(--el-color-primary-light-9);
        color: var(--el-color-primary);
        font-weight: 500;
      }

      @media (max-width: 1200px) {
        span {
          display: none;
        }
      }

      @media (max-width: 992px) {
        display: none;
      }
    }
  }

  .navbar-search {
    flex: 1;
    max-width: 600px;
    display: flex;
    align-items: center;
    gap: 12px;

    .search-input {
      flex: 1;

      :deep(.el-input__wrapper) {
        border-radius: 25px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
      }

      :deep(.el-input__suffix) {
        .search-button {
          border-radius: 20px;
          margin-right: 4px;
        }
      }
    }

    .advanced-search-btn {
      color: var(--el-text-color-regular);
      font-size: 12px;
    }
  }

  .navbar-menu {
    display: flex;
    align-items: center;
    gap: 16px;

    .nav-button {
      display: flex;
      align-items: center;
      gap: 6px;
      color: var(--el-text-color-regular);
      font-size: 14px;

      .nav-text {
        @media (max-width: 768px) {
          display: none;
        }
      }
    }

    .create-btn {
      border-radius: 20px;
      font-size: 14px;

      .nav-text {
        @media (max-width: 768px) {
          display: none;
        }
      }
    }

    .user-dropdown {
      .user-avatar {
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;

        .user-info {
          @media (max-width: 768px) {
            display: none;
          }

          .username {
            font-size: 14px;
            font-weight: 500;
            color: var(--el-text-color-primary);
          }

          .user-role {
            font-size: 12px;
            color: var(--el-text-color-secondary);
          }
        }
      }
    }

    .mobile-menu-btn {
      display: none;

      @media (max-width: 768px) {
        display: flex;
      }
    }
  }
}

.search-suggestion {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;

  .suggestion-icon {
    color: var(--el-color-primary);
  }

  .suggestion-content {
    flex: 1;

    .suggestion-title {
      font-size: 14px;
      font-weight: 500;
      color: var(--el-text-color-primary);
    }

    .suggestion-desc {
      font-size: 12px;
      color: var(--el-text-color-secondary);
      margin-top: 2px;
    }
  }
}

.mobile-menu-drawer {
  :deep(.el-drawer__header) {
    padding: 20px;
    border-bottom: 1px solid var(--el-border-color-light);
  }

  .mobile-menu-header {
    .logo {
      display: flex;
      align-items: center;
      gap: 8px;
      color: var(--el-color-primary);
      font-size: 18px;
      font-weight: 600;
    }
  }

  .mobile-menu-content {
    padding: 20px;

    .mobile-user-info {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 16px;
      background: var(--el-fill-color-light);
      border-radius: 8px;
      margin-bottom: 20px;

      .user-details {
        .username {
          font-size: 16px;
          font-weight: 500;
          color: var(--el-text-color-primary);
        }

        .user-role {
          font-size: 12px;
          color: var(--el-text-color-secondary);
          margin-top: 2px;
        }
      }
    }

    .mobile-nav-section {
      margin-bottom: 24px;
      padding-bottom: 20px;
      border-bottom: 1px solid var(--el-border-color-light);

      .section-title {
        font-size: 14px;
        font-weight: 500;
        color: var(--el-text-color-primary);
        margin-bottom: 12px;
      }

      .mobile-nav-items {
        .nav-item {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 12px 16px;
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.3s ease;
          margin-bottom: 4px;

          &:hover {
            background: var(--el-fill-color-light);
          }

          &.active {
            background: var(--el-color-primary-light-9);
            color: var(--el-color-primary);
          }

          .nav-content {
            flex: 1;

            .nav-title {
              display: block;
              font-size: 14px;
              font-weight: 500;
              color: var(--el-text-color-primary);
            }

            .nav-desc {
              display: block;
              font-size: 12px;
              color: var(--el-text-color-secondary);
              margin-top: 2px;
            }
          }
        }
      }
    }

    .mobile-menu-items {
      .menu-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px 16px;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-bottom: 4px;

        &:hover {
          background: var(--el-fill-color-light);
        }

        &.logout {
          color: var(--el-color-danger);
          margin-top: 12px;
          border-top: 1px solid var(--el-border-color-light);
          padding-top: 16px;
        }

        span {
          font-size: 14px;
        }
      }
    }

    .mobile-language-section {
      margin-top: 24px;
      padding-top: 20px;
      border-top: 1px solid var(--el-border-color-light);

      .section-title {
        font-size: 14px;
        font-weight: 500;
        color: var(--el-text-color-primary);
        margin-bottom: 12px;
      }

      .language-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;

        .language-item {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 12px;
          border-radius: 6px;
          cursor: pointer;
          transition: all 0.3s ease;
          border: 1px solid var(--el-border-color-light);

          &:hover {
            background: var(--el-fill-color-light);
          }

          &.active {
            background: var(--el-color-primary-light-9);
            border-color: var(--el-color-primary);
            color: var(--el-color-primary);
          }

          .lang-flag {
            font-size: 16px;
          }

          .lang-name {
            font-size: 12px;
          }
        }
      }
    }
  }
}

// 语言下拉菜单样式
:deep(.el-dropdown-menu) {
  .el-dropdown-menu__item {
    display: flex;
    align-items: center;
    gap: 8px;

    &.active {
      background: var(--el-color-primary-light-9);
      color: var(--el-color-primary);
    }

    .lang-flag {
      font-size: 14px;
    }
  }
}

// 响应式隐藏
@media (max-width: 768px) {
  .wikipedia-navbar {
    .navbar-container {
      padding: 0 16px;
    }

    .language-dropdown,
    .user-dropdown .user-info {
      display: none;
    }
  }
}
</style>
