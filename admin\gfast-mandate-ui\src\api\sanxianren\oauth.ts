/**
 * OAuth认证相关API接口
 * <AUTHOR> Assistant
 * @date 2025-01-22
 */

import request from '/@/utils/request';

// OAuth认证相关接口类型定义
export interface OAuthAuthorizeReq {
  return_url?: string;
}

export interface OAuthAuthorizeRes {
  auth_url: string;
  message: string;
}

export interface OAuthCallbackReq {
  code: string;
  state: string;
  error?: string;
  error_description?: string;
}

export interface OAuthCallbackRes {
  success: boolean;
  message: string;
  token?: string;
  user_info?: MediaWikiUserInfo;
  return_url?: string;
}

export interface OAuthRefreshTokenReq {
  refresh_token: string;
}

export interface OAuthRefreshTokenRes {
  access_token: string;
  token_type: string;
  expires_in: number;
  refresh_token: string;
  scope: string;
  message: string;
}

export interface OAuthGetUserInfoReq {
  access_token: string;
}

export interface OAuthGetUserInfoRes {
  user_info: MediaWikiUserInfo;
  message: string;
}

export interface OAuthValidateTokenReq {
  access_token: string;
}

export interface OAuthValidateTokenRes {
  valid: boolean;
  message: string;
}

export interface OAuthRevokeTokenReq {
  access_token: string;
}

export interface OAuthRevokeTokenRes {
  success: boolean;
  message: string;
}

export interface OAuthGetSessionReq {
  session_id: string;
}

export interface OAuthGetSessionRes {
  session_id: string;
  user_id: number;
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
  scope: string;
  created_at: string;
  updated_at: string;
  expires_at: string;
  message: string;
}

export interface OAuthDeleteSessionReq {
  session_id: string;
}

export interface OAuthDeleteSessionRes {
  success: boolean;
  message: string;
}

export interface OAuthStatusRes {
  is_authenticated: boolean;
  user_info?: MediaWikiUserInfo;
  session_id?: string;
  expires_at?: string;
  message: string;
}

export interface OAuthConfigRes {
  client_id: string;
  authorization_url: string;
  redirect_uri: string;
  scopes: string[];
  message: string;
}

export interface MediaWikiUserInfo {
  id: number;
  name: string;
  real_name: string;
  email: string;
  groups: string[];
  rights: string[];
  edit_count: number;
  registration: string;
}

/**
 * OAuth API类
 */
export default class OAuthApi {
  /**
   * 发起OAuth授权
   */
  static authorize(data: OAuthAuthorizeReq): Promise<OAuthAuthorizeRes> {
    return request({
      url: '/api/v1/sanxianren/oauth/authorize',
      method: 'post',
      data,
    });
  }

  /**
   * 处理OAuth回调
   */
  static callback(params: OAuthCallbackReq): Promise<OAuthCallbackRes> {
    return request({
      url: '/api/v1/sanxianren/oauth/callback',
      method: 'get',
      params,
    });
  }

  /**
   * 刷新访问令牌
   */
  static refreshToken(data: OAuthRefreshTokenReq): Promise<OAuthRefreshTokenRes> {
    return request({
      url: '/api/v1/sanxianren/oauth/refresh',
      method: 'post',
      data,
    });
  }

  /**
   * 获取MediaWiki用户信息
   */
  static getUserInfo(params: OAuthGetUserInfoReq): Promise<OAuthGetUserInfoRes> {
    return request({
      url: '/api/v1/sanxianren/oauth/userinfo',
      method: 'get',
      params,
    });
  }

  /**
   * 验证访问令牌
   */
  static validateToken(data: OAuthValidateTokenReq): Promise<OAuthValidateTokenRes> {
    return request({
      url: '/api/v1/sanxianren/oauth/validate',
      method: 'post',
      data,
    });
  }

  /**
   * 撤销访问令牌
   */
  static revokeToken(data: OAuthRevokeTokenReq): Promise<OAuthRevokeTokenRes> {
    return request({
      url: '/api/v1/sanxianren/oauth/revoke',
      method: 'post',
      data,
    });
  }

  /**
   * 获取OAuth会话信息
   */
  static getSession(params: OAuthGetSessionReq): Promise<OAuthGetSessionRes> {
    return request({
      url: '/api/v1/sanxianren/oauth/session',
      method: 'get',
      params,
    });
  }

  /**
   * 删除OAuth会话
   */
  static deleteSession(data: OAuthDeleteSessionReq): Promise<OAuthDeleteSessionRes> {
    return request({
      url: '/api/v1/sanxianren/oauth/session',
      method: 'delete',
      data,
    });
  }

  /**
   * 获取OAuth认证状态
   */
  static getStatus(): Promise<OAuthStatusRes> {
    return request({
      url: '/api/v1/sanxianren/oauth/status',
      method: 'get',
    });
  }

  /**
   * 获取OAuth配置信息
   */
  static getConfig(): Promise<OAuthConfigRes> {
    return request({
      url: '/api/v1/sanxianren/oauth/config',
      method: 'get',
    });
  }

  /**
   * 重定向到OAuth授权页面（浏览器直接访问）
   */
  static authorizeRedirect(returnUrl?: string): string {
    const params = new URLSearchParams();
    if (returnUrl) {
      params.append('return_url', returnUrl);
    }
    return `/api/v1/sanxianren/oauth/authorize-redirect?${params.toString()}`;
  }

  /**
   * OAuth回调重定向处理（浏览器直接访问）
   */
  static callbackRedirect(code: string, state: string, error?: string, errorDescription?: string): string {
    const params = new URLSearchParams();
    params.append('code', code);
    params.append('state', state);
    if (error) {
      params.append('error', error);
    }
    if (errorDescription) {
      params.append('error_description', errorDescription);
    }
    return `/api/v1/sanxianren/oauth/callback-redirect?${params.toString()}`;
  }
}

/**
 * OAuth工具类
 */
export class OAuthUtils {
  /**
   * 检查URL中的OAuth回调参数
   */
  static getCallbackParams(): OAuthCallbackReq | null {
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    const state = urlParams.get('state');
    
    if (code && state) {
      return {
        code,
        state,
        error: urlParams.get('error') || undefined,
        error_description: urlParams.get('error_description') || undefined,
      };
    }
    
    return null;
  }

  /**
   * 清理URL中的OAuth参数
   */
  static cleanCallbackParams(): void {
    const url = new URL(window.location.href);
    url.searchParams.delete('code');
    url.searchParams.delete('state');
    url.searchParams.delete('error');
    url.searchParams.delete('error_description');
    
    window.history.replaceState({}, document.title, url.toString());
  }

  /**
   * 生成OAuth状态参数
   */
  static generateState(userId: number, returnUrl?: string): string {
    const stateData = {
      user_id: userId,
      timestamp: Date.now(),
      nonce: Math.random().toString(36).substring(2),
      return_url: returnUrl || '',
    };
    
    return btoa(JSON.stringify(stateData));
  }

  /**
   * 解析OAuth状态参数
   */
  static parseState(state: string): any {
    try {
      return JSON.parse(atob(state));
    } catch (error) {
      console.error('解析OAuth状态参数失败:', error);
      return null;
    }
  }

  /**
   * 检查OAuth令牌是否过期
   */
  static isTokenExpired(expiresAt: string): boolean {
    return new Date(expiresAt) <= new Date();
  }

  /**
   * 格式化过期时间
   */
  static formatExpiresAt(expiresAt: string): string {
    return new Date(expiresAt).toLocaleString('zh-CN');
  }

  /**
   * 遮蔽敏感信息
   */
  static maskSensitiveInfo(str: string, visibleLength: number = 4): string {
    if (!str || str.length <= visibleLength * 2) {
      return str;
    }
    
    const start = str.substring(0, visibleLength);
    const end = str.substring(str.length - visibleLength);
    const middle = '*'.repeat(Math.min(str.length - visibleLength * 2, 8));
    
    return `${start}${middle}${end}`;
  }
}
