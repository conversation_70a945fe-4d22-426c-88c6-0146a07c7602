/**
 * gfast中台与MediaWiki OAuth认证前端集成
 * <AUTHOR> Assistant
 * @date 2025-01-22
 */

class OAuthManager {
    constructor(config = {}) {
        this.config = {
            baseUrl: config.baseUrl || '/api/v1/sanxianren',
            tokenKey: config.tokenKey || 'gfast_token',
            oauthTokenKey: config.oauthTokenKey || 'oauth_token',
            redirectPath: config.redirectPath || '/admin/oauth/success',
            ...config
        };
        
        this.isAuthenticated = false;
        this.userInfo = null;
        this.oauthToken = null;
        
        // 初始化
        this.init();
    }
    
    /**
     * 初始化OAuth管理器
     */
    init() {
        // 检查本地存储的token
        this.loadTokenFromStorage();
        
        // 检查URL中的OAuth回调参数
        this.handleOAuthCallback();
        
        // 绑定页面事件
        this.bindEvents();
    }
    
    /**
     * 从本地存储加载token
     */
    loadTokenFromStorage() {
        const token = localStorage.getItem(this.config.tokenKey);
        const oauthToken = localStorage.getItem(this.config.oauthTokenKey);
        
        if (token) {
            this.setToken(token);
        }
        
        if (oauthToken) {
            this.oauthToken = oauthToken;
        }
    }
    
    /**
     * 处理OAuth回调
     */
    handleOAuthCallback() {
        const urlParams = new URLSearchParams(window.location.search);
        const code = urlParams.get('code');
        const state = urlParams.get('state');
        const error = urlParams.get('error');
        
        if (error) {
            this.handleOAuthError(error, urlParams.get('error_description'));
            return;
        }
        
        if (code && state) {
            this.processOAuthCallback(code, state);
        }
    }
    
    /**
     * 处理OAuth错误
     */
    handleOAuthError(error, description) {
        console.error('OAuth认证失败:', error, description);
        this.showMessage(`OAuth认证失败: ${description || error}`, 'error');
        
        // 清理URL参数
        this.cleanUrl();
    }
    
    /**
     * 处理OAuth回调
     */
    async processOAuthCallback(code, state) {
        try {
            this.showMessage('正在处理OAuth认证...', 'info');
            
            const response = await fetch(`${this.config.baseUrl}/oauth/callback?code=${code}&state=${state}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            const data = await response.json();
            
            if (data.success) {
                // 认证成功
                this.handleOAuthSuccess(data);
            } else {
                // 认证失败
                this.showMessage(`OAuth认证失败: ${data.message}`, 'error');
            }
        } catch (error) {
            console.error('处理OAuth回调失败:', error);
            this.showMessage('OAuth认证处理失败', 'error');
        } finally {
            // 清理URL参数
            this.cleanUrl();
        }
    }
    
    /**
     * 处理OAuth认证成功
     */
    handleOAuthSuccess(data) {
        // 保存token
        if (data.token) {
            this.setToken(data.token);
            localStorage.setItem(this.config.tokenKey, data.token);
        }
        
        // 保存用户信息
        if (data.user_info) {
            this.userInfo = data.user_info;
            localStorage.setItem('oauth_user_info', JSON.stringify(data.user_info));
        }
        
        this.isAuthenticated = true;
        this.showMessage('OAuth认证成功！', 'success');
        
        // 触发认证成功事件
        this.dispatchEvent('oauth:success', { userInfo: this.userInfo, token: data.token });
        
        // 重定向到指定页面
        if (data.return_url) {
            setTimeout(() => {
                window.location.href = data.return_url;
            }, 1500);
        } else {
            setTimeout(() => {
                window.location.href = this.config.redirectPath;
            }, 1500);
        }
    }
    
    /**
     * 发起OAuth认证
     */
    async startOAuth(returnUrl = null) {
        try {
            // 检查是否已登录gfast系统
            const gfastToken = localStorage.getItem(this.config.tokenKey);
            if (!gfastToken) {
                this.showMessage('请先登录gfast系统', 'warning');
                return;
            }
            
            const requestData = {
                return_url: returnUrl || (window.location.origin + this.config.redirectPath)
            };
            
            const response = await fetch(`${this.config.baseUrl}/oauth/authorize`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${gfastToken}`
                },
                body: JSON.stringify(requestData)
            });
            
            const data = await response.json();
            
            if (data.auth_url) {
                // 重定向到MediaWiki授权页面
                window.location.href = data.auth_url;
            } else {
                this.showMessage('获取授权URL失败', 'error');
            }
        } catch (error) {
            console.error('发起OAuth认证失败:', error);
            this.showMessage('发起OAuth认证失败', 'error');
        }
    }
    
    /**
     * 获取OAuth状态
     */
    async getOAuthStatus() {
        try {
            const response = await fetch(`${this.config.baseUrl}/oauth/status`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${this.getToken()}`
                }
            });
            
            const data = await response.json();
            return data;
        } catch (error) {
            console.error('获取OAuth状态失败:', error);
            return { is_authenticated: false };
        }
    }
    
    /**
     * 刷新OAuth令牌
     */
    async refreshOAuthToken(refreshToken) {
        try {
            const response = await fetch(`${this.config.baseUrl}/oauth/refresh`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ refresh_token: refreshToken })
            });
            
            const data = await response.json();
            
            if (data.access_token) {
                this.oauthToken = data.access_token;
                localStorage.setItem(this.config.oauthTokenKey, data.access_token);
                return data;
            }
            
            return null;
        } catch (error) {
            console.error('刷新OAuth令牌失败:', error);
            return null;
        }
    }
    
    /**
     * 撤销OAuth认证
     */
    async revokeOAuth() {
        try {
            if (!this.oauthToken) {
                this.showMessage('没有有效的OAuth令牌', 'warning');
                return;
            }
            
            const response = await fetch(`${this.config.baseUrl}/oauth/revoke`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ access_token: this.oauthToken })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.clearOAuthData();
                this.showMessage('OAuth认证已撤销', 'success');
            } else {
                this.showMessage('撤销OAuth认证失败', 'error');
            }
        } catch (error) {
            console.error('撤销OAuth认证失败:', error);
            this.showMessage('撤销OAuth认证失败', 'error');
        }
    }
    
    /**
     * 清理OAuth数据
     */
    clearOAuthData() {
        this.isAuthenticated = false;
        this.userInfo = null;
        this.oauthToken = null;
        
        localStorage.removeItem(this.config.oauthTokenKey);
        localStorage.removeItem('oauth_user_info');
        
        this.dispatchEvent('oauth:logout');
    }
    
    /**
     * 设置token
     */
    setToken(token) {
        this.token = token;
    }
    
    /**
     * 获取token
     */
    getToken() {
        return this.token || localStorage.getItem(this.config.tokenKey);
    }
    
    /**
     * 清理URL参数
     */
    cleanUrl() {
        const url = new URL(window.location);
        url.searchParams.delete('code');
        url.searchParams.delete('state');
        url.searchParams.delete('error');
        url.searchParams.delete('error_description');
        
        window.history.replaceState({}, document.title, url.toString());
    }
    
    /**
     * 绑定页面事件
     */
    bindEvents() {
        // 绑定OAuth按钮点击事件
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-oauth-action="start"]')) {
                e.preventDefault();
                const returnUrl = e.target.getAttribute('data-return-url');
                this.startOAuth(returnUrl);
            }
            
            if (e.target.matches('[data-oauth-action="revoke"]')) {
                e.preventDefault();
                this.revokeOAuth();
            }
        });
    }
    
    /**
     * 显示消息
     */
    showMessage(message, type = 'info') {
        // 这里可以集成具体的消息提示组件
        console.log(`[${type.toUpperCase()}] ${message}`);
        
        // 简单的消息提示实现
        const messageEl = document.createElement('div');
        messageEl.className = `oauth-message oauth-message-${type}`;
        messageEl.textContent = message;
        messageEl.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 4px;
            color: white;
            z-index: 10000;
            background-color: ${type === 'success' ? '#52c41a' : type === 'error' ? '#ff4d4f' : type === 'warning' ? '#faad14' : '#1890ff'};
        `;
        
        document.body.appendChild(messageEl);
        
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.parentNode.removeChild(messageEl);
            }
        }, 3000);
    }
    
    /**
     * 触发自定义事件
     */
    dispatchEvent(eventName, detail = {}) {
        const event = new CustomEvent(eventName, { detail });
        window.dispatchEvent(event);
    }
}

// 使用示例
const oauthManager = new OAuthManager({
    baseUrl: '/api/v1/sanxianren',
    redirectPath: '/admin/oauth/success'
});

// 监听OAuth事件
window.addEventListener('oauth:success', (e) => {
    console.log('OAuth认证成功:', e.detail);
});

window.addEventListener('oauth:logout', () => {
    console.log('OAuth认证已注销');
});

// 导出供全局使用
window.OAuthManager = OAuthManager;
window.oauthManager = oauthManager;
