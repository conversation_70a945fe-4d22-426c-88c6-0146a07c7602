/**
 * React OAuth集成组件
 * <AUTHOR> Assistant
 * @date 2025-01-22
 */

import React, { useState, useEffect } from 'react';
import { Button, Card, Alert, Descriptions, Tag, Table, message, Modal } from 'antd';
import { LinkOutlined, ReloadOutlined, DisconnectOutlined, LoadingOutlined } from '@ant-design/icons';

const OAuthIntegration = ({ showConfig = false, showLogs = false }) => {
  const [loading, setLoading] = useState(false);
  const [authenticating, setAuthenticating] = useState(false);
  const [revoking, setRevoking] = useState(false);
  
  const [oauthStatus, setOauthStatus] = useState({
    is_authenticated: false,
    user_info: null,
    session_id: null,
    expires_at: null
  });
  
  const [config, setConfig] = useState({
    client_id: '',
    authorization_url: '',
    redirect_uri: '',
    scopes: []
  });
  
  const [logs, setLogs] = useState([]);

  // 获取token
  const getToken = () => {
    return localStorage.getItem('gfast_token') || '';
  };

  // 添加日志
  const addLog = (action, status, logMessage) => {
    const newLog = {
      key: Date.now(),
      action,
      status,
      message: logMessage,
      created_at: new Date().toISOString()
    };
    
    setLogs(prevLogs => {
      const updatedLogs = [newLog, ...prevLogs];
      return updatedLogs.slice(0, 50); // 只保留最近50条
    });
  };

  // 获取OAuth状态
  const getOAuthStatus = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/v1/sanxianren/oauth/status', {
        headers: {
          'Authorization': `Bearer ${getToken()}`
        }
      });
      const data = await response.json();
      
      setOauthStatus(data);
      
      if (data.is_authenticated) {
        addLog('status_check', 'success', '已认证状态');
      } else {
        addLog('status_check', 'success', '未认证状态');
      }
    } catch (error) {
      console.error('获取OAuth状态失败:', error);
      message.error('获取OAuth状态失败');
      addLog('status_check', 'failed', '获取状态失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // 获取OAuth配置
  const getOAuthConfig = async () => {
    try {
      const response = await fetch('/api/v1/sanxianren/oauth/config');
      const data = await response.json();
      setConfig(data);
    } catch (error) {
      console.error('获取OAuth配置失败:', error);
    }
  };

  // 发起OAuth认证
  const startOAuth = async () => {
    setAuthenticating(true);
    try {
      const returnUrl = window.location.origin + '/admin/oauth/success';
      
      const response = await fetch('/api/v1/sanxianren/oauth/authorize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getToken()}`
        },
        body: JSON.stringify({ return_url: returnUrl })
      });
      
      const data = await response.json();
      
      if (data.auth_url) {
        addLog('authorize', 'success', '重定向到MediaWiki授权页面');
        // 重定向到MediaWiki授权页面
        window.location.href = data.auth_url;
      } else {
        message.error('获取授权URL失败');
        addLog('authorize', 'failed', '获取授权URL失败');
      }
    } catch (error) {
      console.error('发起OAuth认证失败:', error);
      message.error('发起OAuth认证失败');
      addLog('authorize', 'failed', '发起认证失败: ' + error.message);
    } finally {
      setAuthenticating(false);
    }
  };

  // 撤销OAuth认证
  const revokeOAuth = () => {
    Modal.confirm({
      title: '确认撤销',
      content: '确定要撤销MediaWiki OAuth认证吗？撤销后将无法访问MediaWiki相关功能。',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        setRevoking(true);
        try {
          const response = await fetch('/api/v1/sanxianren/oauth/revoke', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ 
              access_token: 'current_access_token' // 实际应用中需要从状态中获取
            })
          });
          
          const data = await response.json();
          
          if (data.success) {
            message.success('OAuth认证已撤销');
            addLog('revoke', 'success', 'OAuth认证已撤销');
            // 刷新状态
            await getOAuthStatus();
          } else {
            message.error('撤销OAuth认证失败');
            addLog('revoke', 'failed', '撤销认证失败');
          }
        } catch (error) {
          console.error('撤销OAuth认证失败:', error);
          message.error('撤销OAuth认证失败');
          addLog('revoke', 'failed', '撤销认证失败: ' + error.message);
        } finally {
          setRevoking(false);
        }
      }
    });
  };

  // 格式化日期
  const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleString('zh-CN');
  };

  // 遮蔽字符串
  const maskString = (str) => {
    if (!str || str.length <= 8) return str;
    return str.substring(0, 4) + '****' + str.substring(str.length - 4);
  };

  // 日志表格列定义
  const logColumns = [
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 120
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => (
        <Tag color={status === 'success' ? 'green' : 'red'}>
          {status}
        </Tag>
      )
    },
    {
      title: '消息',
      dataIndex: 'message',
      key: 'message'
    },
    {
      title: '时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      render: (date) => formatDate(date)
    }
  ];

  // 组件挂载时获取状态和配置
  useEffect(() => {
    getOAuthStatus();
    getOAuthConfig();
  }, []);

  return (
    <div style={{ padding: '20px' }}>
      {/* OAuth状态显示 */}
      <Card title="MediaWiki OAuth认证状态" style={{ marginBottom: '20px' }}>
        {loading ? (
          <div style={{ textAlign: 'center', padding: '20px' }}>
            <LoadingOutlined /> 检查认证状态...
          </div>
        ) : oauthStatus.is_authenticated ? (
          <div>
            <Alert
              message="已认证"
              description={`已与MediaWiki账户 ${oauthStatus.user_info?.name} 关联`}
              type="success"
              showIcon
              style={{ marginBottom: '20px' }}
            />
            
            <div style={{ marginBottom: '20px' }}>
              <h4>用户信息</h4>
              <Descriptions bordered column={2}>
                <Descriptions.Item label="用户名">
                  {oauthStatus.user_info?.name}
                </Descriptions.Item>
                <Descriptions.Item label="真实姓名">
                  {oauthStatus.user_info?.real_name}
                </Descriptions.Item>
                <Descriptions.Item label="邮箱">
                  {oauthStatus.user_info?.email}
                </Descriptions.Item>
                <Descriptions.Item label="编辑次数">
                  {oauthStatus.user_info?.edit_count}
                </Descriptions.Item>
                <Descriptions.Item label="用户组">
                  {oauthStatus.user_info?.groups?.map(group => (
                    <Tag key={group} style={{ marginRight: '5px' }}>
                      {group}
                    </Tag>
                  ))}
                </Descriptions.Item>
                <Descriptions.Item label="会话过期时间">
                  {formatDate(oauthStatus.expires_at)}
                </Descriptions.Item>
              </Descriptions>
            </div>
            
            <div style={{ textAlign: 'center' }}>
              <Button
                type="default"
                danger
                icon={<DisconnectOutlined />}
                loading={revoking}
                onClick={revokeOAuth}
                style={{ marginRight: '10px' }}
              >
                撤销认证
              </Button>
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                onClick={getOAuthStatus}
              >
                刷新状态
              </Button>
            </div>
          </div>
        ) : (
          <div>
            <Alert
              message="未认证"
              description="您还没有与MediaWiki账户关联，请点击下方按钮进行OAuth认证"
              type="warning"
              showIcon
              style={{ marginBottom: '20px' }}
            />
            
            <div style={{ textAlign: 'center' }}>
              <Button
                type="primary"
                icon={<LinkOutlined />}
                loading={authenticating}
                onClick={startOAuth}
              >
                连接MediaWiki账户
              </Button>
            </div>
          </div>
        )}
      </Card>

      {/* OAuth配置信息 */}
      {showConfig && (
        <Card title="OAuth配置信息" style={{ marginBottom: '20px' }}>
          <Descriptions bordered column={1}>
            <Descriptions.Item label="客户端ID">
              {maskString(config.client_id)}
            </Descriptions.Item>
            <Descriptions.Item label="授权URL">
              {config.authorization_url}
            </Descriptions.Item>
            <Descriptions.Item label="回调URL">
              {config.redirect_uri}
            </Descriptions.Item>
            <Descriptions.Item label="权限范围">
              {config.scopes?.map(scope => (
                <Tag key={scope} style={{ marginRight: '5px' }}>
                  {scope}
                </Tag>
              ))}
            </Descriptions.Item>
          </Descriptions>
        </Card>
      )}

      {/* 操作日志 */}
      {showLogs && (
        <Card title="操作日志">
          <Table
            columns={logColumns}
            dataSource={logs}
            pagination={{ pageSize: 10 }}
            size="small"
          />
        </Card>
      )}
    </div>
  );
};

export default OAuthIntegration;
