# 🎯 Form-Template-Category系统使用示例演示

## 📋 演示概述

这个演示将向您展示如何使用您设计的完整系统来创建和管理三线建设的历史内容。我们将通过具体的例子来说明整个工作流程。

## 🏭 示例一：创建976工厂档案

### 使用表单创建（推荐方式）

#### 第1步：访问表单
访问：`https://www.sanxianren.com/index.php?title=Form:三线工厂`

#### 第2步：输入页面名称
在"页面名称"输入框中输入：`976工厂`

#### 第3步：点击创建
点击"创建工厂档案"按钮

#### 第4步：填写表单
系统会显示一个美观的表单，您需要填写：

```
基本信息：
- 工厂名称：976工厂
- 工厂代号：976
- 所在地区：山东省
- 主要产品：56式半自动步枪

详细信息：
- 建厂时间：1965年
- 工厂类型：军工
- 职工人数：5000人
- 投资规模：2000万元

地理位置：
- 纬度：36.6586
- 经度：117.0647

历史信息：
- 建设背景：为了加强国防建设，响应三线建设号召...
- 建设过程：1965年开始选址，1966年正式开工建设...
- 投产时间：1968年
- 发展历程：从建厂到成为重要军工企业的发展过程...

人物信息：
- 厂长负责人：[[张三]]、[[李四]]
- 技术骨干：[[王工程师]]、[[赵技师]]
- 劳动模范：[[刘师傅]]

成就荣誉：
- 获得荣誉：全国先进工厂、省级劳动模范单位
- 技术创新：改进生产工艺，提高产品质量
- 生产成果：累计生产步枪50万支

资料档案：
- 历史照片：[[File:976工厂全景.jpg]]
- 相关文档：[[File:976工厂建设方案.pdf]]
```

#### 第5步：保存页面
点击"保存页面"按钮

### 结果展示
保存后，系统会自动：
1. **应用模板格式** - 页面显示为美观的工厂档案
2. **添加到分类** - 自动归类到"Category:三线工厂"
3. **创建地图** - 如果提供了坐标，显示地理位置
4. **建立链接** - 人物名称自动变成可点击链接

## 👥 示例二：创建人员档案

### 创建张三的档案

#### 访问表单
访问：`https://www.sanxianren.com/index.php?title=Form:三线人员`

#### 输入信息
```
基本信息：
- 姓名：张三
- 性别：男
- 籍贯：河北省
- 所在单位：976工厂

个人信息：
- 出生年月：1935年3月
- 职务职业：厂长
- 工厂代号：976
- 参与时间：1965-1985年
- 工作地点：山东省

经历贡献：
- 个人经历：1960年从北京调入，参与三线建设...
- 三线贡献：主持976工厂建设，确保按期投产...
- 主要成就：获得全国劳动模范称号
- 获得荣誉：全国劳动模范、省级先进工作者

其他信息：
- 家庭情况：妻子李女士，子女2人
- 后续发展：1985年退休后继续担任技术顾问
- 相关照片：[[File:张三工作照.jpg]]
```

## 📖 示例三：创建故事记录

### 记录一个感人的三线故事

#### 访问表单
访问：`https://www.sanxianren.com/index.php?title=Form:三线故事馆`

#### 填写故事信息
```
故事标题：深山里的婚礼
故事类型：生活故事
发生时间：1967年
发生地点：976工厂
主要人物：[[张三]]、[[李女士]]

故事内容：
1967年春天，在976工厂的深山里举办了一场特殊的婚礼。
新郎张三是工厂的技术骨干，新娘李女士是从北京支援三线建设的护士。
由于条件艰苦，婚礼很简单，但全厂职工都来祝贺...

故事意义：
体现了三线建设者在艰苦条件下的乐观精神和相互支持...

相关资料：
- [[File:深山婚礼照片.jpg]]
- [[976工厂]]
- [[张三]]
```

## 🎤 示例四：记录口述历史

### 记录老工人的回忆

#### 访问表单
访问：`https://www.sanxianren.com/index.php?title=Form:口述历史`

#### 填写访谈信息
```
访谈标题：老工人王师傅的三线回忆
受访人姓名：王师傅
受访人年龄：85岁
访谈时间：2024年1月
访谈地点：976工厂退休职工活动中心
访谈人：记者小李

访谈内容：
"我1965年从上海来到这里，那时候这里还是一片荒山...
我们住的是临时搭建的工棚，吃的是粗粮...
但是大家都很有干劲，因为我们知道这是为国家建设..."

历史价值：
这段口述历史真实记录了三线建设初期的艰苦条件...

相关资料：
- [[File:王师傅访谈录音.mp3]]
- [[File:王师傅老照片.jpg]]
- [[976工厂]]
```

## 🏞️ 示例五：记录遗址信息

### 记录976工厂旧址

#### 访问表单
访问：`https://www.sanxianren.com/index.php?title=Form:三线遗址`

#### 填写遗址信息
```
遗址名称：976工厂旧址
遗址类型：工业遗址
所在地区：山东省
建设年代：1960年代

地理位置：
- 纬度：36.6586
- 经度：117.0647

历史背景：
976工厂是三线建设时期的重要军工企业...

现状描述：
目前厂房大部分保存完好，部分车间已改为博物馆...

保护价值：
具有重要的历史价值和教育意义...

保护建议：
建议设立保护标志，开发为爱国主义教育基地...

相关资料：
- [[File:976工厂遗址现状.jpg]]
- [[976工厂]]
```

## 🔍 查看和浏览内容

### 通过分类浏览
创建内容后，您可以通过以下方式浏览：

#### 查看所有工厂
访问：`https://www.sanxianren.com/index.php?title=Category:三线工厂`
- 会显示所有工厂的列表
- 包括976工厂等

#### 查看所有人员
访问：`https://www.sanxianren.com/index.php?title=Category:三线人员`
- 会显示所有人员的列表
- 包括张三等

#### 查看所有故事
访问：`https://www.sanxianren.com/index.php?title=Category:三线故事馆`
- 会显示所有故事的列表

### 通过搜索查找
使用MediaWiki的搜索功能：
- 搜索"976"会找到相关的工厂和人员
- 搜索"张三"会找到相关的人员和故事

### 通过链接导航
页面之间会自动建立链接：
- 在976工厂页面可以点击张三的链接
- 在张三的页面可以看到相关的工厂和故事

## 📊 系统优势展示

### 1. 数据一致性
- 所有工厂档案都有统一的格式
- 信息字段标准化
- 便于比较和分析

### 2. 关联性强
- 人员与工厂自动关联
- 故事与相关人物关联
- 形成完整的信息网络

### 3. 易于维护
- 使用表单录入，减少错误
- 模板统一管理样式
- 分类自动归档

### 4. 用户友好
- 表单界面直观易用
- 页面显示美观专业
- 支持多媒体内容

## 🎯 最佳实践建议

### 1. 命名规范
- **工厂页面**：使用工厂代号（如：976工厂）
- **人员页面**：使用真实姓名（如：张三）
- **故事页面**：使用描述性标题（如：深山里的婚礼）

### 2. 内容质量
- **准确性**：确保信息真实可靠
- **完整性**：尽量填写所有相关字段
- **关联性**：建立页面间的链接关系

### 3. 资料管理
- **图片**：先上传到MediaWiki，再在表单中引用
- **文档**：使用标准的文件命名规范
- **链接**：使用双方括号创建内部链接

## 🎉 系统效果

使用您的系统后，www.sanxianren.com 将拥有：

1. **结构化的历史档案** - 工厂、人员、故事等分类清晰
2. **美观的页面展示** - 统一的视觉风格和布局
3. **便捷的内容管理** - 表单化的录入和编辑
4. **强大的关联功能** - 内容之间相互关联
5. **完整的搜索功能** - 支持全文搜索和分类浏览

这将成为一个真正专业的三线建设历史资料库！🚀

## 📞 下一步行动

1. **立即部署** - 按照部署指南上传所有模板文件
2. **开始录入** - 使用表单创建第一个工厂档案
3. **逐步完善** - 根据实际使用情况调整和优化
4. **培训用户** - 教会其他用户如何使用系统

您的Form-Template-Category系统设计得非常出色，一旦部署完成，将为三线建设历史的保存和传承提供强大的技术支持！🎊
