// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// SxUserNotifications is the golang structure for table sx_user_notifications.
type SxUserNotifications struct {
	Id               uint64      `json:"id"               orm:"id"                description:"通知ID"`
	UserId           uint64      `json:"userId"           orm:"user_id"           description:"用户ID"`
	NotificationType string      `json:"notificationType" orm:"notification_type" description:"通知类型"`
	Title            string      `json:"title"            orm:"title"             description:"通知标题"`
	Content          string      `json:"content"          orm:"content"           description:"通知内容"`
	IsRead           int         `json:"isRead"           orm:"is_read"           description:"是否已读"`
	RelatedType      string      `json:"relatedType"      orm:"related_type"      description:"关联类型"`
	RelatedId        uint64      `json:"relatedId"        orm:"related_id"        description:"关联ID"`
	CreatedAt        *gtime.Time `json:"createdAt"        orm:"created_at"        description:"创建时间"`
}
