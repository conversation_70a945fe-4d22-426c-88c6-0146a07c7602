/*
* @desc:安全服务
* @company:山东傲英网络科技股份有限公司
* @Author: wolfman
* @Date:   2024/12/19
 */

package service

import (
	"context"
	"crypto/md5"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcache"
	"github.com/gogf/gf/v2/util/gconv"
)

type securityService struct{}

func init() {
	RegisterSecurity(&securityService{})
}

// RateLimitConfig 限流配置
type RateLimitConfig struct {
	MaxRequests int           `json:"maxRequests"` // 最大请求数
	Window      time.Duration `json:"window"`      // 时间窗口
	BlockTime   time.Duration `json:"blockTime"`   // 阻塞时间
}

// SecurityConfig 安全配置
type SecurityConfig struct {
	RateLimit   map[string]RateLimitConfig `json:"rateLimit"`
	IPWhitelist []string                   `json:"ipWhitelist"`
	IPBlacklist []string                   `json:"ipBlacklist"`
}

// 默认安全配置
var defaultSecurityConfig = SecurityConfig{
	RateLimit: map[string]RateLimitConfig{
		"api":      {MaxRequests: 100, Window: time.Minute, BlockTime: time.Minute * 5},
		"login":    {MaxRequests: 5, Window: time.Minute, BlockTime: time.Minute * 15},
		"register": {MaxRequests: 3, Window: time.Hour, BlockTime: time.Hour},
		"upload":   {MaxRequests: 10, Window: time.Minute, BlockTime: time.Minute * 10},
	},
	IPWhitelist: []string{},
	IPBlacklist: []string{},
}

// CheckRateLimit 检查限流
func (s *securityService) CheckRateLimit(ctx context.Context, key string, identifier string, limitType string) (bool, error) {
	config, exists := defaultSecurityConfig.RateLimit[limitType]
	if !exists {
		config = defaultSecurityConfig.RateLimit["api"] // 默认使用API限流配置
	}

	cacheKey := fmt.Sprintf("rate_limit:%s:%s", limitType, identifier)

	// 检查是否被阻塞
	blockKey := fmt.Sprintf("rate_limit_block:%s:%s", limitType, identifier)
	blocked, err := gcache.Contains(ctx, blockKey)
	if err == nil && blocked {
		g.Log().Warning(ctx, fmt.Sprintf("请求被限流阻塞: %s", identifier))
		return false, fmt.Errorf("请求过于频繁，请稍后再试")
	}

	// 获取当前请求计数
	count, err := gcache.GetOrSetFunc(ctx, cacheKey, func(ctx context.Context) (interface{}, error) {
		return 1, nil
	}, config.Window)
	if err != nil {
		g.Log().Error(ctx, "获取缓存失败:", err)
		return true, nil // 缓存失败时允许请求
	}

	currentCount := gconv.Int(count)

	if currentCount > config.MaxRequests {
		// 触发限流，设置阻塞
		gcache.Set(ctx, blockKey, true, config.BlockTime)
		g.Log().Warning(ctx, fmt.Sprintf("触发限流: %s, 当前请求数: %d", identifier, currentCount))
		return false, fmt.Errorf("请求过于频繁，已被暂时阻塞")
	}

	// 增加计数
	gcache.Set(ctx, cacheKey, currentCount+1, config.Window)

	return true, nil
}

// CheckIPSecurity 检查IP安全性
func (s *securityService) CheckIPSecurity(ctx context.Context, ip string) (bool, error) {
	// 检查IP黑名单
	for _, blackIP := range defaultSecurityConfig.IPBlacklist {
		if s.matchIP(ip, blackIP) {
			g.Log().Warning(ctx, fmt.Sprintf("IP在黑名单中: %s", ip))
			return false, fmt.Errorf("访问被拒绝")
		}
	}

	// 如果有白名单，检查是否在白名单中
	if len(defaultSecurityConfig.IPWhitelist) > 0 {
		inWhitelist := false
		for _, whiteIP := range defaultSecurityConfig.IPWhitelist {
			if s.matchIP(ip, whiteIP) {
				inWhitelist = true
				break
			}
		}
		if !inWhitelist {
			g.Log().Warning(ctx, fmt.Sprintf("IP不在白名单中: %s", ip))
			return false, fmt.Errorf("访问被拒绝")
		}
	}

	return true, nil
}

// ValidateInput 输入验证
func (s *securityService) ValidateInput(ctx context.Context, input string, validationType string) (bool, error) {
	switch validationType {
	case "username":
		return s.validateUsername(input)
	case "password":
		return s.validatePassword(input)
	case "email":
		return s.validateEmail(input)
	case "phone":
		return s.validatePhone(input)
	case "content":
		return s.validateContent(input)
	default:
		return s.validateGeneral(input)
	}
}

// SanitizeInput 输入清理
func (s *securityService) SanitizeInput(ctx context.Context, input string) string {
	// 移除潜在的恶意字符
	input = strings.TrimSpace(input)

	// 移除HTML标签
	re := regexp.MustCompile(`<[^>]*>`)
	input = re.ReplaceAllString(input, "")

	// 移除SQL注入关键字
	sqlKeywords := []string{
		"SELECT", "INSERT", "UPDATE", "DELETE", "DROP", "CREATE", "ALTER",
		"UNION", "OR", "AND", "WHERE", "FROM", "JOIN", "EXEC", "EXECUTE",
	}

	for _, keyword := range sqlKeywords {
		re := regexp.MustCompile(`(?i)\b` + keyword + `\b`)
		input = re.ReplaceAllString(input, "")
	}

	// 移除脚本标签
	re = regexp.MustCompile(`(?i)<script[^>]*>.*?</script>`)
	input = re.ReplaceAllString(input, "")

	return input
}

// GenerateSecureToken 生成安全令牌
func (s *securityService) GenerateSecureToken(ctx context.Context, data string) string {
	timestamp := time.Now().Unix()
	source := fmt.Sprintf("%s_%d_%s", data, timestamp, "sanxianren_secret")

	hash := md5.Sum([]byte(source))
	return fmt.Sprintf("%x", hash)
}

// ValidateToken 验证令牌
func (s *securityService) ValidateToken(ctx context.Context, token string, data string, maxAge time.Duration) bool {
	// 简化实现：检查令牌格式
	if len(token) != 32 {
		return false
	}

	// 实际应该包含时间戳验证和数据验证
	// 这里简化处理
	return true
}

// LogSecurityEvent 记录安全事件
func (s *securityService) LogSecurityEvent(ctx context.Context, eventType string, details map[string]interface{}) {
	logData := g.Map{
		"event_type": eventType,
		"timestamp":  time.Now(),
		"details":    details,
	}

	g.Log().Warning(ctx, fmt.Sprintf("安全事件: %s, 详情: %+v", eventType, logData))

	// 这里可以集成安全监控系统
	// 例如：发送到SIEM系统、告警系统等
}

// 私有方法

// matchIP 匹配IP地址（支持CIDR格式）
func (s *securityService) matchIP(ip string, pattern string) bool {
	// 简化实现：精确匹配
	// 实际应该支持CIDR格式匹配
	return ip == pattern
}

// validateUsername 验证用户名
func (s *securityService) validateUsername(username string) (bool, error) {
	if len(username) < 3 || len(username) > 20 {
		return false, fmt.Errorf("用户名长度必须在3-20个字符之间")
	}

	// 只允许字母、数字、下划线
	re := regexp.MustCompile(`^[a-zA-Z0-9_]+$`)
	if !re.MatchString(username) {
		return false, fmt.Errorf("用户名只能包含字母、数字和下划线")
	}

	return true, nil
}

// validatePassword 验证密码
func (s *securityService) validatePassword(password string) (bool, error) {
	if len(password) < 8 || len(password) > 50 {
		return false, fmt.Errorf("密码长度必须在8-50个字符之间")
	}

	// 检查密码强度
	hasUpper := regexp.MustCompile(`[A-Z]`).MatchString(password)
	hasLower := regexp.MustCompile(`[a-z]`).MatchString(password)
	hasNumber := regexp.MustCompile(`[0-9]`).MatchString(password)
	hasSpecial := regexp.MustCompile(`[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\?]`).MatchString(password)

	strength := 0
	if hasUpper {
		strength++
	}
	if hasLower {
		strength++
	}
	if hasNumber {
		strength++
	}
	if hasSpecial {
		strength++
	}

	if strength < 3 {
		return false, fmt.Errorf("密码强度不足，需要包含大写字母、小写字母、数字、特殊字符中的至少3种")
	}

	return true, nil
}

// validateEmail 验证邮箱
func (s *securityService) validateEmail(email string) (bool, error) {
	re := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	if !re.MatchString(email) {
		return false, fmt.Errorf("邮箱格式不正确")
	}
	return true, nil
}

// validatePhone 验证手机号
func (s *securityService) validatePhone(phone string) (bool, error) {
	re := regexp.MustCompile(`^1[3-9]\d{9}$`)
	if !re.MatchString(phone) {
		return false, fmt.Errorf("手机号格式不正确")
	}
	return true, nil
}

// validateContent 验证内容
func (s *securityService) validateContent(content string) (bool, error) {
	if len(content) > 10000 {
		return false, fmt.Errorf("内容长度不能超过10000个字符")
	}

	// 检查敏感词
	sensitiveWords := []string{
		"敏感词1", "敏感词2", // 这里应该从配置文件或数据库加载
	}

	contentLower := strings.ToLower(content)
	for _, word := range sensitiveWords {
		if strings.Contains(contentLower, strings.ToLower(word)) {
			return false, fmt.Errorf("内容包含敏感词汇")
		}
	}

	return true, nil
}

// validateGeneral 通用验证
func (s *securityService) validateGeneral(input string) (bool, error) {
	if len(input) > 1000 {
		return false, fmt.Errorf("输入长度不能超过1000个字符")
	}

	// 检查是否包含恶意字符
	maliciousPatterns := []string{
		`<script`,
		`javascript:`,
		`vbscript:`,
		`onload=`,
		`onerror=`,
		`onclick=`,
	}

	inputLower := strings.ToLower(input)
	for _, pattern := range maliciousPatterns {
		if strings.Contains(inputLower, pattern) {
			return false, fmt.Errorf("输入包含不安全内容")
		}
	}

	return true, nil
}
