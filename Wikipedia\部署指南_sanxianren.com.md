# 天南地北三线人 - www.sanxianren.com 部署指南

## 🎯 部署目标

将现代化的Wikipedia首页部署到 **www.sanxianren.com**，完全替换传统的MediaWiki首页，提供现代化的用户体验。

## 📋 部署清单

### ✅ 已完成的文件
- `modern.php` - 现代化首页PHP文件
- `frontend/` - 前端资源目录
- `api/sanxianren.php` - API接口
- `LocalSettings.custom.php` - MediaWiki自定义配置
- `.htaccess` - Apache重写规则
- 完整的CSS、JavaScript和数据文件

## 🚀 部署步骤

### 1. 文件上传

将以下文件上传到您的Web服务器根目录：

```bash
# 上传到 www.sanxianren.com 的根目录
/var/www/html/sanxianren.com/
├── modern.php                    # 现代化首页
├── frontend/                     # 前端资源
├── api/                         # API接口
├── LocalSettings.custom.php     # 自定义配置
└── .htaccess                    # 重写规则
```

### 2. 配置MediaWiki

在您的 `LocalSettings.php` 文件末尾添加：

```php
// 包含天南地北三线人自定义配置
if (file_exists(__DIR__ . '/LocalSettings.custom.php')) {
    require_once __DIR__ . '/LocalSettings.custom.php';
}
```

### 3. 设置文件权限

```bash
# 设置正确的文件权限
chmod 644 modern.php
chmod 644 LocalSettings.custom.php
chmod 644 .htaccess
chmod -R 644 frontend/
chmod -R 644 api/

# 设置目录权限
chmod 755 frontend/
chmod 755 api/
chmod 755 frontend/assets/
chmod 755 frontend/assets/css/
chmod 755 frontend/assets/js/

# 如果需要，设置所有者
chown -R www-data:www-data /var/www/html/sanxianren.com/
```

## 🌐 URL访问规则

### 现代化首页访问
- **主域名**: `https://www.sanxianren.com/` → 自动显示现代化首页
- **直接访问**: `https://www.sanxianren.com/modern.php`
- **分类浏览**: `https://www.sanxianren.com/?category=sanxianren`
- **搜索功能**: `https://www.sanxianren.com/?search=关键词`

### 传统Wiki访问
- **传统首页**: `https://www.sanxianren.com/index.php?classic=1`
- **特定页面**: `https://www.sanxianren.com/index.php?title=页面名称`
- **管理后台**: `https://www.sanxianren.com/index.php?title=Special:SpecialPages`

### API接口
- **基础API**: `https://www.sanxianren.com/api/sanxianren.php`
- **RESTful API**: `https://www.sanxianren.com/api/v1/sanxianren/articles`

## ⚙️ Apache虚拟主机配置

在您的Apache配置中添加：

```apache
<VirtualHost *:80>
    ServerName www.sanxianren.com
    ServerAlias sanxianren.com
    DocumentRoot /var/www/html/sanxianren.com
    
    # 重定向HTTP到HTTPS
    RewriteEngine On
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
</VirtualHost>

<VirtualHost *:443>
    ServerName www.sanxianren.com
    ServerAlias sanxianren.com
    DocumentRoot /var/www/html/sanxianren.com
    
    # SSL配置
    SSLEngine on
    SSLCertificateFile /path/to/your/certificate.crt
    SSLCertificateKeyFile /path/to/your/private.key
    SSLCertificateChainFile /path/to/your/chain.crt
    
    # 日志配置
    ErrorLog ${APACHE_LOG_DIR}/sanxianren_error.log
    CustomLog ${APACHE_LOG_DIR}/sanxianren_access.log combined
    
    # PHP配置
    php_value upload_max_filesize 100M
    php_value post_max_size 100M
    php_value memory_limit 256M
    php_value max_execution_time 300
    
    # 安全头部
    Header always set X-Frame-Options "SAMEORIGIN"
    Header always set X-Content-Type-Options "nosniff"
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
</VirtualHost>
```

## 🔧 Nginx配置（可选）

如果使用Nginx，配置如下：

```nginx
server {
    listen 80;
    server_name www.sanxianren.com sanxianren.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name www.sanxianren.com sanxianren.com;
    root /var/www/html/sanxianren.com;
    index modern.php index.php index.html;
    
    # SSL配置
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    # 默认重定向到现代化首页
    location = / {
        try_files $uri /modern.php;
    }
    
    # 现代化首页
    location ~ ^/modern\.php$ {
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        fastcgi_index modern.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    }
    
    # API接口
    location ~ ^/api/v1/sanxianren/(.*)$ {
        try_files $uri /api/sanxianren.php?action=$1;
    }
    
    # 前端静态资源
    location /frontend/ {
        expires 1M;
        add_header Cache-Control "public, immutable";
    }
    
    # MediaWiki PHP处理
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    }
}
```

## 🗄️ 数据库配置

确保您的MediaWiki数据库配置正确：

```php
// 在LocalSettings.php中确认数据库配置
$wgDBserver = "localhost";
$wgDBname = "sanxianren_wiki";
$wgDBuser = "wiki_user";
$wgDBpassword = "your_password";
$wgDBtype = "mysql";
```

## 🔍 测试部署

### 1. 基础功能测试
```bash
# 测试现代化首页
curl -I https://www.sanxianren.com/

# 测试API接口
curl https://www.sanxianren.com/api/sanxianren.php?action=categories

# 测试传统Wiki
curl -I https://www.sanxianren.com/index.php?classic=1
```

### 2. 浏览器测试
- 访问 `https://www.sanxianren.com/` 应显示现代化首页
- 点击导航栏各个栏目应正常工作
- 搜索功能应正常工作
- 移动端访问应显示响应式布局

### 3. 性能测试
- 使用Google PageSpeed Insights测试页面性能
- 检查图片是否正确懒加载
- 验证Gzip压缩是否生效

## 🛠️ 故障排除

### 常见问题

1. **页面显示空白**
   - 检查PHP错误日志
   - 确认文件权限正确
   - 验证.htaccess语法

2. **CSS/JS加载失败**
   - 检查文件路径是否正确
   - 确认CDN资源可访问
   - 验证缓存设置

3. **API接口报错**
   - 检查数据库连接
   - 确认MediaWiki配置正确
   - 查看API错误日志

4. **重定向循环**
   - 检查.htaccess重写规则
   - 确认Apache mod_rewrite已启用
   - 验证虚拟主机配置

### 日志查看
```bash
# Apache错误日志
tail -f /var/log/apache2/sanxianren_error.log

# PHP错误日志
tail -f /var/log/php/error.log

# 访问日志
tail -f /var/log/apache2/sanxianren_access.log
```

## 🎉 部署完成

部署完成后，您的网站将具备以下功能：

### ✅ 现代化首页
- 访问 `www.sanxianren.com` 直接显示现代化首页
- 瀑布流布局展示内容
- 响应式设计适配所有设备

### ✅ 完整导航
- **三线人** - 三线建设者人物故事
- **三线厂** - 三线建设工厂企业
- **故事馆** - 三线建设历史故事
- **口述历史** - 亲历者口述回忆
- **遗址馆** - 三线建设历史遗址

### ✅ 智能搜索
- 实时搜索建议
- 分类筛选功能
- 全文搜索支持

### ✅ 用户功能
- 文章收藏
- 内容分享
- 用户中心
- 管理后台访问

## 📞 技术支持

如果在部署过程中遇到问题：

1. 检查本文档的故障排除部分
2. 查看服务器错误日志
3. 确认所有文件已正确上传
4. 验证文件权限设置

---

**🎊 恭喜！您的现代化Wikipedia首页已成功部署到 www.sanxianren.com！**
