/*
* @desc:用户信息同步服务
* @company:山东傲英网络科技股份有限公司
* @Author: AI Assistant
* @Date: 2025/01/22
 */

package service

import (
	"context"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/crypto/gmd5"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/tiger1103/gfast/v3/internal/app/sanxianren/model"
	systemService "github.com/tiger1103/gfast/v3/internal/app/system/service"
)

// IUserSyncService 用户同步服务接口
type IUserSyncService interface {
	// SyncUserFromMediaWiki 从MediaWiki同步用户信息
	SyncUserFromMediaWiki(ctx context.Context, mediaWikiUser *model.MediaWikiUserInfo, gfastUserId uint64) (*UserSyncResult, error)

	// CreateGfastUser 创建gfast用户
	CreateGfastUser(ctx context.Context, mediaWikiUser *model.MediaWikiUserInfo) (*UserSyncResult, error)

	// UpdateGfastUser 更新gfast用户信息
	UpdateGfastUser(ctx context.Context, gfastUserId uint64, mediaWikiUser *model.MediaWikiUserInfo) error

	// CreateMediaWikiUser 在MediaWiki中创建用户
	CreateMediaWikiUser(ctx context.Context, gfastUser *GfastUserInfo, accessToken string) error

	// GetUserMapping 获取用户映射关系
	GetUserMapping(ctx context.Context, gfastUserId uint64) (*UserMapping, error)

	// SaveUserMapping 保存用户映射关系
	SaveUserMapping(ctx context.Context, mapping *UserMapping) error

	// DeleteUserMapping 删除用户映射关系
	DeleteUserMapping(ctx context.Context, gfastUserId uint64) error

	// GenerateGfastToken 生成gfast系统令牌
	GenerateGfastToken(ctx context.Context, userInfo *GfastUserInfo) (string, error)
}

// userSyncService 用户同步服务实现
type userSyncService struct {
	config *model.MediaWikiOAuthConfig
}

// UserSyncService 用户同步服务实例
var UserSyncService IUserSyncService

func init() {
	// 初始化用户同步服务
	ctx := context.Background()
	config := &model.MediaWikiOAuthConfig{}

	// 从配置文件加载配置
	if err := g.Cfg().MustGet(ctx, "mediawiki").Struct(config); err != nil {
		g.Log().Error(ctx, "加载MediaWiki用户同步配置失败:", err)
		// 使用默认配置
		config = getDefaultOAuthConfig()
	}

	UserSyncService = &userSyncService{
		config: config,
	}
}

// 用户同步相关结构体

// UserSyncResult 用户同步结果
type UserSyncResult struct {
	GfastUserId     uint64         `json:"gfast_user_id"`     // gfast用户ID
	MediaWikiUserId int            `json:"mediawiki_user_id"` // MediaWiki用户ID
	Username        string         `json:"username"`          // 用户名
	Email           string         `json:"email"`             // 邮箱
	RealName        string         `json:"real_name"`         // 真实姓名
	IsNewUser       bool           `json:"is_new_user"`       // 是否为新用户
	Token           string         `json:"token"`             // gfast系统令牌
	UserInfo        *GfastUserInfo `json:"user_info"`         // gfast用户信息
}

// UserMapping 用户映射关系
type UserMapping struct {
	Id              uint64    `json:"id"`                // 主键ID
	GfastUserId     uint64    `json:"gfast_user_id"`     // gfast用户ID
	MediaWikiUserId int       `json:"mediawiki_user_id"` // MediaWiki用户ID
	MediaWikiName   string    `json:"mediawiki_name"`    // MediaWiki用户名
	SyncStatus      int       `json:"sync_status"`       // 同步状态：1-已同步，2-同步失败
	LastSyncAt      time.Time `json:"last_sync_at"`      // 最后同步时间
	CreatedAt       time.Time `json:"created_at"`        // 创建时间
	UpdatedAt       time.Time `json:"updated_at"`        // 更新时间
}

// GfastUserInfo gfast用户信息
type GfastUserInfo struct {
	Id       uint64 `json:"id"`        // 用户ID
	Username string `json:"username"`  // 用户名
	Email    string `json:"email"`     // 邮箱
	RealName string `json:"real_name"` // 真实姓名
	Phone    string `json:"phone"`     // 手机号
	Status   int    `json:"status"`    // 状态
	Type     string `json:"type"`      // 用户类型
}

// SyncUserFromMediaWiki 从MediaWiki同步用户信息
func (s *userSyncService) SyncUserFromMediaWiki(ctx context.Context, mediaWikiUser *model.MediaWikiUserInfo, gfastUserId uint64) (*UserSyncResult, error) {
	// 检查是否已存在映射关系
	mapping, err := s.GetUserMapping(ctx, gfastUserId)
	if err != nil && !gerror.HasStack(err) {
		// 如果不存在映射关系，创建新的映射
		mapping = &UserMapping{
			GfastUserId:     gfastUserId,
			MediaWikiUserId: mediaWikiUser.Id,
			MediaWikiName:   mediaWikiUser.Name,
			SyncStatus:      1,
			LastSyncAt:      time.Now(),
			CreatedAt:       time.Now(),
			UpdatedAt:       time.Now(),
		}

		if err := s.SaveUserMapping(ctx, mapping); err != nil {
			return nil, gerror.Wrap(err, "保存用户映射关系失败")
		}
	}

	// 更新gfast用户信息
	if err := s.UpdateGfastUser(ctx, gfastUserId, mediaWikiUser); err != nil {
		return nil, gerror.Wrap(err, "更新gfast用户信息失败")
	}

	// 获取更新后的gfast用户信息
	gfastUser, err := s.getGfastUserById(ctx, gfastUserId)
	if err != nil {
		return nil, gerror.Wrap(err, "获取gfast用户信息失败")
	}

	// 生成gfast系统令牌
	token, err := s.GenerateGfastToken(ctx, gfastUser)
	if err != nil {
		return nil, gerror.Wrap(err, "生成gfast令牌失败")
	}

	return &UserSyncResult{
		GfastUserId:     gfastUserId,
		MediaWikiUserId: mediaWikiUser.Id,
		Username:        gfastUser.Username,
		Email:           gfastUser.Email,
		RealName:        gfastUser.RealName,
		IsNewUser:       false,
		Token:           token,
		UserInfo:        gfastUser,
	}, nil
}

// CreateGfastUser 创建gfast用户
func (s *userSyncService) CreateGfastUser(ctx context.Context, mediaWikiUser *model.MediaWikiUserInfo) (*UserSyncResult, error) {
	// 检查用户名是否已存在
	existingUser, _ := s.getGfastUserByUsername(ctx, mediaWikiUser.Name)
	if existingUser != nil {
		return nil, gerror.New("用户名已存在")
	}

	// 检查邮箱是否已存在
	if mediaWikiUser.Email != "" {
		existingUser, _ = s.getGfastUserByEmail(ctx, mediaWikiUser.Email)
		if existingUser != nil {
			return nil, gerror.New("邮箱已存在")
		}
	}

	// 生成用户名（如果配置了前缀）
	username := mediaWikiUser.Name
	if s.config.UserSync.UsernamePrefix != "" {
		username = s.config.UserSync.UsernamePrefix + mediaWikiUser.Name
	}

	// 生成随机密码
	password := s.generateRandomPassword()

	// 创建gfast用户
	gfastUser := &GfastUserInfo{
		Username: username,
		Email:    mediaWikiUser.Email,
		RealName: mediaWikiUser.RealName,
		Status:   1, // 启用状态
		Type:     "oauth_user",
	}

	// 插入用户数据
	userId, err := s.insertGfastUser(ctx, gfastUser, password)
	if err != nil {
		return nil, gerror.Wrap(err, "创建gfast用户失败")
	}

	gfastUser.Id = userId

	// 创建用户映射关系
	mapping := &UserMapping{
		GfastUserId:     userId,
		MediaWikiUserId: mediaWikiUser.Id,
		MediaWikiName:   mediaWikiUser.Name,
		SyncStatus:      1,
		LastSyncAt:      time.Now(),
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	if err := s.SaveUserMapping(ctx, mapping); err != nil {
		g.Log().Warning(ctx, "保存用户映射关系失败:", err)
	}

	// 生成gfast系统令牌
	token, err := s.GenerateGfastToken(ctx, gfastUser)
	if err != nil {
		return nil, gerror.Wrap(err, "生成gfast令牌失败")
	}

	return &UserSyncResult{
		GfastUserId:     userId,
		MediaWikiUserId: mediaWikiUser.Id,
		Username:        gfastUser.Username,
		Email:           gfastUser.Email,
		RealName:        gfastUser.RealName,
		IsNewUser:       true,
		Token:           token,
		UserInfo:        gfastUser,
	}, nil
}

// UpdateGfastUser 更新gfast用户信息
func (s *userSyncService) UpdateGfastUser(ctx context.Context, gfastUserId uint64, mediaWikiUser *model.MediaWikiUserInfo) error {
	// 构建更新数据
	updateData := g.Map{}

	// 根据字段映射配置更新用户信息
	if s.config.UserSync.FieldMapping["email"] != "" && mediaWikiUser.Email != "" {
		updateData[s.config.UserSync.FieldMapping["email"]] = mediaWikiUser.Email
	}

	if s.config.UserSync.FieldMapping["realname"] != "" && mediaWikiUser.RealName != "" {
		// 使用nickname字段存储真实姓名
		updateData["nickname"] = mediaWikiUser.RealName
	}

	updateData["updated_at"] = time.Now()

	// 执行更新
	_, err := g.DB().Model("sx_users").Where("id", gfastUserId).Data(updateData).Update()
	if err != nil {
		return gerror.Wrap(err, "更新用户信息失败")
	}

	return nil
}

// CreateMediaWikiUser 在MediaWiki中创建用户
func (s *userSyncService) CreateMediaWikiUser(ctx context.Context, gfastUser *GfastUserInfo, accessToken string) error {
	// 这个功能需要MediaWiki支持用户创建API
	// 由于MediaWiki的用户创建通常需要管理员权限，这里只是一个示例实现
	g.Log().Info(ctx, "创建MediaWiki用户:", gfastUser.Username)

	// 实际实现需要调用MediaWiki的用户创建API
	// 这里暂时返回成功
	return nil
}

// GetUserMapping 获取用户映射关系
func (s *userSyncService) GetUserMapping(ctx context.Context, gfastUserId uint64) (*UserMapping, error) {
	var mapping UserMapping
	err := g.DB().Model("oauth_user_mapping").Where("gfast_user_id", gfastUserId).Scan(&mapping)
	if err != nil {
		return nil, gerror.Wrap(err, "获取用户映射关系失败")
	}

	if mapping.Id == 0 {
		return nil, gerror.New("用户映射关系不存在")
	}

	return &mapping, nil
}

// SaveUserMapping 保存用户映射关系
func (s *userSyncService) SaveUserMapping(ctx context.Context, mapping *UserMapping) error {
	if mapping.Id == 0 {
		// 插入新记录
		result, err := g.DB().Model("oauth_user_mapping").Data(mapping).Insert()
		if err != nil {
			return gerror.Wrap(err, "插入用户映射关系失败")
		}

		id, err := result.LastInsertId()
		if err != nil {
			return gerror.Wrap(err, "获取插入ID失败")
		}

		mapping.Id = uint64(id)
	} else {
		// 更新现有记录
		mapping.UpdatedAt = time.Now()
		_, err := g.DB().Model("oauth_user_mapping").Where("id", mapping.Id).Data(mapping).Update()
		if err != nil {
			return gerror.Wrap(err, "更新用户映射关系失败")
		}
	}

	return nil
}

// DeleteUserMapping 删除用户映射关系
func (s *userSyncService) DeleteUserMapping(ctx context.Context, gfastUserId uint64) error {
	_, err := g.DB().Model("oauth_user_mapping").Where("gfast_user_id", gfastUserId).Delete()
	if err != nil {
		return gerror.Wrap(err, "删除用户映射关系失败")
	}

	return nil
}

// GenerateGfastToken 生成gfast系统令牌
func (s *userSyncService) GenerateGfastToken(ctx context.Context, userInfo *GfastUserInfo) (string, error) {
	// 构建用户数据
	userData := map[string]interface{}{
		"id":       userInfo.Id,
		"username": userInfo.Username,
		"email":    userInfo.Email,
		"type":     userInfo.Type,
	}

	// 生成符合gfast-token要求的key
	key := fmt.Sprintf("oauth_user_%d_%d_%s", userInfo.Id, time.Now().Unix(), "oauth_token_key_suffix")

	// 使用系统token服务生成token
	token, err := systemService.GfToken().GenerateToken(ctx, key, userData)
	if err != nil {
		return "", gerror.Wrap(err, "生成token失败")
	}

	return token, nil
}

// 私有辅助方法

// getGfastUserById 根据ID获取gfast用户
func (s *userSyncService) getGfastUserById(ctx context.Context, userId uint64) (*GfastUserInfo, error) {
	var user GfastUserInfo
	err := g.DB().Model("sx_users").Where("id", userId).Scan(&user)
	if err != nil {
		return nil, gerror.Wrap(err, "获取用户信息失败")
	}

	if user.Id == 0 {
		return nil, gerror.New("用户不存在")
	}

	return &user, nil
}

// getGfastUserByUsername 根据用户名获取gfast用户
func (s *userSyncService) getGfastUserByUsername(ctx context.Context, username string) (*GfastUserInfo, error) {
	var user GfastUserInfo
	err := g.DB().Model("sx_users").Where("username", username).Scan(&user)
	if err != nil {
		return nil, gerror.Wrap(err, "获取用户信息失败")
	}

	if user.Id == 0 {
		return nil, nil // 用户不存在，返回nil而不是错误
	}

	return &user, nil
}

// getGfastUserByEmail 根据邮箱获取gfast用户
func (s *userSyncService) getGfastUserByEmail(ctx context.Context, email string) (*GfastUserInfo, error) {
	var user GfastUserInfo
	err := g.DB().Model("sx_users").Where("email", email).Scan(&user)
	if err != nil {
		return nil, gerror.Wrap(err, "获取用户信息失败")
	}

	if user.Id == 0 {
		return nil, nil // 用户不存在，返回nil而不是错误
	}

	return &user, nil
}

// insertGfastUser 插入gfast用户
func (s *userSyncService) insertGfastUser(ctx context.Context, user *GfastUserInfo, password string) (uint64, error) {
	// 加密密码（OAuth用户暂时不需要密码）
	_ = gmd5.MustEncryptString(password)

	// 构建插入数据
	insertData := g.Map{
		"username":   user.Username,
		"nickname":   user.RealName, // 使用nickname字段存储真实姓名
		"email":      user.Email,
		"phone":      user.Phone,
		"avatar":     "", // 默认头像
		"gender":     0,  // 默认性别
		"province":   "", // 默认省份
		"city":       "", // 默认城市
		"address":    "", // 默认地址
		"bio":        "", // 默认简介
		"status":     user.Status,
		"user_type":  1, // 默认用户类型
		"created_at": time.Now(),
		"updated_at": time.Now(),
	}

	// 注意：这里没有设置密码字段，因为OAuth用户不需要密码登录
	// 如果需要设置密码，可以添加：
	// insertData["password"] = encryptedPassword

	// 执行插入
	result, err := g.DB().Model("sx_users").Data(insertData).Insert()
	if err != nil {
		return 0, gerror.Wrap(err, "插入用户数据失败")
	}

	// 获取插入的用户ID
	id, err := result.LastInsertId()
	if err != nil {
		return 0, gerror.Wrap(err, "获取插入用户ID失败")
	}

	return uint64(id), nil
}

// generateRandomPassword 生成随机密码
func (s *userSyncService) generateRandomPassword() string {
	length := s.config.UserSync.PasswordLength
	if length <= 0 {
		length = 16
	}

	bytes := make([]byte, length)
	rand.Read(bytes)
	return base64.URLEncoding.EncodeToString(bytes)[:length]
}
