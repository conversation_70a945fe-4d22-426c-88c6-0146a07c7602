/*
* @desc:三线人服务接口定义
* @company:三线人项目
* @Author: AI Assistant
* @Date: 2024/01/15
 */

package service

import (
	"context"
	"time"

	v1 "github.com/tiger1103/gfast/v3/api/v1/sanxianren"
	"github.com/tiger1103/gfast/v3/internal/app/sanxianren/model/entity"
)

// IBanner 轮播图管理服务接口
type IBanner interface {
	// BannerList 获取轮播图列表
	BannerList(ctx context.Context, req *v1.BannerSearchReq) (res *v1.BannerSearchRes, err error)
	// AddBanner 添加轮播图
	AddBanner(ctx context.Context, req *v1.BannerAddReq) (err error)
	// GetBanner 获取轮播图详情
	GetBanner(ctx context.Context, id uint64) (res *entity.Banner, err error)
	// EditBanner 编辑轮播图
	EditBanner(ctx context.Context, req *v1.BannerEditReq) (err error)
	// DeleteBanner 删除轮播图
	DeleteBanner(ctx context.Context, ids []uint64) (err error)
	// UpdateBannerStatus 更新轮播图状态
	UpdateBannerStatus(ctx context.Context, req *v1.BannerStatusReq) (err error)
	// GetPublicBanners 获取公开的轮播图列表（供前端使用）
	GetPublicBanners(ctx context.Context, req *v1.PublicBannerReq) (res *v1.PublicBannerRes, err error)
	// RecordBannerClick 记录轮播图点击
	RecordBannerClick(ctx context.Context, req *v1.BannerClickReq) (err error)
}

// IContentRecommendation 内容推荐管理服务接口
type IContentRecommendation interface {
	// ContentRecommendationList 获取内容推荐列表
	ContentRecommendationList(ctx context.Context, req *v1.ContentRecommendationSearchReq) (res *v1.ContentRecommendationSearchRes, err error)
	// AddContentRecommendation 添加内容推荐
	AddContentRecommendation(ctx context.Context, req *v1.ContentRecommendationAddReq) (err error)
	// GetContentRecommendation 获取内容推荐详情
	GetContentRecommendation(ctx context.Context, id uint64) (res *entity.ContentRecommendation, err error)
	// EditContentRecommendation 编辑内容推荐
	EditContentRecommendation(ctx context.Context, req *v1.ContentRecommendationEditReq) (err error)
	// DeleteContentRecommendation 删除内容推荐
	DeleteContentRecommendation(ctx context.Context, ids []uint64) (err error)
	// UpdateContentRecommendationStatus 更新内容推荐状态
	UpdateContentRecommendationStatus(ctx context.Context, req *v1.ContentRecommendationStatusReq) (err error)
	// GetPublicContentRecommendations 获取公开的内容推荐列表（供前端使用）
	GetPublicContentRecommendations(ctx context.Context, req *v1.PublicContentRecommendationReq) (res *v1.PublicContentRecommendationRes, err error)
	// RecordContentRecommendationClick 记录内容推荐点击
	RecordContentRecommendationClick(ctx context.Context, req *v1.ContentRecommendationClickReq) (err error)
}

// IRecommendationConfig 推荐配置管理服务接口
type IRecommendationConfig interface {
	// RecommendationConfigList 获取推荐配置列表
	RecommendationConfigList(ctx context.Context, req *v1.RecommendationConfigSearchReq) (res *v1.RecommendationConfigSearchRes, err error)
	// AddRecommendationConfig 添加推荐配置
	AddRecommendationConfig(ctx context.Context, req *v1.RecommendationConfigAddReq) (err error)
	// GetRecommendationConfig 获取推荐配置详情
	GetRecommendationConfig(ctx context.Context, id uint64) (res *entity.RecommendationConfig, err error)
	// EditRecommendationConfig 编辑推荐配置
	EditRecommendationConfig(ctx context.Context, req *v1.RecommendationConfigEditReq) (err error)
	// DeleteRecommendationConfig 删除推荐配置
	DeleteRecommendationConfig(ctx context.Context, ids []uint64) (err error)
}

// IUserProfileService 用户个人中心服务接口
type IUserProfileService interface {
	// GetUserProfile 获取用户个人资料
	GetUserProfile(ctx context.Context, userId uint64) (*v1.UserProfileRes, error)

	// EditUserProfile 编辑用户个人资料
	EditUserProfile(ctx context.Context, userId uint64, req *v1.EditUserProfileReq) error

	// GetUserSubmissions 获取用户投稿列表
	GetUserSubmissions(ctx context.Context, userId uint64, req *v1.UserSubmissionsReq) (*v1.UserSubmissionsRes, error)

	// GetUserSubmissionStats 获取用户投稿统计
	GetUserSubmissionStats(ctx context.Context, userId uint64) (*v1.UserSubmissionStatsRes, error)

	// GetUserVipInfo 获取用户VIP信息
	GetUserVipInfo(ctx context.Context, userId uint64) (*v1.UserVipInfoRes, error)

	// GetUserBadges 获取用户徽章列表
	GetUserBadges(ctx context.Context, userId uint64) (*v1.UserBadgesRes, error)
}

// IUserCollectionsService 用户收藏服务接口
type IUserCollectionsService interface {
	// AddCollection 添加收藏
	AddCollection(ctx context.Context, userId uint64, req *v1.AddCollectionReq) error

	// RemoveCollection 取消收藏
	RemoveCollection(ctx context.Context, userId uint64, req *v1.RemoveCollectionReq) error

	// GetCollections 获取收藏列表
	GetCollections(ctx context.Context, userId uint64, req *v1.GetCollectionsReq) (*v1.GetCollectionsRes, error)

	// CheckCollection 检查收藏状态
	CheckCollection(ctx context.Context, userId uint64, req *v1.CheckCollectionReq) (*v1.CheckCollectionRes, error)
}

// IUserHistoryService 用户浏览历史服务接口
type IUserHistoryService interface {
	// AddHistory 添加浏览历史
	AddHistory(ctx context.Context, userId uint64, req *v1.AddHistoryReq) error

	// GetHistory 获取浏览历史
	GetHistory(ctx context.Context, userId uint64, req *v1.GetHistoryReq) (*v1.GetHistoryRes, error)

	// ClearHistory 清空浏览历史
	ClearHistory(ctx context.Context, userId uint64, req *v1.ClearHistoryReq) error
}

// IUserNotificationsService 用户通知服务接口
type IUserNotificationsService interface {
	// GetNotifications 获取通知列表
	GetNotifications(ctx context.Context, userId uint64, req *v1.GetNotificationsReq) (*v1.GetNotificationsRes, error)

	// MarkNotificationRead 标记通知已读
	MarkNotificationRead(ctx context.Context, userId uint64, req *v1.MarkNotificationReadReq) error

	// GetNotificationStats 获取通知统计
	GetNotificationStats(ctx context.Context, userId uint64) (*v1.GetNotificationStatsRes, error)
}

var (
	localBanner                IBanner
	localContentRecommendation IContentRecommendation
	localRecommendationConfig  IRecommendationConfig
	localCategory              ICategory
	localTag                   ITag
	localContentCategoryTag    IContentCategoryTag
	localStatistics            IStatistics
	localUserProfile           IUserProfileService
	localUserCollections       IUserCollectionsService
	localUserHistory           IUserHistoryService
	localUserNotifications     IUserNotificationsService
)

// Banner 获取轮播图服务实例
func Banner() IBanner {
	if localBanner == nil {
		panic("implement not found for interface IBanner, forgot register?")
	}
	return localBanner
}

// RegisterBanner 注册轮播图服务
func RegisterBanner(i IBanner) {
	localBanner = i
}

// ContentRecommendation 获取内容推荐服务实例
func ContentRecommendation() IContentRecommendation {
	if localContentRecommendation == nil {
		panic("implement not found for interface IContentRecommendation, forgot register?")
	}
	return localContentRecommendation
}

// RegisterContentRecommendation 注册内容推荐服务
func RegisterContentRecommendation(i IContentRecommendation) {
	localContentRecommendation = i
}

// RecommendationConfig 获取推荐配置服务实例
func RecommendationConfig() IRecommendationConfig {
	if localRecommendationConfig == nil {
		panic("implement not found for interface IRecommendationConfig, forgot register?")
	}
	return localRecommendationConfig
}

// Category 获取分类服务实例
func Category() ICategory {
	if localCategory == nil {
		panic("implement not found for interface ICategory, forgot register?")
	}
	return localCategory
}

// RegisterCategory 注册分类服务
func RegisterCategory(i ICategory) {
	localCategory = i
}

// Tag 获取标签服务实例
func Tag() ITag {
	if localTag == nil {
		panic("implement not found for interface ITag, forgot register?")
	}
	return localTag
}

// RegisterTag 注册标签服务
func RegisterTag(i ITag) {
	localTag = i
}

// ContentCategoryTag 获取内容分类标签服务实例
func ContentCategoryTag() IContentCategoryTag {
	if localContentCategoryTag == nil {
		panic("implement not found for interface IContentCategoryTag, forgot register?")
	}
	return localContentCategoryTag
}

// RegisterContentCategoryTag 注册内容分类标签服务
func RegisterContentCategoryTag(i IContentCategoryTag) {
	localContentCategoryTag = i
}

// RegisterRecommendationConfig 注册推荐配置服务
func RegisterRecommendationConfig(i IRecommendationConfig) {
	localRecommendationConfig = i
}

// ========== 分类管理服务接口 ==========

// ICategory 分类管理服务接口
type ICategory interface {
	// CategoryList 获取分类列表
	CategoryList(ctx context.Context, req *v1.CategorySearchReq) (res *v1.CategorySearchRes, err error)
	// CategoryAdd 添加分类
	CategoryAdd(ctx context.Context, req *v1.CategoryAddReq) (err error)
	// CategoryGet 获取分类详情
	CategoryGet(ctx context.Context, req *v1.CategoryGetReq) (res *v1.CategoryGetRes, err error)
	// CategoryEdit 编辑分类
	CategoryEdit(ctx context.Context, req *v1.CategoryEditReq) (err error)
	// CategoryDelete 删除分类
	CategoryDelete(ctx context.Context, req *v1.CategoryDeleteReq) (err error)
	// CategoryStatus 更新分类状态
	CategoryStatus(ctx context.Context, req *v1.CategoryStatusReq) (err error)
	// CategoryTree 获取分类树
	CategoryTree(ctx context.Context, req *v1.CategoryTreeReq) (res *v1.CategoryTreeRes, err error)
	// PublicCategoryList 获取公开分类列表
	PublicCategoryList(ctx context.Context, req *v1.PublicCategoryReq) (res *v1.PublicCategoryRes, err error)
}

// ITag 标签管理服务接口
type ITag interface {
	// TagList 获取标签列表
	TagList(ctx context.Context, req *v1.TagSearchReq) (res *v1.TagSearchRes, err error)
	// TagAdd 添加标签
	TagAdd(ctx context.Context, req *v1.TagAddReq) (err error)
	// TagGet 获取标签详情
	TagGet(ctx context.Context, req *v1.TagGetReq) (res *v1.TagGetRes, err error)
	// TagEdit 编辑标签
	TagEdit(ctx context.Context, req *v1.TagEditReq) (err error)
	// TagDelete 删除标签
	TagDelete(ctx context.Context, req *v1.TagDeleteReq) (err error)
	// TagStatus 更新标签状态
	TagStatus(ctx context.Context, req *v1.TagStatusReq) (err error)
	// PublicTagList 获取公开标签列表
	PublicTagList(ctx context.Context, req *v1.PublicTagReq) (res *v1.PublicTagRes, err error)
}

// IContentCategoryTag 内容分类标签关联服务接口
type IContentCategoryTag interface {
	// SetContentCategoryTag 设置内容分类标签
	SetContentCategoryTag(ctx context.Context, req *v1.ContentCategoryTagReq) (err error)
	// GetContentCategoryTag 获取内容分类标签
	GetContentCategoryTag(ctx context.Context, req *v1.ContentCategoryTagGetReq) (res *v1.ContentCategoryTagGetRes, err error)
}

// ========== 统计分析服务接口 ==========

// IStatistics 统计分析服务接口
type IStatistics interface {
	// DashboardOverview 统计概览
	DashboardOverview(ctx context.Context, req *v1.DashboardOverviewReq) (res *v1.DashboardOverviewRes, err error)
	// UserStatistics 用户统计
	UserStatistics(ctx context.Context, req *v1.UserStatisticsReq) (res *v1.UserStatisticsRes, err error)
	// ContentStatistics 内容统计
	ContentStatistics(ctx context.Context, req *v1.ContentStatisticsReq) (res *v1.ContentStatisticsRes, err error)
	// RegionStatistics 地域统计
	RegionStatistics(ctx context.Context, req *v1.RegionStatisticsReq) (res *v1.RegionStatisticsRes, err error)
	// FactoryStatistics 工厂统计
	FactoryStatistics(ctx context.Context, req *v1.FactoryStatisticsReq) (res *v1.FactoryStatisticsRes, err error)
	// HotContent 热门内容
	HotContent(ctx context.Context, req *v1.HotContentReq) (res *v1.HotContentRes, err error)
	// TrendAnalysis 趋势分析
	TrendAnalysis(ctx context.Context, req *v1.TrendAnalysisReq) (res *v1.TrendAnalysisRes, err error)
	// RealTimeStats 实时统计
	RealTimeStats(ctx context.Context, req *v1.RealTimeStatsReq) (res *v1.RealTimeStatsRes, err error)
}

// Statistics 获取统计分析服务实例
func Statistics() IStatistics {
	if localStatistics == nil {
		panic("implement not found for interface IStatistics, forgot register?")
	}
	return localStatistics
}

// RegisterStatistics 注册统计分析服务
func RegisterStatistics(i IStatistics) {
	localStatistics = i
}

// UserProfile 获取用户个人中心服务实例
func UserProfile() IUserProfileService {
	if localUserProfile == nil {
		panic("implement not found for interface IUserProfileService, forgot register?")
	}
	return localUserProfile
}

// RegisterUserProfile 注册用户个人中心服务
func RegisterUserProfile(i IUserProfileService) {
	localUserProfile = i
}

// UserCollections 获取用户收藏服务实例
func UserCollections() IUserCollectionsService {
	if localUserCollections == nil {
		panic("implement not found for interface IUserCollectionsService, forgot register?")
	}
	return localUserCollections
}

// RegisterUserCollections 注册用户收藏服务
func RegisterUserCollections(i IUserCollectionsService) {
	localUserCollections = i
}

// UserHistory 获取用户浏览历史服务实例
func UserHistory() IUserHistoryService {
	if localUserHistory == nil {
		panic("implement not found for interface IUserHistoryService, forgot register?")
	}
	return localUserHistory
}

// RegisterUserHistory 注册用户浏览历史服务
func RegisterUserHistory(i IUserHistoryService) {
	localUserHistory = i
}

// UserNotifications 获取用户通知服务实例
func UserNotifications() IUserNotificationsService {
	if localUserNotifications == nil {
		panic("implement not found for interface IUserNotificationsService, forgot register?")
	}
	return localUserNotifications
}

// RegisterUserNotifications 注册用户通知服务
func RegisterUserNotifications(i IUserNotificationsService) {
	localUserNotifications = i
}

// IUserAuth 用户认证服务接口
type IUserAuth interface {
	// Register 用户注册
	Register(ctx context.Context, req *v1.UserRegisterReq) (res *v1.UserRegisterRes, err error)
	// Login 用户登录
	Login(ctx context.Context, req *v1.UserLoginReq) (res *v1.UserLoginRes, err error)
	// SmsLogin 短信验证码登录
	SmsLogin(ctx context.Context, req *v1.SmsLoginReq) (res *v1.SmsLoginRes, err error)
	// SendSmsCode 发送短信验证码
	SendSmsCode(ctx context.Context, req *v1.SendSmsCodeReq) (res *v1.SendSmsCodeRes, err error)
	// VerifySmsCode 验证短信验证码
	VerifySmsCode(ctx context.Context, phone, code, codeType string) (valid bool, err error)
}

// 用户认证服务实例
var localUserAuth IUserAuth

// UserAuth 获取用户认证服务实例
func UserAuth() IUserAuth {
	if localUserAuth == nil {
		panic("implement not found for interface IUserAuth, forgot register?")
	}
	return localUserAuth
}

// RegisterUserAuth 注册用户认证服务
func RegisterUserAuth(i IUserAuth) {
	localUserAuth = i
}

// IRecommendation 推荐算法服务接口
type IRecommendation interface {
	// GetPersonalizedRecommendations 获取个性化推荐
	GetPersonalizedRecommendations(ctx context.Context, userId uint64, limit int) (*v1.RecommendationsRes, error)
	// UpdateUserBehavior 更新用户行为
	UpdateUserBehavior(ctx context.Context, userId uint64, action string, contentType string, contentId uint64) error
}

// 推荐算法服务实例
var localRecommendation IRecommendation

// Recommendation 获取推荐算法服务实例
func Recommendation() IRecommendation {
	if localRecommendation == nil {
		panic("implement not found for interface IRecommendation, forgot register?")
	}
	return localRecommendation
}

// RegisterRecommendation 注册推荐算法服务
func RegisterRecommendation(i IRecommendation) {
	localRecommendation = i
}

// PushMessage 推送消息结构
type PushMessage struct {
	UserId           uint64                 `json:"userId"`
	NotificationType string                 `json:"notificationType"`
	Title            string                 `json:"title"`
	Content          string                 `json:"content"`
	RelatedType      string                 `json:"relatedType"`
	RelatedId        uint64                 `json:"relatedId"`
	Extra            map[string]interface{} `json:"extra"`
	ScheduleTime     *time.Time             `json:"scheduleTime"`
}

// INotificationPush 消息推送服务接口
type INotificationPush interface {
	// SendNotification 发送通知
	SendNotification(ctx context.Context, message *PushMessage) error
	// SendBatchNotifications 批量发送通知
	SendBatchNotifications(ctx context.Context, messages []*PushMessage) error
	// SendSystemNotification 发送系统通知
	SendSystemNotification(ctx context.Context, userIds []uint64, title, content string) error
	// SendSubmissionNotification 发送投稿相关通知
	SendSubmissionNotification(ctx context.Context, userId uint64, submissionId uint64, status string) error
	// SendInteractionNotification 发送互动通知
	SendInteractionNotification(ctx context.Context, userId uint64, interactionType, contentType string, contentId uint64, fromUserId uint64) error
}

// 消息推送服务实例
var localNotificationPush INotificationPush

// NotificationPush 获取消息推送服务实例
func NotificationPush() INotificationPush {
	if localNotificationPush == nil {
		panic("implement not found for interface INotificationPush, forgot register?")
	}
	return localNotificationPush
}

// RegisterNotificationPush 注册消息推送服务
func RegisterNotificationPush(i INotificationPush) {
	localNotificationPush = i
}

// ISecurity 安全服务接口
type ISecurity interface {
	// CheckRateLimit 检查限流
	CheckRateLimit(ctx context.Context, key string, identifier string, limitType string) (bool, error)
	// CheckIPSecurity 检查IP安全性
	CheckIPSecurity(ctx context.Context, ip string) (bool, error)
	// ValidateInput 输入验证
	ValidateInput(ctx context.Context, input string, validationType string) (bool, error)
	// SanitizeInput 输入清理
	SanitizeInput(ctx context.Context, input string) string
	// GenerateSecureToken 生成安全令牌
	GenerateSecureToken(ctx context.Context, data string) string
	// ValidateToken 验证令牌
	ValidateToken(ctx context.Context, token string, data string, maxAge time.Duration) bool
}

// 安全服务实例
var localSecurity ISecurity

// Security 获取安全服务实例
func Security() ISecurity {
	if localSecurity == nil {
		panic("implement not found for interface ISecurity, forgot register?")
	}
	return localSecurity
}

// RegisterSecurity 注册安全服务
func RegisterSecurity(i ISecurity) {
	localSecurity = i
}

// IPerformance 性能优化服务接口
type IPerformance interface {
	// GetCachedData 获取缓存数据
	GetCachedData(ctx context.Context, cacheType string, key string) (interface{}, bool)
	// SetCachedData 设置缓存数据
	SetCachedData(ctx context.Context, cacheType string, key string, data interface{}) error
	// InvalidateCache 清除缓存
	InvalidateCache(ctx context.Context, cacheType string, key string) error
	// GetOrSetCache 获取或设置缓存
	GetOrSetCache(ctx context.Context, cacheType string, key string, dataFunc func() (interface{}, error)) (interface{}, error)
	// OptimizeQuery 查询优化
	OptimizeQuery(ctx context.Context, queryType string, params map[string]interface{}) map[string]interface{}
	// MonitorPerformance 性能监控
	MonitorPerformance(ctx context.Context, operation string, startTime time.Time)
}

// 性能优化服务实例
var localPerformance IPerformance

// Performance 获取性能优化服务实例
func Performance() IPerformance {
	if localPerformance == nil {
		panic("implement not found for interface IPerformance, forgot register?")
	}
	return localPerformance
}

// RegisterPerformance 注册性能优化服务
func RegisterPerformance(i IPerformance) {
	localPerformance = i
}
