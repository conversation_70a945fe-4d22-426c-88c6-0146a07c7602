<?php
/**
 * 天南地北三线人 - 自定义配置文件
 * 
 * 这个文件包含了自定义的MediaWiki配置，用于集成现代化的前端首页
 * 
 * 使用方法：
 * 1. 将此文件内容添加到 LocalSettings.php 的末尾
 * 2. 或者在 LocalSettings.php 中包含此文件：require_once 'LocalSettings.custom.php';
 */

// 设置网站名称
$wgSitename = "天南地北三线人";

// 设置网站描述
$wgMetaNamespace = "三线建设历史百科";

// 自定义首页
$wgMainPageTitle = "首页";

// 启用现代化皮肤
$wgDefaultSkin = "vector-2022";

// 允许文件上传
$wgEnableUploads = true;
$wgUseImageMagick = true;
$wgImageMagickConvertCommand = "/usr/bin/convert";

// 设置上传文件类型
$wgFileExtensions = array_merge($wgFileExtensions, array(
    'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx',
    'mp3', 'mp4', 'avi', 'mov', 'wmv', 'flv',
    'zip', 'rar', '7z', 'tar', 'gz'
));

// 增加上传文件大小限制
$wgMaxUploadSize = 100 * 1024 * 1024; // 100MB

// 启用扩展
wfLoadExtension('ParserFunctions');
wfLoadExtension('Cite');
wfLoadExtension('CategoryTree');
wfLoadExtension('ImageMap');
wfLoadExtension('InputBox');
wfLoadExtension('Interwiki');
wfLoadExtension('LocalisationUpdate');
wfLoadExtension('Nuke');
wfLoadExtension('PdfHandler');
wfLoadExtension('Poem');
wfLoadExtension('Renameuser');
wfLoadExtension('ReplaceText');
wfLoadExtension('SpamBlacklist');
wfLoadExtension('SyntaxHighlight_GeSHi');
wfLoadExtension('TitleBlacklist');
wfLoadExtension('WikiEditor');

// 自定义命名空间
define("NS_SANXIANREN", 3000);
define("NS_SANXIANREN_TALK", 3001);
define("NS_SANXIANCHANG", 3002);
define("NS_SANXIANCHANG_TALK", 3003);
define("NS_STORYHALL", 3004);
define("NS_STORYHALL_TALK", 3005);
define("NS_ORALHISTORY", 3006);
define("NS_ORALHISTORY_TALK", 3007);
define("NS_HERITAGE", 3008);
define("NS_HERITAGE_TALK", 3009);

$wgExtraNamespaces[NS_SANXIANREN] = "三线人";
$wgExtraNamespaces[NS_SANXIANREN_TALK] = "三线人讨论";
$wgExtraNamespaces[NS_SANXIANCHANG] = "三线厂";
$wgExtraNamespaces[NS_SANXIANCHANG_TALK] = "三线厂讨论";
$wgExtraNamespaces[NS_STORYHALL] = "故事馆";
$wgExtraNamespaces[NS_STORYHALL_TALK] = "故事馆讨论";
$wgExtraNamespaces[NS_ORALHISTORY] = "口述历史";
$wgExtraNamespaces[NS_ORALHISTORY_TALK] = "口述历史讨论";
$wgExtraNamespaces[NS_HERITAGE] = "遗址馆";
$wgExtraNamespaces[NS_HERITAGE_TALK] = "遗址馆讨论";

// 设置命名空间内容模型
$wgNamespaceContentModels[NS_SANXIANREN] = CONTENT_MODEL_WIKITEXT;
$wgNamespaceContentModels[NS_SANXIANCHANG] = CONTENT_MODEL_WIKITEXT;
$wgNamespaceContentModels[NS_STORYHALL] = CONTENT_MODEL_WIKITEXT;
$wgNamespaceContentModels[NS_ORALHISTORY] = CONTENT_MODEL_WIKITEXT;
$wgNamespaceContentModels[NS_HERITAGE] = CONTENT_MODEL_WIKITEXT;

// 自定义用户组和权限
$wgGroupPermissions['sanxianren_editor']['edit'] = true;
$wgGroupPermissions['sanxianren_editor']['createpage'] = true;
$wgGroupPermissions['sanxianren_editor']['upload'] = true;
$wgGroupPermissions['sanxianren_editor']['reupload'] = true;

$wgGroupPermissions['heritage_curator']['edit'] = true;
$wgGroupPermissions['heritage_curator']['createpage'] = true;
$wgGroupPermissions['heritage_curator']['upload'] = true;
$wgGroupPermissions['heritage_curator']['delete'] = true;
$wgGroupPermissions['heritage_curator']['undelete'] = true;

// 自定义首页重定向到现代化页面
$wgHooks['BeforePageDisplay'][] = function($out, $skin) {
    $request = $out->getRequest();
    $title = $out->getTitle();

    // 检查是否是首页访问
    $isMainPage = $title->isMainPage();
    $isIndexAccess = $request->getRequestURL() === '/index.php' ||
                     $request->getRequestURL() === '/' ||
                     $request->getRequestURL() === '/index.php?title=Main_Page';

    // 如果是首页访问且没有特殊参数，重定向到现代化首页
    if (($isMainPage || $isIndexAccess) &&
        !$request->getVal('action') &&
        !$request->getVal('oldid') &&
        !$request->getVal('diff')) {

        // 检查User-Agent，避免搜索引擎爬虫重定向
        $userAgent = $request->getHeader('User-Agent');
        $isBot = preg_match('/bot|crawler|spider|crawling/i', $userAgent);

        if (!$isBot) {
            $modernUrl = '/modern.php';

            // 保留搜索和分类参数
            $params = [];
            if ($request->getVal('search')) {
                $params['search'] = $request->getVal('search');
            }
            if ($request->getVal('category')) {
                $params['category'] = $request->getVal('category');
            }

            if (!empty($params)) {
                $modernUrl .= '?' . http_build_query($params);
            }

            $out->redirect($modernUrl);
            return false;
        }
    }

    return true;
};

// 添加自定义CSS和JS
$wgHooks['BeforePageDisplay'][] = function($out, $skin) {
    // 添加自定义样式
    $out->addInlineStyle('
        .mw-wiki-logo {
            background-image: url("/frontend/assets/images/logo.png");
        }
        
        .vector-menu-tabs .selected a {
            color: #409eff;
        }
        
        .mw-body h1.firstHeading {
            color: #303133;
            border-bottom: 2px solid #409eff;
        }
        
        .mw-content-ltr .mw-parser-output .infobox {
            border: 1px solid #e4e7ed;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }
    ');
    
    // 添加自定义JavaScript
    $out->addInlineScript('
        // 添加返回现代化首页的链接
        $(document).ready(function() {
            if ($("#ca-nstab-main").length) {
                $("#ca-nstab-main").after(
                    "<li id=\"ca-modern-home\"><a href=\"/frontend/index.html\" title=\"返回现代化首页\">现代首页</a></li>"
                );
            }
        });
    ');
    
    return true;
};

// 自定义API端点
$wgHooks['ApiMain::moduleManager'][] = function($moduleManager) {
    $moduleManager->addModule('sanxianren', 'action', [
        'class' => 'ApiSanxianren',
        'factory' => function() {
            return new ApiSanxianren();
        }
    ]);
    return true;
};

// 自定义搜索配置
$wgSearchType = 'CirrusSearch';
$wgCirrusSearchServers = ['localhost'];
$wgSearchSuggestCacheExpiry = 300;

// 启用语义MediaWiki（如果安装了）
if (file_exists("$IP/extensions/SemanticMediaWiki/SemanticMediaWiki.php")) {
    wfLoadExtension('SemanticMediaWiki');
    enableSemantics('sanxianren.wiki');
    
    // 语义属性配置
    $smwgNamespacesWithSemanticLinks[NS_SANXIANREN] = true;
    $smwgNamespacesWithSemanticLinks[NS_SANXIANCHANG] = true;
    $smwgNamespacesWithSemanticLinks[NS_STORYHALL] = true;
    $smwgNamespacesWithSemanticLinks[NS_ORALHISTORY] = true;
    $smwgNamespacesWithSemanticLinks[NS_HERITAGE] = true;
}

// 自定义日志类型
$wgLogTypes[] = 'sanxianren';
$wgLogNames['sanxianren'] = 'sanxianren-log-name';
$wgLogHeaders['sanxianren'] = 'sanxianren-log-header';
$wgLogActions['sanxianren/create'] = 'sanxianren-log-create';
$wgLogActions['sanxianren/edit'] = 'sanxianren-log-edit';
$wgLogActions['sanxianren/delete'] = 'sanxianren-log-delete';

// 缓存配置
$wgMainCacheType = CACHE_ACCEL;
$wgMemCachedServers = [];
$wgSessionCacheType = CACHE_DB;
$wgMessageCacheType = CACHE_ACCEL;
$wgParserCacheType = CACHE_ACCEL;
$wgLanguageConverterCacheType = CACHE_ACCEL;

// 性能优化
$wgEnableSidebarCache = true;
$wgUseFileCache = true;
$wgFileCacheDirectory = "$IP/cache";
$wgShowIPinHeader = false;
$wgDisableCounters = true;
$wgMiserMode = false;

// 安全配置
$wgBlockDisablesLogin = true;
$wgInvalidUsernameCharacters = '@:';
$wgSVGConverter = 'ImageMagick';
$wgSVGConverters['ImageMagick'] = '$path/convert -background white -thumbnail $widthx$height\! $input PNG:$output';

// 自定义错误页面
$wgHooks['BeforePageDisplay'][] = function($out, $skin) {
    if ($out->getTitle()->getText() == '404') {
        $out->addHTML('
            <div style="text-align: center; padding: 60px 20px;">
                <h1 style="font-size: 72px; color: #c0c4cc; margin: 0;">404</h1>
                <h2 style="color: #606266; margin: 20px 0;">页面未找到</h2>
                <p style="color: #909399; margin-bottom: 30px;">您访问的页面不存在或已被删除</p>
                <a href="/frontend/index.html" style="
                    display: inline-block;
                    padding: 12px 24px;
                    background: #409eff;
                    color: white;
                    text-decoration: none;
                    border-radius: 6px;
                    transition: all 0.3s ease;
                ">返回首页</a>
            </div>
        ');
    }
    return true;
};

// 移动端优化
$wgMFAutodetectMobileView = true;
$wgMFMobileHeader = 'X-Subdomain';
$wgMobileUrlTemplate = '%h0.m.%h1.%h2';

// 国际化配置
$wgLanguageCode = 'zh-hans';
$wgLocaltimezone = 'Asia/Shanghai';
date_default_timezone_set($wgLocaltimezone);

// 版权信息
$wgRightsPage = "";
$wgRightsUrl = "https://creativecommons.org/licenses/by-sa/4.0/";
$wgRightsText = "知识共享 署名-相同方式共享 4.0";
$wgRightsIcon = "https://i.creativecommons.org/l/by-sa/4.0/88x31.png";

// 联系信息
$wgEmergencyContact = "<EMAIL>";
$wgPasswordSender = "<EMAIL>";

// 调试配置（生产环境应关闭）
$wgShowExceptionDetails = false;
$wgShowDBErrorBacktrace = false;
$wgShowSQLErrors = false;
$wgDebugLogFile = "";

// 自定义魔术词
$wgHooks['MagicWordwgVariableIDs'][] = function(&$variableIDs) {
    $variableIDs[] = 'sanxianren_count';
    $variableIDs[] = 'sanxianchang_count';
    $variableIDs[] = 'story_count';
    return true;
};

$wgHooks['ParserGetVariableValueSwitch'][] = function(&$parser, &$cache, &$magicWordId, &$ret) {
    switch ($magicWordId) {
        case 'sanxianren_count':
            $ret = '1,234'; // 这里应该从数据库获取实际数量
            break;
        case 'sanxianchang_count':
            $ret = '567';
            break;
        case 'story_count':
            $ret = '890';
            break;
    }
    return true;
};

?>
