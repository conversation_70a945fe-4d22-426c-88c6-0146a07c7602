这个现象九成不是“真的过期”，而是**签发/校验配置不一致**或**时间/存储/拦截器**导致的“假过期”。按下面顺序排查 + 给你一套可直接用的配置与代码。

# 一、5分钟定位 checklist

1. **看 token 里的 exp/iat/nbf**
   登录成功后把 `jwt` 解码（不用密钥即可看到 claim），确认：

   * `exp` > 当前时间（秒）；`iat ≤ now`；`nbf ≤ now`；
   * 单位必须是**秒**（不是毫秒/纳秒）。

2. **统一签发与校验配置**

   * 签发端与中间件用**同一个** `secret/issuer/audience`；
   * 多实例部署请用**同一份环境变量**或配置中心。

3. **时间偏差**

   * 服务器开启 NTP 同步；容器里装 `tzdata` 并设 `TZ=Asia/Shanghai`（UTC 也行，关键是校时）。
   * 验证 `date -u` 与本地时间差 ≤ 2s；中间件设置 `clockSkew`（见下）。

4. **gfast/gToken 的存储**

   * 如果用了 gToken 的内存模式，**多实例或重启**后会读不到会话，表现成“过期”。
   * 改成 **Redis** 存储（见配置）。

5. **UniApp 请求头**

   * 每个请求都带 `Authorization: Bearer <accessToken>`，不要带错前缀或重复 `Bearer`；
   * 登录 Promise 还没落盘就发请求，也会拿到 401。给请求封装**拦截+刷新**（代码见下）。

---

# 二、推荐配置（任选其一）

## 方案A：JWT（推荐）

**config.yaml（gfast）**

```yaml
jwt:
  secret: "replace-with-strong-secret"
  issuer: "three-line"
  accessExpire: 7200        # 秒，2小时
  refreshExpire: 2592000    # 秒，30天
  headerKey: "Authorization"
  tokenPrefix: "Bearer "
  allowClockSkew: 5         # 秒，允许时钟偏差
```

**Go：签发与校验（摘录，使用 golang-jwt v5）**

```go
type Claims struct {
  UID int64 `json:"uid"`
  jwt.RegisteredClaims
}

func issueAccess(uid int64) (string, time.Time, error) {
  now := time.Now()
  exp := now.Add(2 * time.Hour)
  c := Claims{
    UID: uid,
    RegisteredClaims: jwt.RegisteredClaims{
      Issuer:    cfg.JWT.Issuer,
      IssuedAt:  jwt.NewNumericDate(now),
      NotBefore: jwt.NewNumericDate(now.Add(-5 * time.Second)), // 抗抖
      ExpiresAt: jwt.NewNumericDate(exp),
    },
  }
  return jwt.NewWithClaims(jwt.SigningMethodHS256, c).
    SignedString([]byte(cfg.JWT.Secret)), exp, nil
}

// 中间件
func Auth() gin.HandlerFunc {
  return func(c *gin.Context) {
    auth := c.GetHeader(cfg.JWT.HeaderKey)
    if !strings.HasPrefix(auth, cfg.JWT.TokenPrefix) {
      c.AbortWithStatusJSON(401, gin.H{"code": "ERR_NO_TOKEN"})
      return
    }
    tokenStr := strings.TrimPrefix(auth, cfg.JWT.TokenPrefix)
    token, err := jwt.ParseWithClaims(tokenStr, &Claims{}, func(t *jwt.Token) (interface{}, error) {
      return []byte(cfg.JWT.Secret), nil
    }, jwt.WithLeeway(time.Duration(cfg.JWT.AllowClockSkew)*time.Second))
    if err != nil {
      var ve *jwt.ValidationError
      if errors.As(err, &ve) && ve.Errors&jwt.ValidationErrorExpired != 0 {
        c.AbortWithStatusJSON(401, gin.H{"code": "ERR_TOKEN_EXPIRED"})
        return
      }
      c.AbortWithStatusJSON(401, gin.H{"code": "ERR_TOKEN_INVALID"})
      return
    }
    claims := token.Claims.(*Claims)
    c.Set("uid", claims.UID)
    c.Next()
  }
}
```

**刷新接口（摘录）**

```go
// POST /v1/auth/refresh {refreshToken:"..."}
func Refresh(c *gin.Context) {
  // 校验 refreshToken（同 secret，不同过期时间），黑名单/版本号可选
  // 通过则下发新的 accessToken (+可滚动刷新 refreshToken)
}
```

## 方案B：gToken（若你正用它）

**config.yaml**

```yaml
gfToken:
  CacheMode: 2              # 2=Redis，1=内存（禁用）
  CacheKey: "gfast:token:"
  Timeout: 7200             # 秒，存活期
  MaxRefresh: 2592000       # 秒，最大刷新期
  EncryptKey: "32-bytes-strong-secret-xxxxxxxx"
  MultiLogin: true
  AuthFailMsg: "登录已过期，请重新登录"
```

> 多实例 / 重启后“立即过期”的典型原因就是 `CacheMode=1`（内存）或每实例 `EncryptKey` 不一致。

---

# 三、UniApp 通用封装（带自动刷新）

把下面放进你的 `/api/request.js`，替换 `API_BASE` 与 refresh 地址即可。

```js
const API_BASE = 'https://api.yourdomain.com';
let isRefreshing = false;
let queue = [];

function authHeader(h = {}) {
  const t = uni.getStorageSync('accessToken');
  if (t) h.Authorization = `Bearer ${t}`;
  return h;
}

function baseRequest(opts, withAuth = true) {
  return new Promise((resolve, reject) => {
    const header = withAuth ? authHeader(opts.header || {}) : (opts.header || {});
    uni.request({
      ...opts,
      url: opts.url.startsWith('http') ? opts.url : API_BASE + opts.url,
      header,
      success: async (res) => {
        if (res.statusCode === 401 && res.data?.code === 'ERR_TOKEN_EXPIRED') {
          // 只发起一次刷新
          if (!isRefreshing) {
            isRefreshing = true;
            try {
              const rt = uni.getStorageSync('refreshToken');
              const r = await baseRequest({
                url: '/v1/auth/refresh',
                method: 'POST',
                data: { refreshToken: rt }
              }, false);
              uni.setStorageSync('accessToken', r.data.accessToken);
              if (r.data.refreshToken) uni.setStorageSync('refreshToken', r.data.refreshToken);
              queue.forEach(fn => fn());
              queue = [];
            } finally { isRefreshing = false; }
          }
          await new Promise(ok => queue.push(ok));
          // 刷新后重试原请求
          return resolve(baseRequest(opts, true));
        }
        resolve(res);
      },
      fail: reject
    });
  });
}

// 用法：await baseRequest({ url:'/v1/user/profile', method:'GET' });
export default baseRequest;
```

> 小提示
>
> * 登录成功后一定要等 `uni.setStorageSync('accessToken')` 完成再跳转或并发请求。
> * 同步封装 `uploadFile` 也加上 `Authorization` 头。
> * **不要**把 `refreshToken` 放到前端长久存储里太久，设置合理过期。

---

# 四、常见“秒过期”真实成因 & 对策

* **exp 单位错**（用毫秒/纳秒）：立刻被判无效/过期 → 改为**秒**。
* **nbf 设置在未来**：库会报“未生效/过期” → 设置 `nbf=now-5s`。
* **多实例密钥不一致**：签发与校验用不同 `secret` → 使用同一密钥来源。
* **gToken 内存会话**：重启或流量打到其它实例就找不到 → 改 Redis。
* **请求没带 Authorization**：拦截器时序问题 → 按上面的封装处理并串行刷新。
* **服务器时间漂移**：NTP 未同步 → `chrony/ntpd` 保持时钟一致；中间件给 `leeway`。

---

需要的话，我可以把你当前的 **登录接口返回** 和 **中间件代码/配置**贴过来，我直接帮你对标改成上面的“标准版（JWT+刷新）”。
