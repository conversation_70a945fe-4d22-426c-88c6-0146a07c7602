// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// SxUserBadges is the golang structure for table sx_user_badges.
type SxUserBadges struct {
	Id               uint64      `json:"id"               orm:"id"                description:"徽章ID"`
	UserId           uint64      `json:"userId"           orm:"user_id"           description:"用户ID"`
	BadgeType        string      `json:"badgeType"        orm:"badge_type"        description:"徽章类型：contributor-贡献者，storyteller-故事家，collector-收藏家，active-活跃用户"`
	BadgeName        string      `json:"badgeName"        orm:"badge_name"        description:"徽章名称"`
	BadgeIcon        string      `json:"badgeIcon"        orm:"badge_icon"        description:"徽章图标"`
	BadgeDescription string      `json:"badgeDescription" orm:"badge_description" description:"徽章描述"`
	EarnedAt         *gtime.Time `json:"earnedAt"         orm:"earned_at"         description:"获得时间"`
	IsDisplayed      int         `json:"isDisplayed"      orm:"is_displayed"      description:"是否显示：1-显示，0-隐藏"`
}
