2025-09-05T00:04:42.582+08:00 [ERRO] {30b36233e71f6218b06e7c79beea7cb3} C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/library/liberr/err.go:21: SELECT `Id`,`PersonUuid`,`SubmissionId`,`Name`,`Gender`,`BirthDate`,`BirthPlace`,`DeathDate`,`JobTitle`,`Employer`,`ParticipationTime`,`WorkLocation`,`PersonalExperience`,`Contributions`,`Achievements`,`Skills`,`ImportantEvents`,`FamilySituation`,`LaterDevelopment`,`RelatedPhotos`,`RelatedDocuments`,`RelatedOralRecords`,`RelatedPeople`,`References`,`Notes`,`WikiPageId`,`WikiPageTitle`,`Status`,`IsFeatured`,`ViewCount`,`CreatedAt`,`UpdatedAt` FROM `factory_people` WHERE `id`=3 LIMIT 1: Error 1146 (42S02): Table 'sanxian1.factory_people' doesn't exist 
Stack:
1.  github.com/tiger1103/gfast/v3/library/liberr.ErrIsNil
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/library/liberr/err.go:21
2.  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/factoryPeople.(*sFactoryPeople).GetById.func1
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/factoryPeople/factory_people.go:104
3.  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/factoryPeople.(*sFactoryPeople).GetById
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/factoryPeople/factory_people.go:102
4.  github.com/tiger1103/gfast/v3/internal/app/sanxianren/controller.(*factoryPeopleController).Get
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/controller/factory_people.go:29
5.  github.com/tiger1103/gfast/v3/internal/app/system/logic/middleware.(*sMiddleware).Auth
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/system/logic/middleware/middleware.go:77
6.  github.com/tiger1103/gfast-token/gftoken.(*GfToken).authMiddleware
    C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/middleware.go:21
7.  github.com/tiger1103/gfast/v3/internal/app/system/logic/middleware.(*sMiddleware).Ctx
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/system/logic/middleware/middleware.go:62
8.  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCharsetUTF8
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:39
9.  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:30

2025-09-05T00:17:00.482+08:00 [INFO] {00442c07932062184009a323e7905e3f} C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/cmd/cmd.go:25: 
   ____________           __ 
  / ____/ ____/___ ______/ /_
 / / __/ /_  / __ `/ ___/ __/
/ /_/ / __/ / /_/ (__  ) /_  
\____/_/    \__,_/____/\__/   Version: 3.3.7
2025-09-05T00:17:01.340+08:00 [DEBU] C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/library/libWebsocket/init.go:20: start websocket..
2025-09-05T00:45:47.180+08:00 [INFO] {80d7780e25226218e1d0eb7d21fb24f6} C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/cmd/cmd.go:25: 
   ____________           __ 
  / ____/ ____/___ ______/ /_
 / / __/ /_  / __ `/ ___/ __/
/ /_/ / __/ / /_/ (__  ) /_  
\____/_/    \__,_/____/\__/   Version: 3.3.7
2025-09-05T00:45:47.282+08:00 [DEBU] C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/library/libWebsocket/init.go:20: start websocket..
2025-09-05T00:57:31.213+08:00 [INFO] {40e21dfac82262181dbf4a0df296deff} C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/cmd/cmd.go:25: 
   ____________           __ 
  / ____/ ____/___ ______/ /_
 / / __/ /_  / __ `/ ___/ __/
/ /_/ / __/ / /_/ (__  ) /_  
\____/_/    \__,_/____/\__/   Version: 3.3.7
2025-09-05T00:57:31.300+08:00 [DEBU] C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/library/libWebsocket/init.go:20: start websocket..
2025-09-05T00:58:13.511+08:00 [INFO] {dcbe3fd3d22262186dee1a4bccde053d} C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/cmd/cmd.go:25: 
   ____________           __ 
  / ____/ ____/___ ______/ /_
 / / __/ /_  / __ `/ ___/ __/
/ /_/ / __/ / /_/ (__  ) /_  
\____/_/    \__,_/____/\__/   Version: 3.3.7
2025-09-05T00:58:13.572+08:00 [DEBU] C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/library/libWebsocket/init.go:20: start websocket..
2025-09-05T00:58:45.383+08:00 [ERRO] {cc66d83bda22621873ee1a4bd9294e76} C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/library/liberr/err.go:21: SELECT * FROM `factory_submission_stats_view` WHERE (`submission_type`='story') AND (`status`='all') ORDER BY `submission_date` asc,`submission_type` asc,`status` asc: Error 1146 (42S02): Table 'sanxian1.factory_submission_stats_view' doesn't exist 
Stack:
1.  github.com/tiger1103/gfast/v3/library/liberr.ErrIsNil
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/library/liberr/err.go:21
2.  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/submissions.(*sSubmissions).SubmissionStats.func1
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/submissions/submissions.go:755
3.  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/submissions.(*sSubmissions).SubmissionStats
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/submissions/submissions.go:741
4.  github.com/tiger1103/gfast/v3/internal/app/sanxianren/controller.(*storyController).Stats
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/controller/story.go:239
5.  github.com/tiger1103/gfast/v3/internal/app/system/logic/middleware.(*sMiddleware).Ctx
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/system/logic/middleware/middleware.go:47
6.  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCharsetUTF8
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:39
7.  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:30

2025-09-05T01:30:05.069+08:00 [INFO] {d827fbe48f2462182c67337da8ba05c8} C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/cmd/cmd.go:25: 
   ____________           __ 
  / ____/ ____/___ ______/ /_
 / / __/ /_  / __ `/ ___/ __/
/ /_/ / __/ / /_/ (__  ) /_  
\____/_/    \__,_/____/\__/   Version: 3.3.7
2025-09-05T01:30:05.146+08:00 [DEBU] C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/library/libWebsocket/init.go:20: start websocket..
2025-09-05T01:35:27.762+08:00 [INFO] {60c80507db24621809d9b533dcf6a0b4} C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/cmd/cmd.go:25: 
   ____________           __ 
  / ____/ ____/___ ______/ /_
 / / __/ /_  / __ `/ ___/ __/
/ /_/ / __/ / /_/ (__  ) /_  
\____/_/    \__,_/____/\__/   Version: 3.3.7
2025-09-05T01:35:27.949+08:00 [DEBU] C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/library/libWebsocket/init.go:20: start websocket..
2025-09-05T01:41:03.735+08:00 [INFO] {54d8884029256218b85e655899ef053a} C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/cmd/cmd.go:25: 
   ____________           __ 
  / ____/ ____/___ ______/ /_
 / / __/ /_  / __ `/ ___/ __/
/ /_/ / __/ / /_/ (__  ) /_  
\____/_/    \__,_/____/\__/   Version: 3.3.7
2025-09-05T01:41:03.796+08:00 [DEBU] C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/library/libWebsocket/init.go:20: start websocket..
2025-09-05T13:34:12.439+08:00 [INFO] {4c0bd6c5134c6218c85e65586c67f63e} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: token is invalid
2025-09-05T13:41:36.518+08:00 [INFO] {90573c2b7b4c6218def30355a50876ea} C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/cmd/cmd.go:25: 
   ____________           __ 
  / ____/ ____/___ ______/ /_
 / / __/ /_  / __ `/ ___/ __/
/ /_/ / __/ / /_/ (__  ) /_  
\____/_/    \__,_/____/\__/   Version: 3.3.7
2025-09-05T13:41:36.733+08:00 [DEBU] C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/library/libWebsocket/init.go:20: start websocket..
2025-09-05T13:42:41.037+08:00 [INFO] {401e5c308a4c6218ca5e65580050d12f} C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:189: 发送短信验证码到 13800138000: 437376 (类型: login)
2025-09-05T13:47:05.823+08:00 [INFO] {ac8463d7c74c6218d05e655815b64a48} C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:189: 发送短信验证码到 13800138000: 754088 (类型: login)
2025-09-05T13:49:22.358+08:00 [INFO] {149475a1e74c62183654312d66a81883} C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/cmd/cmd.go:25: 
   ____________           __ 
  / ____/ ____/___ ______/ /_
 / / __/ /_  / __ `/ ___/ __/
/ /_/ / __/ / /_/ (__  ) /_  
\____/_/    \__,_/____/\__/   Version: 3.3.7
2025-09-05T13:49:23.762+08:00 [DEBU] C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/library/libWebsocket/init.go:20: start websocket..
2025-09-05T13:49:58.887+08:00 [INFO] {dc47c522f04c6218d65e655858968053} C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:189: 发送短信验证码到 13800138000: 528936 (类型: login)
2025-09-05T13:50:40.256+08:00 [INFO] {647e8dc4f94c6218d670dd607ed91396} C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/cmd/cmd.go:25: 
   ____________           __ 
  / ____/ ____/___ ______/ /_
 / / __/ /_  / __ `/ ___/ __/
/ /_/ / __/ / /_/ (__  ) /_  
\____/_/    \__,_/____/\__/   Version: 3.3.7
2025-09-05T13:50:40.337+08:00 [DEBU] C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/library/libWebsocket/init.go:20: start websocket..
2025-09-05T13:51:31.532+08:00 [INFO] {fc69d6b4054d62182cf19f35f3d50e88} C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/cmd/cmd.go:25: 
   ____________           __ 
  / ____/ ____/___ ______/ /_
 / / __/ /_  / __ `/ ___/ __/
/ /_/ / __/ / /_/ (__  ) /_  
\____/_/    \__,_/____/\__/   Version: 3.3.7
2025-09-05T13:51:31.717+08:00 [DEBU] C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/library/libWebsocket/init.go:20: start websocket..
2025-09-05T13:55:02.776+08:00 [INFO] {249bf9e3364d6218950e4f456d72e64d} C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/cmd/cmd.go:25: 
   ____________           __ 
  / ____/ ____/___ ______/ /_
 / / __/ /_  / __ `/ ___/ __/
/ /_/ / __/ / /_/ (__  ) /_  
\____/_/    \__,_/____/\__/   Version: 3.3.7
2025-09-05T13:55:02.868+08:00 [DEBU] C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/library/libWebsocket/init.go:20: start websocket..
2025-09-05T13:55:37.195+08:00 [INFO] {60d176e73e4d6218e87bb042763de296} C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/cmd/cmd.go:25: 
   ____________           __ 
  / ____/ ____/___ ______/ /_
 / / __/ /_  / __ `/ ___/ __/
/ /_/ / __/ / /_/ (__  ) /_  
\____/_/    \__,_/____/\__/   Version: 3.3.7
2025-09-05T13:55:37.250+08:00 [DEBU] C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/library/libWebsocket/init.go:20: start websocket..
2025-09-05T13:58:00.948+08:00 [INFO] {5ce6d05f604d6218b58b676c97866dca} C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/cmd/cmd.go:25: 
   ____________           __ 
  / ____/ ____/___ ______/ /_
 / / __/ /_  / __ `/ ___/ __/
/ /_/ / __/ / /_/ (__  ) /_  
\____/_/    \__,_/____/\__/   Version: 3.3.7
2025-09-05T13:58:01.477+08:00 [DEBU] C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/library/libWebsocket/init.go:20: start websocket..
2025-09-05T14:02:56.689+08:00 [INFO] {3816543ba54d62184af41970b18af027} C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/cmd/cmd.go:25: 
   ____________           __ 
  / ____/ ____/___ ______/ /_
 / / __/ /_  / __ `/ ___/ __/
/ /_/ / __/ / /_/ (__  ) /_  
\____/_/    \__,_/____/\__/   Version: 3.3.7
2025-09-05T14:02:56.764+08:00 [DEBU] C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/library/libWebsocket/init.go:20: start websocket..
2025-09-05T14:04:10.237+08:00 [INFO] {34a5295bb64d6218d316db0889144da3} C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/cmd/cmd.go:25: 
   ____________           __ 
  / ____/ ____/___ ______/ /_
 / / __/ /_  / __ `/ ___/ __/
/ /_/ / __/ / /_/ (__  ) /_  
\____/_/    \__,_/____/\__/   Version: 3.3.7
2025-09-05T14:04:11.688+08:00 [DEBU] C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/library/libWebsocket/init.go:20: start websocket..
2025-09-05T14:04:59.523+08:00 [INFO] {a06659d4c14d6218d916db089c953798} C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:271: 发送短信验证码到 13800138000: 756593 (类型: login)
2025-09-05T14:09:08.670+08:00 [INFO] {f03728d7fb4d62184dacbc2bd819c38b} C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/cmd/cmd.go:25: 
   ____________           __ 
  / ____/ ____/___ ______/ /_
 / / __/ /_  / __ `/ ___/ __/
/ /_/ / __/ / /_/ (__  ) /_  
\____/_/    \__,_/____/\__/   Version: 3.3.7
2025-09-05T14:09:08.768+08:00 [DEBU] C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/library/libWebsocket/init.go:20: start websocket..
2025-09-05T14:11:07.118+08:00 [INFO] {480d326b174e6218744f605f0bbae265} C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/cmd/cmd.go:25: 
   ____________           __ 
  / ____/ ____/___ ______/ /_
 / / __/ /_  / __ `/ ___/ __/
/ /_/ / __/ / /_/ (__  ) /_  
\____/_/    \__,_/____/\__/   Version: 3.3.7
2025-09-05T14:11:07.458+08:00 [DEBU] C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/library/libWebsocket/init.go:20: start websocket..
2025-09-05T14:29:07.881+08:00 [INFO] {e49a7a0b134f6218814f605f11bd73ba} C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:271: 发送短信验证码到 13800138000: 119481 (类型: login)
2025-09-05T14:42:19.167+08:00 [INFO] {94260b4acb4f6218e56bef36b74c2b55} C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/cmd/cmd.go:25: 
   ____________           __ 
  / ____/ ____/___ ______/ /_
 / / __/ /_  / __ `/ ___/ __/
/ /_/ / __/ / /_/ (__  ) /_  
\____/_/    \__,_/____/\__/   Version: 3.3.7
2025-09-05T14:42:19.323+08:00 [DEBU] C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/library/libWebsocket/init.go:20: start websocket..
2025-09-05T14:44:23.335+08:00 [INFO] {7c370733e84f6218f1c36032a50a31db} C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/cmd/cmd.go:25: 
   ____________           __ 
  / ____/ ____/___ ______/ /_
 / / __/ /_  / __ `/ ___/ __/
/ /_/ / __/ / /_/ (__  ) /_  
\____/_/    \__,_/____/\__/   Version: 3.3.7
2025-09-05T14:44:23.451+08:00 [DEBU] C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/library/libWebsocket/init.go:20: start websocket..
2025-09-05T15:29:29.207+08:00 [INFO] {883983355e526218e64ffe06b91bc7ee} C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/cmd/cmd.go:25: 
   ____________           __ 
  / ____/ ____/___ ______/ /_
 / / __/ /_  / __ `/ ___/ __/
/ /_/ / __/ / /_/ (__  ) /_  
\____/_/    \__,_/____/\__/   Version: 3.3.7
2025-09-05T15:29:29.295+08:00 [DEBU] C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/library/libWebsocket/init.go:20: start websocket..
2025-09-05T16:25:17.525+08:00 [INFO] {a817d0cc69556218f1d0b849904a0363} C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/cmd/cmd.go:25: 
   ____________           __ 
  / ____/ ____/___ ______/ /_
 / / __/ /_  / __ `/ ___/ __/
/ /_/ / __/ / /_/ (__  ) /_  
\____/_/    \__,_/____/\__/   Version: 3.3.7
2025-09-05T16:25:17.672+08:00 [DEBU] C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/library/libWebsocket/init.go:20: start websocket..
2025-09-05T17:33:28.673+08:00 [INFO] {10adb75322596218fad0b84900d0fe21} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: token is invalid
2025-09-05T17:33:28.677+08:00 [INFO] {f8f9465822596218fbd0b849a6d2b3ec} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: token is invalid
2025-09-05T17:33:30.823+08:00 [INFO] {58411ad822596218fcd0b849b89247e9} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-09-05T17:33:30.823+08:00 [INFO] {58411ad822596218fcd0b849b89247e9} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-09-05T17:49:30.071+08:00 [INFO] C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/library/libWebsocket/client.go:82: websocket client quit, user:&{LoginUserRes:0xc000bd2b00}
2025-09-05T17:55:42.552+08:00 [INFO] C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/library/libWebsocket/client.go:82: websocket client quit, user:&{LoginUserRes:0xc000c44080}
2025-09-05T17:57:06.933+08:00 [INFO] C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/library/libWebsocket/client.go:82: websocket client quit, user:&{LoginUserRes:0xc001d04780}
2025-09-05T18:06:00.281+08:00 [INFO] C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/library/libWebsocket/client.go:82: websocket client quit, user:&{LoginUserRes:0xc002026000}
2025-09-05T19:54:55.726+08:00 [INFO] 生成OAuth授权URL: https://www.sanxianren.com/wiki/Special:OAuth/authorize?client_id=your_mediawiki_oauth_client_id&redirect_uri=http%3A%2F%2Flocalhost%3A8808%2Fapi%2Fv1%2Fsanxianren%2Foauth%2Fcallback&response_type=code&scope=basic+editpage+createpage+uploadfile&state=eyJ1c2VyX2lkIjoxMjM0NSwidGltZXN0YW1wIjoxNzU3MDczMjk1LCJub25jZSI6IjhlelhIUFdIRlhONFFVTkcxRkF3dVE9PSIsInJldHVybl91cmwiOiJodHRwOi8vbG9jYWxob3N0OjUxNzMvYWRtaW4vb2F1dGgvc3VjY2VzcyJ9
2025-09-05T19:54:55.743+08:00 [INFO] 生成OAuth授权URL: https://www.sanxianren.com/wiki/Special:OAuth/authorize?client_id=your_mediawiki_oauth_client_id&redirect_uri=http%3A%2F%2Flocalhost%3A8808%2Fapi%2Fv1%2Fsanxianren%2Foauth%2Fcallback&response_type=code&scope=basic+editpage+createpage+uploadfile&state=eyJ1c2VyX2lkIjoxMjM0NSwidGltZXN0YW1wIjoxNzU3MDczMjk1LCJub25jZSI6InRYUE9QWk5nREc1WWxOTDI4RXN6RFE9PSIsInJldHVybl91cmwiOiJodHRwOi8vbG9jYWxob3N0OjUxNzMvYWRtaW4ifQ%3D%3D
2025-09-05T19:54:55.845+08:00 [INFO] 生成OAuth授权URL: https://www.sanxianren.com/wiki/Special:OAuth/authorize?client_id=your_mediawiki_oauth_client_id&redirect_uri=http%3A%2F%2Flocalhost%3A8808%2Fapi%2Fv1%2Fsanxianren%2Foauth%2Fcallback&response_type=code&scope=basic+editpage+createpage+uploadfile&state=eyJ1c2VyX2lkIjowLCJ0aW1lc3RhbXAiOjE3NTcwNzMyOTUsIm5vbmNlIjoiaVRfT0pNM2M4bmFUaVF4dmF5WU1aUT09IiwicmV0dXJuX3VybCI6IiJ9
2025-09-05T20:33:30.973+08:00 [INFO] C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/library/libWebsocket/client.go:82: websocket client quit, user:&{LoginUserRes:0xc002026100}
2025-09-05T22:37:24.527+08:00 [INFO] {b0c20635b869621828d1b849ae21e9ce} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: token is invalid
2025-09-05T22:37:30.854+08:00 [INFO] {b82ffdaeb96962182ad1b8499e58d371} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: token is invalid
2025-09-05T22:37:31.973+08:00 [INFO] {7c40b3f1b96962182bd1b849e6e2b915} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: token is invalid
2025-09-05T22:37:32.004+08:00 [INFO] {0cd18bf3b96962182ed1b84934c28148} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: token is invalid
2025-09-05T22:37:33.551+08:00 [INFO] {08cc9c4fba69621830d1b849f6b3f83b} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-09-05T22:37:33.551+08:00 [INFO] {08cc9c4fba69621830d1b849f6b3f83b} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-09-05T22:46:30.170+08:00 [INFO] C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/library/libWebsocket/client.go:82: websocket client quit, user:&{LoginUserRes:0xc000bd3280}
2025-09-05T23:18:30.068+08:00 [INFO] C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/library/libWebsocket/client.go:82: websocket client quit, user:&{LoginUserRes:0xc002224000}
