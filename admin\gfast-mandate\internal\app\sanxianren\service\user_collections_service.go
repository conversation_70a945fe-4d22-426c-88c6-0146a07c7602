/*
* @desc:用户收藏服务
* @company:山东傲英网络科技股份有限公司
* @Author: wolfman
* @Date:   2024/12/19
 */

package service

import (
	"context"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	v1 "github.com/tiger1103/gfast/v3/api/v1/sanxianren"
	"github.com/tiger1103/gfast/v3/internal/app/sanxianren/dao"
	"github.com/tiger1103/gfast/v3/internal/app/sanxianren/model/entity"
)

type userCollectionsService struct{}

func init() {
	RegisterUserCollections(&userCollectionsService{})
}

// AddCollection 添加收藏
func (s *userCollectionsService) AddCollection(ctx context.Context, userId uint64, req *v1.AddCollectionReq) error {
	// 检查是否已收藏
	count, err := dao.SxUserCollections.Ctx(ctx).
		Where("user_id", userId).
		Where("content_type", req.ContentType).
		Where("content_id", req.ContentId).
		Count()
	if err != nil {
		g.Log().Error(ctx, "检查收藏状态失败:", err)
		return fmt.Errorf("添加收藏失败")
	}

	if count > 0 {
		return fmt.Errorf("已经收藏过了")
	}

	// 获取内容信息
	title, summary, coverImage := s.getContentInfo(ctx, req.ContentType, req.ContentId)

	// 添加收藏记录
	_, err = dao.SxUserCollections.Ctx(ctx).Insert(g.Map{
		"user_id":      userId,
		"content_type": req.ContentType,
		"content_id":   req.ContentId,
		"title":        title,
		"summary":      summary,
		"cover_image":  coverImage,
		"created_at":   time.Now(),
	})

	if err != nil {
		g.Log().Error(ctx, "添加收藏失败:", err)
		return fmt.Errorf("添加收藏失败")
	}

	return nil
}

// RemoveCollection 取消收藏
func (s *userCollectionsService) RemoveCollection(ctx context.Context, userId uint64, req *v1.RemoveCollectionReq) error {
	_, err := dao.SxUserCollections.Ctx(ctx).
		Where("user_id", userId).
		Where("content_type", req.ContentType).
		Where("content_id", req.ContentId).
		Delete()

	if err != nil {
		g.Log().Error(ctx, "取消收藏失败:", err)
		return fmt.Errorf("取消收藏失败")
	}

	return nil
}

// GetCollections 获取收藏列表
func (s *userCollectionsService) GetCollections(ctx context.Context, userId uint64, req *v1.GetCollectionsReq) (*v1.GetCollectionsRes, error) {
	query := dao.SxUserCollections.Ctx(ctx).Where("user_id", userId)

	// 添加内容类型过滤
	if req.ContentType != "" && req.ContentType != "all" {
		query = query.Where("content_type", req.ContentType)
	}

	// 获取总数
	total, err := query.Count()
	if err != nil {
		g.Log().Error(ctx, "获取收藏总数失败:", err)
		return nil, fmt.Errorf("获取收藏列表失败")
	}

	// 分页查询
	var collections []entity.SxUserCollections
	err = query.Page(req.PageNum, req.PageSize).OrderDesc("created_at").Scan(&collections)
	if err != nil {
		g.Log().Error(ctx, "获取收藏列表失败:", err)
		return nil, fmt.Errorf("获取收藏列表失败")
	}

	// 转换为响应格式
	list := make([]v1.CollectionInfo, 0, len(collections))
	for _, collection := range collections {
		item := v1.CollectionInfo{
			Id:          collection.Id,
			ContentType: collection.ContentType,
			ContentId:   collection.ContentId,
			Title:       collection.Title,
			Summary:     collection.Summary,
			CoverImage:  collection.CoverImage,
			CreatedAt:   collection.CreatedAt.Format("2006-01-02 15:04:05"),
		}
		list = append(list, item)
	}

	res := &v1.GetCollectionsRes{
		ListRes: commonApi.ListRes{
			CurrentPage: req.PageNum,
			Total:       int(total),
		},
		List: list,
	}

	return res, nil
}

// CheckCollection 检查收藏状态
func (s *userCollectionsService) CheckCollection(ctx context.Context, userId uint64, req *v1.CheckCollectionReq) (*v1.CheckCollectionRes, error) {
	count, err := dao.SxUserCollections.Ctx(ctx).
		Where("user_id", userId).
		Where("content_type", req.ContentType).
		Where("content_id", req.ContentId).
		Count()

	if err != nil {
		g.Log().Error(ctx, "检查收藏状态失败:", err)
		return nil, fmt.Errorf("检查收藏状态失败")
	}

	return &v1.CheckCollectionRes{
		IsCollected: count > 0,
	}, nil
}

// getContentInfo 获取内容信息
func (s *userCollectionsService) getContentInfo(ctx context.Context, contentType string, contentId uint64) (title, summary, coverImage string) {
	switch contentType {
	case "story":
		// 暂时使用默认值，后续可以添加故事表
		title = "故事内容"
		summary = "故事摘要"
		coverImage = "/static/story-default.jpg"
	case "factory":
		// 暂时使用默认值，后续可以添加工厂表
		title = "工厂内容"
		summary = "工厂描述"
		coverImage = "/static/factory-default.jpg"
	case "people":
		// 获取人物信息
		var people entity.FactoryPeople
		err := dao.FactoryPeople.Ctx(ctx).Where("id", contentId).Scan(&people)
		if err == nil {
			title = people.Name
			if people.PersonalExperience != nil {
				summary = *people.PersonalExperience
			}
			// 从相关照片JSON中获取第一张照片作为封面
			if people.RelatedPhotos != nil && *people.RelatedPhotos != "" {
				// 这里可以解析JSON获取第一张照片，暂时使用默认值
				coverImage = "/static/people-default.jpg"
			}
		}
	}

	// 设置默认值
	if title == "" {
		title = "未知内容"
	}
	if summary == "" {
		summary = "暂无描述"
	}
	if coverImage == "" {
		coverImage = "/static/default-cover.jpg"
	}

	return title, summary, coverImage
}
