/*
* @desc:性能优化服务
* @company:山东傲英网络科技股份有限公司
* @Author: wolfman
* @Date:   2024/12/19
 */

package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcache"
	"github.com/gogf/gf/v2/util/gconv"
)

type performanceService struct{}

func init() {
	RegisterPerformance(&performanceService{})
}

// CacheConfig 缓存配置
type CacheConfig struct {
	TTL     time.Duration `json:"ttl"`     // 缓存时间
	MaxSize int           `json:"maxSize"` // 最大缓存数量
}

// 缓存配置映射
var cacheConfigs = map[string]CacheConfig{
	"user_profile":     {TTL: time.Minute * 30, MaxSize: 1000},
	"user_collections": {TTL: time.Minute * 15, MaxSize: 500},
	"user_history":     {TTL: time.Minute * 10, MaxSize: 500},
	"notifications":    {TTL: time.Minute * 5, MaxSize: 200},
	"recommendations":  {TTL: time.Hour, MaxSize: 100},
	"hot_content":      {TTL: time.Hour * 2, MaxSize: 50},
}

// GetCachedData 获取缓存数据
func (s *performanceService) GetCachedData(ctx context.Context, cacheType string, key string) (interface{}, bool) {
	cacheKey := s.buildCacheKey(cacheType, key)

	value, err := gcache.Get(ctx, cacheKey)
	if err == nil && value != nil {
		g.Log().Debug(ctx, fmt.Sprintf("缓存命中: %s", cacheKey))
		return value, true
	}

	return nil, false
}

// SetCachedData 设置缓存数据
func (s *performanceService) SetCachedData(ctx context.Context, cacheType string, key string, data interface{}) error {
	config, exists := cacheConfigs[cacheType]
	if !exists {
		config = CacheConfig{TTL: time.Minute * 10, MaxSize: 100} // 默认配置
	}

	cacheKey := s.buildCacheKey(cacheType, key)

	err := gcache.Set(ctx, cacheKey, data, config.TTL)
	if err != nil {
		g.Log().Error(ctx, fmt.Sprintf("设置缓存失败: %s, 错误: %v", cacheKey, err))
		return err
	}

	g.Log().Debug(ctx, fmt.Sprintf("缓存设置成功: %s", cacheKey))
	return nil
}

// InvalidateCache 清除缓存
func (s *performanceService) InvalidateCache(ctx context.Context, cacheType string, key string) error {
	cacheKey := s.buildCacheKey(cacheType, key)

	_, err := gcache.Remove(ctx, cacheKey)
	if err != nil {
		g.Log().Error(ctx, fmt.Sprintf("清除缓存失败: %s, 错误: %v", cacheKey, err))
		return err
	}

	g.Log().Debug(ctx, fmt.Sprintf("缓存清除成功: %s", cacheKey))
	return nil
}

// InvalidateCachePattern 按模式清除缓存
func (s *performanceService) InvalidateCachePattern(ctx context.Context, pattern string) error {
	// 这里需要根据实际缓存实现来处理模式匹配
	// gcache 可能不直接支持模式删除，需要遍历所有key
	g.Log().Info(ctx, fmt.Sprintf("清除缓存模式: %s", pattern))
	return nil
}

// GetOrSetCache 获取或设置缓存
func (s *performanceService) GetOrSetCache(ctx context.Context, cacheType string, key string, dataFunc func() (interface{}, error)) (interface{}, error) {
	// 先尝试从缓存获取
	if data, exists := s.GetCachedData(ctx, cacheType, key); exists {
		return data, nil
	}

	// 缓存不存在，执行数据获取函数
	data, err := dataFunc()
	if err != nil {
		return nil, err
	}

	// 设置缓存
	err = s.SetCachedData(ctx, cacheType, key, data)
	if err != nil {
		g.Log().Warning(ctx, fmt.Sprintf("设置缓存失败，但数据获取成功: %v", err))
	}

	return data, nil
}

// PreloadCache 预加载缓存
func (s *performanceService) PreloadCache(ctx context.Context, cacheType string, keys []string, dataFunc func(key string) (interface{}, error)) error {
	for _, key := range keys {
		// 检查缓存是否已存在
		if _, exists := s.GetCachedData(ctx, cacheType, key); exists {
			continue
		}

		// 获取数据并设置缓存
		data, err := dataFunc(key)
		if err != nil {
			g.Log().Warning(ctx, fmt.Sprintf("预加载缓存失败: %s, 错误: %v", key, err))
			continue
		}

		err = s.SetCachedData(ctx, cacheType, key, data)
		if err != nil {
			g.Log().Warning(ctx, fmt.Sprintf("预加载设置缓存失败: %s, 错误: %v", key, err))
		}
	}

	return nil
}

// OptimizeQuery 查询优化
func (s *performanceService) OptimizeQuery(ctx context.Context, queryType string, params map[string]interface{}) map[string]interface{} {
	optimizedParams := make(map[string]interface{})

	// 复制原始参数
	for k, v := range params {
		optimizedParams[k] = v
	}

	switch queryType {
	case "user_collections":
		s.optimizeCollectionsQuery(optimizedParams)
	case "user_history":
		s.optimizeHistoryQuery(optimizedParams)
	case "notifications":
		s.optimizeNotificationsQuery(optimizedParams)
	case "recommendations":
		s.optimizeRecommendationsQuery(optimizedParams)
	}

	return optimizedParams
}

// BatchProcess 批量处理
func (s *performanceService) BatchProcess(ctx context.Context, items []interface{}, batchSize int, processFunc func(batch []interface{}) error) error {
	total := len(items)

	for i := 0; i < total; i += batchSize {
		end := i + batchSize
		if end > total {
			end = total
		}

		batch := items[i:end]
		err := processFunc(batch)
		if err != nil {
			g.Log().Error(ctx, fmt.Sprintf("批量处理失败: 批次 %d-%d, 错误: %v", i, end-1, err))
			return err
		}

		g.Log().Debug(ctx, fmt.Sprintf("批量处理完成: 批次 %d-%d", i, end-1))
	}

	return nil
}

// MonitorPerformance 性能监控
func (s *performanceService) MonitorPerformance(ctx context.Context, operation string, startTime time.Time) {
	duration := time.Since(startTime)

	// 记录性能指标
	g.Log().Info(ctx, fmt.Sprintf("性能监控: 操作=%s, 耗时=%v", operation, duration))

	// 如果耗时过长，记录警告
	if duration > time.Second*5 {
		g.Log().Warning(ctx, fmt.Sprintf("性能警告: 操作=%s, 耗时=%v (超过5秒)", operation, duration))
	}

	// 这里可以集成性能监控系统
	// 例如：发送到Prometheus、Grafana等
}

// GetCacheStats 获取缓存统计
func (s *performanceService) GetCacheStats(ctx context.Context) map[string]interface{} {
	stats := make(map[string]interface{})

	// 这里应该从实际缓存系统获取统计信息
	// gcache 可能不直接提供详细统计，需要自己实现
	stats["total_keys"] = 0
	stats["hit_rate"] = 0.0
	stats["miss_rate"] = 0.0
	stats["memory_usage"] = "0MB"

	return stats
}

// 私有方法

// buildCacheKey 构建缓存键
func (s *performanceService) buildCacheKey(cacheType string, key string) string {
	return fmt.Sprintf("sanxianren:%s:%s", cacheType, key)
}

// optimizeCollectionsQuery 优化收藏查询
func (s *performanceService) optimizeCollectionsQuery(params map[string]interface{}) {
	// 限制页面大小
	if pageSize, exists := params["pageSize"]; exists {
		if gconv.Int(pageSize) > 50 {
			params["pageSize"] = 50
		}
	}

	// 添加索引提示
	params["useIndex"] = "idx_user_content_type_created"
}

// optimizeHistoryQuery 优化历史查询
func (s *performanceService) optimizeHistoryQuery(params map[string]interface{}) {
	// 限制页面大小
	if pageSize, exists := params["pageSize"]; exists {
		if gconv.Int(pageSize) > 50 {
			params["pageSize"] = 50
		}
	}

	// 限制查询时间范围（只查询最近3个月）
	if _, exists := params["timeRange"]; !exists {
		params["timeRange"] = time.Now().AddDate(0, -3, 0)
	}
}

// optimizeNotificationsQuery 优化通知查询
func (s *performanceService) optimizeNotificationsQuery(params map[string]interface{}) {
	// 限制页面大小
	if pageSize, exists := params["pageSize"]; exists {
		if gconv.Int(pageSize) > 20 {
			params["pageSize"] = 20
		}
	}

	// 优先查询未读通知
	if _, exists := params["prioritizeUnread"]; !exists {
		params["prioritizeUnread"] = true
	}
}

// optimizeRecommendationsQuery 优化推荐查询
func (s *performanceService) optimizeRecommendationsQuery(params map[string]interface{}) {
	// 限制推荐数量
	if limit, exists := params["limit"]; exists {
		if gconv.Int(limit) > 20 {
			params["limit"] = 20
		}
	}

	// 设置推荐算法类型
	if _, exists := params["algorithm"]; !exists {
		params["algorithm"] = "hybrid" // 混合算法
	}
}

// CompressData 数据压缩
func (s *performanceService) CompressData(ctx context.Context, data interface{}) ([]byte, error) {
	// 将数据序列化为JSON
	jsonData, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}

	// 这里可以添加压缩算法（如gzip）
	// 简化实现：直接返回JSON数据
	return jsonData, nil
}

// DecompressData 数据解压缩
func (s *performanceService) DecompressData(ctx context.Context, compressedData []byte, target interface{}) error {
	// 这里应该对应压缩算法进行解压缩
	// 简化实现：直接解析JSON
	return json.Unmarshal(compressedData, target)
}

// OptimizeImageSize 优化图片大小
func (s *performanceService) OptimizeImageSize(ctx context.Context, imagePath string, maxWidth, maxHeight int) (string, error) {
	// 这里应该实现图片压缩和尺寸优化
	// 可以集成图片处理库（如：imaging、resize等）
	g.Log().Info(ctx, fmt.Sprintf("优化图片: %s, 最大尺寸: %dx%d", imagePath, maxWidth, maxHeight))

	// 简化实现：返回原路径
	return imagePath, nil
}

// CleanupExpiredCache 清理过期缓存
func (s *performanceService) CleanupExpiredCache(ctx context.Context) error {
	// 这里应该清理过期的缓存数据
	// gcache 会自动清理过期数据，但可以手动触发
	g.Log().Info(ctx, "清理过期缓存")
	return nil
}
