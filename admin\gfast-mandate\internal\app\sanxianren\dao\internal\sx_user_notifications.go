// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SxUserNotificationsDao is the data access object for table sx_user_notifications.
type SxUserNotificationsDao struct {
	table   string                     // table is the underlying table name of the DAO.
	group   string                     // group is the database configuration group name of current DAO.
	columns SxUserNotificationsColumns // columns contains all the column names of Table for convenient usage.
}

// SxUserNotificationsColumns defines and stores column names for table sx_user_notifications.
type SxUserNotificationsColumns struct {
	Id               string // 通知ID
	UserId           string // 用户ID
	NotificationType string // 通知类型
	Title            string // 通知标题
	Content          string // 通知内容
	IsRead           string // 是否已读
	RelatedType      string // 关联类型
	RelatedId        string // 关联ID
	CreatedAt        string // 创建时间
}

// sxUserNotificationsColumns holds the columns for table sx_user_notifications.
var sxUserNotificationsColumns = SxUserNotificationsColumns{
	Id:               "id",
	UserId:           "user_id",
	NotificationType: "notification_type",
	Title:            "title",
	Content:          "content",
	IsRead:           "is_read",
	RelatedType:      "related_type",
	RelatedId:        "related_id",
	CreatedAt:        "created_at",
}

// NewSxUserNotificationsDao creates and returns a new DAO object for table data access.
func NewSxUserNotificationsDao() *SxUserNotificationsDao {
	return &SxUserNotificationsDao{
		group:   "default",
		table:   "sx_user_notifications",
		columns: sxUserNotificationsColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *SxUserNotificationsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *SxUserNotificationsDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *SxUserNotificationsDao) Columns() SxUserNotificationsColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *SxUserNotificationsDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *SxUserNotificationsDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}
