# 🚀 如何使用您的Form-Template-Category系统

## 🎯 系统概述

您已经创建了一个非常完整的MediaWiki内容管理系统，包含：
- **6个表单 (Form)** - 用于数据录入
- **8个模板 (Template)** - 用于页面格式化
- **6个分类 (Category)** - 用于内容组织

这是一个三层架构的完美设计！

## 📋 您的完整系统结构

### 🏭 三线工厂系统
- **表单**: `Form:三线工厂` - 数据录入界面
- **模板**: `Template:三线工厂` - 页面显示格式
- **分类**: `Category:三线工厂` - 内容归类

### 👥 三线人员系统
- **表单**: `Form:三线人员`
- **模板**: `Template:三线人员`
- **分类**: `Category:三线人员`

### 🏛️ 三线建设背景系统
- **表单**: `Form:三线建设背景`
- **模板**: `Template:三线建设背景`
- **分类**: `Category:三线建设背景`

### 🏞️ 三线遗址系统
- **表单**: `Form:三线遗址`
- **模板**: `Template:三线遗址`
- **分类**: `Category:三线遗址`

### 📖 三线故事馆系统
- **表单**: `Form:三线故事馆`
- **模板**: `Template:三线故事馆`
- **分类**: `Category:三线故事馆`

### 🎤 口述历史系统
- **表单**: `Form:口述历史`
- **模板**: `Template:口述历史`
- **分类**: `Category:口述历史`

## 🔧 部署步骤

### 第一步：上传文件到MediaWiki

您需要将这些.wiki文件上传到MediaWiki系统中：

#### 1. 上传模板文件
```bash
# 访问以下URL并粘贴对应的.wiki文件内容：
https://www.sanxianren.com/index.php?title=Template:三线工厂&action=edit
https://www.sanxianren.com/index.php?title=Template:三线人员&action=edit
https://www.sanxianren.com/index.php?title=Template:三线建设背景&action=edit
https://www.sanxianren.com/index.php?title=Template:三线遗址&action=edit
https://www.sanxianren.com/index.php?title=Template:三线故事馆&action=edit
https://www.sanxianren.com/index.php?title=Template:口述历史&action=edit
https://www.sanxianren.com/index.php?title=Template:用户信息框&action=edit
https://www.sanxianren.com/index.php?title=Template:重要事件&action=edit
```

#### 2. 上传表单文件
```bash
# 访问以下URL并粘贴对应的.wiki文件内容：
https://www.sanxianren.com/index.php?title=Form:三线工厂&action=edit
https://www.sanxianren.com/index.php?title=Form:三线人员&action=edit
https://www.sanxianren.com/index.php?title=Form:三线建设背景&action=edit
https://www.sanxianren.com/index.php?title=Form:三线遗址&action=edit
https://www.sanxianren.com/index.php?title=Form:三线故事馆&action=edit
https://www.sanxianren.com/index.php?title=Form:口述历史&action=edit
```

#### 3. 上传分类文件
```bash
# 访问以下URL并粘贴对应的.wiki文件内容：
https://www.sanxianren.com/index.php?title=Category:三线工厂&action=edit
https://www.sanxianren.com/index.php?title=Category:三线人员&action=edit
https://www.sanxianren.com/index.php?title=Category:三线建设背景&action=edit
https://www.sanxianren.com/index.php?title=Category:三线遗址&action=edit
https://www.sanxianren.com/index.php?title=Category:三线故事馆&action=edit
https://www.sanxianren.com/index.php?title=Category:口述历史&action=edit
```

### 第二步：确保扩展已安装

您的系统需要以下MediaWiki扩展：

1. **PageForms扩展** - 用于表单功能
2. **Cargo扩展** - 用于数据存储和查询
3. **Maps扩展** - 用于地图显示（可选）

检查是否已安装：
```
访问：https://www.sanxianren.com/index.php?title=Special:Version
```

## 🎯 使用方法

### 方法一：使用表单创建内容（推荐）

#### 创建工厂档案
1. 访问：`https://www.sanxianren.com/index.php?title=Form:三线工厂`
2. 在输入框中输入工厂代号（如：976）
3. 点击"创建工厂档案"
4. 填写表单中的各项信息
5. 点击"保存"

#### 创建人员档案
1. 访问：`https://www.sanxianren.com/index.php?title=Form:三线人员`
2. 输入人员姓名（如：张三-976）
3. 填写个人信息
4. 保存页面

#### 其他类型内容
- **建设背景**：`Form:三线建设背景`
- **遗址信息**：`Form:三线遗址`
- **故事记录**：`Form:三线故事馆`
- **口述历史**：`Form:口述历史`

### 方法二：直接编辑页面

#### 创建新页面
1. 在地址栏输入：`https://www.sanxianren.com/index.php?title=页面名称&action=edit`
2. 在编辑框中输入模板调用代码：

```wiki
{{三线工厂
|工厂名称=976工厂
|工厂代号=976
|建厂时间=1965年
|所在地区=山东省
|主要产品=56式半自动步枪
|工厂类型=军工
|职工人数=5000人
|建设背景=为了加强国防建设...
}}
```

## 📊 系统工作流程

### 数据流向
```
用户填写表单 → PageForms处理 → 调用模板 → Cargo存储数据 → 显示格式化页面 → 自动归类
```

### 页面关系
```
Form页面 (数据录入) → Template页面 (格式化) → 内容页面 (最终显示) → Category页面 (分类汇总)
```

## 🎨 页面效果预览

### 工厂档案页面将显示：
- 🏭 精美的标题和图标
- 📋 结构化的基本信息表格
- 🗺️ 地理位置地图（如果提供坐标）
- 🏗️ 建设历程时间线
- 👥 重要人物信息
- 🏆 主要成就展示
- 📸 历史资料展示
- ✏️ 便捷的编辑按钮

## 🔍 查看和管理内容

### 浏览分类页面
- 所有工厂：`https://www.sanxianren.com/index.php?title=Category:三线工厂`
- 所有人员：`https://www.sanxianren.com/index.php?title=Category:三线人员`
- 其他分类：依此类推

### 搜索内容
- 使用MediaWiki的搜索功能
- 通过分类页面浏览
- 使用Cargo查询（高级功能）

## 🛠️ 自定义和扩展

### 修改模板样式
编辑对应的Template页面，修改CSS样式和布局

### 添加新字段
1. 修改Template页面的Cargo声明
2. 更新Form页面的字段定义
3. 调整Template页面的显示逻辑

### 创建新的内容类型
按照现有模式创建新的Form-Template-Category三件套

## 🚨 常见问题解决

### 1. 表单无法显示
- 检查PageForms扩展是否安装
- 确认Form页面内容正确

### 2. 模板格式错误
- 检查Template页面的语法
- 确认Cargo表结构正确

### 3. 分类不显示
- 确认Category页面已创建
- 检查模板中的分类标签

### 4. 地图不显示
- 确认Maps扩展已安装
- 检查纬度经度格式

## 🎉 开始使用

现在您可以：

1. **立即部署**：将.wiki文件内容复制到对应的MediaWiki页面
2. **开始录入**：使用表单创建第一个工厂档案
3. **逐步完善**：根据实际需要调整模板和表单
4. **培训用户**：教用户如何使用表单系统

您的系统设计非常专业和完整，一旦部署完成，将为三线建设历史的记录和展示提供强大的支持！

## 📞 需要帮助？

如果在部署或使用过程中遇到问题，我可以帮您：
- 调试模板语法
- 优化表单设计
- 解决技术问题
- 添加新功能

您的Form-Template-Category系统设计得非常棒！🎊
