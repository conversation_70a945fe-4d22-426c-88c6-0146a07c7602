<template>
  <div 
    class="wikipedia-card"
    :class="[
      `card-${article.type}`,
      { 'card-loading': loading, 'card-featured': article.featured }
    ]"
    @click="handleClick"
    v-loading="loading"
    element-loading-background="rgba(255, 255, 255, 0.8)"
  >
    <!-- 特色标识 -->
    <div v-if="article.featured" class="featured-badge">
      <SvgIcon name="ele-Star" :size="14" />
      <span>精选</span>
    </div>

    <!-- 图片区域 -->
    <div v-if="article.image" class="card-image">
      <el-image
        :src="article.image"
        :alt="article.title"
        fit="cover"
        lazy
        :preview-src-list="[article.image]"
        :initial-index="0"
        class="image"
      >
        <template #placeholder>
          <div class="image-placeholder">
            <SvgIcon name="ele-Picture" :size="32" />
          </div>
        </template>
        <template #error>
          <div class="image-error">
            <SvgIcon name="ele-PictureFilled" :size="32" />
          </div>
        </template>
      </el-image>

      <!-- 图片覆盖层 -->
      <div class="image-overlay">
        <div class="overlay-actions">
          <el-button 
            circle 
            size="small"
            @click.stop="toggleFavorite"
            :type="article.isFavorite ? 'danger' : 'default'"
          >
            <SvgIcon 
              :name="article.isFavorite ? 'ele-StarFilled' : 'ele-Star'" 
              :size="14" 
            />
          </el-button>
          <el-button 
            circle 
            size="small"
            @click.stop="shareArticle"
          >
            <SvgIcon name="ele-Share" :size="14" />
          </el-button>
        </div>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="card-content">
      <!-- 分类标签 -->
      <div class="card-category">
        <el-tag 
          :type="getCategoryType(article.category)"
          size="small"
          effect="light"
        >
          <SvgIcon :name="getCategoryIcon(article.category)" :size="12" class="mr4" />
          {{ getCategoryName(article.category) }}
        </el-tag>
      </div>

      <!-- 标题 -->
      <h3 class="card-title" :title="article.title">
        {{ article.title }}
      </h3>

      <!-- 摘要 -->
      <p class="card-summary">
        {{ article.summary }}
      </p>

      <!-- 标签 -->
      <div v-if="article.tags && article.tags.length" class="card-tags">
        <el-tag
          v-for="tag in article.tags.slice(0, 3)"
          :key="tag"
          size="small"
          type="info"
          effect="plain"
          class="tag-item"
        >
          {{ tag }}
        </el-tag>
        <span v-if="article.tags.length > 3" class="more-tags">
          +{{ article.tags.length - 3 }}
        </span>
      </div>

      <!-- 统计信息 -->
      <div class="card-stats">
        <div class="stat-item">
          <SvgIcon name="ele-View" :size="14" />
          <span>{{ formatNumber(article.views) }}</span>
        </div>
        <div class="stat-item">
          <SvgIcon name="ele-Edit" :size="14" />
          <span>{{ formatNumber(article.edits) }}</span>
        </div>
        <div class="stat-item">
          <SvgIcon name="ele-Star" :size="14" />
          <span>{{ formatNumber(article.favorites) }}</span>
        </div>
      </div>

      <!-- 作者和时间 -->
      <div class="card-meta">
        <div class="author-info">
          <el-avatar :size="20" :src="article.author?.avatar">
            <SvgIcon name="ele-User" :size="12" />
          </el-avatar>
          <span class="author-name">{{ article.author?.name || '匿名' }}</span>
        </div>
        <div class="time-info">
          <SvgIcon name="ele-Clock" :size="12" />
          <span>{{ formatTime(article.updatedAt) }}</span>
        </div>
      </div>

      <!-- 质量评级 -->
      <div v-if="article.quality" class="card-quality">
        <div class="quality-label">质量评级:</div>
        <div class="quality-stars">
          <SvgIcon
            v-for="i in 5"
            :key="i"
            name="ele-Star"
            :size="12"
            :class="{ 
              'star-filled': i <= getQualityStars(article.quality),
              'star-empty': i > getQualityStars(article.quality)
            }"
          />
        </div>
        <span class="quality-text">{{ getQualityText(article.quality) }}</span>
      </div>
    </div>

    <!-- 悬浮操作栏 -->
    <div class="card-actions">
      <el-button 
        text 
        size="small"
        @click.stop="editArticle"
        v-if="canEdit"
      >
        <SvgIcon name="ele-Edit" :size="14" />
        编辑
      </el-button>
      <el-button 
        text 
        size="small"
        @click.stop="viewHistory"
      >
        <SvgIcon name="ele-Clock" :size="14" />
        历史
      </el-button>
      <el-button 
        text 
        size="small"
        @click.stop="discussArticle"
      >
        <SvgIcon name="ele-ChatDotRound" :size="14" />
        讨论
      </el-button>
    </div>

    <!-- 加载骨架屏 -->
    <div v-if="loading" class="card-skeleton">
      <el-skeleton animated>
        <template #template>
          <el-skeleton-item variant="image" style="width: 100%; height: 200px;" />
          <div style="padding: 16px;">
            <el-skeleton-item variant="h3" style="width: 80%;" />
            <el-skeleton-item variant="text" style="width: 100%; margin-top: 8px;" />
            <el-skeleton-item variant="text" style="width: 60%; margin-top: 8px;" />
          </div>
        </template>
      </el-skeleton>
    </div>
  </div>
</template>

<script setup lang="ts" name="WikipediaCard">
import { computed } from 'vue';
import { ElMessage } from 'element-plus';
import { useRouter } from 'vue-router';
import { useUserStore } from '/@/stores/userStore';
import SvgIcon from '/@/components/svgIcon/index.vue';

// Props
interface Props {
  article: any;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
});

// Emits
const emit = defineEmits<{
  click: [article: any];
  favorite: [article: any];
  share: [article: any];
  edit: [article: any];
}>();

// Router
const router = useRouter();

// Store
const userStore = useUserStore();

// 计算属性
const canEdit = computed(() => {
  // 检查用户是否有编辑权限
  return userStore.hasPermission('edit') || props.article.author?.id === userStore.userInfo?.id;
});

// 方法
const handleClick = () => {
  if (!props.loading) {
    emit('click', props.article);
  }
};

const toggleFavorite = () => {
  emit('favorite', props.article);
};

const shareArticle = () => {
  emit('share', props.article);
};

const editArticle = () => {
  emit('edit', props.article);
};

const viewHistory = () => {
  router.push(`/sanxianren/wikipedia/history/${props.article.id}`);
};

const discussArticle = () => {
  router.push(`/sanxianren/wikipedia/discuss/${props.article.id}`);
};

const getCategoryType = (category: string) => {
  const typeMap: Record<string, string> = {
    history: 'warning',
    science: 'success',
    technology: 'primary',
    culture: 'danger',
    geography: 'info',
    biography: 'success',
    arts: 'danger',
  };
  return typeMap[category] || 'primary';
};

const getCategoryIcon = (category: string) => {
  const iconMap: Record<string, string> = {
    history: 'ele-Clock',
    science: 'ele-Experiment',
    technology: 'ele-Cpu',
    culture: 'ele-Picture',
    geography: 'ele-Location',
    biography: 'ele-User',
    arts: 'ele-Brush',
  };
  return iconMap[category] || 'ele-Document';
};

const getCategoryName = (category: string) => {
  const nameMap: Record<string, string> = {
    history: '历史',
    science: '科学',
    technology: '技术',
    culture: '文化',
    geography: '地理',
    biography: '人物',
    arts: '艺术',
  };
  return nameMap[category] || '其他';
};

const getQualityStars = (quality: string) => {
  const qualityMap: Record<string, number> = {
    stub: 1,
    start: 2,
    c: 3,
    b: 4,
    ga: 4,
    fa: 5,
  };
  return qualityMap[quality] || 3;
};

const getQualityText = (quality: string) => {
  const textMap: Record<string, string> = {
    stub: '小作品',
    start: '初级',
    c: '良好',
    b: '优秀',
    ga: '优良',
    fa: '特色',
  };
  return textMap[quality] || '一般';
};

const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
};

const formatTime = (time: string) => {
  const date = new Date(time);
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  
  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  
  if (minutes < 60) {
    return `${minutes}分钟前`;
  } else if (hours < 24) {
    return `${hours}小时前`;
  } else if (days < 30) {
    return `${days}天前`;
  } else {
    return date.toLocaleDateString('zh-CN');
  }
};
</script>

<style scoped lang="scss">
.wikipedia-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  margin-bottom: 20px;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);

    .card-actions {
      opacity: 1;
      transform: translateY(0);
    }

    .image-overlay {
      opacity: 1;
    }
  }

  &.card-featured {
    border: 2px solid var(--el-color-warning);
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, #ffd700, #ffed4e);
      z-index: 1;
    }
  }

  &.card-loading {
    pointer-events: none;
  }

  .featured-badge {
    position: absolute;
    top: 12px;
    right: 12px;
    background: var(--el-color-warning);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
    z-index: 2;
  }

  .card-image {
    position: relative;
    width: 100%;
    height: 200px;
    overflow: hidden;

    .image {
      width: 100%;
      height: 100%;
      transition: transform 0.3s ease;

      &:hover {
        transform: scale(1.05);
      }
    }

    .image-placeholder,
    .image-error {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: var(--el-fill-color-light);
      color: var(--el-text-color-placeholder);
    }

    .image-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.3);
      opacity: 0;
      transition: opacity 0.3s ease;
      display: flex;
      align-items: flex-end;
      justify-content: flex-end;
      padding: 12px;

      .overlay-actions {
        display: flex;
        gap: 8px;
      }
    }
  }

  .card-content {
    padding: 16px;

    .card-category {
      margin-bottom: 8px;
    }

    .card-title {
      margin: 0 0 8px 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .card-summary {
      margin: 0 0 12px 0;
      font-size: 14px;
      color: var(--el-text-color-regular);
      line-height: 1.5;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .card-tags {
      margin-bottom: 12px;
      display: flex;
      flex-wrap: wrap;
      gap: 6px;
      align-items: center;

      .tag-item {
        font-size: 12px;
      }

      .more-tags {
        font-size: 12px;
        color: var(--el-text-color-secondary);
      }
    }

    .card-stats {
      display: flex;
      justify-content: space-between;
      margin-bottom: 12px;
      padding: 8px 0;
      border-top: 1px solid var(--el-border-color-lighter);
      border-bottom: 1px solid var(--el-border-color-lighter);

      .stat-item {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;
        color: var(--el-text-color-secondary);
      }
    }

    .card-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      .author-info {
        display: flex;
        align-items: center;
        gap: 6px;

        .author-name {
          font-size: 12px;
          color: var(--el-text-color-regular);
        }
      }

      .time-info {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;
        color: var(--el-text-color-secondary);
      }
    }

    .card-quality {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 12px;

      .quality-label {
        color: var(--el-text-color-secondary);
      }

      .quality-stars {
        display: flex;
        gap: 2px;

        .star-filled {
          color: var(--el-color-warning);
        }

        .star-empty {
          color: var(--el-border-color-light);
        }
      }

      .quality-text {
        color: var(--el-text-color-regular);
        font-weight: 500;
      }
    }
  }

  .card-actions {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 8px 16px;
    display: flex;
    justify-content: space-around;
    opacity: 0;
    transform: translateY(100%);
    transition: all 0.3s ease;
    border-top: 1px solid var(--el-border-color-lighter);
  }

  .mr4 {
    margin-right: 4px;
  }
}

// 不同类型的卡片样式
.card-history {
  border-left: 4px solid var(--el-color-warning);
}

.card-science {
  border-left: 4px solid var(--el-color-success);
}

.card-technology {
  border-left: 4px solid var(--el-color-primary);
}

.card-culture {
  border-left: 4px solid var(--el-color-danger);
}

.card-geography {
  border-left: 4px solid var(--el-color-info);
}

.card-biography {
  border-left: 4px solid var(--el-color-success);
}

.card-arts {
  border-left: 4px solid var(--el-color-danger);
}

// 响应式设计
@media (max-width: 768px) {
  .wikipedia-card {
    .card-content {
      padding: 12px;
    }

    .card-image {
      height: 160px;
    }

    .card-title {
      font-size: 14px;
    }

    .card-summary {
      font-size: 13px;
      -webkit-line-clamp: 2;
    }
  }
}
</style>
