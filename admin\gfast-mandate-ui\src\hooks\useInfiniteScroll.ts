/**
 * 无限滚动Hook
 * <AUTHOR> Assistant
 * @date 2025-01-22
 */

import { ref, onMounted, onUnmounted, Ref } from 'vue';

export interface InfiniteScrollOptions {
  // 触发加载的距离阈值（距离底部多少像素时触发）
  threshold?: number;
  // 是否立即检查
  immediate?: boolean;
  // 加载函数
  onLoad?: () => Promise<void> | void;
  // 是否禁用
  disabled?: boolean;
  // 延迟时间（防抖）
  delay?: number;
}

export interface InfiniteScrollReturn {
  // 是否正在加载
  isLoading: Ref<boolean>;
  // 是否还有更多数据
  hasMore: Ref<boolean>;
  // 手动触发加载
  loadMore: () => Promise<void>;
  // 重置状态
  reset: () => void;
  // 设置是否还有更多数据
  setHasMore: (value: boolean) => void;
}

/**
 * 无限滚动Hook
 */
export function useInfiniteScroll(
  target: Ref<HTMLElement | undefined>,
  options: InfiniteScrollOptions = {}
): InfiniteScrollReturn {
  const {
    threshold = 200,
    immediate = true,
    onLoad,
    disabled = false,
    delay = 100,
  } = options;

  const isLoading = ref(false);
  const hasMore = ref(true);

  let timeoutId: number | null = null;

  // 检查是否需要加载更多
  const checkIfNeedLoad = () => {
    if (!target.value || disabled || isLoading.value || !hasMore.value) {
      return;
    }

    const element = target.value;
    const { scrollTop, scrollHeight, clientHeight } = element;
    
    // 计算距离底部的距离
    const distanceToBottom = scrollHeight - scrollTop - clientHeight;
    
    if (distanceToBottom <= threshold) {
      loadMore();
    }
  };

  // 防抖处理的滚动事件
  const handleScroll = () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    
    timeoutId = window.setTimeout(() => {
      checkIfNeedLoad();
    }, delay);
  };

  // 手动触发加载
  const loadMore = async () => {
    if (isLoading.value || !hasMore.value || disabled) {
      return;
    }

    isLoading.value = true;
    
    try {
      if (onLoad) {
        await onLoad();
      }
    } catch (error) {
      console.error('无限滚动加载失败:', error);
    } finally {
      isLoading.value = false;
    }
  };

  // 重置状态
  const reset = () => {
    isLoading.value = false;
    hasMore.value = true;
  };

  // 设置是否还有更多数据
  const setHasMore = (value: boolean) => {
    hasMore.value = value;
  };

  // 监听滚动事件
  const addScrollListener = () => {
    if (target.value) {
      target.value.addEventListener('scroll', handleScroll, { passive: true });
    }
  };

  // 移除滚动事件监听
  const removeScrollListener = () => {
    if (target.value) {
      target.value.removeEventListener('scroll', handleScroll);
    }
    
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }
  };

  onMounted(() => {
    if (immediate) {
      addScrollListener();
    }
  });

  onUnmounted(() => {
    removeScrollListener();
  });

  return {
    isLoading,
    hasMore,
    loadMore,
    reset,
    setHasMore,
  };
}

/**
 * 窗口滚动的无限滚动Hook
 */
export function useWindowInfiniteScroll(
  options: InfiniteScrollOptions = {}
): InfiniteScrollReturn {
  const {
    threshold = 200,
    immediate = true,
    onLoad,
    disabled = false,
    delay = 100,
  } = options;

  const isLoading = ref(false);
  const hasMore = ref(true);

  let timeoutId: number | null = null;

  // 检查是否需要加载更多
  const checkIfNeedLoad = () => {
    if (disabled || isLoading.value || !hasMore.value) {
      return;
    }

    const { scrollTop, scrollHeight, clientHeight } = document.documentElement;
    
    // 计算距离底部的距离
    const distanceToBottom = scrollHeight - scrollTop - clientHeight;
    
    if (distanceToBottom <= threshold) {
      loadMore();
    }
  };

  // 防抖处理的滚动事件
  const handleScroll = () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    
    timeoutId = window.setTimeout(() => {
      checkIfNeedLoad();
    }, delay);
  };

  // 手动触发加载
  const loadMore = async () => {
    if (isLoading.value || !hasMore.value || disabled) {
      return;
    }

    isLoading.value = true;
    
    try {
      if (onLoad) {
        await onLoad();
      }
    } catch (error) {
      console.error('无限滚动加载失败:', error);
    } finally {
      isLoading.value = false;
    }
  };

  // 重置状态
  const reset = () => {
    isLoading.value = false;
    hasMore.value = true;
  };

  // 设置是否还有更多数据
  const setHasMore = (value: boolean) => {
    hasMore.value = value;
  };

  // 监听滚动事件
  const addScrollListener = () => {
    window.addEventListener('scroll', handleScroll, { passive: true });
  };

  // 移除滚动事件监听
  const removeScrollListener = () => {
    window.removeEventListener('scroll', handleScroll);
    
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }
  };

  onMounted(() => {
    if (immediate) {
      addScrollListener();
    }
  });

  onUnmounted(() => {
    removeScrollListener();
  });

  return {
    isLoading,
    hasMore,
    loadMore,
    reset,
    setHasMore,
  };
}

/**
 * 交叉观察器无限滚动Hook
 */
export function useIntersectionInfiniteScroll(
  target: Ref<HTMLElement | undefined>,
  options: InfiniteScrollOptions & {
    rootMargin?: string;
  } = {}
): InfiniteScrollReturn {
  const {
    threshold = 0,
    immediate = true,
    onLoad,
    disabled = false,
    rootMargin = '0px 0px 200px 0px',
  } = options;

  const isLoading = ref(false);
  const hasMore = ref(true);

  let observer: IntersectionObserver | null = null;

  // 手动触发加载
  const loadMore = async () => {
    if (isLoading.value || !hasMore.value || disabled) {
      return;
    }

    isLoading.value = true;
    
    try {
      if (onLoad) {
        await onLoad();
      }
    } catch (error) {
      console.error('无限滚动加载失败:', error);
    } finally {
      isLoading.value = false;
    }
  };

  // 重置状态
  const reset = () => {
    isLoading.value = false;
    hasMore.value = true;
  };

  // 设置是否还有更多数据
  const setHasMore = (value: boolean) => {
    hasMore.value = value;
  };

  // 创建观察器
  const createObserver = () => {
    if (!target.value || !window.IntersectionObserver) {
      return;
    }

    observer = new IntersectionObserver(
      (entries) => {
        const entry = entries[0];
        if (entry.isIntersecting && hasMore.value && !isLoading.value && !disabled) {
          loadMore();
        }
      },
      {
        rootMargin,
        threshold: threshold as number,
      }
    );

    observer.observe(target.value);
  };

  // 销毁观察器
  const destroyObserver = () => {
    if (observer) {
      observer.disconnect();
      observer = null;
    }
  };

  onMounted(() => {
    if (immediate) {
      createObserver();
    }
  });

  onUnmounted(() => {
    destroyObserver();
  });

  return {
    isLoading,
    hasMore,
    loadMore,
    reset,
    setHasMore,
  };
}
