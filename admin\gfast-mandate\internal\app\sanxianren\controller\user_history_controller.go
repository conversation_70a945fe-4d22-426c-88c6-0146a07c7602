/*
* @desc:用户浏览历史控制器
* @company:山东傲英网络科技股份有限公司
* @Author: wolfman
* @Date:   2024/12/19
 */

package controller

import (
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	v1 "github.com/tiger1103/gfast/v3/api/v1/sanxianren"
	"github.com/tiger1103/gfast/v3/internal/app/sanxianren/service"
	systemService "github.com/tiger1103/gfast/v3/internal/app/system/service"
)

// UserHistoryController 用户浏览历史控制器
type UserHistoryController struct{}

// AddHistory 添加浏览历史
func (c *UserHistoryController) AddHistory(ctx context.Context, req *v1.AddHistoryReq) (res *v1.AddHistoryRes, err error) {
	// 获取当前用户ID
	userId := systemService.Context().GetUserId(ctx)
	if userId == 0 {
		return nil, gerror.New("用户未登录")
	}

	// 调用服务添加浏览历史
	err = service.UserHistory().AddHistory(ctx, userId, req)
	if err != nil {
		g.Log().Error(ctx, "添加浏览历史失败:", err)
		return nil, gerror.New(err.Error())
	}

	return &v1.AddHistoryRes{}, nil
}

// GetHistory 获取浏览历史
func (c *UserHistoryController) GetHistory(ctx context.Context, req *v1.GetHistoryReq) (res *v1.GetHistoryRes, err error) {
	// 获取当前用户ID
	userId := systemService.Context().GetUserId(ctx)
	if userId == 0 {
		return nil, gerror.New("用户未登录")
	}

	// 调用服务获取浏览历史
	res, err = service.UserHistory().GetHistory(ctx, userId, req)
	if err != nil {
		g.Log().Error(ctx, "获取浏览历史失败:", err)
		return nil, gerror.New(err.Error())
	}

	return res, nil
}

// ClearHistory 清空浏览历史
func (c *UserHistoryController) ClearHistory(ctx context.Context, req *v1.ClearHistoryReq) (res *v1.ClearHistoryRes, err error) {
	// 获取当前用户ID
	userId := systemService.Context().GetUserId(ctx)
	if userId == 0 {
		return nil, gerror.New("用户未登录")
	}

	// 调用服务清空浏览历史
	err = service.UserHistory().ClearHistory(ctx, userId, req)
	if err != nil {
		g.Log().Error(ctx, "清空浏览历史失败:", err)
		return nil, gerror.New(err.Error())
	}

	return &v1.ClearHistoryRes{}, nil
}
