/*
* @desc:内容推荐算法服务
* @company:山东傲英网络科技股份有限公司
* @Author: wolfman
* @Date:   2024/12/19
 */

package service

import (
	"context"
	"fmt"
	"math"
	"sort"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	v1 "github.com/tiger1103/gfast/v3/api/v1/sanxianren"
	"github.com/tiger1103/gfast/v3/internal/app/sanxianren/dao"
	"github.com/tiger1103/gfast/v3/internal/app/sanxianren/model/entity"
)

type recommendationService struct{}

func init() {
	RegisterRecommendation(&recommendationService{})
}

// RecommendationItem 推荐项目
type RecommendationItem struct {
	ContentType string  `json:"contentType"`
	ContentId   uint64  `json:"contentId"`
	Title       string  `json:"title"`
	Summary     string  `json:"summary"`
	CoverImage  string  `json:"coverImage"`
	Score       float64 `json:"score"`
	Reason      string  `json:"reason"`
}

// GetPersonalizedRecommendations 获取个性化推荐
func (s *recommendationService) GetPersonalizedRecommendations(ctx context.Context, userId uint64, limit int) (*v1.RecommendationsRes, error) {
	// 获取用户行为数据
	userBehavior, err := s.getUserBehavior(ctx, userId)
	if err != nil {
		g.Log().Error(ctx, "获取用户行为数据失败:", err)
		return s.getDefaultRecommendations(ctx, limit)
	}

	// 基于协同过滤的推荐
	collaborativeItems, err := s.getCollaborativeFilteringRecommendations(ctx, userId, userBehavior)
	if err != nil {
		g.Log().Error(ctx, "协同过滤推荐失败:", err)
	}

	// 基于内容的推荐
	contentBasedItems, err := s.getContentBasedRecommendations(ctx, userId, userBehavior)
	if err != nil {
		g.Log().Error(ctx, "基于内容推荐失败:", err)
	}

	// 热门内容推荐
	popularItems, err := s.getPopularRecommendations(ctx, userId)
	if err != nil {
		g.Log().Error(ctx, "热门推荐失败:", err)
	}

	// 合并和排序推荐结果
	allItems := append(collaborativeItems, contentBasedItems...)
	allItems = append(allItems, popularItems...)

	// 去重和排序
	finalItems := s.deduplicateAndSort(allItems, limit)

	return &v1.RecommendationsRes{
		Items: s.convertToAPIFormat(finalItems),
		Total: len(finalItems),
	}, nil
}

// getUserBehavior 获取用户行为数据
func (s *recommendationService) getUserBehavior(ctx context.Context, userId uint64) (*UserBehavior, error) {
	behavior := &UserBehavior{
		UserId:      userId,
		Collections: make(map[string][]uint64),
		History:     make(map[string][]uint64),
		Preferences: make(map[string]float64),
	}

	// 获取收藏数据
	var collections []entity.SxUserCollections
	err := dao.SxUserCollections.Ctx(ctx).
		Where("user_id", userId).
		OrderDesc("created_at").
		Limit(100).
		Scan(&collections)
	if err == nil {
		for _, item := range collections {
			behavior.Collections[item.ContentType] = append(
				behavior.Collections[item.ContentType],
				item.ContentId,
			)
		}
	}

	// 获取浏览历史
	var histories []entity.SxUserHistory
	err = dao.SxUserHistory.Ctx(ctx).
		Where("user_id", userId).
		OrderDesc("viewed_at").
		Limit(200).
		Scan(&histories)
	if err == nil {
		for _, item := range histories {
			behavior.History[item.ContentType] = append(
				behavior.History[item.ContentType],
				item.ContentId,
			)
		}
	}

	// 计算用户偏好
	s.calculateUserPreferences(behavior)

	return behavior, nil
}

// UserBehavior 用户行为数据
type UserBehavior struct {
	UserId      uint64              `json:"userId"`
	Collections map[string][]uint64 `json:"collections"`
	History     map[string][]uint64 `json:"history"`
	Preferences map[string]float64  `json:"preferences"`
}

// calculateUserPreferences 计算用户偏好
func (s *recommendationService) calculateUserPreferences(behavior *UserBehavior) {
	// 收藏权重更高
	for contentType, items := range behavior.Collections {
		behavior.Preferences[contentType] += float64(len(items)) * 2.0
	}

	// 浏览历史权重较低
	for contentType, items := range behavior.History {
		behavior.Preferences[contentType] += float64(len(items)) * 1.0
	}

	// 归一化偏好分数
	total := 0.0
	for _, score := range behavior.Preferences {
		total += score
	}

	if total > 0 {
		for contentType := range behavior.Preferences {
			behavior.Preferences[contentType] /= total
		}
	}
}

// getCollaborativeFilteringRecommendations 协同过滤推荐
func (s *recommendationService) getCollaborativeFilteringRecommendations(ctx context.Context, userId uint64, behavior *UserBehavior) ([]RecommendationItem, error) {
	// 找到相似用户
	similarUsers, err := s.findSimilarUsers(ctx, userId, behavior)
	if err != nil {
		return nil, err
	}

	var recommendations []RecommendationItem

	// 基于相似用户的行为推荐内容
	for _, similarUser := range similarUsers {
		userBehavior, err := s.getUserBehavior(ctx, similarUser.UserId)
		if err != nil {
			continue
		}

		// 推荐相似用户收藏但当前用户未收藏的内容
		for contentType, items := range userBehavior.Collections {
			for _, contentId := range items {
				if !s.userHasInteracted(behavior, contentType, contentId) {
					item := s.createRecommendationItem(ctx, contentType, contentId,
						similarUser.Similarity*0.8, "相似用户喜欢")
					if item != nil {
						recommendations = append(recommendations, *item)
					}
				}
			}
		}
	}

	return recommendations, nil
}

// SimilarUser 相似用户
type SimilarUser struct {
	UserId     uint64  `json:"userId"`
	Similarity float64 `json:"similarity"`
}

// findSimilarUsers 找到相似用户
func (s *recommendationService) findSimilarUsers(ctx context.Context, userId uint64, behavior *UserBehavior) ([]SimilarUser, error) {
	// 简化实现：基于内容类型偏好计算相似度
	var similarUsers []SimilarUser

	// 获取其他用户的行为数据（这里简化为随机选择一些用户）
	var otherUserIds []uint64
	userIdArray, err := dao.SxUserCollections.Ctx(ctx).
		Fields("DISTINCT user_id").
		Where("user_id != ?", userId).
		Limit(50).
		Array()
	if err == nil {
		for _, id := range userIdArray {
			if userId := gconv.Uint64(id); userId > 0 {
				otherUserIds = append(otherUserIds, userId)
			}
		}
	}
	if err != nil {
		return nil, err
	}

	for _, otherUserId := range otherUserIds {
		otherBehavior, err := s.getUserBehavior(ctx, otherUserId)
		if err != nil {
			continue
		}

		similarity := s.calculateUserSimilarity(behavior, otherBehavior)
		if similarity > 0.3 { // 相似度阈值
			similarUsers = append(similarUsers, SimilarUser{
				UserId:     otherUserId,
				Similarity: similarity,
			})
		}
	}

	// 按相似度排序
	sort.Slice(similarUsers, func(i, j int) bool {
		return similarUsers[i].Similarity > similarUsers[j].Similarity
	})

	// 返回前10个最相似的用户
	if len(similarUsers) > 10 {
		similarUsers = similarUsers[:10]
	}

	return similarUsers, nil
}

// calculateUserSimilarity 计算用户相似度（余弦相似度）
func (s *recommendationService) calculateUserSimilarity(behavior1, behavior2 *UserBehavior) float64 {
	// 计算偏好向量的余弦相似度
	var dotProduct, norm1, norm2 float64

	allTypes := make(map[string]bool)
	for t := range behavior1.Preferences {
		allTypes[t] = true
	}
	for t := range behavior2.Preferences {
		allTypes[t] = true
	}

	for contentType := range allTypes {
		pref1 := behavior1.Preferences[contentType]
		pref2 := behavior2.Preferences[contentType]

		dotProduct += pref1 * pref2
		norm1 += pref1 * pref1
		norm2 += pref2 * pref2
	}

	if norm1 == 0 || norm2 == 0 {
		return 0
	}

	return dotProduct / (math.Sqrt(norm1) * math.Sqrt(norm2))
}

// getContentBasedRecommendations 基于内容的推荐
func (s *recommendationService) getContentBasedRecommendations(ctx context.Context, userId uint64, behavior *UserBehavior) ([]RecommendationItem, error) {
	var recommendations []RecommendationItem

	// 基于用户偏好的内容类型推荐
	for contentType, preference := range behavior.Preferences {
		if preference > 0.1 { // 偏好阈值
			items := s.getContentByType(ctx, contentType, userId, int(preference*20))
			for _, item := range items {
				item.Score *= preference
				item.Reason = fmt.Sprintf("基于您对%s的偏好", s.getContentTypeLabel(contentType))
				recommendations = append(recommendations, item)
			}
		}
	}

	return recommendations, nil
}

// getPopularRecommendations 热门内容推荐
func (s *recommendationService) getPopularRecommendations(ctx context.Context, userId uint64) ([]RecommendationItem, error) {
	var recommendations []RecommendationItem

	// 获取最近热门的内容（基于收藏数和浏览数）
	// 这里简化实现，实际应该有更复杂的热度计算算法

	// 热门人物
	var popularPeople []entity.FactoryPeople
	err := dao.FactoryPeople.Ctx(ctx).
		OrderDesc("id"). // 简化：按ID倒序，实际应该按热度排序
		Limit(5).
		Scan(&popularPeople)
	if err == nil {
		for _, people := range popularPeople {
			item := RecommendationItem{
				ContentType: "people",
				ContentId:   uint64(people.Id),
				Title:       people.Name,
				Summary:     s.getPersonSummary(&people),
				CoverImage:  "/static/people-default.jpg",
				Score:       0.6,
				Reason:      "热门人物",
			}
			recommendations = append(recommendations, item)
		}
	}

	return recommendations, nil
}

// 辅助方法
func (s *recommendationService) userHasInteracted(behavior *UserBehavior, contentType string, contentId uint64) bool {
	// 检查收藏
	for _, id := range behavior.Collections[contentType] {
		if id == contentId {
			return true
		}
	}

	// 检查浏览历史
	for _, id := range behavior.History[contentType] {
		if id == contentId {
			return true
		}
	}

	return false
}

func (s *recommendationService) createRecommendationItem(ctx context.Context, contentType string, contentId uint64, score float64, reason string) *RecommendationItem {
	switch contentType {
	case "people":
		var people entity.FactoryPeople
		err := dao.FactoryPeople.Ctx(ctx).Where("id", contentId).Scan(&people)
		if err != nil {
			return nil
		}

		return &RecommendationItem{
			ContentType: contentType,
			ContentId:   contentId,
			Title:       people.Name,
			Summary:     s.getPersonSummary(&people),
			CoverImage:  "/static/people-default.jpg",
			Score:       score,
			Reason:      reason,
		}
	}

	return nil
}

func (s *recommendationService) getContentByType(ctx context.Context, contentType string, userId uint64, limit int) []RecommendationItem {
	var items []RecommendationItem

	switch contentType {
	case "people":
		var people []entity.FactoryPeople
		err := dao.FactoryPeople.Ctx(ctx).
			OrderDesc("id").
			Limit(limit).
			Scan(&people)
		if err == nil {
			for _, p := range people {
				items = append(items, RecommendationItem{
					ContentType: contentType,
					ContentId:   uint64(p.Id),
					Title:       p.Name,
					Summary:     s.getPersonSummary(&p),
					CoverImage:  "/static/people-default.jpg",
					Score:       0.5,
					Reason:      "基于内容推荐",
				})
			}
		}
	}

	return items
}

func (s *recommendationService) getPersonSummary(people *entity.FactoryPeople) string {
	summary := ""
	if people.PersonalExperience != nil && *people.PersonalExperience != "" {
		summary = *people.PersonalExperience
		if len(summary) > 100 {
			summary = summary[:100] + "..."
		}
	}
	if summary == "" {
		summary = fmt.Sprintf("%s的故事", people.Name)
	}
	return summary
}

func (s *recommendationService) getContentTypeLabel(contentType string) string {
	labels := map[string]string{
		"story":   "故事",
		"factory": "工厂",
		"people":  "人物",
	}
	return labels[contentType]
}

func (s *recommendationService) deduplicateAndSort(items []RecommendationItem, limit int) []RecommendationItem {
	// 去重
	seen := make(map[string]bool)
	var unique []RecommendationItem

	for _, item := range items {
		key := fmt.Sprintf("%s_%d", item.ContentType, item.ContentId)
		if !seen[key] {
			seen[key] = true
			unique = append(unique, item)
		}
	}

	// 按分数排序
	sort.Slice(unique, func(i, j int) bool {
		return unique[i].Score > unique[j].Score
	})

	// 限制数量
	if len(unique) > limit {
		unique = unique[:limit]
	}

	return unique
}

func (s *recommendationService) convertToAPIFormat(items []RecommendationItem) []v1.RecommendationItem {
	var result []v1.RecommendationItem
	for _, item := range items {
		result = append(result, v1.RecommendationItem{
			ContentType: item.ContentType,
			ContentId:   item.ContentId,
			Title:       item.Title,
			Summary:     item.Summary,
			CoverImage:  item.CoverImage,
			Score:       item.Score,
			Reason:      item.Reason,
		})
	}
	return result
}

func (s *recommendationService) getDefaultRecommendations(ctx context.Context, limit int) (*v1.RecommendationsRes, error) {
	// 返回默认推荐（最新内容）
	var items []RecommendationItem

	// 获取最新人物
	var people []entity.FactoryPeople
	err := dao.FactoryPeople.Ctx(ctx).
		OrderDesc("id").
		Limit(limit).
		Scan(&people)
	if err == nil {
		for _, p := range people {
			items = append(items, RecommendationItem{
				ContentType: "people",
				ContentId:   uint64(p.Id),
				Title:       p.Name,
				Summary:     s.getPersonSummary(&p),
				CoverImage:  "/static/people-default.jpg",
				Score:       0.3,
				Reason:      "最新内容",
			})
		}
	}

	return &v1.RecommendationsRes{
		Items: s.convertToAPIFormat(items),
		Total: len(items),
	}, nil
}

// UpdateUserBehavior 更新用户行为（用于实时推荐优化）
func (s *recommendationService) UpdateUserBehavior(ctx context.Context, userId uint64, action string, contentType string, contentId uint64) error {
	// 记录用户行为用于推荐算法优化
	g.Log().Info(ctx, fmt.Sprintf("用户行为更新: 用户%d %s了%s %d", userId, action, contentType, contentId))

	// 这里可以添加实时行为记录逻辑
	// 例如：记录到Redis、更新推荐模型等

	return nil
}
