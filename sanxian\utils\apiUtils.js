/**
 * API 工具函数
 * 提供统一的API调用方法和错误处理
 * 后端API由gfast3.2框架实现
 */

/**
 * MD5加密函数
 * @param {string} str - 要加密的字符串
 * @returns {string} MD5哈希值
 */
function md5(str) {
	// 使用uni-app内置的crypto API或者简化实现
	// 如果在uni-app环境中，可以使用内置的crypto
	if (typeof uni !== 'undefined' && uni.requireNativePlugin) {
		try {
			const crypto = uni.requireNativePlugin('DCloud-Crypto');
			if (crypto && crypto.md5) {
				return crypto.md5(str);
			}
		} catch (e) {
			// 如果原生插件不可用，使用JavaScript实现
		}
	}

	// JavaScript MD5实现
	function rotateLeft(value, amount) {
		return (value << amount) | (value >>> (32 - amount));
	}

	function addUnsigned(x, y) {
		const lsw = (x & 0xFFFF) + (y & 0xFFFF);
		const msw = (x >> 16) + (y >> 16) + (lsw >> 16);
		return (msw << 16) | (lsw & 0xFFFF);
	}

	function md5cmn(q, a, b, x, s, t) {
		return addUnsigned(rotateLeft(addUnsigned(addUnsigned(a, q), addUnsigned(x, t)), s), b);
	}

	function md5ff(a, b, c, d, x, s, t) {
		return md5cmn((b & c) | ((~b) & d), a, b, x, s, t);
	}

	function md5gg(a, b, c, d, x, s, t) {
		return md5cmn((b & d) | (c & (~d)), a, b, x, s, t);
	}

	function md5hh(a, b, c, d, x, s, t) {
		return md5cmn(b ^ c ^ d, a, b, x, s, t);
	}

	function md5ii(a, b, c, d, x, s, t) {
		return md5cmn(c ^ (b | (~d)), a, b, x, s, t);
	}

	function coreMD5(x, len) {
		x[len >> 5] |= 0x80 << ((len) % 32);
		x[(((len + 64) >>> 9) << 4) + 14] = len;

		let a = 1732584193;
		let b = -271733879;
		let c = -1732584194;
		let d = 271733878;

		for (let i = 0; i < x.length; i += 16) {
			const olda = a, oldb = b, oldc = c, oldd = d;

			a = md5ff(a, b, c, d, x[i + 0], 7, -680876936);
			d = md5ff(d, a, b, c, x[i + 1], 12, -389564586);
			c = md5ff(c, d, a, b, x[i + 2], 17, 606105819);
			b = md5ff(b, c, d, a, x[i + 3], 22, -1044525330);
			a = md5ff(a, b, c, d, x[i + 4], 7, -176418897);
			d = md5ff(d, a, b, c, x[i + 5], 12, 1200080426);
			c = md5ff(c, d, a, b, x[i + 6], 17, -1473231341);
			b = md5ff(b, c, d, a, x[i + 7], 22, -45705983);
			a = md5ff(a, b, c, d, x[i + 8], 7, 1770035416);
			d = md5ff(d, a, b, c, x[i + 9], 12, -1958414417);
			c = md5ff(c, d, a, b, x[i + 10], 17, -42063);
			b = md5ff(b, c, d, a, x[i + 11], 22, -1990404162);
			a = md5ff(a, b, c, d, x[i + 12], 7, 1804603682);
			d = md5ff(d, a, b, c, x[i + 13], 12, -40341101);
			c = md5ff(c, d, a, b, x[i + 14], 17, -1502002290);
			b = md5ff(b, c, d, a, x[i + 15], 22, 1236535329);

			a = md5gg(a, b, c, d, x[i + 1], 5, -165796510);
			d = md5gg(d, a, b, c, x[i + 6], 9, -1069501632);
			c = md5gg(c, d, a, b, x[i + 11], 14, 643717713);
			b = md5gg(b, c, d, a, x[i + 0], 20, -373897302);
			a = md5gg(a, b, c, d, x[i + 5], 5, -701558691);
			d = md5gg(d, a, b, c, x[i + 10], 9, 38016083);
			c = md5gg(c, d, a, b, x[i + 15], 14, -660478335);
			b = md5gg(b, c, d, a, x[i + 4], 20, -405537848);
			a = md5gg(a, b, c, d, x[i + 9], 5, 568446438);
			d = md5gg(d, a, b, c, x[i + 14], 9, -1019803690);
			c = md5gg(c, d, a, b, x[i + 3], 14, -187363961);
			b = md5gg(b, c, d, a, x[i + 8], 20, 1163531501);
			a = md5gg(a, b, c, d, x[i + 13], 5, -1444681467);
			d = md5gg(d, a, b, c, x[i + 2], 9, -51403784);
			c = md5gg(c, d, a, b, x[i + 7], 14, 1735328473);
			b = md5gg(b, c, d, a, x[i + 12], 20, -1926607734);

			a = md5hh(a, b, c, d, x[i + 5], 4, -378558);
			d = md5hh(d, a, b, c, x[i + 8], 11, -2022574463);
			c = md5hh(c, d, a, b, x[i + 11], 16, 1839030562);
			b = md5hh(b, c, d, a, x[i + 14], 23, -35309556);
			a = md5hh(a, b, c, d, x[i + 1], 4, -1530992060);
			d = md5hh(d, a, b, c, x[i + 4], 11, 1272893353);
			c = md5hh(c, d, a, b, x[i + 7], 16, -155497632);
			b = md5hh(b, c, d, a, x[i + 10], 23, -1094730640);
			a = md5hh(a, b, c, d, x[i + 13], 4, 681279174);
			d = md5hh(d, a, b, c, x[i + 0], 11, -358537222);
			c = md5hh(c, d, a, b, x[i + 3], 16, -722521979);
			b = md5hh(b, c, d, a, x[i + 6], 23, 76029189);
			a = md5hh(a, b, c, d, x[i + 9], 4, -640364487);
			d = md5hh(d, a, b, c, x[i + 12], 11, -421815835);
			c = md5hh(c, d, a, b, x[i + 15], 16, 530742520);
			b = md5hh(b, c, d, a, x[i + 2], 23, -995338651);

			a = md5ii(a, b, c, d, x[i + 0], 6, -198630844);
			d = md5ii(d, a, b, c, x[i + 7], 10, 1126891415);
			c = md5ii(c, d, a, b, x[i + 14], 15, -1416354905);
			b = md5ii(b, c, d, a, x[i + 5], 21, -57434055);
			a = md5ii(a, b, c, d, x[i + 12], 6, 1700485571);
			d = md5ii(d, a, b, c, x[i + 3], 10, -1894986606);
			c = md5ii(c, d, a, b, x[i + 10], 15, -1051523);
			b = md5ii(b, c, d, a, x[i + 1], 21, -2054922799);
			a = md5ii(a, b, c, d, x[i + 8], 6, 1873313359);
			d = md5ii(d, a, b, c, x[i + 15], 10, -30611744);
			c = md5ii(c, d, a, b, x[i + 6], 15, -1560198380);
			b = md5ii(b, c, d, a, x[i + 13], 21, 1309151649);
			a = md5ii(a, b, c, d, x[i + 4], 6, -145523070);
			d = md5ii(d, a, b, c, x[i + 11], 10, -1120210379);
			c = md5ii(c, d, a, b, x[i + 2], 15, 718787259);
			b = md5ii(b, c, d, a, x[i + 9], 21, -343485551);

			a = addUnsigned(a, olda);
			b = addUnsigned(b, oldb);
			c = addUnsigned(c, oldc);
			d = addUnsigned(d, oldd);
		}
		return [a, b, c, d];
	}

	function utf8Encode(str) {
		str = str.replace(/\r\n/g, '\n');
		let utftext = '';
		for (let n = 0; n < str.length; n++) {
			const c = str.charCodeAt(n);
			if (c < 128) {
				utftext += String.fromCharCode(c);
			} else if ((c > 127) && (c < 2048)) {
				utftext += String.fromCharCode((c >> 6) | 192);
				utftext += String.fromCharCode((c & 63) | 128);
			} else {
				utftext += String.fromCharCode((c >> 12) | 224);
				utftext += String.fromCharCode(((c >> 6) & 63) | 128);
				utftext += String.fromCharCode((c & 63) | 128);
			}
		}
		return utftext;
	}

	function convertToWordArray(str) {
		const wordArray = [];
		for (let i = 0; i < str.length * 8; i += 8) {
			wordArray[i >> 5] |= (str.charCodeAt(i / 8) & 0xFF) << (i % 32);
		}
		return wordArray;
	}

	function convertToHex(wordArray) {
		const hexChars = '0123456789abcdef';
		let hex = '';
		for (let i = 0; i < wordArray.length * 4; i++) {
			hex += hexChars.charAt((wordArray[i >> 2] >> ((i % 4) * 8 + 4)) & 0xF) +
				   hexChars.charAt((wordArray[i >> 2] >> ((i % 4) * 8)) & 0xF);
		}
		return hex;
	}

	const utf8String = utf8Encode(str);
	const wordArray = convertToWordArray(utf8String);
	const hash = coreMD5(wordArray, utf8String.length * 8);
	return convertToHex(hash);
}

/**
 * 密码加密函数
 * 使用与后端相同的加密方式：MD5(password + "sanxianren_salt")
 * @param {string} password - 原始密码
 * @returns {string} 加密后的密码
 */
function encryptPassword(password) {
	// 与后端保持一致的加密方式
	const passwordWithSalt = password + "sanxianren_salt";
	return md5(passwordWithSalt);
}

// API基础配置
const API_CONFIG = {
	// 开发环境API地址 - 使用代理，vue.config.js会将/api重写为/api/v1并转发到后端
	BASE_URL: '/api',
	// 生产环境API地址
	PROD_BASE_URL: 'https://www.sanxianren.com/api/v1',
	// 请求超时时间
	TIMEOUT: 10000,
	// 重试次数
	RETRY_COUNT: 3,
	// 开发模式：当后端不可用时使用模拟数据
	MOCK_MODE: true
};

/**
 * 获取API基础URL
 * @returns {string} API基础URL
 */
function getBaseUrl() {
	return API_CONFIG.BASE_URL;
}

/**
 * 生成模拟数据
 * @param {string} type - 数据类型
 * @returns {Object} 模拟数据
 */
function generateMockData(type) {
	const mockData = {
		stories: {
			success: true,
			data: {
				list: [
					{
						id: 1,
						title: '三线建设的峥嵘岁月',
						summary: '回忆那段激情燃烧的岁月，三线建设者们用青春和汗水书写了历史...',
						content: '在那个特殊的年代，无数的建设者响应国家号召，来到祖国的大西南...',
						cover_image: '/static/sanxianren.jpg',
						author_name: '张建国',
						view_count: 1250,
						like_count: 89,
						created_at: '2024-01-15 10:30:00'
					},
					{
						id: 2,
						title: '山沟里的工业奇迹',
						summary: '从无到有建设现代工厂，见证了三线建设的伟大成就...',
						content: '在崇山峻岭之间，一座座现代化工厂拔地而起...',
						cover_image: '/static/sanxianren.jpg',
						author_name: '李明华',
						view_count: 980,
						like_count: 67,
						created_at: '2024-01-14 15:20:00'
					},
					{
						id: 3,
						title: '青春献给三线厂',
						summary: '那些年轻的面孔和无悔的选择，诠释了什么是奉献精神...',
						content: '年轻的工程师们放弃了城市的舒适生活...',
						cover_image: '/static/sanxianren.jpg',
						author_name: '王芳',
						view_count: 1100,
						like_count: 78,
						created_at: '2024-01-13 09:15:00'
					}
				],
				total: 156,
				pageNum: 1,
				pageSize: 10
			}
		},
		factories: {
			success: true,
			data: {
				list: [
					{
						id: 1,
						name: '攀枝花钢铁厂',
						factory_name: '攀枝花钢铁厂',
						location: '四川攀枝花',
						address: '四川省攀枝花市',
						cover_image: '/static/sanxianren.jpg',
						description: '中国重要的钢铁生产基地',
						created_at: '2024-01-10 08:00:00'
					},
					{
						id: 2,
						name: '酒泉钢铁厂',
						factory_name: '酒泉钢铁厂',
						location: '甘肃酒泉',
						address: '甘肃省酒泉市',
						cover_image: '/static/sanxianren.jpg',
						description: '西北地区重要的钢铁企业',
						created_at: '2024-01-09 10:30:00'
					},
					{
						id: 3,
						name: '六盘水煤矿',
						factory_name: '六盘水煤矿',
						location: '贵州六盘水',
						address: '贵州省六盘水市',
						cover_image: '/static/sanxianren.jpg',
						description: '重要的煤炭生产基地',
						created_at: '2024-01-08 14:20:00'
					}
				],
				total: 45,
				pageNum: 1,
				pageSize: 10
			}
		},
		people: {
			success: true,
			data: {
				list: [
					{
						id: 1,
						name: '老张师傅',
						position: '钢铁工人',
						role: '钢铁工人',
						avatar: '/static/sanxianren.jpg',
						factory: '攀枝花钢铁厂',
						work_years: 35,
						created_at: '2024-01-12 16:45:00'
					},
					{
						id: 2,
						name: '李工程师',
						position: '技术专家',
						role: '技术专家',
						avatar: '/static/sanxianren.jpg',
						factory: '酒泉钢铁厂',
						work_years: 28,
						created_at: '2024-01-11 11:30:00'
					},
					{
						id: 3,
						name: '王阿姨',
						position: '后勤保障',
						role: '后勤保障',
						avatar: '/static/sanxianren.jpg',
						factory: '六盘水煤矿',
						work_years: 32,
						created_at: '2024-01-10 13:15:00'
					}
				],
				total: 89,
				pageNum: 1,
				pageSize: 10
			}
		},
		locations: {
			success: true,
			data: {
				list: [
					{
						id: 1,
						name: '三线博物馆',
						description: '展示三线建设历史',
						intro: '展示三线建设历史的重要场所',
						cover_image: '/static/sanxianren.jpg',
						address: '四川省攀枝花市',
						created_at: '2024-01-05 09:00:00'
					},
					{
						id: 2,
						name: '工人文化宫',
						description: '三线工人的精神家园',
						intro: '三线工人的精神家园和文化活动中心',
						cover_image: '/static/sanxianren.jpg',
						address: '甘肃省酒泉市',
						created_at: '2024-01-04 14:30:00'
					},
					{
						id: 3,
						name: '三线遗址公园',
						description: '保护三线工业遗产',
						intro: '保护和展示三线工业遗产的公园',
						cover_image: '/static/sanxianren.jpg',
						address: '贵州省六盘水市',
						created_at: '2024-01-03 10:45:00'
					}
				],
				total: 23,
				pageNum: 1,
				pageSize: 10
			}
		},
		stats: {
			success: true,
			data: {
				totalStories: 156,
				story_count: 156,
				totalFactories: 45,
				factory_count: 45,
				totalPeople: 89,
				people_count: 89,
				totalUsers: 234,
				user_count: 234
			}
		}
	};

	return mockData[type] || { success: false, error: '未知的数据类型' };
}

/**
 * 规范化并拼接完整的请求 URL
 * - 应用端所有 API 只写 /模块/方法，不再写 /api 或 /v1 前缀
 * - BASE_URL 由配置文件统一指定（如 /api/v1），最终拼接为 BASE_URL + /模块/方法
 * @param {string} url 相对的请求路径（如 /sanxianren/user/login）
 * @returns {string} 拼接后的完整 URL
 */
function composeUrl(url) {
	if (!url) return getBaseUrl();
	if (typeof url === 'string' && /^https?:\/\//i.test(url)) {
		return url;
	}
	// 基础地址去除尾部斜杠
	const base = (getBaseUrl() || '').replace(/\/+$/g, '');
	// 标准化 path：确保以单个斜杠开头
	let path = (url || '').replace(/^(?!\/)/, '/');
	// 不再去除任何 /api 或 /v1 前缀，全部由 BASE_URL 控制
	return base + path;
}

/**
 * 获取存储的用户Token
 * @returns {string} 用户Token
 */
/**
 * 获取本地存储的 token
 * - 优先从 userStore 读，兜底从 'user_token' 与旧键 'token' 读取
 */
export function getToken() {
  try {
    // 优先从统一的 userStore 读取，避免多处状态源
    const userStore = require('@/utils/userStore.js').default;
    if (userStore && userStore.token) return userStore.token;
  } catch (e) {
    // ignore
  }
  const v = uni.getStorageSync('user_token') || uni.getStorageSync('token');
  return v || null;
}

/**
 * 设置 token
 * - 同步写入新旧两套键，确保历史页面可读
 */
export function setToken(token) {
  try {
    const userStore = require('@/utils/userStore.js').default;
    if (userStore) {
      userStore.token = token;
      userStore.saveToStorage();
      return;
    }
  } catch (e) {
    // ignore
  }
  if (token) {
    uni.setStorageSync('user_token', token);
    uni.setStorageSync('token', token);
  } else {
    uni.removeStorageSync('user_token');
    uni.removeStorageSync('token');
  }
}

/**
 * 清除 token
 * - 同时清理新旧键
 */
export function clearToken() {
  try {
    const userStore = require('@/utils/userStore.js').default;
    if (userStore) {
      userStore.clearUserData();
      return;
    }
  } catch (e) {
    // ignore
  }
  uni.removeStorageSync('user_token');
  uni.removeStorageSync('token');
}

/**
 * 获取当前页面完整路径（含查询参数）
 * - 用于 401 未授权时记录登录后返回路径
 * - 兼容 H5/小程序：优先使用 $page.fullPath，其次 route + options
 * @returns {string} 形如 /pages/xx/yy?foo=bar 的完整路径，若无法获取则返回首页
 */
function getCurrentFullPath() {
	try {
		const pages = typeof getCurrentPages === 'function' ? getCurrentPages() : [];
		const current = pages && pages.length ? pages[pages.length - 1] : null;
		if (current) {
			// H5/新版 uni-app 提供 $page.fullPath
			if (current.$page && current.$page.fullPath) {
				return current.$page.fullPath;
			}
			// 退化：使用 route + options 拼装查询串
			const route = current.route ? (current.route.startsWith('/') ? current.route : `/${current.route}`) : '';
			const opts = current.options || {};
			const query = Object.keys(opts)
				.map(k => `${encodeURIComponent(k)}=${encodeURIComponent(opts[k])}`)
				.join('&');
			return route ? (query ? `${route}?${query}` : route) : '/pages/index/index';
		}
	} catch (e) {
		console.warn('获取当前路径失败:', e);
	}
	return '/pages/index/index';
}

/**
 * 判断是否为登录页路径
 * @param {string} path - 路径
 * @returns {boolean} 是否为登录页
 */
function isLoginPagePath(path = '') {
	return typeof path === 'string' && path.includes('/pages/auth/login');
}

/**
 * 安全的 Loading 计数器
 * - 解决并发请求下 showLoading/hideLoading 未配对的问题
 * - 跨页面跳转前可使用 force=true 清零，避免残留
 */
let activeLoadingCount = 0;

/**
 * 安全显示 Loading
 * @param {string} title - 显示的提示文字
 */
function safeShowLoading(title = '加载中...') {
	try {
		activeLoadingCount++;
		if (activeLoadingCount === 1) {
			uni.showLoading({ title, mask: true });
		}
	} catch (e) {
		console.warn('safeShowLoading 调用失败:', e);
	}
}

/**
 * 安全隐藏 Loading
 * @param {boolean} [force=false] - 是否强制清零计数
 * 说明：
 * - 当 force=true 时，仅清零内部计数；
 * - 只有在此前计数大于 0 时才会调用 uni.hideLoading，避免出现“showLoading 与 hideLoading 必须配对使用”的控制台告警（H5）。
 */
function safeHideLoading(force = false) {
	try {
		const prev = activeLoadingCount;
		if (force) {
			// 仅重置计数，不无脑 hide，避免未配对的警告
			activeLoadingCount = 0;
		} else if (activeLoadingCount > 0) {
			activeLoadingCount--;
		}
		// 只有在此前确实显示过（prev>0）且计数归零时，才真正关闭 Loading
		if (activeLoadingCount === 0 && prev > 0) {
			uni.hideLoading();
		}
	} catch (e) {
		console.warn('safeHideLoading 调用失败:', e);
	}
}

/**
 * 判断当前是否存在活动中的 Loading
 * @returns {boolean} 是否存在 Loading
 */
function hasActiveLoading() {
	return activeLoadingCount > 0;
}

/**
 * 处理未授权（401）后的统一跳转逻辑
 * - 保存当前完整路径为 redirect_url，便于登录成功后返回原页面
 * - 避免在登录页自身重复保存与重定向，防止递归跳转
 * - 使用 redirectTo 将当前页替换为登录页，避免返回栈出现多个登录页
 * - 在极端场景（如当前栈异常）下，使用 reLaunch 作为兜底
 */
function handleUnauthorizedRedirect() {
	try {
		// 在跳转前尽量关闭潜在残留的 Loading，避免跨页未配对告警（强制清零）
		try { safeHideLoading(true); } catch (e) {}

		// 计算当前完整路径（含查询参数）
		const currentPath = getCurrentFullPath();

		// 避免在登录页自身保存或重定向，防止进入循环
		if (!isLoginPagePath(currentPath)) {
			try {
				uni.setStorageSync('redirect_url', currentPath);
			} catch (e) {
				console.warn('保存 redirect_url 失败:', e);
			}
		}

		// 轻微延迟，确保 Toast 等提示能被用户看到
		setTimeout(() => {
			// 再次兜底关闭 Loading（强制清零）
			try { safeHideLoading(true); } catch (e) {}
			uni.redirectTo({
				url: '/pages/auth/login',
				fail: () => {
					// 兜底：如 redirectTo 失败（如页面栈异常），强制重启到登录页
					try { safeHideLoading(true); } catch (e) {}
					uni.reLaunch({ url: '/pages/auth/login' });
				}
			});
		}, 300);
	} catch (error) {
		console.error('未授权跳转处理失败:', error);
		// 最终兜底，保证用户能回到登录页
		setTimeout(() => {
			try { safeHideLoading(true); } catch (e) {}
			uni.reLaunch({ url: '/pages/auth/login' });
		}, 300);
	}
}

/**
 * 统一的API请求方法
 * @param {Object} options - 请求配置
 * @param {string} options.url - 请求URL（相对路径）
 * @param {string} options.method - 请求方法
 * @param {Object} options.data - 请求数据
 * @param {Object} options.header - 请求头
 * @param {boolean} options.needAuth - 是否需要认证
 * @param {boolean} options.showLoading - 是否显示加载提示
 * @param {string} options.loadingText - 加载提示文本
 * @returns {Promise} 请求Promise
 */
function request(options = {}) {
	return new Promise((resolve, reject) => {
		const {
			url,
			method = 'GET',
			data = {},
			header = {},
			needAuth = true,
			showLoading = false,
			loadingText = '加载中...'
		} = options;
		
		// 显示加载提示（安全封装）
		if (showLoading) {
			safeShowLoading(loadingText);
		}
		
		// 构建完整URL（自动处理多种前缀写法）
		const fullUrl = composeUrl(url);
		
		// 构建请求头
		const requestHeader = {
			'Content-Type': 'application/json',
			...header
		};
		
		// 添加认证Token
		if (needAuth) {
			const token = getToken();
			if (token) {
				// gfast-token通常从X-Token header或query参数中获取token
				requestHeader['X-Token'] = token;
				requestHeader['Authorization'] = 'Bearer ' + token;
			}
		}
		
		// 发起请求
		uni.request({
			url: fullUrl,
			method: method.toUpperCase(),
			data,
			header: requestHeader,
			timeout: API_CONFIG.TIMEOUT,
			success: (res) => {
				// 隐藏加载提示（安全封装）
				if (showLoading) {
					safeHideLoading();
				}
				
				// 处理响应
				handleResponse(res, resolve, reject);
			},
			fail: (error) => {
				// 隐藏加载提示（安全封装）
				if (showLoading) {
					safeHideLoading();
				}
				
				// 处理网络错误
				handleNetworkError(error, reject);
			}
		});
	});
}

/**
 * 处理API响应
 * @param {Object} res - 响应对象
 * @param {Function} resolve - Promise resolve
 * @param {Function} reject - Promise reject
 */
function handleResponse(res, resolve, reject) {
	const { statusCode, data } = res;
	
	// HTTP状态码检查
	if (statusCode === 200) {
		// 允许返回体为对象且包含常见的字段组合（code/success/data/msg/message）
		if (typeof data === 'object' && data !== null) {
			const code = typeof data.code !== 'undefined' ? data.code : undefined;
			// 统一以 success 字段为成功判断标准，去除对 code 值的依赖
			// 扩展兼容：后端返回 code === 0 也视为成功（gfast 规范）
			const successFlag = (data && (data.success === true || code === 0));
			const message = data.message || data.msg || '';
			// 兼容不同的载荷字段名：data 或 result，没有则回传原始对象
			const payload = (typeof data.data !== 'undefined') ? data.data : (typeof data.result !== 'undefined' ? data.result : data);
			
			if (successFlag) {
				// 请求成功，转换为前端期望的统一格式
				resolve({
					success: true,
					data: payload,
					message,
					code: typeof code !== 'undefined' ? code : 200
				});
			} else if (typeof code !== 'undefined') {
				// 存在业务 code 但非成功，按业务错误处理
				handleBusinessError(data, reject);
			} else {
				// 对象但不含可识别的成功/错误标记
				console.error('API响应格式不包含 code/success 等常见字段:', data);
				reject({
					success: false,
					error: '请求失败',
					code: undefined,
					data
				});
			}
		} else {
			// 响应不是预期的JSON格式（可能是HTML页面）
			console.error('API响应格式错误，收到非JSON数据:', typeof data === 'string' ? data.substring(0, 200) + '...' : data);
			reject({
				success: false,
				error: '请求失败',
				code: undefined,
				data
			});
		}
	} else if (statusCode === 401) {
		// 认证失败处理：支持“登录后短时间内首个401抑制清理”
		try {
			const userStore = require('@/utils/userStore.js').default;
			if (userStore && typeof userStore.shouldSuppressImmediateLogout === 'function' && userStore.shouldSuppressImmediateLogout()) {
				// 函数说明：登录刚完成的短时间内，可能因后端传播延迟/接口竞态导致401，首个401不清理会话，只提示用户稍后重试
				uni.showToast({
					title: '登录状态正在生效，请稍后重试',
					icon: 'none'
				});
				return reject({ success: false, code: 401, error: '未授权（已抑制清理）' });
			}
		} catch (e) { /* ignore */ }
		// 认证失败：清除Token、提示并跳转登录（带回跳地址）
		clearToken();
		uni.showToast({
			title: '登录已过期，请重新登录',
			icon: 'none'
		});
		handleUnauthorizedRedirect();
		return reject({ success: false, code: 401, error: '未授权' });
	} else if (statusCode >= 500) {
		// 服务器错误
		uni.showToast({
			title: '服务器开小差了，请稍后重试',
			icon: 'none'
		});
		reject({ success: false, code: statusCode, error: '服务器错误' });
	} else {
		// 其他HTTP状态码
		uni.showToast({
			title: `请求失败（${statusCode}）`,
			icon: 'none'
		});
		reject({ success: false, code: statusCode, error: '请求失败' });
	}
}

/**
 * 处理网络错误
 * @param {Object} error - 错误对象
 * @param {Function} reject - Promise reject
 */
function handleNetworkError(error, reject) {
	console.error('网络请求失败:', error);
	uni.showToast({
		title: '网络连接失败，请检查网络设置',
		icon: 'none'
	});
	reject({
		success: false,
		error: error || '网络错误'
	});
}

/**
 * 处理业务错误
 * @param {Object} data - 响应数据
 * @param {Function} reject - Promise reject
 */
function handleBusinessError(data, reject) {
	const message = data.message || data.msg || '操作失败';
	const code = data.code;
	
	// 根据业务错误码进行特殊处理
	if (code === 401) {
		// Token过期或无效：加入“登录后短时间内首个401抑制清理”
		try {
			const userStore = require('@/utils/userStore.js').default;
			if (userStore && typeof userStore.shouldSuppressImmediateLogout === 'function' && userStore.shouldSuppressImmediateLogout()) {
				// 函数说明：登录刚完成的短时间内，可能因后端传播延迟/接口竞态导致401，首个401不清理会话，只提示用户稍后重试
				uni.showToast({
					title: '登录状态正在生效，请稍后重试',
					icon: 'none'
				});
				return reject({ success: false, code: 401, error: '未授权（已抑制清理）', data });
			}
		} catch (e) { /* ignore */ }
		// Token过期或无效：清理并跳转登录（带回跳地址）
		clearToken();
		uni.showToast({
			title: '登录已过期，请重新登录',
			icon: 'none'
		});
		handleUnauthorizedRedirect();
	} else {
		// 其他业务错误
		uni.showToast({
			title: message,
			icon: 'none'
		});
	}
	
	reject({
		success: false,
		code,
		error: message,
		data
	});
}

// 导出API工具函数
export {
	request,
	// getToken, // 已通过 export function 导出，避免重复导出
	// setToken,
	// clearToken,
	getBaseUrl,
	composeUrl,
	getVerificationLink,
	// 导出安全 Loading 方法，便于跨模块（如登录跳转、上传）在导航前强制关闭残留 Loading
	safeShowLoading,
	safeHideLoading,
	hasActiveLoading
};

// 用户相关API
export function sendSmsCode(phoneOrParams) {
	/**
	 * 发送短信验证码
	 * - 兼容两种调用方式：
	 *   1) sendSmsCode('13800138000')              -> 默认 type = 'register'
	 *   2) sendSmsCode({ phone, type: 'login' })   -> 可显式指定 type，默认为 'login'
	 * @param {string|Object} phoneOrParams - 手机号或包含 phone/type 的对象
	 * @returns {Promise}
	 */
	const isObj = typeof phoneOrParams === 'object' && phoneOrParams !== null;
	const phone = isObj ? phoneOrParams.phone : phoneOrParams;
	// 字符串入参多来自注册流程，默认 register；对象入参多来自登录页，默认 login
	const type = isObj ? (phoneOrParams.type || 'login') : 'register';
	return request({
		url: '/sanxianren/user/send-sms-code',
		method: 'POST',
		data: { phone, type },
		needAuth: false
	});
}

export async function smsLogin(arg1, arg2) {
	/**
	 * 短信验证码登录
	 * - 兼容两种调用方式：
	 *   1) smsLogin('13800138000', '123456')
	 *   2) smsLogin({ phone: '13800138000', code: '123456' })
	 * - 路由回退策略：
	 *   a) 首选 /sanxianren/user/sms-login
	 *   b) 如返回 404，再尝试 /auth/sms-login（部分后端模块化为 auth）
	 *   c) 如仍 404，再尝试 /sanxianren/user/login（部分后端将短信登录合并到通用登录）
	 * @param {string|Object} arg1 - 手机号或对象
	 * @param {string} [arg2] - 验证码（当第一个参数为字符串时）
	 * @returns {Promise}
	 */
	const isObj = typeof arg1 === 'object' && arg1 !== null;
	const phone = isObj ? arg1.phone : arg1;
	const code = isObj ? (arg1.code || arg1.smsCode) : arg2;
	// 统一载荷，兼容不同后端字段名
	const payload = { phone, code, smsCode: code };

	// 优先尝试 sanxianren 模块路径
	try {
		return await request({
			url: '/sanxianren/user/sms-login',
			method: 'POST',
			data: payload,
			needAuth: false
		});
	} catch (e) {
		if (e && e.code === 404) {
			// 回退到 auth 模块路径
			try {
				return await request({
					url: '/auth/sms-login',
					method: 'POST',
					data: payload,
					needAuth: false
				});
			} catch (e2) {
				if (e2 && e2.code === 404) {
					// 最后回退到通用登录（部分后端用同一路径区分参数）
					return request({
						url: '/sanxianren/user/login',
						method: 'POST',
						data: payload,
						needAuth: false
					});
				}
				throw e2;
			}
		}
		throw e;
	}
}

export function verifySmsCode(arg1, arg2) {
	/**
	 * 验证短信验证码
	 * - 兼容两种调用方式：
	 *   1) verifySmsCode('13800138000', '123456')
	 *   2) verifySmsCode({ phone: '13800138000', code: '123456' })
	 * @param {string|Object} arg1 - 手机号或对象
	 * @param {string} [arg2] - 验证码（当第一个参数为字符串时）
	 * @returns {Promise}
	 */
	const isObj = typeof arg1 === 'object' && arg1 !== null;
	const phone = isObj ? arg1.phone : arg1;
	const code = isObj ? (arg1.code || arg1.smsCode) : arg2;
	return request({
		url: '/sanxianren/user/verify-sms-code',
		method: 'POST',
		data: { phone, code },
		needAuth: false
	});
}

export function register(userData) {
	// 如果包含密码，需要加密
	const data = { ...userData };
	if (data.password) {
		data.password = encryptPassword(data.password);
	}
	return request({
		url: '/sanxianren/user/register',
		method: 'POST',
		data,
		needAuth: false
	});
}

export function registerViaVerification(userData) {
	// 如果包含密码，需要加密
	const data = { ...userData };
	if (data.password) {
		data.password = encryptPassword(data.password);
	}
	return request({
		url: '/sanxianren/user/register-via-verification',
		method: 'POST',
		data,
		needAuth: false
	});
}

export function verifyFactory(factoryCode) {
	return request({
		url: '/sanxianren/factory/verify',
		method: 'POST',
		data: { factoryCode },
		needAuth: false
	});
}

export function getOrderStats() {
	// 获取订单统计（需要登录鉴权）
	return request({
		url: '/sanxianren/order/stats',
		method: 'GET',
		needAuth: true
	});
}




export function uploadAvatar(fileOrOptions) {
	/**
	 * 上传头像
	 * - 采用后端统一上传接口：POST /system/upload/singleImg（multipart/form-data）
	 * - 入参兼容两种形式：
	 *   1) 传入对象：{ file: { path, name, size, type } }
	 *   2) 直接传入文件对象：{ path, name, size, type } 或本地路径字符串
	 * - 返回统一结构：{ success: true, data: { url, id? } }
	 * @param {object|string} fileOrOptions 文件或包含 file 字段的对象
	 * @returns {Promise<{success:boolean,data:{url:string,id?:any}}>} 上传结果
	 */
	try {
		const uploadUtils = require('@/utils/uploadUtils.js');
		const { uploadFile } = uploadUtils;
		let file = null;
		if (fileOrOptions && typeof fileOrOptions === 'object' && fileOrOptions.file) {
			file = fileOrOptions.file;
		} else if (fileOrOptions && typeof fileOrOptions === 'object' && (fileOrOptions.path || fileOrOptions.name)) {
			file = fileOrOptions;
		} else if (typeof fileOrOptions === 'string') {
			file = { path: fileOrOptions, name: 'avatar.jpg', size: 0, type: 'image' };
		}
		if (!file || !file.path) {
			return Promise.reject(new Error('无效的头像文件'));
		}
		return uploadFile(file, { url: '/system/upload/singleImg', name: 'file' })
			.then(res => ({
				success: true,
				data: {
					url: (res && (res.url || res.path)) || '',
					id: res && res.id
				}
			}));
	} catch (e) {
		return Promise.reject(e);
	}
}

/**
 * 获取故事列表
 * - 首选路径：POST /sanxianren/story/list
 * - 404/405 回退顺序（兼容不同后端路由差异）：
 *   1) POST /sanxianren/story/page
 *   2) POST /sanxianren/story/search
 *   3) GET  /sanxianren/story/list（极少数后端使用 GET）
 * - 注意：URL 仅写业务路径（/sanxianren/...），前缀由 BASE_URL=/api 与本地代理重写到 /api/v1 统一处理
 * @param {Object} params - 查询参数，如 { pageNum, pageSize, orderBy }
 * @returns {Promise<{success:boolean,data:any,message?:string,code?:number}>}
 */
export async function getStoryList(params = {}) {
	try {
		return await request({
			url: '/sanxianren/story/list',
			method: 'POST',
			data: params,
			needAuth: false
		});
	} catch (e1) {
		if (e1 && (e1.code === 404 || e1.code === 405)) {
			try {
				return await request({
					url: '/sanxianren/story/page',
					method: 'POST',
					data: params,
					needAuth: false
				});
			} catch (e2) {
				if (e2 && (e2.code === 404 || e2.code === 405)) {
					try {
						return await request({
							url: '/sanxianren/story/search',
							method: 'POST',
							data: params,
							needAuth: false
						});
					} catch (e3) {
						if (e3 && (e3.code === 404 || e3.code === 405)) {
							// 最后再尝试 GET 列表（少数服务使用 GET 查询）
							try {
								return await request({
									url: '/sanxianren/story/list',
									method: 'GET',
									data: params,
									needAuth: false
								});
							} catch (e4) {
								// 如果开启模拟模式，返回模拟数据
								if (API_CONFIG.MOCK_MODE) {
									console.warn('API请求失败，使用模拟数据:', e4);
									return generateMockData('stories');
								}
								throw e4;
							}
						}
						throw e3;
					}
				}
				throw e2;
			}
		}
		// 如果开启模拟模式，返回模拟数据
		if (API_CONFIG.MOCK_MODE) {
			console.warn('API请求失败，使用模拟数据:', e1);
			return generateMockData('stories');
		}
		throw e1;
	}
}

export function createStory(storyData) {
	return request({
		url: '/sanxianren/story/add',
		method: 'POST',
		data: storyData,
		needAuth: true
	});
}

export function updateStory(id, storyData) {
	return request({
		url: '/sanxianren/story/edit',
		method: 'PUT',
		data: { id, ...storyData },
		needAuth: true
	});
}

export function deleteStory(ids) {
	return request({
		url: '/sanxianren/story/delete',
		method: 'DELETE',
		params: { ids },
		needAuth: true
	});
}

export function likeStory(storyId) {
	return request({
		url: '/sanxianren/story/like',
		method: 'POST',
		data: { storyId },
		needAuth: true
	});
}

export function unlikeStory(storyId) {
	return request({
		url: '/sanxianren/story/unlike',
		method: 'POST',
		data: { storyId },
		needAuth: true
	});
}

export function followUser(userId) {
	return request({
		url: '/sanxianren/user/follow',
		method: 'POST',
		data: { userId },
		needAuth: true
	});
}

export function unfollowUser(userId) {
	return request({
		url: '/sanxianren/user/unfollow',
		method: 'POST',
		data: { userId },
		needAuth: true
	});
}

export function collectStory(storyId) {
	return request({
		url: '/sanxianren/story/collect',
		method: 'POST',
		data: { storyId },
		needAuth: true
	});
}

export function getComments(storyId, params = {}) {
	return request({
		url: '/sanxianren/comment/list',
		method: 'GET',
		data: { storyId, ...params },
		needAuth: false
	});
}

export function addComment(commentData) {
	return request({
		url: '/sanxianren/comment/add',
		method: 'POST',
		data: commentData,
		needAuth: true
	});
}

export function deleteComment(commentId) {
	return request({
		url: '/sanxianren/comment/delete',
		method: 'DELETE',
		data: { id: commentId },
		needAuth: true
	});
}

export function getFactoryPeopleDetail(id, options = {}) {
	/**
	 * 获取工厂人员详情
	 * @param {number|string} id - 人员ID
	 * @param {Object} [options] - 额外配置
	 * @param {boolean} [options.showLoading=false] - 是否显示加载中
	 * @param {string} [options.loadingText='加载中...'] - 加载提示文案
	 * @returns {Promise}
	 */
	return request({
		url: '/sanxianren/factoryPeople/get',
		method: 'GET',
		data: { id },
		needAuth: false,
		showLoading: !!options.showLoading,
		loadingText: options.loadingText || '加载中...'
	});
}

export function getFactoryPeopleList(params = {}) {
	return request({
		url: '/sanxianren/factoryPeople/list',
		method: 'GET',
		data: params,
		needAuth: false
	});
}

export function addFactoryPeople(data) {
	return request({
		url: '/sanxianren/factoryPeople/add',
		method: 'POST',
		data,
		needAuth: false
	});
}

/**
 * 更新工厂人员资料（个人资料编辑）
 * - 路由：PUT /sanxianren/factoryPeople/edit
 * - 需要登录态（Token）：由后端 /api/v1 路由组统一挂载鉴权中间件
 * - data 字段示例：{ id, avatar, name, gender, birthYear, phone, factoryCode, factory, position, workYears, bio }
 * @param {Object} data 需要提交的资料数据
 * @returns {Promise<{success:boolean, data?:any, code?:number, error?:string}>}
 */
export function editFactoryPeople(data) {
	return request({
		url: '/sanxianren/factoryPeople/edit',
		method: 'PUT',
		data,
		needAuth: true
	});
}

export function deleteFactoryPeople(ids) {
	return request({
		url: '/sanxianren/factoryPeople/delete',
		method: 'DELETE',
		params: { ids },
		needAuth: false
	});
}

// ==================== 用户个人中心相关API ====================

/**
 * 获取用户个人资料
 * @returns {Promise<{success:boolean, data?:any, code?:number, error?:string}>}
 */
export function getUserProfile() {
	return request({
		url: '/sanxianren/user/profile',
		method: 'GET',
		needAuth: true
	});
}

/**
 * 编辑用户个人资料
 * @param {Object} data 用户资料数据
 * @returns {Promise<{success:boolean, data?:any, code?:number, error?:string}>}
 */
export function editUserProfile(data) {
	return request({
		url: '/sanxianren/user/profile/edit',
		method: 'PUT',
		data,
		needAuth: true
	});
}

/**
 * 获取用户投稿列表
 * @param {Object} params 查询参数
 * @returns {Promise<{success:boolean, data?:any, code?:number, error?:string}>}
 */
export function getUserSubmissions(params = {}) {
	return request({
		url: '/sanxianren/user/submissions',
		method: 'GET',
		params,
		needAuth: true
	});
}

/**
 * 获取用户投稿统计
 * @returns {Promise<{success:boolean, data?:any, code?:number, error?:string}>}
 */
export function getUserSubmissionStats() {
	return request({
		url: '/sanxianren/user/submissions/stats',
		method: 'GET',
		needAuth: true
	});
}

/**
 * 获取用户VIP信息
 * @returns {Promise<{success:boolean, data?:any, code?:number, error?:string}>}
 */
export function getUserVipInfo() {
	return request({
		url: '/sanxianren/user/vip',
		method: 'GET',
		needAuth: true
	});
}

/**
 * 获取用户徽章列表
 * @returns {Promise<{success:boolean, data?:any, code?:number, error?:string}>}
 */
export function getUserBadges() {
	return request({
		url: '/sanxianren/user/badges',
		method: 'GET',
		needAuth: true
	});
}

// ==================== 用户收藏相关API ====================

/**
 * 添加收藏
 * @param {Object} data 收藏数据 {contentType, contentId}
 * @returns {Promise<{success:boolean, data?:any, code?:number, error?:string}>}
 */
export function addCollection(data) {
	return request({
		url: '/sanxianren/user/collections/add',
		method: 'POST',
		data,
		needAuth: true
	});
}

/**
 * 取消收藏
 * @param {Object} data 收藏数据 {contentType, contentId}
 * @returns {Promise<{success:boolean, data?:any, code?:number, error?:string}>}
 */
export function removeCollection(data) {
	return request({
		url: '/sanxianren/user/collections/remove',
		method: 'DELETE',
		data,
		needAuth: true
	});
}

/**
 * 获取收藏列表
 * @param {Object} params 查询参数
 * @returns {Promise<{success:boolean, data?:any, code?:number, error?:string}>}
 */
export function getCollections(params = {}) {
	return request({
		url: '/sanxianren/user/collections',
		method: 'GET',
		params,
		needAuth: true
	});
}

/**
 * 检查收藏状态
 * @param {Object} params 查询参数 {contentType, contentId}
 * @returns {Promise<{success:boolean, data?:any, code?:number, error?:string}>}
 */
export function checkCollection(params) {
	return request({
		url: '/sanxianren/user/collections/check',
		method: 'GET',
		params,
		needAuth: true
	});
}

// ==================== 用户浏览历史相关API ====================

/**
 * 添加浏览历史
 * @param {Object} data 浏览历史数据 {contentType, contentId}
 * @returns {Promise<{success:boolean, data?:any, code?:number, error?:string}>}
 */
export function addHistory(data) {
	return request({
		url: '/sanxianren/user/history/add',
		method: 'POST',
		data,
		needAuth: true
	});
}

/**
 * 获取浏览历史
 * @param {Object} params 查询参数
 * @returns {Promise<{success:boolean, data?:any, code?:number, error?:string}>}
 */
export function getHistory(params = {}) {
	return request({
		url: '/sanxianren/user/history',
		method: 'GET',
		params,
		needAuth: true
	});
}

/**
 * 清空浏览历史
 * @param {Object} data 清空参数 {contentType}
 * @returns {Promise<{success:boolean, data?:any, code?:number, error?:string}>}
 */
export function clearHistory(data = {}) {
	return request({
		url: '/sanxianren/user/history/clear',
		method: 'DELETE',
		data,
		needAuth: true
	});
}

// ==================== 用户通知相关API ====================

/**
 * 获取通知列表
 * @param {Object} params 查询参数
 * @returns {Promise<{success:boolean, data?:any, code?:number, error?:string}>}
 */
export function getNotifications(params = {}) {
	return request({
		url: '/sanxianren/user/notifications',
		method: 'GET',
		params,
		needAuth: true
	});
}

/**
 * 标记通知已读
 * @param {Object} data 标记参数 {notificationId}
 * @returns {Promise<{success:boolean, data?:any, code?:number, error?:string}>}
 */
export function markNotificationRead(data) {
	return request({
		url: '/sanxianren/user/notifications/read',
		method: 'PUT',
		data,
		needAuth: true
	});
}

/**
 * 获取通知统计
 * @returns {Promise<{success:boolean, data?:any, code?:number, error?:string}>}
 */
export function getNotificationStats() {
	return request({
		url: '/sanxianren/user/notifications/stats',
		method: 'GET',
		needAuth: true
	});
}

/**
 * 获取提交统计数据
 * @returns {Promise} 提交统计数据
 */
export function getSubmissionStats() {
	// 获取提交统计数据（需要登录鉴权）
	return request({
		url: '/sanxianren/stats/submissions',
		method: 'GET',
		needAuth: true
	});
}

/**
 * 系统账号密码登录
 * - 路由回退策略（处理不同后端路由差异导致的 404）
 *   1) 优先 /sanxianren/user/login（sanxianren 模块）
 *   2) 如返回 404 回退到 /auth/login（部分项目认证模块独立为 auth）
 *   3) 如仍返回 404 回退到 /user/login（通用 user 空间）
 * - 统一 needAuth=false，由请求层处理 Token 注入与状态
 * @param {string} phone 手机号
 * @param {string} password 密码
 * @returns {Promise} 登录结果 Promise
 */
export async function systemLogin(phone, password) {
	// 对密码进行加密，与后端保持一致
	const encryptedPassword = encryptPassword(password);
	const data = { phone, password: encryptedPassword };

	return await request({
		url: '/sanxianren/user/login',
		method: 'POST',
		data,
		needAuth: false
	});
}

/**
 * 用户登录（账号/密码）
 * - 首选 /sanxianren/user/login（sanxianren 模块）
 * - 如返回 404，回退至 /auth/login（部分后端将登录放在 auth 模块）
 * - 如仍返回 404，回退至 /user/login（部分后端使用通用 user 空间）
 * 说明：统一 needAuth=false，由请求层处理 Token 注入与状态
 * @param {Object} data 登录数据
 * @returns {Promise} 登录结果 Promise
 */
export async function login(data) {
	try {
		return await request({
			url: '/sanxianren/user/login',
			method: 'POST',
			data,
			needAuth: false
		});
	} catch (e1) {
		if (e1 && e1.code === 404) {
			try {
				return await request({
					url: '/auth/login',
					method: 'POST',
					data,
					needAuth: false
				});
			} catch (e2) {
				if (e2 && e2.code === 404) {
					return request({
						url: '/user/login',
						method: 'POST',
						data,
						needAuth: false
					});
				}
				throw e2;
			}
		}
		throw e1;
	}
}

/**
 * 确认用户
 * @param {Object} data - 用户确认数据
 * @returns {Promise} 确认结果
 */
export function confirmUser(data) {
	/**
	 * 确认用户（证明人操作）
	 * - 通过分享链接进入的确认流程，允许未登录提交
	 * - 服务端可基于链接签名/一次性令牌校验权限
	 * @param {Object} data - 用户确认数据
	 * @returns {Promise} 确认结果
	 */
	return request({
		url: '/sanxianren/user/confirm',
		method: 'POST',
		data,
		needAuth: false
	});
}

/**
 * 获取故事状态
 * @param {string} storyId - 故事ID
 * @returns {Promise} 故事状态
 */
export function getStoryStatus(storyId) {
	return request({
		url: '/sanxianren/story/status',
		method: 'GET',
		data: { id: storyId },
		needAuth: false
	});
}

/**
 * 获取用户认证信息
 * @param {string} userId - 用户ID
 * @returns {Promise} 用户认证信息
 */
export function getUserVerifications(userId) {
	return request({
		url: '/sanxianren/user/verifications',
		method: 'GET',
		data: { userId },
		needAuth: true
	});
}

/**
 * 获取认证状态
 * @param {string} verificationId - 认证ID
 * @returns {Promise} 认证状态
 */
export function getVerificationStatus(verificationId) {
	/**
	 * 获取认证状态
	 * - 用于分享页/确认页展示，不强制登录
	 * @param {string} verificationId - 认证ID
	 * @returns {Promise} 认证状态
	 */
	return request({
		url: '/sanxianren/verification/status',
		method: 'GET',
		data: { id: verificationId },
		needAuth: false
	});
}

/**
 * 提交故事
 * @param {Object} storyData - 故事数据
 * @returns {Promise} 提交结果
 */
export function submitStory(storyData) {
	return request({
		url: '/sanxianren/story/submit',
		method: 'POST',
		data: storyData,
		needAuth: true
	});
}

/**
 * 申请工厂认证
 * @param {Object} data - 认证申请数据
 * @returns {Promise} 申请结果
 */
export function applyFactoryVerification(data) {
	return request({
		url: '/sanxianren/factory/apply-verification',
		method: 'POST',
		data,
		needAuth: true
	});
}

/**
 * 更新故事计数器
 * @param {Object} data - 计数器数据
 * @returns {Promise} 更新结果
 */
export function updateStoryCounters(data) {
	return request({
		url: '/sanxianren/story/updateCounters',
		method: 'POST',
		data,
		needAuth: false
	});
}

/**
 * 获取认证链接信息
 * @param {string} verificationId - 认证ID
 * @returns {Promise} 认证链接信息
 */
function getVerificationLink(verificationId) {
	return request({
		url: `/verification/link/${verificationId}`,
		method: 'GET',
		needAuth: false
	});
}

/**
 * 文件上传（重新导出uploadUtils中的函数）
 */
export { uploadFile } from './uploadUtils.js';

/**
 * 获取故事详情
 * - 后端接口为 POST /sanxianren/story/detail（经代理重写为 /api/v1/...）
 * - 入参使用 id；如后端同时兼容 storyId，可在服务端做兼容
 * @param {number|string} id 故事ID
 * @returns {Promise} 请求 Promise
 */
export function getStoryDetail(id) {
	return request({
		url: '/sanxianren/story/detail',
		method: 'POST',
		data: { id },
		needAuth: false
	});
}

// ==================== 首页数据相关API ====================

/**
 * 获取热门工厂列表
 * @param {Object} params - 查询参数，如 { pageNum, pageSize, orderBy }
 * @returns {Promise<{success:boolean,data:any,message?:string,code?:number}>}
 */
export async function getHotFactories(params = {}) {
	try {
		return await request({
			url: '/sanxianren/factory/hot',
			method: 'POST',
			data: params,
			needAuth: false
		});
	} catch (e1) {
		// 如果热门工厂接口不存在，尝试获取普通工厂列表
		if (e1 && (e1.code === 404 || e1.code === 405)) {
			try {
				return await request({
					url: '/sanxianren/factory/list',
					method: 'POST',
					data: { ...params, orderBy: 'created_at desc' },
					needAuth: false
				});
			} catch (e2) {
				// 如果开启模拟模式，返回模拟数据
				if (API_CONFIG.MOCK_MODE) {
					console.warn('工厂API请求失败，使用模拟数据:', e2);
					return generateMockData('factories');
				}
				throw e2;
			}
		}
		// 如果开启模拟模式，返回模拟数据
		if (API_CONFIG.MOCK_MODE) {
			console.warn('工厂API请求失败，使用模拟数据:', e1);
			return generateMockData('factories');
		}
		throw e1;
	}
}

/**
 * 获取热门三线人列表
 * @param {Object} params - 查询参数，如 { pageNum, pageSize, orderBy }
 * @returns {Promise<{success:boolean,data:any,message?:string,code?:number}>}
 */
export async function getHotPeople(params = {}) {
	try {
		return await request({
			url: '/sanxianren/factoryPeople/hot',
			method: 'POST',
			data: params,
			needAuth: false
		});
	} catch (e1) {
		// 如果热门人员接口不存在，尝试获取普通人员列表
		if (e1 && (e1.code === 404 || e1.code === 405)) {
			try {
				return await getFactoryPeopleList({ ...params, orderBy: 'created_at desc' });
			} catch (e2) {
				// 如果开启模拟模式，返回模拟数据
				if (API_CONFIG.MOCK_MODE) {
					console.warn('人员API请求失败，使用模拟数据:', e2);
					return generateMockData('people');
				}
				throw e2;
			}
		}
		// 如果开启模拟模式，返回模拟数据
		if (API_CONFIG.MOCK_MODE) {
			console.warn('人员API请求失败，使用模拟数据:', e1);
			return generateMockData('people');
		}
		throw e1;
	}
}

/**
 * 获取热门地点列表
 * @param {Object} params - 查询参数，如 { pageNum, pageSize, orderBy }
 * @returns {Promise<{success:boolean,data:any,message?:string,code?:number}>}
 */
export async function getHotLocations(params = {}) {
	try {
		return await request({
			url: '/sanxianren/location/hot',
			method: 'POST',
			data: params,
			needAuth: false
		});
	} catch (e1) {
		// 如果热门地点接口不存在，尝试获取普通地点列表
		if (e1 && (e1.code === 404 || e1.code === 405)) {
			try {
				return await request({
					url: '/sanxianren/location/list',
					method: 'POST',
					data: { ...params, orderBy: 'created_at desc' },
					needAuth: false
				});
			} catch (e2) {
				// 如果开启模拟模式，返回模拟数据
				if (API_CONFIG.MOCK_MODE) {
					console.warn('地点API请求失败，使用模拟数据:', e2);
					return generateMockData('locations');
				}
				throw e2;
			}
		}
		// 如果开启模拟模式，返回模拟数据
		if (API_CONFIG.MOCK_MODE) {
			console.warn('地点API请求失败，使用模拟数据:', e1);
			return generateMockData('locations');
		}
		throw e1;
	}
}

/**
 * 获取首页统计数据
 * @returns {Promise<{success:boolean,data:any,message?:string,code?:number}>}
 */
export async function getHomeStats() {
	try {
		return await request({
			url: '/sanxianren/home/<USER>',
			method: 'GET',
			needAuth: false
		});
	} catch (e1) {
		// 如果开启模拟模式，返回模拟数据
		if (API_CONFIG.MOCK_MODE) {
			console.warn('统计API请求失败，使用模拟数据:', e1);
			return generateMockData('stats');
		}
		throw e1;
	}
}