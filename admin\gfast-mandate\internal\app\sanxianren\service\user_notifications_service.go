/*
* @desc:用户通知服务
* @company:山东傲英网络科技股份有限公司
* @Author: wolfman
* @Date:   2024/12/19
 */

package service

import (
	"context"
	"fmt"

	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	v1 "github.com/tiger1103/gfast/v3/api/v1/sanxianren"
	"github.com/tiger1103/gfast/v3/internal/app/sanxianren/dao"
	"github.com/tiger1103/gfast/v3/internal/app/sanxianren/model/entity"
)

type userNotificationsService struct{}

func init() {
	RegisterUserNotifications(&userNotificationsService{})
}

// GetNotifications 获取通知列表
func (s *userNotificationsService) GetNotifications(ctx context.Context, userId uint64, req *v1.GetNotificationsReq) (*v1.GetNotificationsRes, error) {
	query := dao.SxUserNotifications.Ctx(ctx).Where("user_id", userId)

	// 添加通知类型过滤
	if req.NotificationType != "" && req.NotificationType != "all" {
		query = query.Where("notification_type", req.NotificationType)
	}

	// 添加已读状态过滤
	if req.IsRead != nil {
		query = query.Where("is_read", *req.IsRead)
	}

	// 获取总数
	total, err := query.Count()
	if err != nil {
		g.Log().Error(ctx, "获取通知总数失败:", err)
		return nil, fmt.Errorf("获取通知列表失败")
	}

	// 分页查询
	var notifications []entity.SxUserNotifications
	err = query.Page(req.PageNum, req.PageSize).OrderDesc("created_at").Scan(&notifications)
	if err != nil {
		g.Log().Error(ctx, "获取通知列表失败:", err)
		return nil, fmt.Errorf("获取通知列表失败")
	}

	// 获取未读数量
	unreadCount, _ := dao.SxUserNotifications.Ctx(ctx).
		Where("user_id", userId).
		Where("is_read", 0).
		Count()

	// 转换为响应格式
	list := make([]v1.NotificationInfo, 0, len(notifications))
	for _, notification := range notifications {
		item := v1.NotificationInfo{
			Id:               notification.Id,
			NotificationType: notification.NotificationType,
			Title:            notification.Title,
			Content:          notification.Content,
			IsRead:           notification.IsRead,
			RelatedType:      notification.RelatedType,
			RelatedId:        notification.RelatedId,
			CreatedAt:        notification.CreatedAt.Format("2006-01-02 15:04:05"),
		}
		list = append(list, item)
	}

	res := &v1.GetNotificationsRes{
		ListRes: commonApi.ListRes{
			CurrentPage: req.PageNum,
			Total:       int(total),
		},
		List:        list,
		UnreadCount: int(unreadCount),
	}

	return res, nil
}

// MarkNotificationRead 标记通知已读
func (s *userNotificationsService) MarkNotificationRead(ctx context.Context, userId uint64, req *v1.MarkNotificationReadReq) error {
	query := dao.SxUserNotifications.Ctx(ctx).Where("user_id", userId)

	if req.NotificationId > 0 {
		// 标记指定通知为已读
		query = query.Where("id", req.NotificationId)
	}

	_, err := query.Update(g.Map{
		"is_read": 1,
	})

	if err != nil {
		g.Log().Error(ctx, "标记通知已读失败:", err)
		return fmt.Errorf("标记通知已读失败")
	}

	return nil
}

// GetNotificationStats 获取通知统计
func (s *userNotificationsService) GetNotificationStats(ctx context.Context, userId uint64) (*v1.GetNotificationStatsRes, error) {
	stats := &v1.GetNotificationStatsRes{}

	// 总数
	total, _ := dao.SxUserNotifications.Ctx(ctx).Where("user_id", userId).Count()
	stats.Total = int(total)

	// 未读数
	unread, _ := dao.SxUserNotifications.Ctx(ctx).Where("user_id", userId).Where("is_read", 0).Count()
	stats.Unread = int(unread)

	// 系统通知数
	system, _ := dao.SxUserNotifications.Ctx(ctx).Where("user_id", userId).Where("notification_type", "system").Count()
	stats.System = int(system)

	// 投稿通知数
	submission, _ := dao.SxUserNotifications.Ctx(ctx).Where("user_id", userId).Where("notification_type", "submission").Count()
	stats.Submission = int(submission)

	// 互动通知数
	interaction, _ := dao.SxUserNotifications.Ctx(ctx).Where("user_id", userId).Where("notification_type", "interaction").Count()
	stats.Interaction = int(interaction)

	return stats, nil
}
