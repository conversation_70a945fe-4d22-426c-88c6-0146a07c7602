// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/sanxianren/dao/internal"
)

// internalSxUserBadgesDao is internal type for wrapping internal DAO implements.
type internalSxUserBadgesDao = *internal.SxUserBadgesDao

// sxUserBadgesDao is the data access object for table sx_user_badges.
// You can define custom methods on it to extend its functionality as you wish.
type sxUserBadgesDao struct {
	internalSxUserBadgesDao
}

var (
	// SxUserBadges is globally public accessible object for table sx_user_badges operations.
	SxUserBadges = sxUserBadgesDao{
		internal.NewSxUserBadgesDao(),
	}
)

// Fill with you ideas below.
