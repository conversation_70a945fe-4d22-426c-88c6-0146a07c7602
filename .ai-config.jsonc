{
  // AI助手配置文件 - 天南地北三线人项目
  // 此文件定义了项目的开发规范、数据库设计规则和AI助手行为准则
  "project": {
    "name": "天南地北三线人", // 项目名称
    "type": "fullstack", // 项目类型：全栈应用
    "description": "基于Wikipedia、Gfast3.2中台和uni-app前端的三线建设人员管理系统", // 项目描述
    "version": "1.0.0" // 当前版本号
  },
  "development": {
    "language": "中文", // 开发语言环境
    "operating_system": "Windows", // 操作系统类型
    "preferred_ports": {
      "frontend": 5173, // 前端开发服务器端口
      "backend": 8808 // 后端API服务器端口
    },
    "port_policy": {
      "frontend_fixed_port": 5173, // 前端固定端口号
      "description": "uni-app必须使用5173端口，vite按需编译支持运行时动态加载页面", // 端口策略说明 
      "no_restart_needed": true // 无需重复启动服务器
    },
    "code_style": {
      "add_function_comments": true, // 是否添加函数注释
      "comment_language": "中文", // 注释语言
      "indentation": "2_spaces", // 缩进格式：2个空格
      "line_ending": "crlf" // 行结束符：Windows格式
    }
  },
  "frontend": {
    "framework": "uni-app", // 前端框架
    "build_tool": "vue-cli", // 构建工具
    "css_framework": "custom", // CSS框架：自定义
    "main_directory": "sanxian", // 前端主目录
    "dev_command": "npm run serve", // 开发服务器启动命令
    "build_command": "npm run build:h5", // H5构建命令
    "routing": {
      "type": "hash", // 路由类型：哈希路由
      "base_url": "/#/" // 基础URL路径
    }
  },
  "backend": {
    "language": "Go", // 后端开发语言
    "framework": "Gin", // Web框架
    "database": "MySQL", // 数据库类型
    "main_file": "main.go", // 主程序文件
    "api_prefix": "/api", // API路径前缀
    "run_command": "go run main.go" // 后端启动命令
  },
  "coding_rules": {
    "function_naming": "camelCase", // 函数命名规范：驼峰命名
    "variable_naming": "camelCase", // 变量命名规范：驼峰命名
    "constant_naming": "UPPER_SNAKE_CASE", // 常量命名规范：大写下划线
    "file_naming": "kebab-case", // 文件命名规范：短横线连接
    "always_add_comments": true, // 始终添加注释
    "error_handling": "always_check_errors", // 错误处理：始终检查错误
    "security": {
      "never_log_secrets": true, // 安全规则：永不记录敏感信息
      "validate_all_inputs": true, // 验证所有输入
      "use_https_in_production": true // 生产环境使用HTTPS
    }
  },
  "project_structure": {
    "frontend_path": "sanxian/", // 前端代码路径
    "backend_path": "./", // 后端代码路径
    "admin_path": "admin/", // 管理后台路径
    "docs_path": "doc/", // 文档路径
    "wiki_path": "Wikipedia/" // Wiki系统路径
  },
  "api_conventions": {
    "response_format": {
      "success": {
        "code": 200, // 成功响应状态码
        "message": "success", // 成功响应消息
        "data": "actual_data" // 实际数据字段
      },
      "error": {
        "code": "error_code", // 错误状态码
        "message": "error_message" // 错误消息
      }
    },
    "authentication": {
      "type": "JWT", // 认证类型：JWT令牌
      "header": "Authorization", // 认证头字段名
      "prefix": "Bearer " // 令牌前缀
    }
  },
  "deployment": {
    "environment": "development", // 部署环境：开发环境
    // 说明：后端已在 http://localhost:8808 正常运行，本地调试时无需考虑后端状态
    "proxy_config": {
      "/api": "http://localhost:8808" // API代理配置：保持 /api 前缀不变，避免 404
    }
  },
  "database_rules": {
    "reference_schema_file": "admin/gfast-mandate/sanxianren.sql", // 参考数据库结构文件
    "design_priority": "reference_existing_first", // 设计优先级：优先参考现有结构
    "new_table_location": "append_to_end", // 新表位置：追加到文件末尾
    "naming_convention": "snake_case", // 命名规范：下划线命名
    "charset": "utf8mb4", // 字符集
    "collation": "utf8mb4_unicode_ci", // 排序规则
    "engine": "InnoDB", // 存储引擎
    "required_fields": {
      "id": "主键，通常为 bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT", // 主键字段规范
      "created_at": "创建时间，datetime NOT NULL DEFAULT CURRENT_TIMESTAMP", // 创建时间字段规范
      "updated_at": "更新时间，datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP" // 更新时间字段规范
    },
    "comment_requirement": "所有表和字段必须有中文注释", // 注释要求
    "index_rules": {
      "primary_key": "使用 BTREE 索引", // 主键索引规则
      "foreign_key": "添加外键约束和索引", // 外键索引规则
      "common_queries": "为常用查询字段添加索引" // 查询字段索引规则
    }
  },
  "ai_assistant_rules": {
    "always_use_chinese": true, // 始终使用中文
    "add_function_level_comments": true, // 添加函数级注释
    "follow_existing_patterns": true, // 遵循现有模式
    "minimize_breaking_changes": true, // 最小化破坏性变更
    "test_before_completion": true, // 完成前进行测试
    "prefer_existing_libraries": true, // 优先使用现有库
    "maintain_code_consistency": true, // 保持代码一致性
    "handle_errors_gracefully": true, // 优雅处理错误
    "database_design": {
      "always_reference_schema_first": true, // 始终优先参考现有架构
      "check_existing_tables": true, // 检查现有表结构
      "maintain_consistency": true, // 保持一致性
      "add_proper_indexes": true // 添加适当的索引
    }
  },
  "common_issues": {
    "css_loading": "确保使用正确的端口(5173)，CSS样式才能正常加载", // CSS加载问题解决方案
    "api_exports": "检查apiUtils.js中的导出函数是否完整定义", // API导出问题解决方案
    "routing": "使用hash路由格式: /#/pages/path", // 路由格式规范
    "port_management": "uni-app固定使用5173端口，vite按需编译无需重复启动服务器", // 端口管理说明
    "vite_compilation": "vite支持按需编译，点击未编译页面时会自动编译后加载" // Vite编译机制说明
  },
  "api_path_rule": {
    "description": "所有 API 路径拼接必须遵循：配置文件（如 vue.config.js 或 .env）中写 baseUrl（如 /api），应用端所有 API 只写 /模块/方法，不再写 /api 或 /v1 前缀，最终拼接为 baseUrl + /模块/方法，保证所有 API 路径统一且易于维护。",
    "example": {
      "config_baseUrl": "/api",
      "frontend_api_path": "/sanxianren/user/login",
      "final_request_path": "/api/sanxianren/user/login"
    }
  }
}