<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户个人中心API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .api-section {
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 15px;
            overflow: hidden;
        }
        .api-header {
            background: #f8f9fa;
            padding: 10px 15px;
            border-bottom: 1px solid #ddd;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .api-header:hover {
            background: #e9ecef;
        }
        .api-content {
            padding: 15px;
            display: none;
        }
        .api-content.active {
            display: block;
        }
        .method {
            padding: 2px 8px;
            border-radius: 3px;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }
        .method.get { background: #28a745; }
        .method.post { background: #007bff; }
        .method.put { background: #ffc107; color: #212529; }
        .method.delete { background: #dc3545; }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .response {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            margin-top: 15px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { border-color: #28a745; background: #d4edda; }
        .error { border-color: #dc3545; background: #f8d7da; }
        .auth-section {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>用户个人中心API测试工具</h1>
    
    <div class="auth-section">
        <h3>认证设置</h3>
        <div class="form-group">
            <label for="baseUrl">API基础URL:</label>
            <input type="text" id="baseUrl" value="http://localhost:8808/api/v1/sanxianren" />
        </div>
        <div class="form-group">
            <label for="authToken">认证Token:</label>
            <input type="text" id="authToken" placeholder="Bearer token (可选)" />
            <button onclick="getTestToken()" style="margin-left: 10px;">获取测试Token</button>
            <button onclick="validateCurrentToken()" style="margin-left: 10px;">验证Token</button>
        </div>
        <div class="form-group">
            <label for="userId">测试用户ID:</label>
            <input type="number" id="userId" value="1" />
        </div>
        <div id="token-status" class="response" style="margin-top: 10px; display: none;"></div>
    </div>

    <div class="container">
        <h2>用户个人资料相关API</h2>
        
        <!-- 获取用户个人资料 -->
        <div class="api-section">
            <div class="api-header" onclick="toggleSection(this)">
                <div>
                    <span class="method get">GET</span>
                    <strong>/user/profile</strong> - 获取用户个人资料
                </div>
                <span>▼</span>
            </div>
            <div class="api-content">
                <button onclick="testGetUserProfile()">测试接口</button>
                <div id="getUserProfile-response" class="response"></div>
            </div>
        </div>

        <!-- 获取用户投稿统计 -->
        <div class="api-section">
            <div class="api-header" onclick="toggleSection(this)">
                <div>
                    <span class="method get">GET</span>
                    <strong>/user/submissions/stats</strong> - 获取用户投稿统计
                </div>
                <span>▼</span>
            </div>
            <div class="api-content">
                <button onclick="testGetUserSubmissionStats()">测试接口</button>
                <div id="getUserSubmissionStats-response" class="response"></div>
            </div>
        </div>

        <!-- 获取用户VIP信息 -->
        <div class="api-section">
            <div class="api-header" onclick="toggleSection(this)">
                <div>
                    <span class="method get">GET</span>
                    <strong>/user/vip</strong> - 获取用户VIP信息
                </div>
                <span>▼</span>
            </div>
            <div class="api-content">
                <button onclick="testGetUserVipInfo()">测试接口</button>
                <div id="getUserVipInfo-response" class="response"></div>
            </div>
        </div>

        <!-- 获取用户徽章列表 -->
        <div class="api-section">
            <div class="api-header" onclick="toggleSection(this)">
                <div>
                    <span class="method get">GET</span>
                    <strong>/user/badges</strong> - 获取用户徽章列表
                </div>
                <span>▼</span>
            </div>
            <div class="api-content">
                <button onclick="testGetUserBadges()">测试接口</button>
                <div id="getUserBadges-response" class="response"></div>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>用户收藏相关API</h2>
        
        <!-- 添加收藏 -->
        <div class="api-section">
            <div class="api-header" onclick="toggleSection(this)">
                <div>
                    <span class="method post">POST</span>
                    <strong>/user/collections/add</strong> - 添加收藏
                </div>
                <span>▼</span>
            </div>
            <div class="api-content">
                <div class="form-group">
                    <label for="addCollection-contentType">内容类型:</label>
                    <select id="addCollection-contentType">
                        <option value="story">故事</option>
                        <option value="factory">工厂</option>
                        <option value="people">人物</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="addCollection-contentId">内容ID:</label>
                    <input type="number" id="addCollection-contentId" value="1" />
                </div>
                <button onclick="testAddCollection()">测试接口</button>
                <div id="addCollection-response" class="response"></div>
            </div>
        </div>

        <!-- 获取收藏列表 -->
        <div class="api-section">
            <div class="api-header" onclick="toggleSection(this)">
                <div>
                    <span class="method get">GET</span>
                    <strong>/user/collections</strong> - 获取收藏列表
                </div>
                <span>▼</span>
            </div>
            <div class="api-content">
                <div class="form-group">
                    <label for="getCollections-contentType">内容类型:</label>
                    <select id="getCollections-contentType">
                        <option value="all">全部</option>
                        <option value="story">故事</option>
                        <option value="factory">工厂</option>
                        <option value="people">人物</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="getCollections-pageNum">页码:</label>
                    <input type="number" id="getCollections-pageNum" value="1" />
                </div>
                <div class="form-group">
                    <label for="getCollections-pageSize">每页数量:</label>
                    <input type="number" id="getCollections-pageSize" value="10" />
                </div>
                <button onclick="testGetCollections()">测试接口</button>
                <div id="getCollections-response" class="response"></div>
            </div>
        </div>
    </div>

    <script>
        function toggleSection(header) {
            const content = header.nextElementSibling;
            const arrow = header.querySelector('span:last-child');
            
            if (content.classList.contains('active')) {
                content.classList.remove('active');
                arrow.textContent = '▼';
            } else {
                content.classList.add('active');
                arrow.textContent = '▲';
            }
        }

        function getBaseUrl() {
            return document.getElementById('baseUrl').value;
        }

        function getAuthHeaders() {
            const token = document.getElementById('authToken').value;
            const headers = {
                'Content-Type': 'application/json'
            };
            if (token) {
                headers['Authorization'] = token.startsWith('Bearer ') ? token : `Bearer ${token}`;
            }
            return headers;
        }

        // Token管理函数
        async function getTestToken() {
            const userId = document.getElementById('userId').value;
            const url = `${getBaseUrl()}/test/token?userId=${userId}`;

            try {
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                const result = await response.json();

                if (result.success && result.data && result.data.token) {
                    document.getElementById('authToken').value = result.data.token;
                    displayTokenStatus(`Token获取成功！用户ID: ${result.data.userId}, 有效期: ${result.data.expiresIn}秒`, false);
                } else {
                    displayTokenStatus(`Token获取失败: ${result.message || '未知错误'}`, true);
                }
            } catch (error) {
                displayTokenStatus(`Token获取失败: ${error.message}`, true);
            }
        }

        async function validateCurrentToken() {
            const token = document.getElementById('authToken').value;
            if (!token) {
                displayTokenStatus('请先输入Token', true);
                return;
            }

            const url = `${getBaseUrl()}/test/validate`;

            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({ token: token })
                });
                const result = await response.json();

                if (result.success && result.data) {
                    if (result.data.valid) {
                        displayTokenStatus(`Token有效！用户ID: ${result.data.userId}`, false);
                    } else {
                        displayTokenStatus(`Token无效: ${result.data.message}`, true);
                    }
                } else {
                    displayTokenStatus(`Token验证失败: ${result.message || '未知错误'}`, true);
                }
            } catch (error) {
                displayTokenStatus(`Token验证失败: ${error.message}`, true);
            }
        }

        function displayTokenStatus(message, isError) {
            const element = document.getElementById('token-status');
            element.textContent = message;
            element.className = `response ${isError ? 'error' : 'success'}`;
            element.style.display = 'block';
        }

        function displayResponse(elementId, response, isError = false) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(response, null, 2);
            element.className = `response ${isError ? 'error' : 'success'}`;
        }

        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: getAuthHeaders(),
                    ...options
                });
                const data = await response.json();
                return { success: response.ok, data, status: response.status };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        // 用户个人资料相关API测试
        async function testGetUserProfile() {
            const url = `${getBaseUrl()}/user/profile`;
            const result = await makeRequest(url);
            displayResponse('getUserProfile-response', result, !result.success);
        }

        async function testGetUserSubmissionStats() {
            const url = `${getBaseUrl()}/user/submissions/stats`;
            const result = await makeRequest(url);
            displayResponse('getUserSubmissionStats-response', result, !result.success);
        }

        async function testGetUserVipInfo() {
            const url = `${getBaseUrl()}/user/vip`;
            const result = await makeRequest(url);
            displayResponse('getUserVipInfo-response', result, !result.success);
        }

        async function testGetUserBadges() {
            const url = `${getBaseUrl()}/user/badges`;
            const result = await makeRequest(url);
            displayResponse('getUserBadges-response', result, !result.success);
        }

        // 用户收藏相关API测试
        async function testAddCollection() {
            const url = `${getBaseUrl()}/user/collections/add`;
            const data = {
                contentType: document.getElementById('addCollection-contentType').value,
                contentId: parseInt(document.getElementById('addCollection-contentId').value)
            };
            const result = await makeRequest(url, {
                method: 'POST',
                body: JSON.stringify(data)
            });
            displayResponse('addCollection-response', result, !result.success);
        }

        async function testGetCollections() {
            const contentType = document.getElementById('getCollections-contentType').value;
            const pageNum = document.getElementById('getCollections-pageNum').value;
            const pageSize = document.getElementById('getCollections-pageSize').value;
            
            const params = new URLSearchParams({
                pageNum,
                pageSize
            });
            if (contentType !== 'all') {
                params.append('contentType', contentType);
            }
            
            const url = `${getBaseUrl()}/user/collections?${params}`;
            const result = await makeRequest(url);
            displayResponse('getCollections-response', result, !result.success);
        }
    </script>
</body>
</html>
