/*
* @desc:用户收藏相关API
* @company:山东傲英网络科技股份有限公司
* @Author: wolfman
* @Date:   2024/12/19
 */

package sanxianren

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
)

// AddCollectionReq 添加收藏请求
type AddCollectionReq struct {
	g.Meta      `path:"/user/collections/add" tags:"三线人/用户收藏" method:"post" summary:"添加收藏"`
	ContentType string `json:"contentType" description:"内容类型：story-故事，factory-工厂，people-人物" v:"required#内容类型不能为空"`
	ContentId   uint64 `json:"contentId" description:"内容ID" v:"required#内容ID不能为空"`
	commonApi.Author
}

// AddCollectionRes 添加收藏响应
type AddCollectionRes struct {
	g.Meta `mime:"application/json"`
}

// RemoveCollectionReq 取消收藏请求
type RemoveCollectionReq struct {
	g.<PERSON>a      `path:"/user/collections/remove" tags:"三线人/用户收藏" method:"delete" summary:"取消收藏"`
	ContentType string `json:"contentType" description:"内容类型：story-故事，factory-工厂，people-人物" v:"required#内容类型不能为空"`
	ContentId   uint64 `json:"contentId" description:"内容ID" v:"required#内容ID不能为空"`
	commonApi.Author
}

// RemoveCollectionRes 取消收藏响应
type RemoveCollectionRes struct {
	g.Meta `mime:"application/json"`
}

// GetCollectionsReq 获取收藏列表请求
type GetCollectionsReq struct {
	g.Meta      `path:"/user/collections" tags:"三线人/用户收藏" method:"get" summary:"获取收藏列表"`
	ContentType string `json:"contentType" description:"内容类型：story-故事，factory-工厂，people-人物，all-全部"`
	commonApi.PageReq
	commonApi.Author
}

// GetCollectionsRes 获取收藏列表响应
type GetCollectionsRes struct {
	g.Meta `mime:"application/json"`
	commonApi.ListRes
	List []CollectionInfo `json:"list" description:"收藏列表"`
}

// CollectionInfo 收藏信息
type CollectionInfo struct {
	Id          uint64 `json:"id" description:"收藏ID"`
	ContentType string `json:"contentType" description:"内容类型"`
	ContentId   uint64 `json:"contentId" description:"内容ID"`
	Title       string `json:"title" description:"内容标题"`
	Summary     string `json:"summary" description:"内容摘要"`
	CoverImage  string `json:"coverImage" description:"封面图片"`
	CreatedAt   string `json:"createdAt" description:"收藏时间"`
}

// CheckCollectionReq 检查收藏状态请求
type CheckCollectionReq struct {
	g.Meta      `path:"/user/collections/check" tags:"三线人/用户收藏" method:"get" summary:"检查收藏状态"`
	ContentType string `json:"contentType" description:"内容类型：story-故事，factory-工厂，people-人物" v:"required#内容类型不能为空"`
	ContentId   uint64 `json:"contentId" description:"内容ID" v:"required#内容ID不能为空"`
	commonApi.Author
}

// CheckCollectionRes 检查收藏状态响应
type CheckCollectionRes struct {
	g.Meta      `mime:"application/json"`
	IsCollected bool `json:"isCollected" description:"是否已收藏"`
}
