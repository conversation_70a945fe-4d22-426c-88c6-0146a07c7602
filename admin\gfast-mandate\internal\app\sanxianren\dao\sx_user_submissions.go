// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/sanxianren/dao/internal"
)

// internalSxUserSubmissionsDao is internal type for wrapping internal DAO implements.
type internalSxUserSubmissionsDao = *internal.SxUserSubmissionsDao

// sxUserSubmissionsDao is the data access object for table sx_user_submissions.
// You can define custom methods on it to extend its functionality as you wish.
type sxUserSubmissionsDao struct {
	internalSxUserSubmissionsDao
}

var (
	// SxUserSubmissions is globally public accessible object for table sx_user_submissions operations.
	SxUserSubmissions = sxUserSubmissionsDao{
		internal.NewSxUserSubmissionsDao(),
	}
)

// Fill with you ideas below.
