2025-09-05T00:00:05.024+08:00 [DEBU] {64191554dc1c6218876e7c7956126d94} [ 14 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T00:00:05.032+08:00 [DEBU] {64191554dc1c6218876e7c7956126d94} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T00:00:05.035+08:00 [DEBU] {64191554dc1c6218876e7c7956126d94} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T00:00:05.037+08:00 [DEBU] {64191554dc1c6218876e7c7956126d94} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T00:00:05.143+08:00 [DEBU] {64191554dc1c6218876e7c7956126d94} [104 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 00:00:05','在线用户定时更新，执行成功') 
2025-09-05T00:02:38.715+08:00 [DEBU] {58c01d1aca1f6218a26e7c79fd3cced3} [1107 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL
2025-09-05T00:02:39.067+08:00 [DEBU] {58c01d1aca1f6218a26e7c79fd3cced3} [325 ms] [default] [sanxian1] [rows:1  ] SELECT `submission_uuid`,`title`,`submitter_name`,`status`,`created_at`,`updated_at` FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL ORDER BY `like_count` desc LIMIT 0,9
2025-09-05T00:02:39.146+08:00 [DEBU] {c4b19f7bca1f6218a36e7c79786e630a} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL
2025-09-05T00:02:39.148+08:00 [DEBU] {c4b19f7bca1f6218a36e7c79786e630a} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT `submission_uuid`,`title`,`submitter_name`,`status`,`created_at`,`updated_at` FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL ORDER BY `created_at` desc LIMIT 0,3
2025-09-05T00:04:18.580+08:00 [DEBU] {786d61a1e11f6218a46e7c794f4a01f6} [ 14 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL
2025-09-05T00:04:18.625+08:00 [DEBU] {786d61a1e11f6218a46e7c794f4a01f6} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT `submission_uuid`,`title`,`submitter_name`,`status`,`created_at`,`updated_at` FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL ORDER BY `like_count` desc LIMIT 0,9
2025-09-05T00:04:20.103+08:00 [DEBU] {c830deb6e11f6218a56e7c79eb51bc67} [1174 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/sanxianren/story/list','POST',1,'demo','财务部门','/api/v1/sanxianren/story/list','127.0.0.1','内网IP','{"orderBy":"like_count desc","pageNum":1,"pageSize":9}','2025-09-05 00:04:18') 
2025-09-05T00:04:20.568+08:00 [DEBU] {282facb7e11f6218a66e7c7931003d70} [1629 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/sanxianren/story/list','POST',1,'demo','财务部门','/api/v1/sanxianren/story/list','127.0.0.1','内网IP','{"orderBy":"like_count desc","pageNum":1,"pageSize":9}','2025-09-05 00:04:18') 
2025-09-05T00:04:21.694+08:00 [DEBU] {988cda5be21f6218a76e7c797c55189a} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL
2025-09-05T00:04:21.709+08:00 [DEBU] {988cda5be21f6218a76e7c797c55189a} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT `submission_uuid`,`title`,`submitter_name`,`status`,`created_at`,`updated_at` FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL ORDER BY `created_at` desc LIMIT 0,3
2025-09-05T00:04:22.306+08:00 [DEBU] {bc431564e21f6218a86e7c7912ad36df} [357 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/sanxianren/story/list','POST',1,'demo','财务部门','/api/v1/sanxianren/story/list','127.0.0.1','内网IP','{"orderBy":"created_at desc","pageNum":1,"pageSize":3}','2025-09-05 00:04:21') 
2025-09-05T00:04:22.487+08:00 [DEBU] {d4be1869e21f6218a96e7c793bbe0e2a} [498 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/sanxianren/story/list','POST',1,'demo','财务部门','/api/v1/sanxianren/story/list','127.0.0.1','内网IP','{"orderBy":"created_at desc","pageNum":1,"pageSize":3}','2025-09-05 00:04:21') 
2025-09-05T00:04:36.793+08:00 [DEBU] {3c1bd5dfe51f6218aa6e7c7957604896} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL
2025-09-05T00:04:36.797+08:00 [DEBU] {3c1bd5dfe51f6218aa6e7c7957604896} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT `submission_uuid`,`title`,`submitter_name`,`status`,`created_at`,`updated_at` FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL ORDER BY `view_count` desc LIMIT 0,5
2025-09-05T00:04:36.862+08:00 [DEBU] {80bb6de0e51f6218ab6e7c79d3ca1a6e} [ 59 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/sanxianren/story/list','POST',1,'demo','财务部门','/api/v1/sanxianren/story/list','127.0.0.1','内网IP','{"orderBy":"view_count desc","pageNum":1,"pageSize":5}','2025-09-05 00:04:36') 
2025-09-05T00:04:36.868+08:00 [DEBU] {e03c8ce0e51f6218ac6e7c7957e12629} [ 57 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/sanxianren/story/list','POST',1,'demo','财务部门','/api/v1/sanxianren/story/list','127.0.0.1','内网IP','{"orderBy":"view_count desc","pageNum":1,"pageSize":5}','2025-09-05 00:04:36') 
2025-09-05T00:04:37.038+08:00 [DEBU] {64c27eeee51f6218ad6e7c7903f5ca30} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL
2025-09-05T00:04:37.045+08:00 [DEBU] {64c27eeee51f6218ad6e7c7903f5ca30} [  3 ms] [default] [sanxian1] [rows:1  ] SELECT `submission_uuid`,`title`,`submitter_name`,`status`,`created_at`,`updated_at` FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL ORDER BY `created_at` desc LIMIT 0,5
2025-09-05T00:04:37.078+08:00 [DEBU] {6413a4f0e51f6218af6e7c7927fe0532} [  4 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/sanxianren/story/list','POST',1,'demo','财务部门','/api/v1/sanxianren/story/list','127.0.0.1','内网IP','{"orderBy":"created_at desc","pageNum":1,"pageSize":5}','2025-09-05 00:04:37') 
2025-09-05T00:04:37.079+08:00 [DEBU] {60298df0e51f6218ae6e7c7995a8fe10} [  5 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/sanxianren/story/list','POST',1,'demo','财务部门','/api/v1/sanxianren/story/list','127.0.0.1','内网IP','{"orderBy":"created_at desc","pageNum":1,"pageSize":5}','2025-09-05 00:04:37') 
2025-09-05T00:04:42.562+08:00 [ERRO] {30b36233e71f6218b06e7c79beea7cb3} [ 60 ms] [default] [sanxian1] [rows:0  ] SHOW FULL COLUMNS FROM `factory_people`
Error: Error 1146 (42S02): Table 'sanxian1.factory_people' doesn't exist 
Stack:
1.  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/factoryPeople.(*sFactoryPeople).GetById.func1
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/factoryPeople/factory_people.go:103
2.  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/factoryPeople.(*sFactoryPeople).GetById
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/factoryPeople/factory_people.go:102
3.  github.com/tiger1103/gfast/v3/internal/app/sanxianren/controller.(*factoryPeopleController).Get
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/controller/factory_people.go:29
4.  github.com/tiger1103/gfast/v3/internal/app/system/logic/middleware.(*sMiddleware).Auth
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/system/logic/middleware/middleware.go:77
5.  github.com/tiger1103/gfast-token/gftoken.(*GfToken).authMiddleware
    C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/middleware.go:21
6.  github.com/tiger1103/gfast/v3/internal/app/system/logic/middleware.(*sMiddleware).Ctx
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/system/logic/middleware/middleware.go:62
7.  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCharsetUTF8
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:39
8.  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:30

2025-09-05T00:04:42.568+08:00 [ERRO] {30b36233e71f6218b06e7c79beea7cb3} [  0 ms] [default] [sanxian1] [rows:0  ] SHOW FULL COLUMNS FROM `factory_people`
Error: Error 1146 (42S02): Table 'sanxian1.factory_people' doesn't exist 
Stack:
1.  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/factoryPeople.(*sFactoryPeople).GetById.func1
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/factoryPeople/factory_people.go:103
2.  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/factoryPeople.(*sFactoryPeople).GetById
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/factoryPeople/factory_people.go:102
3.  github.com/tiger1103/gfast/v3/internal/app/sanxianren/controller.(*factoryPeopleController).Get
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/controller/factory_people.go:29
4.  github.com/tiger1103/gfast/v3/internal/app/system/logic/middleware.(*sMiddleware).Auth
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/system/logic/middleware/middleware.go:77
5.  github.com/tiger1103/gfast-token/gftoken.(*GfToken).authMiddleware
    C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/middleware.go:21
6.  github.com/tiger1103/gfast/v3/internal/app/system/logic/middleware.(*sMiddleware).Ctx
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/system/logic/middleware/middleware.go:62
7.  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCharsetUTF8
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:39
8.  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:30

2025-09-05T00:04:42.577+08:00 [ERRO] {30b36233e71f6218b06e7c79beea7cb3} [  4 ms] [default] [sanxian1] [rows:0  ] SELECT `Id`,`PersonUuid`,`SubmissionId`,`Name`,`Gender`,`BirthDate`,`BirthPlace`,`DeathDate`,`JobTitle`,`Employer`,`ParticipationTime`,`WorkLocation`,`PersonalExperience`,`Contributions`,`Achievements`,`Skills`,`ImportantEvents`,`FamilySituation`,`LaterDevelopment`,`RelatedPhotos`,`RelatedDocuments`,`RelatedOralRecords`,`RelatedPeople`,`References`,`Notes`,`WikiPageId`,`WikiPageTitle`,`Status`,`IsFeatured`,`ViewCount`,`CreatedAt`,`UpdatedAt` FROM `factory_people` WHERE `id`=3 LIMIT 1
Error: Error 1146 (42S02): Table 'sanxian1.factory_people' doesn't exist 
Stack:
1.  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/factoryPeople.(*sFactoryPeople).GetById.func1
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/factoryPeople/factory_people.go:103
2.  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/factoryPeople.(*sFactoryPeople).GetById
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/factoryPeople/factory_people.go:102
3.  github.com/tiger1103/gfast/v3/internal/app/sanxianren/controller.(*factoryPeopleController).Get
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/controller/factory_people.go:29
4.  github.com/tiger1103/gfast/v3/internal/app/system/logic/middleware.(*sMiddleware).Auth
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/system/logic/middleware/middleware.go:77
5.  github.com/tiger1103/gfast-token/gftoken.(*GfToken).authMiddleware
    C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/middleware.go:21
6.  github.com/tiger1103/gfast/v3/internal/app/system/logic/middleware.(*sMiddleware).Ctx
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/system/logic/middleware/middleware.go:62
7.  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCharsetUTF8
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:39
8.  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:30

2025-09-05T00:04:42.602+08:00 [DEBU] {********************************} [ 16 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('工厂人员查询','/api/v1/sanxianren/factoryPeople/get','GET',1,'demo','财务部门','/api/v1/sanxianren/factoryPeople/get?id=3','127.0.0.1','内网IP','{"id":"3"}','2025-09-05 00:04:42') 
2025-09-05T00:04:42.604+08:00 [DEBU] {d89e4139e71f6218b26e7c79beb6047d} [ 16 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('工厂人员查询','/api/v1/sanxianren/factoryPeople/get','GET',1,'demo','财务部门','/api/v1/sanxianren/factoryPeople/get?id=3','127.0.0.1','内网IP','{"id":"3"}','2025-09-05 00:04:42') 
2025-09-05T00:10:05.313+08:00 [DEBU] {64191554dc1c6218876e7c7956126d94} [  3 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T00:10:05.317+08:00 [DEBU] {64191554dc1c6218876e7c7956126d94} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T00:10:05.320+08:00 [DEBU] {64191554dc1c6218876e7c7956126d94} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T00:10:05.322+08:00 [DEBU] {64191554dc1c6218876e7c7956126d94} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T00:10:05.357+08:00 [DEBU] {64191554dc1c6218876e7c7956126d94} [ 33 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 00:10:05','在线用户定时更新，执行成功') 
2025-09-05T00:17:01.196+08:00 [DEBU] {dc146508932062184109a323698ca44c} [692 ms] [default] [sanxian1] [rows:100] SHOW TABLES
2025-09-05T00:17:01.277+08:00 [DEBU] {dc146508932062184109a323698ca44c} [ 76 ms] [default] [sanxian1] [rows:14 ] SHOW FULL COLUMNS FROM `sys_job`
2025-09-05T00:17:01.295+08:00 [DEBU] {dc146508932062184109a323698ca44c} [ 15 ms] [default] [sanxian1] [rows:1  ] SELECT `job_id`,`job_name`,`job_params`,`job_group`,`invoke_target`,`cron_expression`,`misfire_policy`,`concurrent`,`status`,`created_by`,`updated_by`,`remark`,`created_at`,`updated_at` FROM `sys_job` WHERE `status`=0
2025-09-05T00:17:01.304+08:00 [DEBU] {dc146508932062184109a323698ca44c} [  7 ms] [default] [sanxian1] [rows:0  ] UPDATE `sys_job` SET `status`=0 WHERE `job_id`=8
2025-09-05T00:17:01.311+08:00 [DEBU] {88256438932062184309a32308a431fe} [  5 ms] [default] [sanxian1] [rows:5  ] SHOW FULL COLUMNS FROM `plugin_sms_config`
2025-09-05T00:17:01.328+08:00 [DEBU] {88256438932062184309a32308a431fe} [ 15 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`sms_type`,`remark`,`status`,`config` FROM `plugin_sms_config` WHERE `status`=1 LIMIT 1
2025-09-05T00:17:01.334+08:00 [DEBU] {64ffd939932062184409a3239699e902} [  4 ms] [default] [sanxian1] [rows:1  ] SELECT * FROM `plugin_sms_config` WHERE `sms_type`='yunxin' LIMIT 1
2025-09-05T00:45:47.247+08:00 [DEBU] {3059280f25226218e2d0eb7d62fdfd33} [ 55 ms] [default] [sanxian1] [rows:100] SHOW TABLES
2025-09-05T00:45:47.256+08:00 [DEBU] {3059280f25226218e2d0eb7d62fdfd33} [  7 ms] [default] [sanxian1] [rows:14 ] SHOW FULL COLUMNS FROM `sys_job`
2025-09-05T00:45:47.262+08:00 [DEBU] {3059280f25226218e2d0eb7d62fdfd33} [  3 ms] [default] [sanxian1] [rows:1  ] SELECT `job_id`,`job_name`,`job_params`,`job_group`,`invoke_target`,`cron_expression`,`misfire_policy`,`concurrent`,`status`,`created_by`,`updated_by`,`remark`,`created_at`,`updated_at` FROM `sys_job` WHERE `status`=0
2025-09-05T00:45:47.267+08:00 [DEBU] {3059280f25226218e2d0eb7d62fdfd33} [  3 ms] [default] [sanxian1] [rows:0  ] UPDATE `sys_job` SET `status`=0 WHERE `job_id`=8
2025-09-05T00:45:47.271+08:00 [DEBU] {784dbf1325226218e4d0eb7d062f3f72} [  3 ms] [default] [sanxian1] [rows:5  ] SHOW FULL COLUMNS FROM `plugin_sms_config`
2025-09-05T00:45:47.274+08:00 [DEBU] {784dbf1325226218e4d0eb7d062f3f72} [  3 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`sms_type`,`remark`,`status`,`config` FROM `plugin_sms_config` WHERE `status`=1 LIMIT 1
2025-09-05T00:45:47.278+08:00 [DEBU] {8004411425226218e5d0eb7ddb9f6bf1} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT * FROM `plugin_sms_config` WHERE `sms_type`='yunxin' LIMIT 1
2025-09-05T00:50:05.264+08:00 [DEBU] {402c821325226218e3d0eb7dfdefb9fb} [ 41 ms] [default] [sanxian1] [rows:8  ] SHOW FULL COLUMNS FROM `sys_user_online`
2025-09-05T00:50:05.274+08:00 [DEBU] {402c821325226218e3d0eb7dfdefb9fb} [  8 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T00:50:05.283+08:00 [DEBU] {402c821325226218e3d0eb7dfdefb9fb} [  6 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T00:50:05.311+08:00 [DEBU] {402c821325226218e3d0eb7dfdefb9fb} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T00:50:05.315+08:00 [DEBU] {402c821325226218e3d0eb7dfdefb9fb} [  2 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T00:50:05.379+08:00 [DEBU] {402c821325226218e3d0eb7dfdefb9fb} [ 62 ms] [default] [sanxian1] [rows:4  ] SHOW FULL COLUMNS FROM `sys_job_log`
2025-09-05T00:50:05.517+08:00 [DEBU] {402c821325226218e3d0eb7dfdefb9fb} [104 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 00:50:05','在线用户定时更新，执行成功') 
2025-09-05T00:54:52.947+08:00 [DEBU] {7075a61ea4226218e6d0eb7deeb6ac64} [ 27 ms] [default] [sanxian1] [rows:26 ] SHOW FULL COLUMNS FROM `factory_submissions`
2025-09-05T00:54:52.962+08:00 [DEBU] {7075a61ea4226218e6d0eb7deeb6ac64} [ 14 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL
2025-09-05T00:54:52.966+08:00 [DEBU] {7075a61ea4226218e6d0eb7deeb6ac64} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT `submission_uuid`,`title`,`submitter_name`,`status`,`created_at`,`updated_at` FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL ORDER BY `created_at` DESC LIMIT 0,10
2025-09-05T00:55:01.763+08:00 [DEBU] {506a102ea6226218e7d0eb7d9a4e1ed2} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT * FROM `factory_submissions` WHERE (`submission_uuid`='jwcvol095w0dc9ypmsaauys20073r6lw') AND `deleted_at` IS NULL LIMIT 1
2025-09-05T00:57:31.273+08:00 [DEBU] {001dbefac82262181ebf4a0d17b940b9} [ 49 ms] [default] [sanxian1] [rows:100] SHOW TABLES
2025-09-05T00:57:31.279+08:00 [DEBU] {001dbefac82262181ebf4a0d17b940b9} [  4 ms] [default] [sanxian1] [rows:14 ] SHOW FULL COLUMNS FROM `sys_job`
2025-09-05T00:57:31.285+08:00 [DEBU] {001dbefac82262181ebf4a0d17b940b9} [  3 ms] [default] [sanxian1] [rows:1  ] SELECT `job_id`,`job_name`,`job_params`,`job_group`,`invoke_target`,`cron_expression`,`misfire_policy`,`concurrent`,`status`,`created_by`,`updated_by`,`remark`,`created_at`,`updated_at` FROM `sys_job` WHERE `status`=0
2025-09-05T00:57:31.288+08:00 [DEBU] {001dbefac82262181ebf4a0d17b940b9} [  1 ms] [default] [sanxian1] [rows:0  ] UPDATE `sys_job` SET `status`=0 WHERE `job_id`=8
2025-09-05T00:57:31.292+08:00 [DEBU] {c42eadfec822621820bf4a0ddff6d037} [  3 ms] [default] [sanxian1] [rows:5  ] SHOW FULL COLUMNS FROM `plugin_sms_config`
2025-09-05T00:57:31.294+08:00 [DEBU] {c42eadfec822621820bf4a0ddff6d037} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`sms_type`,`remark`,`status`,`config` FROM `plugin_sms_config` WHERE `status`=1 LIMIT 1
2025-09-05T00:57:31.296+08:00 [DEBU] {fc4c10ffc822621821bf4a0de25414df} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT * FROM `plugin_sms_config` WHERE `sms_type`='yunxin' LIMIT 1
2025-09-05T00:58:13.544+08:00 [DEBU] {f00d1dd4d22262186eee1a4b9d9952e4} [ 18 ms] [default] [sanxian1] [rows:100] SHOW TABLES
2025-09-05T00:58:13.552+08:00 [DEBU] {f00d1dd4d22262186eee1a4b9d9952e4} [  6 ms] [default] [sanxian1] [rows:14 ] SHOW FULL COLUMNS FROM `sys_job`
2025-09-05T00:58:13.559+08:00 [DEBU] {f00d1dd4d22262186eee1a4b9d9952e4} [  4 ms] [default] [sanxian1] [rows:1  ] SELECT `job_id`,`job_name`,`job_params`,`job_group`,`invoke_target`,`cron_expression`,`misfire_policy`,`concurrent`,`status`,`created_by`,`updated_by`,`remark`,`created_at`,`updated_at` FROM `sys_job` WHERE `status`=0
2025-09-05T00:58:13.561+08:00 [DEBU] {f00d1dd4d22262186eee1a4b9d9952e4} [  1 ms] [default] [sanxian1] [rows:0  ] UPDATE `sys_job` SET `status`=0 WHERE `job_id`=8
2025-09-05T00:58:13.564+08:00 [DEBU] {e49c51d6d222621870ee1a4bb2e632b2} [  2 ms] [default] [sanxian1] [rows:5  ] SHOW FULL COLUMNS FROM `plugin_sms_config`
2025-09-05T00:58:13.566+08:00 [DEBU] {e49c51d6d222621870ee1a4bb2e632b2} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`sms_type`,`remark`,`status`,`config` FROM `plugin_sms_config` WHERE `status`=1 LIMIT 1
2025-09-05T00:58:13.568+08:00 [DEBU] {0c8aa5d6d222621871ee1a4bf4f8adcf} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT * FROM `plugin_sms_config` WHERE `sms_type`='yunxin' LIMIT 1
2025-09-05T00:58:30.797+08:00 [DEBU] {34145bd9d622621872ee1a4b0ec63886} [  4 ms] [default] [sanxian1] [rows:26 ] SHOW FULL COLUMNS FROM `factory_submissions`
2025-09-05T00:58:30.847+08:00 [DEBU] {34145bd9d622621872ee1a4b0ec63886} [ 48 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `factory_submissions`(`submission_uuid`,`submitter_id`,`submitter_name`,`title`,`submission_type`,`content_data`,`status`,`created_at`,`updated_at`) VALUES('h8d4xs05yk0dck6bwyur6gs10067ut61',0,'','','story','{"formData":{"author":"","contact":"","coverImage":"","description":"","factoryCode":"","factoryName":"","images":null,"mediaFiles":null,"relationship":"","title":""},"mediaType":"text"}','pending','2025-09-05 00:58:30','2025-09-05 00:58:30') 
2025-09-05T00:58:45.368+08:00 [ERRO] {cc66d83bda22621873ee1a4bd9294e76} [ 36 ms] [default] [sanxian1] [rows:0  ] SHOW FULL COLUMNS FROM `factory_submission_stats_view`
Error: Error 1146 (42S02): Table 'sanxian1.factory_submission_stats_view' doesn't exist 
Stack:
1.  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/submissions.(*sSubmissions).SubmissionStats.func1
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/submissions/submissions.go:754
2.  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/submissions.(*sSubmissions).SubmissionStats
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/submissions/submissions.go:741
3.  github.com/tiger1103/gfast/v3/internal/app/sanxianren/controller.(*storyController).Stats
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/controller/story.go:239
4.  github.com/tiger1103/gfast/v3/internal/app/system/logic/middleware.(*sMiddleware).Ctx
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/system/logic/middleware/middleware.go:47
5.  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCharsetUTF8
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:39
6.  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:30

2025-09-05T00:58:45.373+08:00 [ERRO] {cc66d83bda22621873ee1a4bd9294e76} [  1 ms] [default] [sanxian1] [rows:0  ] SHOW FULL COLUMNS FROM `factory_submission_stats_view`
Error: Error 1146 (42S02): Table 'sanxian1.factory_submission_stats_view' doesn't exist 
Stack:
1.  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/submissions.(*sSubmissions).SubmissionStats.func1
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/submissions/submissions.go:754
2.  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/submissions.(*sSubmissions).SubmissionStats
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/submissions/submissions.go:741
3.  github.com/tiger1103/gfast/v3/internal/app/sanxianren/controller.(*storyController).Stats
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/controller/story.go:239
4.  github.com/tiger1103/gfast/v3/internal/app/system/logic/middleware.(*sMiddleware).Ctx
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/system/logic/middleware/middleware.go:47
5.  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCharsetUTF8
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:39
6.  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:30

2025-09-05T00:58:45.380+08:00 [ERRO] {cc66d83bda22621873ee1a4bd9294e76} [  4 ms] [default] [sanxian1] [rows:0  ] SELECT * FROM `factory_submission_stats_view` WHERE (`submission_type`='story') AND (`status`='all') ORDER BY `submission_date` asc,`submission_type` asc,`status` asc
Error: Error 1146 (42S02): Table 'sanxian1.factory_submission_stats_view' doesn't exist 
Stack:
1.  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/submissions.(*sSubmissions).SubmissionStats.func1
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/submissions/submissions.go:754
2.  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/submissions.(*sSubmissions).SubmissionStats
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/submissions/submissions.go:741
3.  github.com/tiger1103/gfast/v3/internal/app/sanxianren/controller.(*storyController).Stats
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/controller/story.go:239
4.  github.com/tiger1103/gfast/v3/internal/app/system/logic/middleware.(*sMiddleware).Ctx
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/system/logic/middleware/middleware.go:47
5.  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCharsetUTF8
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:39
6.  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
    C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:30

2025-09-05T01:00:05.661+08:00 [DEBU] {881233d6d22262186fee1a4b5d6ec0ad} [ 19 ms] [default] [sanxian1] [rows:8  ] SHOW FULL COLUMNS FROM `sys_user_online`
2025-09-05T01:00:05.664+08:00 [DEBU] {881233d6d22262186fee1a4b5d6ec0ad} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T01:00:05.668+08:00 [DEBU] {881233d6d22262186fee1a4b5d6ec0ad} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T01:00:05.674+08:00 [DEBU] {881233d6d22262186fee1a4b5d6ec0ad} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T01:00:05.677+08:00 [DEBU] {881233d6d22262186fee1a4b5d6ec0ad} [  2 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T01:00:05.680+08:00 [DEBU] {881233d6d22262186fee1a4b5d6ec0ad} [  2 ms] [default] [sanxian1] [rows:4  ] SHOW FULL COLUMNS FROM `sys_job_log`
2025-09-05T01:00:05.719+08:00 [DEBU] {881233d6d22262186fee1a4b5d6ec0ad} [ 37 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 01:00:05','在线用户定时更新，执行成功') 
2025-09-05T01:10:05.654+08:00 [DEBU] {881233d6d22262186fee1a4b5d6ec0ad} [ 13 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T01:10:05.657+08:00 [DEBU] {881233d6d22262186fee1a4b5d6ec0ad} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T01:10:05.661+08:00 [DEBU] {881233d6d22262186fee1a4b5d6ec0ad} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T01:10:05.663+08:00 [DEBU] {881233d6d22262186fee1a4b5d6ec0ad} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T01:10:05.742+08:00 [DEBU] {881233d6d22262186fee1a4b5d6ec0ad} [ 78 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 01:10:05','在线用户定时更新，执行成功') 
2025-09-05T01:20:05.853+08:00 [DEBU] {881233d6d22262186fee1a4b5d6ec0ad} [  8 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T01:20:05.856+08:00 [DEBU] {881233d6d22262186fee1a4b5d6ec0ad} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T01:20:05.860+08:00 [DEBU] {881233d6d22262186fee1a4b5d6ec0ad} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T01:20:05.862+08:00 [DEBU] {881233d6d22262186fee1a4b5d6ec0ad} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T01:20:05.941+08:00 [DEBU] {881233d6d22262186fee1a4b5d6ec0ad} [ 78 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 01:20:05','在线用户定时更新，执行成功') 
2025-09-05T01:30:05.113+08:00 [DEBU] {f497aae58f2462182d67337d8455ba6b} [ 32 ms] [default] [sanxian1] [rows:100] SHOW TABLES
2025-09-05T01:30:05.121+08:00 [DEBU] {f497aae58f2462182d67337d8455ba6b} [  6 ms] [default] [sanxian1] [rows:14 ] SHOW FULL COLUMNS FROM `sys_job`
2025-09-05T01:30:05.128+08:00 [DEBU] {f497aae58f2462182d67337d8455ba6b} [  4 ms] [default] [sanxian1] [rows:1  ] SELECT `job_id`,`job_name`,`job_params`,`job_group`,`invoke_target`,`cron_expression`,`misfire_policy`,`concurrent`,`status`,`created_by`,`updated_by`,`remark`,`created_at`,`updated_at` FROM `sys_job` WHERE `status`=0
2025-09-05T01:30:05.130+08:00 [DEBU] {f497aae58f2462182d67337d8455ba6b} [  0 ms] [default] [sanxian1] [rows:0  ] UPDATE `sys_job` SET `status`=0 WHERE `job_id`=8
2025-09-05T01:30:05.135+08:00 [DEBU] {9061d3e88f2462182f67337d601ca9ab} [  2 ms] [default] [sanxian1] [rows:5  ] SHOW FULL COLUMNS FROM `plugin_sms_config`
2025-09-05T01:30:05.137+08:00 [DEBU] {9061d3e88f2462182f67337d601ca9ab} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`sms_type`,`remark`,`status`,`config` FROM `plugin_sms_config` WHERE `status`=1 LIMIT 1
2025-09-05T01:30:05.140+08:00 [DEBU] {fc3e3ee98f2462183067337d096bed98} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT * FROM `plugin_sms_config` WHERE `sms_type`='yunxin' LIMIT 1
2025-09-05T01:30:21.558+08:00 [DEBU] {2c09a0bb932462183167337daddc40f3} [  3 ms] [default] [sanxian1] [rows:26 ] SHOW FULL COLUMNS FROM `factory_submissions`
2025-09-05T01:30:21.566+08:00 [DEBU] {2c09a0bb932462183167337daddc40f3} [  6 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL
2025-09-05T01:30:21.577+08:00 [DEBU] {2c09a0bb932462183167337daddc40f3} [  2 ms] [default] [sanxian1] [rows:2  ] SELECT `submission_uuid`,`title`,`submitter_name`,`status`,`created_at`,`updated_at` FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL ORDER BY `created_at` DESC LIMIT 0,10
2025-09-05T01:30:34.143+08:00 [DEBU] {7c1ce7a9962462183267337ddd999ba5} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL
2025-09-05T01:30:34.145+08:00 [DEBU] {7c1ce7a9962462183267337ddd999ba5} [  1 ms] [default] [sanxian1] [rows:2  ] SELECT `submission_uuid`,`title`,`submitter_name`,`status`,`created_at`,`updated_at` FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL ORDER BY `created_at` DESC LIMIT 0,10
2025-09-05T01:30:45.660+08:00 [DEBU] {3ccdf757992462183367337d936f47f4} [  8 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL
2025-09-05T01:30:45.664+08:00 [DEBU] {3ccdf757992462183367337d936f47f4} [  2 ms] [default] [sanxian1] [rows:2  ] SELECT `submission_uuid`,`title`,`submitter_name`,`status`,`created_at`,`updated_at` FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL ORDER BY `created_at` DESC LIMIT 0,10
2025-09-05T01:35:27.857+08:00 [DEBU] {087fc407db2462180ad9b53314fb99ad} [ 81 ms] [default] [sanxian1] [rows:100] SHOW TABLES
2025-09-05T01:35:27.881+08:00 [DEBU] {087fc407db2462180ad9b53314fb99ad} [ 18 ms] [default] [sanxian1] [rows:14 ] SHOW FULL COLUMNS FROM `sys_job`
2025-09-05T01:35:27.921+08:00 [DEBU] {087fc407db2462180ad9b53314fb99ad} [ 37 ms] [default] [sanxian1] [rows:1  ] SELECT `job_id`,`job_name`,`job_params`,`job_group`,`invoke_target`,`cron_expression`,`misfire_policy`,`concurrent`,`status`,`created_by`,`updated_by`,`remark`,`created_at`,`updated_at` FROM `sys_job` WHERE `status`=0
2025-09-05T01:35:27.924+08:00 [DEBU] {087fc407db2462180ad9b53314fb99ad} [  1 ms] [default] [sanxian1] [rows:0  ] UPDATE `sys_job` SET `status`=0 WHERE `job_id`=8
2025-09-05T01:35:27.928+08:00 [DEBU] {0092c410db2462180cd9b533779d9446} [  3 ms] [default] [sanxian1] [rows:5  ] SHOW FULL COLUMNS FROM `plugin_sms_config`
2025-09-05T01:35:27.941+08:00 [DEBU] {0092c410db2462180cd9b533779d9446} [ 11 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`sms_type`,`remark`,`status`,`config` FROM `plugin_sms_config` WHERE `status`=1 LIMIT 1
2025-09-05T01:35:27.944+08:00 [DEBU] {084ed711db2462180dd9b533e4bb7746} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT * FROM `plugin_sms_config` WHERE `sms_type`='yunxin' LIMIT 1
2025-09-05T01:40:05.984+08:00 [DEBU] {1c6f9e10db2462180bd9b533181390d4} [ 41 ms] [default] [sanxian1] [rows:8  ] SHOW FULL COLUMNS FROM `sys_user_online`
2025-09-05T01:40:05.995+08:00 [DEBU] {1c6f9e10db2462180bd9b533181390d4} [  5 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T01:40:06.001+08:00 [DEBU] {1c6f9e10db2462180bd9b533181390d4} [  3 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T01:40:06.122+08:00 [DEBU] {1c6f9e10db2462180bd9b533181390d4} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T01:40:06.132+08:00 [DEBU] {1c6f9e10db2462180bd9b533181390d4} [  8 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T01:40:06.143+08:00 [DEBU] {1c6f9e10db2462180bd9b533181390d4} [  5 ms] [default] [sanxian1] [rows:4  ] SHOW FULL COLUMNS FROM `sys_job_log`
2025-09-05T01:40:06.244+08:00 [DEBU] {1c6f9e10db2462180bd9b533181390d4} [100 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 01:40:06','在线用户定时更新，执行成功') 
2025-09-05T01:41:03.768+08:00 [DEBU] {c80e294129256218b95e655832215348} [ 22 ms] [default] [sanxian1] [rows:100] SHOW TABLES
2025-09-05T01:41:03.775+08:00 [DEBU] {c80e294129256218b95e655832215348} [  5 ms] [default] [sanxian1] [rows:14 ] SHOW FULL COLUMNS FROM `sys_job`
2025-09-05T01:41:03.780+08:00 [DEBU] {c80e294129256218b95e655832215348} [  3 ms] [default] [sanxian1] [rows:1  ] SELECT `job_id`,`job_name`,`job_params`,`job_group`,`invoke_target`,`cron_expression`,`misfire_policy`,`concurrent`,`status`,`created_by`,`updated_by`,`remark`,`created_at`,`updated_at` FROM `sys_job` WHERE `status`=0
2025-09-05T01:41:03.781+08:00 [DEBU] {c80e294129256218b95e655832215348} [  0 ms] [default] [sanxian1] [rows:0  ] UPDATE `sys_job` SET `status`=0 WHERE `job_id`=8
2025-09-05T01:41:03.784+08:00 [DEBU] {f84a654329256218bb5e6558cc8eb00e} [  1 ms] [default] [sanxian1] [rows:5  ] SHOW FULL COLUMNS FROM `plugin_sms_config`
2025-09-05T01:41:03.790+08:00 [DEBU] {f84a654329256218bb5e6558cc8eb00e} [  4 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`sms_type`,`remark`,`status`,`config` FROM `plugin_sms_config` WHERE `status`=1 LIMIT 1
2025-09-05T01:41:03.792+08:00 [DEBU] {cc97ee4329256218bc5e65585cdb2633} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT * FROM `plugin_sms_config` WHERE `sms_type`='yunxin' LIMIT 1
2025-09-05T01:50:05.863+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 17 ms] [default] [sanxian1] [rows:8  ] SHOW FULL COLUMNS FROM `sys_user_online`
2025-09-05T01:50:05.868+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T01:50:05.871+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T01:50:05.889+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T01:50:05.891+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T01:50:05.894+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [  2 ms] [default] [sanxian1] [rows:4  ] SHOW FULL COLUMNS FROM `sys_job_log`
2025-09-05T01:50:05.975+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 79 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 01:50:05','在线用户定时更新，执行成功') 
2025-09-05T02:00:05.350+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 10 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T02:00:05.355+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [  3 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T02:00:05.359+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T02:00:05.361+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T02:00:05.387+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 21 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 02:00:05','在线用户定时更新，执行成功') 
2025-09-05T02:10:05.349+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [  7 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T02:10:05.354+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T02:10:05.358+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T02:10:05.361+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T02:10:05.450+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 87 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 02:10:05','在线用户定时更新，执行成功') 
2025-09-05T02:20:05.847+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [  8 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T02:20:05.851+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T02:20:05.854+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T02:20:05.856+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T02:20:05.928+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 70 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 02:20:05','在线用户定时更新，执行成功') 
2025-09-05T02:30:05.947+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [  9 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T02:30:05.952+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T02:30:05.957+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T02:30:05.959+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T02:30:05.987+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 27 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 02:30:05','在线用户定时更新，执行成功') 
2025-09-05T10:40:05.187+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 49 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T10:40:05.192+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T10:40:05.257+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 51 ms] [default] [sanxian1] [rows:1  ] DELETE FROM `sys_user_online` WHERE `token`='7ZUSfVIf2HyYjcv86SKPPs29v003ECPEScsdYsYYqO20RmsaCQZk6qhkyNxg3PcBr83CTbpKek2tGBORC7k2NXyfYUSb2riknBC+OOfBxaft7uPNGw7HqYf89rmh1FtBp453QXYCyFuy7n+/MgeWiQ=='
2025-09-05T10:40:05.260+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T10:40:05.263+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T10:40:05.282+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 17 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 10:40:05','在线用户定时更新，执行成功') 
2025-09-05T10:50:05.249+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 10 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T10:50:05.253+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T10:50:05.331+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 76 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 10:50:05','在线用户定时更新，执行成功') 
2025-09-05T11:00:05.549+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 11 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T11:00:05.553+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T11:00:05.608+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 53 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 11:00:05','在线用户定时更新，执行成功') 
2025-09-05T11:10:05.951+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 12 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T11:10:05.954+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T11:10:05.980+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 23 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 11:10:05','在线用户定时更新，执行成功') 
2025-09-05T11:20:05.957+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 18 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T11:20:05.960+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T11:20:06.015+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 52 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 11:20:05','在线用户定时更新，执行成功') 
2025-09-05T11:30:05.257+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 19 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T11:30:05.261+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [  2 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T11:30:05.329+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 66 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 11:30:05','在线用户定时更新，执行成功') 
2025-09-05T11:40:05.256+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 18 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T11:40:05.259+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T11:40:05.293+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 32 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 11:40:05','在线用户定时更新，执行成功') 
2025-09-05T11:50:05.459+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 21 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T11:50:05.464+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [  2 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T11:50:05.552+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 84 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 11:50:05','在线用户定时更新，执行成功') 
2025-09-05T12:00:05.197+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 59 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T12:00:05.201+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T12:00:05.230+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 25 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 12:00:05','在线用户定时更新，执行成功') 
2025-09-05T12:10:05.758+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 20 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T12:10:05.761+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T12:10:05.845+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 82 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 12:10:05','在线用户定时更新，执行成功') 
2025-09-05T12:20:05.864+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 26 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T12:20:05.869+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T12:20:05.971+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [100 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 12:20:05','在线用户定时更新，执行成功') 
2025-09-05T12:30:05.360+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 22 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T12:30:05.364+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T12:30:05.408+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 27 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 12:30:05','在线用户定时更新，执行成功') 
2025-09-05T12:40:05.860+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 22 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T12:40:05.867+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [  2 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T12:40:05.902+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 33 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 12:40:05','在线用户定时更新，执行成功') 
2025-09-05T12:50:05.851+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 13 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T12:50:05.853+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T12:50:05.941+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 85 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 12:50:05','在线用户定时更新，执行成功') 
2025-09-05T13:00:05.356+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 16 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T13:00:05.361+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T13:00:05.377+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 13 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 13:00:05','在线用户定时更新，执行成功') 
2025-09-05T13:10:05.357+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 18 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T13:10:05.360+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T13:10:05.407+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 45 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 13:10:05','在线用户定时更新，执行成功') 
2025-09-05T13:20:05.356+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 18 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T13:20:05.360+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T13:20:05.425+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 62 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 13:20:05','在线用户定时更新，执行成功') 
2025-09-05T13:27:12.238+08:00 [DEBU] {04ffbcdbb14b6218bd5e655895f85f84} [230 ms] [default] [sanxian1] [rows:26 ] SHOW FULL COLUMNS FROM `factory_submissions`
2025-09-05T13:27:12.256+08:00 [DEBU] {04ffbcdbb14b6218bd5e655895f85f84} [ 14 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL
2025-09-05T13:27:12.264+08:00 [DEBU] {04ffbcdbb14b6218bd5e655895f85f84} [  4 ms] [default] [sanxian1] [rows:2  ] SELECT `submission_uuid`,`title`,`submitter_name`,`status`,`created_at`,`updated_at` FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL ORDER BY `like_count` DESC LIMIT 0,9
2025-09-05T13:27:12.324+08:00 [DEBU] {d4fc11f5b14b6218be5e6558559301be} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL
2025-09-05T13:27:12.329+08:00 [DEBU] {d4fc11f5b14b6218be5e6558559301be} [  1 ms] [default] [sanxian1] [rows:2  ] SELECT `submission_uuid`,`title`,`submitter_name`,`status`,`created_at`,`updated_at` FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL ORDER BY `created_at` DESC LIMIT 0,3
2025-09-05T13:27:31.053+08:00 [DEBU] {58298851b64b6218bf5e655879c9aa92} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL
2025-09-05T13:27:31.057+08:00 [DEBU] {58298851b64b6218bf5e655879c9aa92} [  1 ms] [default] [sanxian1] [rows:2  ] SELECT `submission_uuid`,`title`,`submitter_name`,`status`,`created_at`,`updated_at` FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL ORDER BY `view_count` DESC LIMIT 0,5
2025-09-05T13:27:31.106+08:00 [DEBU] {8ce2b054b64b6218c05e6558d4c26388} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL
2025-09-05T13:27:31.110+08:00 [DEBU] {8ce2b054b64b6218c05e6558d4c26388} [  1 ms] [default] [sanxian1] [rows:2  ] SELECT `submission_uuid`,`title`,`submitter_name`,`status`,`created_at`,`updated_at` FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL ORDER BY `created_at` DESC LIMIT 0,5
2025-09-05T13:27:45.006+08:00 [DEBU] {7843178fb94b6218c15e655808a736c8} [ 36 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL
2025-09-05T13:27:45.012+08:00 [DEBU] {7843178fb94b6218c15e655808a736c8} [  2 ms] [default] [sanxian1] [rows:2  ] SELECT `submission_uuid`,`title`,`submitter_name`,`status`,`created_at`,`updated_at` FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL ORDER BY `view_count` DESC LIMIT 0,5
2025-09-05T13:27:45.032+08:00 [DEBU] {fc87be92b94b6218c25e6558d0e6bdf4} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL
2025-09-05T13:27:45.038+08:00 [DEBU] {fc87be92b94b6218c25e6558d0e6bdf4} [  1 ms] [default] [sanxian1] [rows:2  ] SELECT `submission_uuid`,`title`,`submitter_name`,`status`,`created_at`,`updated_at` FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL ORDER BY `created_at` DESC LIMIT 0,5
2025-09-05T13:30:05.654+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 15 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T13:30:05.669+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T13:30:05.846+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 90 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 13:30:05','在线用户定时更新，执行成功') 
2025-09-05T13:32:56.369+08:00 [DEBU] {d4d8e1c6014c6218c35e65583fcae28f} [1224 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL
2025-09-05T13:32:56.566+08:00 [DEBU] {d4d8e1c6014c6218c35e65583fcae28f} [ 29 ms] [default] [sanxian1] [rows:2  ] SELECT `submission_uuid`,`title`,`submitter_name`,`status`,`created_at`,`updated_at` FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL ORDER BY `like_count` DESC LIMIT 0,9
2025-09-05T13:32:56.742+08:00 [DEBU] {94746e24024c6218c45e65581d8bf18c} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL
2025-09-05T13:32:56.791+08:00 [DEBU] {94746e24024c6218c45e65581d8bf18c} [  0 ms] [default] [sanxian1] [rows:2  ] SELECT `submission_uuid`,`title`,`submitter_name`,`status`,`created_at`,`updated_at` FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL ORDER BY `created_at` DESC LIMIT 0,3
2025-09-05T13:34:00.532+08:00 [DEBU] {c05d08fd104c6218c55e6558a076a48d} [ 41 ms] [default] [sanxian1] [rows:15 ] SHOW FULL COLUMNS FROM `sxr_users`
2025-09-05T13:34:00.546+08:00 [DEBU] {c05d08fd104c6218c55e6558a076a48d} [ 10 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `phone`='18663328966' LIMIT 1
2025-09-05T13:34:00.592+08:00 [DEBU] {c05d08fd104c6218c55e6558a076a48d} [ 31 ms] [default] [sanxian1] [rows:1  ] UPDATE `sxr_users` SET `updated_at`='2025-09-05 13:34:00',`last_login_at`='2025-09-05 13:34:00' WHERE `id`='3'
2025-09-05T13:34:00.947+08:00 [DEBU] {fcc9a818114c6218c65e655885f34e60} [  7 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL
2025-09-05T13:34:00.950+08:00 [DEBU] {fcc9a818114c6218c65e655885f34e60} [  2 ms] [default] [sanxian1] [rows:2  ] SELECT `submission_uuid`,`title`,`submitter_name`,`status`,`created_at`,`updated_at` FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL ORDER BY `like_count` DESC LIMIT 0,9
2025-09-05T13:34:01.302+08:00 [DEBU] {0c52322e114c6218c75e65584470c7c5} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL
2025-09-05T13:34:01.305+08:00 [DEBU] {0c52322e114c6218c75e65584470c7c5} [  1 ms] [default] [sanxian1] [rows:2  ] SELECT `submission_uuid`,`title`,`submitter_name`,`status`,`created_at`,`updated_at` FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL ORDER BY `created_at` DESC LIMIT 0,3
2025-09-05T13:40:05.559+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 20 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T13:40:05.565+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [  2 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T13:40:05.661+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 91 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 13:40:05','在线用户定时更新，执行成功') 
2025-09-05T13:41:36.654+08:00 [DEBU] {b0b1022c7b4c6218dff30355d20767de} [123 ms] [default] [sanxian1] [rows:100] SHOW TABLES
2025-09-05T13:41:36.674+08:00 [DEBU] {b0b1022c7b4c6218dff30355d20767de} [ 17 ms] [default] [sanxian1] [rows:14 ] SHOW FULL COLUMNS FROM `sys_job`
2025-09-05T13:41:36.685+08:00 [DEBU] {b0b1022c7b4c6218dff30355d20767de} [  6 ms] [default] [sanxian1] [rows:1  ] SELECT `job_id`,`job_name`,`job_params`,`job_group`,`invoke_target`,`cron_expression`,`misfire_policy`,`concurrent`,`status`,`created_by`,`updated_by`,`remark`,`created_at`,`updated_at` FROM `sys_job` WHERE `status`=0
2025-09-05T13:41:36.689+08:00 [DEBU] {b0b1022c7b4c6218dff30355d20767de} [  1 ms] [default] [sanxian1] [rows:0  ] UPDATE `sys_job` SET `status`=0 WHERE `job_id`=8
2025-09-05T13:41:36.708+08:00 [DEBU] {b4659b357b4c6218e1f30355b5966778} [ 17 ms] [default] [sanxian1] [rows:5  ] SHOW FULL COLUMNS FROM `plugin_sms_config`
2025-09-05T13:41:36.718+08:00 [DEBU] {b4659b357b4c6218e1f30355b5966778} [  7 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`sms_type`,`remark`,`status`,`config` FROM `plugin_sms_config` WHERE `status`=1 LIMIT 1
2025-09-05T13:41:36.724+08:00 [DEBU] {ac775d377b4c6218e2f30355254f2da0} [  3 ms] [default] [sanxian1] [rows:1  ] SELECT * FROM `plugin_sms_config` WHERE `sms_type`='yunxin' LIMIT 1
2025-09-05T13:42:57.978+08:00 [DEBU] {20e8d6208e4c6218cd5e6558f1f8d016} [ 30 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `phone`='13800138000' LIMIT 1
2025-09-05T13:42:58.010+08:00 [DEBU] {20e8d6208e4c6218cd5e6558f1f8d016} [ 30 ms] [default] [sanxian1] [rows:1  ] UPDATE `sxr_users` SET `updated_at`='2025-09-05 13:42:57',`last_login_at`='2025-09-05 13:42:57' WHERE `id`='1'
2025-09-05T13:49:23.622+08:00 [DEBU] {7ccc15a2e74c62183754312df04ba564} [1253 ms] [default] [sanxian1] [rows:100] SHOW TABLES
2025-09-05T13:49:23.650+08:00 [DEBU] {7ccc15a2e74c62183754312df04ba564} [ 22 ms] [default] [sanxian1] [rows:14 ] SHOW FULL COLUMNS FROM `sys_job`
2025-09-05T13:49:23.676+08:00 [DEBU] {7ccc15a2e74c62183754312df04ba564} [  8 ms] [default] [sanxian1] [rows:1  ] SELECT `job_id`,`job_name`,`job_params`,`job_group`,`invoke_target`,`cron_expression`,`misfire_policy`,`concurrent`,`status`,`created_by`,`updated_by`,`remark`,`created_at`,`updated_at` FROM `sys_job` WHERE `status`=0
2025-09-05T13:49:23.692+08:00 [DEBU] {7ccc15a2e74c62183754312df04ba564} [  2 ms] [default] [sanxian1] [rows:0  ] UPDATE `sys_job` SET `status`=0 WHERE `job_id`=8
2025-09-05T13:49:23.706+08:00 [DEBU] {3c1c8ef1e74c62183954312d971fb288} [  3 ms] [default] [sanxian1] [rows:5  ] SHOW FULL COLUMNS FROM `plugin_sms_config`
2025-09-05T13:49:23.717+08:00 [DEBU] {3c1c8ef1e74c62183954312d971fb288} [  8 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`sms_type`,`remark`,`status`,`config` FROM `plugin_sms_config` WHERE `status`=1 LIMIT 1
2025-09-05T13:49:23.722+08:00 [DEBU] {6c5bc3f2e74c62183a54312d8ae6b9ae} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT * FROM `plugin_sms_config` WHERE `sms_type`='yunxin' LIMIT 1
2025-09-05T13:50:05.852+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 13 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T13:50:05.855+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T13:50:05.910+08:00 [DEBU] {445c4e4329256218ba5e655856a5eee4} [ 52 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 13:50:05','在线用户定时更新，执行成功') 
2025-09-05T13:50:40.303+08:00 [DEBU] {88783cc5f94c6218d770dd60443e21c9} [ 35 ms] [default] [sanxian1] [rows:100] SHOW TABLES
2025-09-05T13:50:40.310+08:00 [DEBU] {88783cc5f94c6218d770dd60443e21c9} [  5 ms] [default] [sanxian1] [rows:14 ] SHOW FULL COLUMNS FROM `sys_job`
2025-09-05T13:50:40.317+08:00 [DEBU] {88783cc5f94c6218d770dd60443e21c9} [  5 ms] [default] [sanxian1] [rows:1  ] SELECT `job_id`,`job_name`,`job_params`,`job_group`,`invoke_target`,`cron_expression`,`misfire_policy`,`concurrent`,`status`,`created_by`,`updated_by`,`remark`,`created_at`,`updated_at` FROM `sys_job` WHERE `status`=0
2025-09-05T13:50:40.319+08:00 [DEBU] {88783cc5f94c6218d770dd60443e21c9} [  1 ms] [default] [sanxian1] [rows:0  ] UPDATE `sys_job` SET `status`=0 WHERE `job_id`=8
2025-09-05T13:50:40.322+08:00 [DEBU] {d8196bc8f94c6218d970dd60b1eb6a28} [  2 ms] [default] [sanxian1] [rows:5  ] SHOW FULL COLUMNS FROM `plugin_sms_config`
2025-09-05T13:50:40.331+08:00 [DEBU] {d8196bc8f94c6218d970dd60b1eb6a28} [  8 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`sms_type`,`remark`,`status`,`config` FROM `plugin_sms_config` WHERE `status`=1 LIMIT 1
2025-09-05T13:50:40.333+08:00 [DEBU] {282e1ac9f94c6218da70dd609c199303} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT * FROM `plugin_sms_config` WHERE `sms_type`='yunxin' LIMIT 1
2025-09-05T13:51:31.611+08:00 [DEBU] {68fb6eb5054d62182df19f35eb8b8f4f} [ 68 ms] [default] [sanxian1] [rows:100] SHOW TABLES
2025-09-05T13:51:31.636+08:00 [DEBU] {68fb6eb5054d62182df19f35eb8b8f4f} [  6 ms] [default] [sanxian1] [rows:14 ] SHOW FULL COLUMNS FROM `sys_job`
2025-09-05T13:51:31.662+08:00 [DEBU] {68fb6eb5054d62182df19f35eb8b8f4f} [  5 ms] [default] [sanxian1] [rows:1  ] SELECT `job_id`,`job_name`,`job_params`,`job_group`,`invoke_target`,`cron_expression`,`misfire_policy`,`concurrent`,`status`,`created_by`,`updated_by`,`remark`,`created_at`,`updated_at` FROM `sys_job` WHERE `status`=0
2025-09-05T13:51:31.665+08:00 [DEBU] {68fb6eb5054d62182df19f35eb8b8f4f} [  1 ms] [default] [sanxian1] [rows:0  ] UPDATE `sys_job` SET `status`=0 WHERE `job_id`=8
2025-09-05T13:51:31.676+08:00 [DEBU] {14a12ebd054d62182ff19f357b2aa054} [  4 ms] [default] [sanxian1] [rows:5  ] SHOW FULL COLUMNS FROM `plugin_sms_config`
2025-09-05T13:51:31.685+08:00 [DEBU] {14a12ebd054d62182ff19f357b2aa054} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`sms_type`,`remark`,`status`,`config` FROM `plugin_sms_config` WHERE `status`=1 LIMIT 1
2025-09-05T13:51:31.702+08:00 [DEBU] {4c1be9be054d621830f19f350e7d36d9} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT * FROM `plugin_sms_config` WHERE `sms_type`='yunxin' LIMIT 1
2025-09-05T13:55:02.813+08:00 [DEBU] {d0d099e4364d6218960e4f4592096536} [ 26 ms] [default] [sanxian1] [rows:100] SHOW TABLES
2025-09-05T13:55:02.821+08:00 [DEBU] {d0d099e4364d6218960e4f4592096536} [  4 ms] [default] [sanxian1] [rows:14 ] SHOW FULL COLUMNS FROM `sys_job`
2025-09-05T13:55:02.832+08:00 [DEBU] {d0d099e4364d6218960e4f4592096536} [  7 ms] [default] [sanxian1] [rows:1  ] SELECT `job_id`,`job_name`,`job_params`,`job_group`,`invoke_target`,`cron_expression`,`misfire_policy`,`concurrent`,`status`,`created_by`,`updated_by`,`remark`,`created_at`,`updated_at` FROM `sys_job` WHERE `status`=0
2025-09-05T13:55:02.834+08:00 [DEBU] {d0d099e4364d6218960e4f4592096536} [  1 ms] [default] [sanxian1] [rows:0  ] UPDATE `sys_job` SET `status`=0 WHERE `job_id`=8
2025-09-05T13:55:02.839+08:00 [DEBU] {d0ac8de7364d6218980e4f4507448df2} [  3 ms] [default] [sanxian1] [rows:5  ] SHOW FULL COLUMNS FROM `plugin_sms_config`
2025-09-05T13:55:02.847+08:00 [DEBU] {d0ac8de7364d6218980e4f4507448df2} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`sms_type`,`remark`,`status`,`config` FROM `plugin_sms_config` WHERE `status`=1 LIMIT 1
2025-09-05T13:55:02.858+08:00 [DEBU] {bcb3b7e8364d6218990e4f45a9d3447a} [  3 ms] [default] [sanxian1] [rows:1  ] SELECT * FROM `plugin_sms_config` WHERE `sms_type`='yunxin' LIMIT 1
2025-09-05T13:55:37.219+08:00 [DEBU] {602ce9e73e4d6218e97bb042f797bf38} [ 16 ms] [default] [sanxian1] [rows:100] SHOW TABLES
2025-09-05T13:55:37.226+08:00 [DEBU] {602ce9e73e4d6218e97bb042f797bf38} [  5 ms] [default] [sanxian1] [rows:14 ] SHOW FULL COLUMNS FROM `sys_job`
2025-09-05T13:55:37.230+08:00 [DEBU] {602ce9e73e4d6218e97bb042f797bf38} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT `job_id`,`job_name`,`job_params`,`job_group`,`invoke_target`,`cron_expression`,`misfire_policy`,`concurrent`,`status`,`created_by`,`updated_by`,`remark`,`created_at`,`updated_at` FROM `sys_job` WHERE `status`=0
2025-09-05T13:55:37.234+08:00 [DEBU] {602ce9e73e4d6218e97bb042f797bf38} [  2 ms] [default] [sanxian1] [rows:0  ] UPDATE `sys_job` SET `status`=0 WHERE `job_id`=8
2025-09-05T13:55:37.242+08:00 [DEBU] {20ffefe93e4d6218eb7bb042c7c3e3ca} [  6 ms] [default] [sanxian1] [rows:5  ] SHOW FULL COLUMNS FROM `plugin_sms_config`
2025-09-05T13:55:37.245+08:00 [DEBU] {20ffefe93e4d6218eb7bb042c7c3e3ca} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`sms_type`,`remark`,`status`,`config` FROM `plugin_sms_config` WHERE `status`=1 LIMIT 1
2025-09-05T13:55:37.247+08:00 [DEBU] {d8d097ea3e4d6218ec7bb042d43a0ce8} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT * FROM `plugin_sms_config` WHERE `sms_type`='yunxin' LIMIT 1
2025-09-05T13:58:01.054+08:00 [DEBU] {2cb0ff64604d6218b68b676cc5bca743} [ 19 ms] [default] [sanxian1] [rows:100] SHOW TABLES
2025-09-05T13:58:01.113+08:00 [DEBU] {2cb0ff64604d6218b68b676cc5bca743} [  5 ms] [default] [sanxian1] [rows:14 ] SHOW FULL COLUMNS FROM `sys_job`
2025-09-05T13:58:01.182+08:00 [DEBU] {2cb0ff64604d6218b68b676cc5bca743} [  5 ms] [default] [sanxian1] [rows:1  ] SELECT `job_id`,`job_name`,`job_params`,`job_group`,`invoke_target`,`cron_expression`,`misfire_policy`,`concurrent`,`status`,`created_by`,`updated_by`,`remark`,`created_at`,`updated_at` FROM `sys_job` WHERE `status`=0
2025-09-05T13:58:01.292+08:00 [DEBU] {2cb0ff64604d6218b68b676cc5bca743} [  0 ms] [default] [sanxian1] [rows:0  ] UPDATE `sys_job` SET `status`=0 WHERE `job_id`=8
2025-09-05T13:58:01.304+08:00 [DEBU] {609bda74604d6218b88b676c94d231ce} [  3 ms] [default] [sanxian1] [rows:5  ] SHOW FULL COLUMNS FROM `plugin_sms_config`
2025-09-05T13:58:01.354+08:00 [DEBU] {609bda74604d6218b88b676c94d231ce} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`sms_type`,`remark`,`status`,`config` FROM `plugin_sms_config` WHERE `status`=1 LIMIT 1
2025-09-05T13:58:01.360+08:00 [DEBU] {782b5778604d6218b98b676cdecd10a6} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT * FROM `plugin_sms_config` WHERE `sms_type`='yunxin' LIMIT 1
2025-09-05T14:00:05.261+08:00 [DEBU] {985dabe93e4d6218ea7bb042a638cdf3} [ 13 ms] [default] [sanxian1] [rows:8  ] SHOW FULL COLUMNS FROM `sys_user_online`
2025-09-05T14:00:05.264+08:00 [DEBU] {985dabe93e4d6218ea7bb042a638cdf3} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T14:00:05.267+08:00 [DEBU] {985dabe93e4d6218ea7bb042a638cdf3} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T14:00:05.271+08:00 [DEBU] {985dabe93e4d6218ea7bb042a638cdf3} [  2 ms] [default] [sanxian1] [rows:4  ] SHOW FULL COLUMNS FROM `sys_job_log`
2025-09-05T14:00:05.368+08:00 [DEBU] {985dabe93e4d6218ea7bb042a638cdf3} [ 91 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 14:00:05','在线用户定时更新，执行成功') 
2025-09-05T14:02:56.728+08:00 [DEBU] {709b403ca54d62184bf41970f3f1dbfd} [ 23 ms] [default] [sanxian1] [rows:100] SHOW TABLES
2025-09-05T14:02:56.736+08:00 [DEBU] {709b403ca54d62184bf41970f3f1dbfd} [  5 ms] [default] [sanxian1] [rows:14 ] SHOW FULL COLUMNS FROM `sys_job`
2025-09-05T14:02:56.743+08:00 [DEBU] {709b403ca54d62184bf41970f3f1dbfd} [  3 ms] [default] [sanxian1] [rows:1  ] SELECT `job_id`,`job_name`,`job_params`,`job_group`,`invoke_target`,`cron_expression`,`misfire_policy`,`concurrent`,`status`,`created_by`,`updated_by`,`remark`,`created_at`,`updated_at` FROM `sys_job` WHERE `status`=0
2025-09-05T14:02:56.746+08:00 [DEBU] {709b403ca54d62184bf41970f3f1dbfd} [  1 ms] [default] [sanxian1] [rows:0  ] UPDATE `sys_job` SET `status`=0 WHERE `job_id`=8
2025-09-05T14:02:56.753+08:00 [DEBU] {c48ee73ea54d62184df41970105dd05e} [  5 ms] [default] [sanxian1] [rows:5  ] SHOW FULL COLUMNS FROM `plugin_sms_config`
2025-09-05T14:02:56.757+08:00 [DEBU] {c48ee73ea54d62184df41970105dd05e} [  3 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`sms_type`,`remark`,`status`,`config` FROM `plugin_sms_config` WHERE `status`=1 LIMIT 1
2025-09-05T14:02:56.760+08:00 [DEBU] {d4628f3fa54d62184ef419705ed6e4ce} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT * FROM `plugin_sms_config` WHERE `sms_type`='yunxin' LIMIT 1
2025-09-05T14:04:11.182+08:00 [DEBU] {30df065cb64d6218d416db0818225050} [930 ms] [default] [sanxian1] [rows:100] SHOW TABLES
2025-09-05T14:04:11.524+08:00 [DEBU] {30df065cb64d6218d416db0818225050} [301 ms] [default] [sanxian1] [rows:14 ] SHOW FULL COLUMNS FROM `sys_job`
2025-09-05T14:04:11.623+08:00 [DEBU] {30df065cb64d6218d416db0818225050} [ 97 ms] [default] [sanxian1] [rows:1  ] SELECT `job_id`,`job_name`,`job_params`,`job_group`,`invoke_target`,`cron_expression`,`misfire_policy`,`concurrent`,`status`,`created_by`,`updated_by`,`remark`,`created_at`,`updated_at` FROM `sys_job` WHERE `status`=0
2025-09-05T14:04:11.626+08:00 [DEBU] {30df065cb64d6218d416db0818225050} [  1 ms] [default] [sanxian1] [rows:0  ] UPDATE `sys_job` SET `status`=0 WHERE `job_id`=8
2025-09-05T14:04:11.638+08:00 [DEBU] {14b40faeb64d6218d616db08de6c7a49} [ 11 ms] [default] [sanxian1] [rows:5  ] SHOW FULL COLUMNS FROM `plugin_sms_config`
2025-09-05T14:04:11.657+08:00 [DEBU] {14b40faeb64d6218d616db08de6c7a49} [ 17 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`sms_type`,`remark`,`status`,`config` FROM `plugin_sms_config` WHERE `status`=1 LIMIT 1
2025-09-05T14:04:11.683+08:00 [DEBU] {f0e8f7afb64d6218d716db08ba82f54b} [ 24 ms] [default] [sanxian1] [rows:1  ] SELECT * FROM `plugin_sms_config` WHERE `sms_type`='yunxin' LIMIT 1
2025-09-05T14:05:05.322+08:00 [DEBU] {44140d2dc34d6218db16db08698ddc91} [ 24 ms] [default] [sanxian1] [rows:15 ] SHOW FULL COLUMNS FROM `sxr_users`
2025-09-05T14:05:05.331+08:00 [DEBU] {44140d2dc34d6218db16db08698ddc91} [  8 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `phone`='13800138000' LIMIT 1
2025-09-05T14:05:20.156+08:00 [DEBU] {d026a0a2c64d6218dc16db08879ce02f} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `phone`='13800138000' LIMIT 1
2025-09-05T14:05:40.894+08:00 [DEBU] {80d31076cb4d6218de16db0872cb8a70} [ 12 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `phone`='13800138000' LIMIT 1
2025-09-05T14:06:21.739+08:00 [DEBU] {e03819f9d44d6218e116db08e1f5438f} [  3 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `phone`='13800138000' LIMIT 1
2025-09-05T14:06:47.917+08:00 [DEBU] {d0958f11db4d6218e316db08b208cb82} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `phone`='13800138000' LIMIT 1
2025-09-05T14:09:08.740+08:00 [DEBU] {f05fc8d7fb4d62184eacbc2b1acfae92} [ 59 ms] [default] [sanxian1] [rows:100] SHOW TABLES
2025-09-05T14:09:08.746+08:00 [DEBU] {f05fc8d7fb4d62184eacbc2b1acfae92} [  3 ms] [default] [sanxian1] [rows:14 ] SHOW FULL COLUMNS FROM `sys_job`
2025-09-05T14:09:08.753+08:00 [DEBU] {f05fc8d7fb4d62184eacbc2b1acfae92} [  4 ms] [default] [sanxian1] [rows:1  ] SELECT `job_id`,`job_name`,`job_params`,`job_group`,`invoke_target`,`cron_expression`,`misfire_policy`,`concurrent`,`status`,`created_by`,`updated_by`,`remark`,`created_at`,`updated_at` FROM `sys_job` WHERE `status`=0
2025-09-05T14:09:08.756+08:00 [DEBU] {f05fc8d7fb4d62184eacbc2b1acfae92} [  1 ms] [default] [sanxian1] [rows:0  ] UPDATE `sys_job` SET `status`=0 WHERE `job_id`=8
2025-09-05T14:09:08.759+08:00 [DEBU] {28fd66dcfb4d621850acbc2b68b65de1} [  2 ms] [default] [sanxian1] [rows:5  ] SHOW FULL COLUMNS FROM `plugin_sms_config`
2025-09-05T14:09:08.761+08:00 [DEBU] {28fd66dcfb4d621850acbc2b68b65de1} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`sms_type`,`remark`,`status`,`config` FROM `plugin_sms_config` WHERE `status`=1 LIMIT 1
2025-09-05T14:09:08.763+08:00 [DEBU] {3ce5badcfb4d621851acbc2b4c4228c9} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT * FROM `plugin_sms_config` WHERE `sms_type`='yunxin' LIMIT 1
2025-09-05T14:09:38.054+08:00 [DEBU] {c8336aa6024e621852acbc2b9db502e6} [ 12 ms] [default] [sanxian1] [rows:15 ] SHOW FULL COLUMNS FROM `sxr_users`
2025-09-05T14:09:38.413+08:00 [DEBU] {c8336aa6024e621852acbc2b9db502e6} [  5 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `phone`='13800138000' LIMIT 1
2025-09-05T14:10:05.212+08:00 [DEBU] {642639dcfb4d62184facbc2b9624bf99} [ 10 ms] [default] [sanxian1] [rows:8  ] SHOW FULL COLUMNS FROM `sys_user_online`
2025-09-05T14:10:05.214+08:00 [DEBU] {642639dcfb4d62184facbc2b9624bf99} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T14:10:05.217+08:00 [DEBU] {642639dcfb4d62184facbc2b9624bf99} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T14:10:05.221+08:00 [DEBU] {642639dcfb4d62184facbc2b9624bf99} [  2 ms] [default] [sanxian1] [rows:4  ] SHOW FULL COLUMNS FROM `sys_job_log`
2025-09-05T14:10:05.247+08:00 [DEBU] {642639dcfb4d62184facbc2b9624bf99} [ 25 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 14:10:05','在线用户定时更新，执行成功') 
2025-09-05T14:11:07.349+08:00 [DEBU] {60530f6c174e6218754f605f5ab95a1c} [216 ms] [default] [sanxian1] [rows:100] SHOW TABLES
2025-09-05T14:11:07.392+08:00 [DEBU] {60530f6c174e6218754f605f5ab95a1c} [ 36 ms] [default] [sanxian1] [rows:14 ] SHOW FULL COLUMNS FROM `sys_job`
2025-09-05T14:11:07.424+08:00 [DEBU] {60530f6c174e6218754f605f5ab95a1c} [ 29 ms] [default] [sanxian1] [rows:1  ] SELECT `job_id`,`job_name`,`job_params`,`job_group`,`invoke_target`,`cron_expression`,`misfire_policy`,`concurrent`,`status`,`created_by`,`updated_by`,`remark`,`created_at`,`updated_at` FROM `sys_job` WHERE `status`=0
2025-09-05T14:11:07.436+08:00 [DEBU] {60530f6c174e6218754f605f5ab95a1c} [ 11 ms] [default] [sanxian1] [rows:0  ] UPDATE `sys_job` SET `status`=0 WHERE `job_id`=8
2025-09-05T14:11:07.439+08:00 [DEBU] {fcd8447e174e6218774f605f8bd54abe} [  1 ms] [default] [sanxian1] [rows:5  ] SHOW FULL COLUMNS FROM `plugin_sms_config`
2025-09-05T14:11:07.447+08:00 [DEBU] {fcd8447e174e6218774f605f8bd54abe} [  6 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`sms_type`,`remark`,`status`,`config` FROM `plugin_sms_config` WHERE `status`=1 LIMIT 1
2025-09-05T14:11:07.449+08:00 [DEBU] {40b5ec7e174e6218784f605fcfa7c46f} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT * FROM `plugin_sms_config` WHERE `sms_type`='yunxin' LIMIT 1
2025-09-05T14:11:15.733+08:00 [DEBU] {b415c868194e6218794f605f6b9dcef5} [ 22 ms] [default] [sanxian1] [rows:15 ] SHOW FULL COLUMNS FROM `sxr_users`
2025-09-05T14:11:15.779+08:00 [DEBU] {b415c868194e6218794f605f6b9dcef5} [ 15 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `phone`='13800138000' LIMIT 1
2025-09-05T14:11:15.822+08:00 [DEBU] {b415c868194e6218794f605f6b9dcef5} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `id`=7 LIMIT 1
2025-09-05T14:11:49.751+08:00 [DEBU] {34340558214e62187c4f605f7eee997d} [  5 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `phone`='13800138000' LIMIT 1
2025-09-05T14:11:49.760+08:00 [DEBU] {34340558214e62187c4f605f7eee997d} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `id`=7 LIMIT 1
2025-09-05T14:20:05.928+08:00 [DEBU] {5cc78d7d174e6218764f605f51cf00d8} [251 ms] [default] [sanxian1] [rows:8  ] SHOW FULL COLUMNS FROM `sys_user_online`
2025-09-05T14:20:05.943+08:00 [DEBU] {5cc78d7d174e6218764f605f51cf00d8} [  3 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T14:20:05.969+08:00 [DEBU] {5cc78d7d174e6218764f605f51cf00d8} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T14:20:05.980+08:00 [DEBU] {5cc78d7d174e6218764f605f51cf00d8} [  3 ms] [default] [sanxian1] [rows:4  ] SHOW FULL COLUMNS FROM `sys_job_log`
2025-09-05T14:20:06.088+08:00 [DEBU] {5cc78d7d174e6218764f605f51cf00d8} [104 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 14:20:05','在线用户定时更新，执行成功') 
2025-09-05T14:29:09.728+08:00 [DEBU] {e856e878134f6218824f605fa40f61cf} [ 46 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `phone`='13800138000' LIMIT 1
2025-09-05T14:29:09.781+08:00 [DEBU] {e856e878134f6218824f605fa40f61cf} [  4 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `id`=7 LIMIT 1
2025-09-05T14:29:15.514+08:00 [DEBU] {24e273d4144f6218844f605f4531917b} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `phone`='13800138000' LIMIT 1
2025-09-05T14:30:05.618+08:00 [DEBU] {5cc78d7d174e6218764f605f51cf00d8} [ 74 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T14:30:05.627+08:00 [DEBU] {5cc78d7d174e6218764f605f51cf00d8} [  4 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T14:30:05.717+08:00 [DEBU] {5cc78d7d174e6218764f605f51cf00d8} [ 78 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 14:30:05','在线用户定时更新，执行成功') 
2025-09-05T14:38:33.613+08:00 [DEBU] {50d037c4964f6218864f605f15128224} [ 29 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `phone`='13800138000' LIMIT 1
2025-09-05T14:40:05.073+08:00 [DEBU] {5cc78d7d174e6218764f605f51cf00d8} [ 31 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T14:40:05.091+08:00 [DEBU] {5cc78d7d174e6218764f605f51cf00d8} [  3 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T14:40:05.165+08:00 [DEBU] {5cc78d7d174e6218764f605f51cf00d8} [ 61 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 14:40:05','在线用户定时更新，执行成功') 
2025-09-05T14:40:10.404+08:00 [DEBU] {20ca164bad4f6218874f605f356da8c2} [ 66 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `phone`='13800138000' LIMIT 1
2025-09-05T14:40:10.434+08:00 [DEBU] {20ca164bad4f6218874f605f356da8c2} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `id`=7 LIMIT 1
2025-09-05T14:40:12.732+08:00 [DEBU] {1cd261d8ad4f6218894f605f220e06f0} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `phone`='13800138000' LIMIT 1
2025-09-05T14:40:48.166+08:00 [DEBU] {58f82a18b64f62188a4f605f93fb7a89} [ 28 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `phone`='13800138000' LIMIT 1
2025-09-05T14:40:48.186+08:00 [DEBU] {58f82a18b64f62188a4f605f93fb7a89} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `id`=7 LIMIT 1
2025-09-05T14:40:49.238+08:00 [DEBU] {245fac59b64f62188c4f605f0c5075df} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `phone`='13800138000' LIMIT 1
2025-09-05T14:42:19.257+08:00 [DEBU] {5818f04acb4f6218e66bef36ccdf0b1c} [ 74 ms] [default] [sanxian1] [rows:100] SHOW TABLES
2025-09-05T14:42:19.270+08:00 [DEBU] {5818f04acb4f6218e66bef36ccdf0b1c} [  9 ms] [default] [sanxian1] [rows:14 ] SHOW FULL COLUMNS FROM `sys_job`
2025-09-05T14:42:19.284+08:00 [DEBU] {5818f04acb4f6218e66bef36ccdf0b1c} [ 10 ms] [default] [sanxian1] [rows:1  ] SELECT `job_id`,`job_name`,`job_params`,`job_group`,`invoke_target`,`cron_expression`,`misfire_policy`,`concurrent`,`status`,`created_by`,`updated_by`,`remark`,`created_at`,`updated_at` FROM `sys_job` WHERE `status`=0
2025-09-05T14:42:19.299+08:00 [DEBU] {5818f04acb4f6218e66bef36ccdf0b1c} [ 12 ms] [default] [sanxian1] [rows:0  ] UPDATE `sys_job` SET `status`=0 WHERE `job_id`=8
2025-09-05T14:42:19.306+08:00 [DEBU] {6c740f52cb4f6218e86bef368248bcf5} [  5 ms] [default] [sanxian1] [rows:5  ] SHOW FULL COLUMNS FROM `plugin_sms_config`
2025-09-05T14:42:19.310+08:00 [DEBU] {6c740f52cb4f6218e86bef368248bcf5} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`sms_type`,`remark`,`status`,`config` FROM `plugin_sms_config` WHERE `status`=1 LIMIT 1
2025-09-05T14:42:19.315+08:00 [DEBU] {f0f4be52cb4f6218e96bef36501ea544} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT * FROM `plugin_sms_config` WHERE `sms_type`='yunxin' LIMIT 1
2025-09-05T14:42:53.409+08:00 [DEBU] {bc1c4841d34f6218ea6bef369e0862f4} [ 29 ms] [default] [sanxian1] [rows:15 ] SHOW FULL COLUMNS FROM `sxr_users`
2025-09-05T14:42:53.444+08:00 [DEBU] {bc1c4841d34f6218ea6bef369e0862f4} [  4 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `phone`='13800138001' LIMIT 1
2025-09-05T14:42:53.477+08:00 [DEBU] {bc1c4841d34f6218ea6bef369e0862f4} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `id`=2 LIMIT 1
2025-09-05T14:42:53.630+08:00 [DEBU] {4c262750d34f6218ec6bef3659a1aaca} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `phone`='13800138001' LIMIT 1
2025-09-05T14:44:23.399+08:00 [DEBU] {0c7aa733e84f6218f2c36032122d4649} [ 53 ms] [default] [sanxian1] [rows:100] SHOW TABLES
2025-09-05T14:44:23.421+08:00 [DEBU] {0c7aa733e84f6218f2c36032122d4649} [ 19 ms] [default] [sanxian1] [rows:14 ] SHOW FULL COLUMNS FROM `sys_job`
2025-09-05T14:44:23.429+08:00 [DEBU] {0c7aa733e84f6218f2c36032122d4649} [  6 ms] [default] [sanxian1] [rows:1  ] SELECT `job_id`,`job_name`,`job_params`,`job_group`,`invoke_target`,`cron_expression`,`misfire_policy`,`concurrent`,`status`,`created_by`,`updated_by`,`remark`,`created_at`,`updated_at` FROM `sys_job` WHERE `status`=0
2025-09-05T14:44:23.431+08:00 [DEBU] {0c7aa733e84f6218f2c36032122d4649} [  0 ms] [default] [sanxian1] [rows:0  ] UPDATE `sys_job` SET `status`=0 WHERE `job_id`=8
2025-09-05T14:44:23.437+08:00 [DEBU] {40f3d638e84f6218f4c36032b10e228d} [  4 ms] [default] [sanxian1] [rows:5  ] SHOW FULL COLUMNS FROM `plugin_sms_config`
2025-09-05T14:44:23.444+08:00 [DEBU] {40f3d638e84f6218f4c36032b10e228d} [  6 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`sms_type`,`remark`,`status`,`config` FROM `plugin_sms_config` WHERE `status`=1 LIMIT 1
2025-09-05T14:44:23.446+08:00 [DEBU] {5c569d39e84f6218f5c36032157e45f0} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT * FROM `plugin_sms_config` WHERE `sms_type`='yunxin' LIMIT 1
2025-09-05T14:44:44.181+08:00 [DEBU] {1cce970ced4f6218f6c3603281fb963a} [  5 ms] [default] [sanxian1] [rows:15 ] SHOW FULL COLUMNS FROM `sxr_users`
2025-09-05T14:44:44.197+08:00 [DEBU] {1cce970ced4f6218f6c3603281fb963a} [  6 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `phone`='13800138001' LIMIT 1
2025-09-05T14:44:44.319+08:00 [DEBU] {1cce970ced4f6218f6c3603281fb963a} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `id`=2 LIMIT 1
2025-09-05T14:44:44.602+08:00 [DEBU] {4c237626ed4f6218f8c360325479a89c} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `phone`='13800138001' LIMIT 1
2025-09-05T14:45:19.511+08:00 [DEBU] {d4b5ab46f54f6218f9c36032b20d2cce} [ 11 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `phone`='13900139999' LIMIT 1
2025-09-05T14:45:19.611+08:00 [DEBU] {d4b5ab46f54f6218f9c36032b20d2cce} [ 88 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sxr_users`(`username`,`phone`,`password_hash`,`nickname`,`status`,`created_at`,`updated_at`) VALUES('13900139999','13900139999','986166e127a2d25cc099255e7d06e296','13900139999',1,'2025-09-05 14:45:19','2025-09-05 14:45:19') 
2025-09-05T14:45:19.622+08:00 [DEBU] {d4b5ab46f54f6218f9c36032b20d2cce} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `id`=8 LIMIT 1
2025-09-05T14:45:20.515+08:00 [DEBU] {e4cf1c83f54f6218fbc36032caf462e9} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `phone`='13900139999' LIMIT 1
2025-09-05T14:50:05.984+08:00 [DEBU] {b867b838e84f6218f3c36032e8895c50} [ 37 ms] [default] [sanxian1] [rows:8  ] SHOW FULL COLUMNS FROM `sys_user_online`
2025-09-05T14:50:05.989+08:00 [DEBU] {b867b838e84f6218f3c36032e8895c50} [  3 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T14:50:05.992+08:00 [DEBU] {b867b838e84f6218f3c36032e8895c50} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T14:50:05.995+08:00 [DEBU] {b867b838e84f6218f3c36032e8895c50} [  2 ms] [default] [sanxian1] [rows:4  ] SHOW FULL COLUMNS FROM `sys_job_log`
2025-09-05T14:50:06.091+08:00 [DEBU] {b867b838e84f6218f3c36032e8895c50} [ 95 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 14:50:05','在线用户定时更新，执行成功') 
2025-09-05T15:00:05.696+08:00 [DEBU] {b867b838e84f6218f3c36032e8895c50} [ 35 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T15:00:05.729+08:00 [DEBU] {b867b838e84f6218f3c36032e8895c50} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T15:00:05.997+08:00 [DEBU] {b867b838e84f6218f3c36032e8895c50} [ 87 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 15:00:05','在线用户定时更新，执行成功') 
2025-09-05T15:10:05.356+08:00 [DEBU] {b867b838e84f6218f3c36032e8895c50} [ 20 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T15:10:05.359+08:00 [DEBU] {b867b838e84f6218f3c36032e8895c50} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T15:10:05.416+08:00 [DEBU] {b867b838e84f6218f3c36032e8895c50} [ 55 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 15:10:05','在线用户定时更新，执行成功') 
2025-09-05T15:20:05.353+08:00 [DEBU] {b867b838e84f6218f3c36032e8895c50} [ 17 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T15:20:05.357+08:00 [DEBU] {b867b838e84f6218f3c36032e8895c50} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T15:20:05.400+08:00 [DEBU] {b867b838e84f6218f3c36032e8895c50} [ 40 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 15:20:05','在线用户定时更新，执行成功') 
2025-09-05T15:28:39.990+08:00 [DEBU] {14fddabc52526218fdc3603225b3a139} [ 17 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `phone`='13800138000' LIMIT 1
2025-09-05T15:28:55.280+08:00 [DEBU] {10451f4f56526218fec360325252b104} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `phone`='13999999999' LIMIT 1
2025-09-05T15:28:55.364+08:00 [DEBU] {10451f4f56526218fec360325252b104} [ 80 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sxr_users`(`username`,`phone`,`password_hash`,`nickname`,`status`,`created_at`,`updated_at`) VALUES('13999999999','13999999999','72a91dd1df0660644a2f14e338118d9e','13999999999',1,'2025-09-05 15:28:55','2025-09-05 15:28:55') 
2025-09-05T15:28:55.421+08:00 [DEBU] {10451f4f56526218fec360325252b104} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `id`=9 LIMIT 1
2025-09-05T15:28:55.546+08:00 [DEBU] {9093185f5652621800c46032216a8cbc} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `phone`='13999999999' LIMIT 1
2025-09-05T15:29:29.263+08:00 [DEBU] {f48123365e526218e74ffe069f63aac3} [ 46 ms] [default] [sanxian1] [rows:100] SHOW TABLES
2025-09-05T15:29:29.270+08:00 [DEBU] {f48123365e526218e74ffe069f63aac3} [  5 ms] [default] [sanxian1] [rows:14 ] SHOW FULL COLUMNS FROM `sys_job`
2025-09-05T15:29:29.279+08:00 [DEBU] {f48123365e526218e74ffe069f63aac3} [  6 ms] [default] [sanxian1] [rows:1  ] SELECT `job_id`,`job_name`,`job_params`,`job_group`,`invoke_target`,`cron_expression`,`misfire_policy`,`concurrent`,`status`,`created_by`,`updated_by`,`remark`,`created_at`,`updated_at` FROM `sys_job` WHERE `status`=0
2025-09-05T15:29:29.282+08:00 [DEBU] {f48123365e526218e74ffe069f63aac3} [  1 ms] [default] [sanxian1] [rows:0  ] UPDATE `sys_job` SET `status`=0 WHERE `job_id`=8
2025-09-05T15:29:29.285+08:00 [DEBU] {fc2e1a3a5e526218e94ffe06805a0ba8} [  2 ms] [default] [sanxian1] [rows:5  ] SHOW FULL COLUMNS FROM `plugin_sms_config`
2025-09-05T15:29:29.288+08:00 [DEBU] {fc2e1a3a5e526218e94ffe06805a0ba8} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`sms_type`,`remark`,`status`,`config` FROM `plugin_sms_config` WHERE `status`=1 LIMIT 1
2025-09-05T15:29:29.290+08:00 [DEBU] {445b7d3a5e526218ea4ffe06ef9f5181} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT * FROM `plugin_sms_config` WHERE `sms_type`='yunxin' LIMIT 1
2025-09-05T15:30:10.392+08:00 [DEBU] {d0fef3395e526218e84ffe067dd62093} [898 ms] [default] [sanxian1] [rows:8  ] SHOW FULL COLUMNS FROM `sys_user_online`
2025-09-05T15:30:10.394+08:00 [DEBU] {d0fef3395e526218e84ffe067dd62093} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T15:30:10.397+08:00 [DEBU] {d0fef3395e526218e84ffe067dd62093} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T15:30:10.637+08:00 [DEBU] {d0fef3395e526218e84ffe067dd62093} [200 ms] [default] [sanxian1] [rows:4  ] SHOW FULL COLUMNS FROM `sys_job_log`
2025-09-05T15:30:12.559+08:00 [DEBU] {d0fef3395e526218e84ffe067dd62093} [1921 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 15:30:10','在线用户定时更新，执行成功') 
2025-09-05T15:30:29.915+08:00 [DEBU] {3cd71b566c526218eb4ffe065fcb9a2e} [ 11 ms] [default] [sanxian1] [rows:15 ] SHOW FULL COLUMNS FROM `sxr_users`
2025-09-05T15:30:29.929+08:00 [DEBU] {3cd71b566c526218eb4ffe065fcb9a2e} [  9 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `phone`='13888888888' LIMIT 1
2025-09-05T15:30:30.066+08:00 [DEBU] {3cd71b566c526218eb4ffe065fcb9a2e} [105 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sxr_users`(`username`,`phone`,`password_hash`,`nickname`,`status`,`created_at`,`updated_at`) VALUES('13888888888','13888888888','8457ed23f346e8275e30f4af47aa1a9c','13888888888',1,'2025-09-05 15:30:29','2025-09-05 15:30:29') 
2025-09-05T15:30:30.110+08:00 [DEBU] {3cd71b566c526218eb4ffe065fcb9a2e} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `id`=10 LIMIT 1
2025-09-05T15:30:30.396+08:00 [DEBU] {1844a0746c526218ed4ffe0661895525} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `phone`='13888888888' LIMIT 1
2025-09-05T15:30:30.505+08:00 [DEBU] {c4801f7b6c526218ee4ffe06b00f9f13} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `phone`='13888888888' LIMIT 1
2025-09-05T15:30:30.538+08:00 [DEBU] {8c670f7d6c526218ef4ffe0600875c2a} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `phone`='13888888888' LIMIT 1
2025-09-05T15:30:30.582+08:00 [DEBU] {0829a77f6c526218f04ffe0668b02a5d} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `phone`='13888888888' LIMIT 1
2025-09-05T15:31:43.007+08:00 [DEBU] {8c7e5d5c7d526218f24ffe06fbf165cc} [  5 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `phone`='13888888888' LIMIT 1
2025-09-05T15:31:43.012+08:00 [DEBU] {8c7e5d5c7d526218f24ffe06fbf165cc} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `id`=10 LIMIT 1
2025-09-05T15:32:56.201+08:00 [DEBU] {447ddb668e526218f44ffe0622311103} [  8 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `phone`='13888888888' LIMIT 1
2025-09-05T15:40:05.631+08:00 [DEBU] {d0fef3395e526218e84ffe067dd62093} [ 11 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T15:40:05.634+08:00 [DEBU] {d0fef3395e526218e84ffe067dd62093} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T15:40:05.728+08:00 [DEBU] {d0fef3395e526218e84ffe067dd62093} [ 90 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 15:40:05','在线用户定时更新，执行成功') 
2025-09-05T15:50:07.330+08:00 [DEBU] {d0fef3395e526218e84ffe067dd62093} [891 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T15:50:07.723+08:00 [DEBU] {d0fef3395e526218e84ffe067dd62093} [ 57 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T15:50:08.792+08:00 [DEBU] {d0fef3395e526218e84ffe067dd62093} [1006 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 15:50:07','在线用户定时更新，执行成功') 
2025-09-05T15:53:15.207+08:00 [DEBU] {0081d729aa536218f54ffe069b04e917} [125 ms] [default] [sanxian1] [rows:26 ] SHOW FULL COLUMNS FROM `factory_submissions`
2025-09-05T15:53:15.339+08:00 [DEBU] {0081d729aa536218f54ffe069b04e917} [122 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL
2025-09-05T15:53:15.345+08:00 [DEBU] {0081d729aa536218f54ffe069b04e917} [  4 ms] [default] [sanxian1] [rows:2  ] SELECT `submission_uuid`,`title`,`submitter_name`,`status`,`created_at`,`updated_at` FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL ORDER BY `like_count` DESC LIMIT 0,9
2025-09-05T15:53:15.551+08:00 [DEBU] {24f9fd4daa536218f74ffe06fcab435e} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL
2025-09-05T15:53:15.632+08:00 [DEBU] {24f9fd4daa536218f74ffe06fcab435e} [ 63 ms] [default] [sanxian1] [rows:2  ] SELECT `submission_uuid`,`title`,`submitter_name`,`status`,`created_at`,`updated_at` FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL ORDER BY `created_at` DESC LIMIT 0,3
2025-09-05T15:53:15.628+08:00 [DEBU] {a85f9d50aa536218f84ffe06138a9c1f} [ 38 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL
2025-09-05T15:53:15.643+08:00 [DEBU] {a85f9d50aa536218f84ffe06138a9c1f} [  2 ms] [default] [sanxian1] [rows:2  ] SELECT `submission_uuid`,`title`,`submitter_name`,`status`,`created_at`,`updated_at` FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL ORDER BY `like_count` DESC LIMIT 0,9
2025-09-05T15:53:15.687+08:00 [DEBU] {dc132356aa536218f94ffe06d968513f} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL
2025-09-05T15:53:15.700+08:00 [DEBU] {dc132356aa536218f94ffe06d968513f} [  9 ms] [default] [sanxian1] [rows:2  ] SELECT `submission_uuid`,`title`,`submitter_name`,`status`,`created_at`,`updated_at` FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL ORDER BY `created_at` DESC LIMIT 0,3
2025-09-05T15:53:40.564+08:00 [DEBU] {7416db20b0536218fa4ffe06d334e97c} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL
2025-09-05T15:53:40.567+08:00 [DEBU] {7416db20b0536218fa4ffe06d334e97c} [  1 ms] [default] [sanxian1] [rows:2  ] SELECT `submission_uuid`,`title`,`submitter_name`,`status`,`created_at`,`updated_at` FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL ORDER BY `like_count` DESC LIMIT 0,9
2025-09-05T15:53:40.605+08:00 [DEBU] {5cdf7223b0536218fb4ffe0677b10fcf} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL
2025-09-05T15:53:40.608+08:00 [DEBU] {5cdf7223b0536218fb4ffe0677b10fcf} [  1 ms] [default] [sanxian1] [rows:2  ] SELECT `submission_uuid`,`title`,`submitter_name`,`status`,`created_at`,`updated_at` FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL ORDER BY `like_count` DESC LIMIT 0,9
2025-09-05T15:53:40.806+08:00 [DEBU] {24ab9b2fb0536218fc4ffe06e74eb967} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL
2025-09-05T15:53:40.812+08:00 [DEBU] {24ab9b2fb0536218fc4ffe06e74eb967} [  2 ms] [default] [sanxian1] [rows:2  ] SELECT `submission_uuid`,`title`,`submitter_name`,`status`,`created_at`,`updated_at` FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL ORDER BY `created_at` DESC LIMIT 0,3
2025-09-05T15:53:40.905+08:00 [DEBU] {6ca67a35b0536218fd4ffe06cca512e4} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL
2025-09-05T15:53:40.914+08:00 [DEBU] {6ca67a35b0536218fd4ffe06cca512e4} [  4 ms] [default] [sanxian1] [rows:2  ] SELECT `submission_uuid`,`title`,`submitter_name`,`status`,`created_at`,`updated_at` FROM `factory_submissions` WHERE (`submission_type`='story') AND `deleted_at` IS NULL ORDER BY `created_at` DESC LIMIT 0,3
2025-09-05T16:00:05.929+08:00 [DEBU] {d0fef3395e526218e84ffe067dd62093} [ 17 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T16:00:05.933+08:00 [DEBU] {d0fef3395e526218e84ffe067dd62093} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T16:00:06.016+08:00 [DEBU] {d0fef3395e526218e84ffe067dd62093} [ 80 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 16:00:05','在线用户定时更新，执行成功') 
2025-09-05T16:10:05.427+08:00 [DEBU] {d0fef3395e526218e84ffe067dd62093} [ 15 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T16:10:05.430+08:00 [DEBU] {d0fef3395e526218e84ffe067dd62093} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T16:10:05.463+08:00 [DEBU] {d0fef3395e526218e84ffe067dd62093} [ 30 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 16:10:05','在线用户定时更新，执行成功') 
2025-09-05T16:25:17.618+08:00 [DEBU] {289568cd69556218f2d0b8492987a5aa} [ 83 ms] [default] [sanxian1] [rows:100] SHOW TABLES
2025-09-05T16:25:17.638+08:00 [DEBU] {289568cd69556218f2d0b8492987a5aa} [ 18 ms] [default] [sanxian1] [rows:14 ] SHOW FULL COLUMNS FROM `sys_job`
2025-09-05T16:25:17.654+08:00 [DEBU] {289568cd69556218f2d0b8492987a5aa} [ 14 ms] [default] [sanxian1] [rows:1  ] SELECT `job_id`,`job_name`,`job_params`,`job_group`,`invoke_target`,`cron_expression`,`misfire_policy`,`concurrent`,`status`,`created_by`,`updated_by`,`remark`,`created_at`,`updated_at` FROM `sys_job` WHERE `status`=0
2025-09-05T16:25:17.657+08:00 [DEBU] {289568cd69556218f2d0b8492987a5aa} [  2 ms] [default] [sanxian1] [rows:0  ] UPDATE `sys_job` SET `status`=0 WHERE `job_id`=8
2025-09-05T16:25:17.662+08:00 [DEBU] {cc68d0d469556218f4d0b849d18fe218} [  4 ms] [default] [sanxian1] [rows:5  ] SHOW FULL COLUMNS FROM `plugin_sms_config`
2025-09-05T16:25:17.666+08:00 [DEBU] {cc68d0d469556218f4d0b849d18fe218} [  4 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`sms_type`,`remark`,`status`,`config` FROM `plugin_sms_config` WHERE `status`=1 LIMIT 1
2025-09-05T16:25:17.668+08:00 [DEBU] {501c52d569556218f5d0b84982e0170a} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT * FROM `plugin_sms_config` WHERE `sms_type`='yunxin' LIMIT 1
2025-09-05T16:30:05.720+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 40 ms] [default] [sanxian1] [rows:8  ] SHOW FULL COLUMNS FROM `sys_user_online`
2025-09-05T16:30:05.724+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  3 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T16:30:05.726+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T16:30:05.730+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  2 ms] [default] [sanxian1] [rows:4  ] SHOW FULL COLUMNS FROM `sys_job_log`
2025-09-05T16:30:05.816+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 85 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 16:30:05','在线用户定时更新，执行成功') 
2025-09-05T16:40:05.985+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 18 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T16:40:05.989+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  2 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T16:40:06.101+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [111 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 16:40:05','在线用户定时更新，执行成功') 
2025-09-05T16:50:07.519+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [1104 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T16:50:07.589+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 16 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T16:50:07.745+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [137 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 16:50:07','在线用户定时更新，执行成功') 
2025-09-05T17:00:05.237+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [100 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T17:00:05.289+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  2 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T17:00:05.359+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 43 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 17:00:05','在线用户定时更新，执行成功') 
2025-09-05T17:10:05.412+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 45 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T17:10:05.419+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  3 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T17:10:05.506+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 86 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 17:10:05','在线用户定时更新，执行成功') 
2025-09-05T17:20:05.387+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 20 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T17:20:05.390+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T17:20:05.405+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 13 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 17:20:05','在线用户定时更新，执行成功') 
2025-09-05T17:30:06.084+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [117 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T17:30:06.157+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  5 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T17:30:06.282+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [118 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 17:30:06','在线用户定时更新，执行成功') 
2025-09-05T17:33:32.624+08:00 [DEBU] {e4e05c4123596218ffd0b8494745ba11} [ 27 ms] [default] [sanxian1] [rows:22 ] SHOW FULL COLUMNS FROM `sys_user`
2025-09-05T17:33:32.643+08:00 [DEBU] {e4e05c4123596218ffd0b8494745ba11} [ 15 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`user_name`,`mobile`,`user_nickname`,`user_password`,`user_salt`,`user_status`,`is_admin`,`avatar`,`dept_id` FROM `sys_user` WHERE (`user_name`='demo') AND `deleted_at` IS NULL LIMIT 1
2025-09-05T17:33:32.696+08:00 [DEBU] {e4e05c4123596218ffd0b8494745ba11} [ 49 ms] [default] [sanxian1] [rows:1  ] UPDATE `sys_user` SET `last_login_ip`='::1',`last_login_time`='2025-09-05 17:33:32' WHERE `id`=31
2025-09-05T17:33:32.715+08:00 [DEBU] {f8d420482359621800d1b84915dff55a} [  5 ms] [default] [sanxian1] [rows:10 ] SHOW FULL COLUMNS FROM `sys_login_log`
2025-09-05T17:33:32.742+08:00 [DEBU] {f8d420482359621800d1b84915dff55a} [ 26 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_login_log`(`login_name`,`ipaddr`,`login_location`,`browser`,`os`,`status`,`msg`,`login_time`,`module`) VALUES('demo','::1','内网IP','Edge','Windows 10',1,'登录成功','2025-09-05 17:33:32','系统后台') 
2025-09-05T17:33:32.750+08:00 [DEBU] {e4e05c4123596218ffd0b8494745ba11} [  4 ms] [default] [sanxian1] [rows:7  ] SHOW FULL COLUMNS FROM `casbin_rule`
2025-09-05T17:33:32.757+08:00 [DEBU] {e4e05c4123596218ffd0b8494745ba11} [  6 ms] [default] [sanxian1] [rows:65 ] SELECT `ptype`,`v0`,`v1`,`v2`,`v3`,`v4`,`v5` FROM `casbin_rule`
2025-09-05T17:33:32.782+08:00 [DEBU] {7cf2894c2359621801d1b849d1cac493} [  8 ms] [default] [sanxian1] [rows:0  ] SELECT `id` FROM `sys_user_online` WHERE `token`='7ZUSfVIf2HyYjcv86SKPPs29v003ECPEScsdYsYYqO0UZxl82Rru4U8rKfP4TZcL/vYHAEQw+N0KWm3rAatyIARAk1Wv+sx1pb8ufCJEo/gM7zAbfx2bAwMMLRHczAeLB2O3Cad1fGKz/R3+GlY4WA==' LIMIT 1
2025-09-05T17:33:32.793+08:00 [DEBU] {7cf2894c2359621801d1b849d1cac493} [  9 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_user_online`(`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os`) VALUES('4d56b756e58290333902e8624aa15b3e','7ZUSfVIf2HyYjcv86SKPPs29v003ECPEScsdYsYYqO0UZxl82Rru4U8rKfP4TZcL/vYHAEQw+N0KWm3rAatyIARAk1Wv+sx1pb8ufCJEo/gM7zAbfx2bAwMMLRHczAeLB2O3Cad1fGKz/R3+GlY4WA==','2025-09-05 17:33:32','demo','::1','Edge','Windows 10') 
2025-09-05T17:40:06.186+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [219 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T17:40:06.279+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 53 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T17:40:06.667+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T17:40:06.720+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 11 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T17:40:07.161+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [440 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 17:40:06','在线用户定时更新，执行成功') 
2025-09-05T17:50:05.982+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 15 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T17:50:05.986+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  3 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T17:50:05.989+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T17:50:05.990+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T17:50:06.006+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 14 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 17:50:05','在线用户定时更新，执行成功') 
2025-09-05T17:55:40.390+08:00 [DEBU] {18babc61585a621807d1b849e5cbda4a} [101 ms] [default] [sanxian1] [rows:10 ] SHOW FULL COLUMNS FROM `sys_dict_type`
2025-09-05T17:55:40.397+08:00 [DEBU] {d86a2263585a621809d1b8496ff7a07b} [  6 ms] [default] [sanxian1] [rows:10 ] SHOW FULL COLUMNS FROM `sys_config`
2025-09-05T17:55:40.463+08:00 [DEBU] {18babc61585a621807d1b849e5cbda4a} [ 65 ms] [default] [sanxian1] [rows:1  ] SELECT `dict_name`,`remark` FROM `sys_dict_type` WHERE (`dict_type`='sys_yes_no') AND (`status`=1) LIMIT 1
2025-09-05T17:55:40.464+08:00 [DEBU] {d86a2263585a621809d1b8496ff7a07b} [ 66 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_config`
2025-09-05T17:55:40.468+08:00 [DEBU] {d86a2263585a621809d1b8496ff7a07b} [  1 ms] [default] [sanxian1] [rows:5  ] SELECT `config_id`,`config_name`,`config_key`,`config_value`,`config_type`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_config` ORDER BY `config_id` asc LIMIT 0,10
2025-09-05T17:55:40.480+08:00 [DEBU] {18babc61585a621807d1b849e5cbda4a} [  9 ms] [default] [sanxian1] [rows:14 ] SHOW FULL COLUMNS FROM `sys_dict_data`
2025-09-05T17:55:40.498+08:00 [DEBU] {18babc61585a621807d1b849e5cbda4a} [ 17 ms] [default] [sanxian1] [rows:2  ] SELECT `dict_value`,`dict_label`,`is_default`,`remark` FROM `sys_dict_data` WHERE (`dict_type`='sys_yes_no') AND (`status`=1) ORDER BY `dict_sort` asc,`dict_code` asc
2025-09-05T17:55:40.514+08:00 [DEBU] {382f886f585a62180bd1b849a131c253} [  4 ms] [default] [sanxian1] [rows:14 ] SHOW FULL COLUMNS FROM `sys_oper_log`
2025-09-05T17:55:40.574+08:00 [DEBU] {84f7786f585a62180ad1b849559bb5af} [ 59 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('参数管理','/api/v1/system/config/list','GET',1,'demo','财务部门','/api/v1/system/config/list?pageNum=1&pageSize=10&configName=&configKey=&configType=','::1','内网IP','{"configKey":"","configName":"","configType":"","pageNum":"1","pageSize":"10"}','2025-09-05 17:55:40') 
2025-09-05T17:55:40.575+08:00 [DEBU] {382f886f585a62180bd1b849a131c253} [ 60 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/getDictData','GET',1,'demo','财务部门','/api/v1/system/dict/data/getDictData?dictType=sys_yes_no&defaultValue=','::1','内网IP','{"defaultValue":"","dictType":"sys_yes_no"}','2025-09-05 17:55:40') 
2025-09-05T17:55:42.525+08:00 [DEBU] {94b212e7585a62180dd1b8498cc03ba8} [  6 ms] [default] [sanxian1] [rows:22 ] SHOW FULL COLUMNS FROM `sys_auth_rule`
2025-09-05T17:55:42.537+08:00 [DEBU] {94b212e7585a62180dd1b8498cc03ba8} [ 11 ms] [default] [sanxian1] [rows:80 ] SELECT `id`,`pid`,`name`,`title`,`icon`,`condition`,`remark`,`menu_type`,`weigh`,`is_hide`,`is_cached`,`is_affix`,`path`,`redirect`,`component`,`is_iframe`,`is_link`,`link_url` FROM `sys_auth_rule` ORDER BY `weigh` desc,`id` asc
2025-09-05T17:55:42.545+08:00 [DEBU] {50bc09e9585a62180ed1b849d2d215d8} [  4 ms] [default] [sanxian1] [rows:14 ] SHOW FULL COLUMNS FROM `sys_dept`
2025-09-05T17:55:42.550+08:00 [DEBU] {50bc09e9585a62180ed1b849d2d215d8} [  5 ms] [default] [sanxian1] [rows:14 ] SELECT `dept_id`,`parent_id`,`ancestors`,`dept_name`,`order_num`,`leader`,`phone`,`email`,`status`,`created_by`,`updated_by`,`created_at`,`updated_at`,`deleted_at` FROM `sys_dept` WHERE `deleted_at` IS NULL
2025-09-05T17:55:42.560+08:00 [DEBU] {50bc09e9585a62180ed1b849d2d215d8} [  6 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/cache/remove','DELETE',1,'demo','财务部门','/api/v1/system/cache/remove','::1','内网IP','{}','2025-09-05 17:55:42') 
2025-09-05T17:55:42.813+08:00 [DEBU] {b02decf8585a621810d1b8491bc9e857} [  2 ms] [default] [sanxian1] [rows:11 ] SHOW FULL COLUMNS FROM `sys_role`
2025-09-05T17:55:42.821+08:00 [DEBU] {b02decf8585a621810d1b8491bc9e857} [  7 ms] [default] [sanxian1] [rows:7  ] SELECT `id`,`pid`,`status`,`list_order`,`name`,`remark`,`data_scope`,`created_at`,`updated_at`,`created_by`,`effectiveTime` FROM `sys_role` ORDER BY `list_order` asc,`id` asc
2025-09-05T17:55:42.860+08:00 [DEBU] {3cce5dfb585a621811d1b849c0dbd6a1} [  9 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/user/getUserMenus','GET',1,'demo','财务部门','/api/v1/system/user/getUserMenus','::1','内网IP','{}','2025-09-05 17:55:42') 
2025-09-05T17:55:43.295+08:00 [DEBU] {2c2fb615595a621812d1b84977407c9b} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT `dict_name`,`remark` FROM `sys_dict_type` WHERE (`dict_type`='sys_yes_no') AND (`status`=1) LIMIT 1
2025-09-05T17:55:43.296+08:00 [DEBU] {2c2fb615595a621812d1b84977407c9b} [  1 ms] [default] [sanxian1] [rows:2  ] SELECT `dict_value`,`dict_label`,`is_default`,`remark` FROM `sys_dict_data` WHERE (`dict_type`='sys_yes_no') AND (`status`=1) ORDER BY `dict_sort` asc,`dict_code` asc
2025-09-05T17:55:43.296+08:00 [DEBU] {2c2fb615595a621813d1b849f1459252} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_config`
2025-09-05T17:55:43.299+08:00 [DEBU] {2c2fb615595a621813d1b849f1459252} [  2 ms] [default] [sanxian1] [rows:5  ] SELECT `config_id`,`config_name`,`config_key`,`config_value`,`config_type`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_config` ORDER BY `config_id` asc LIMIT 0,10
2025-09-05T17:55:43.304+08:00 [DEBU] {14453016595a621814d1b84948f36c34} [  4 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/getDictData','GET',1,'demo','财务部门','/api/v1/system/dict/data/getDictData?dictType=sys_yes_no&defaultValue=','::1','内网IP','{"defaultValue":"","dictType":"sys_yes_no"}','2025-09-05 17:55:43') 
2025-09-05T17:55:43.311+08:00 [DEBU] {ac635616595a621815d1b849f261acc5} [  6 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('参数管理','/api/v1/system/config/list','GET',1,'demo','财务部门','/api/v1/system/config/list?pageNum=1&pageSize=10&configName=&configKey=&configType=','::1','内网IP','{"configKey":"","configName":"","configType":"","pageNum":"1","pageSize":"10"}','2025-09-05 17:55:43') 
2025-09-05T17:55:56.132+08:00 [DEBU] {a0178c125c5a621818d1b849af862c2f} [  8 ms] [default] [sanxian1] [rows:1  ] SELECT `dict_name`,`remark` FROM `sys_dict_type` WHERE (`dict_type`='sys_show_hide') AND (`status`=1) LIMIT 1
2025-09-05T17:55:56.135+08:00 [DEBU] {90fca2125c5a621819d1b849982aa6c5} [  3 ms] [default] [sanxian1] [rows:80 ] SELECT `id`,`pid`,`name`,`title`,`icon`,`condition`,`remark`,`menu_type`,`weigh`,`is_hide`,`is_cached`,`is_affix`,`path`,`redirect`,`component`,`is_iframe`,`is_link`,`link_url` FROM `sys_auth_rule` ORDER BY `weigh` desc,`id` asc
2025-09-05T17:55:56.140+08:00 [DEBU] {a0178c125c5a621818d1b849af862c2f} [  7 ms] [default] [sanxian1] [rows:2  ] SELECT `dict_value`,`dict_label`,`is_default`,`remark` FROM `sys_dict_data` WHERE (`dict_type`='sys_show_hide') AND (`status`=1) ORDER BY `dict_sort` asc,`dict_code` asc
2025-09-05T17:55:56.217+08:00 [DEBU] {cc81cc135c5a62181ad1b849bb3d4b93} [ 58 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/getDictData','GET',1,'demo','财务部门','/api/v1/system/dict/data/getDictData?dictType=sys_show_hide&defaultValue=','::1','内网IP','{"defaultValue":"","dictType":"sys_show_hide"}','2025-09-05 17:55:56') 
2025-09-05T17:55:56.218+08:00 [DEBU] {1469e3135c5a62181bd1b8495a46c090} [ 57 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/menu/list','GET',1,'demo','财务部门','/api/v1/system/menu/list?title=&component=','::1','内网IP','{"component":"","title":""}','2025-09-05 17:55:56') 
2025-09-05T17:57:06.919+08:00 [DEBU] {b4a35b8d6c5a62181cd1b849ffb394d3} [ 12 ms] [default] [sanxian1] [rows:80 ] SELECT `id`,`pid`,`name`,`title`,`icon`,`condition`,`remark`,`menu_type`,`weigh`,`is_hide`,`is_cached`,`is_affix`,`path`,`redirect`,`component`,`is_iframe`,`is_link`,`link_url` FROM `sys_auth_rule` ORDER BY `weigh` desc,`id` asc
2025-09-05T17:57:06.934+08:00 [DEBU] {bcbae08e6c5a62181dd1b84991fb0e69} [  6 ms] [default] [sanxian1] [rows:14 ] SELECT `dept_id`,`parent_id`,`ancestors`,`dept_name`,`order_num`,`leader`,`phone`,`email`,`status`,`created_by`,`updated_by`,`created_at`,`updated_at`,`deleted_at` FROM `sys_dept` WHERE `deleted_at` IS NULL
2025-09-05T17:57:06.996+08:00 [DEBU] {bcbae08e6c5a62181dd1b84991fb0e69} [ 58 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/cache/remove','DELETE',1,'demo','财务部门','/api/v1/system/cache/remove','::1','内网IP','{}','2025-09-05 17:57:06') 
2025-09-05T17:57:07.469+08:00 [DEBU] {********************************} [  4 ms] [default] [sanxian1] [rows:7  ] SELECT `id`,`pid`,`status`,`list_order`,`name`,`remark`,`data_scope`,`created_at`,`updated_at`,`created_by`,`effectiveTime` FROM `sys_role` ORDER BY `list_order` asc,`id` asc
2025-09-05T17:57:07.487+08:00 [DEBU] {fc98d8af6c5a621820d1b849fade8f3d} [  5 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/user/getUserMenus','GET',1,'demo','财务部门','/api/v1/system/user/getUserMenus','::1','内网IP','{}','2025-09-05 17:57:07') 
2025-09-05T17:57:07.946+08:00 [DEBU] {005f34cb6c5a621821d1b849e3826f8e} [  5 ms] [default] [sanxian1] [rows:1  ] SELECT `dict_name`,`remark` FROM `sys_dict_type` WHERE (`dict_type`='sys_show_hide') AND (`status`=1) LIMIT 1
2025-09-05T17:57:07.951+08:00 [DEBU] {005f34cb6c5a621822d1b8497754f678} [  8 ms] [default] [sanxian1] [rows:80 ] SELECT `id`,`pid`,`name`,`title`,`icon`,`condition`,`remark`,`menu_type`,`weigh`,`is_hide`,`is_cached`,`is_affix`,`path`,`redirect`,`component`,`is_iframe`,`is_link`,`link_url` FROM `sys_auth_rule` ORDER BY `weigh` desc,`id` asc
2025-09-05T17:57:07.954+08:00 [DEBU] {005f34cb6c5a621821d1b849e3826f8e} [  8 ms] [default] [sanxian1] [rows:2  ] SELECT `dict_value`,`dict_label`,`is_default`,`remark` FROM `sys_dict_data` WHERE (`dict_type`='sys_show_hide') AND (`status`=1) ORDER BY `dict_sort` asc,`dict_code` asc
2025-09-05T17:57:07.968+08:00 [DEBU] {fcc737cc6c5a621824d1b849e2902a21} [ 10 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/menu/list','GET',1,'demo','财务部门','/api/v1/system/menu/list?title=&component=','::1','内网IP','{"component":"","title":""}','2025-09-05 17:57:07') 
2025-09-05T17:57:07.970+08:00 [DEBU] {fcc737cc6c5a621823d1b84965a67799} [ 11 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/getDictData','GET',1,'demo','财务部门','/api/v1/system/dict/data/getDictData?dictType=sys_show_hide&defaultValue=','::1','内网IP','{"defaultValue":"","dictType":"sys_show_hide"}','2025-09-05 17:57:07') 
2025-09-05T18:00:05.919+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [139 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T18:00:05.940+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  9 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T18:00:05.950+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T18:00:05.958+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  8 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T18:00:06.109+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [150 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 18:00:05','在线用户定时更新，执行成功') 
2025-09-05T18:10:05.387+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 20 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T18:10:05.390+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T18:10:05.394+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T18:10:05.395+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T18:10:05.434+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 38 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 18:10:05','在线用户定时更新，执行成功') 
2025-09-05T18:20:06.018+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 39 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T18:20:06.027+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  3 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T18:20:06.032+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T18:20:06.033+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T18:20:06.103+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 69 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 18:20:06','在线用户定时更新，执行成功') 
2025-09-05T18:30:05.595+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 19 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T18:30:05.599+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T18:30:05.605+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T18:30:05.606+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T18:30:05.733+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [123 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 18:30:05','在线用户定时更新，执行成功') 
2025-09-05T18:40:05.581+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 14 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T18:40:05.584+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T18:40:05.588+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T18:40:05.589+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T18:40:05.677+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 85 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 18:40:05','在线用户定时更新，执行成功') 
2025-09-05T18:50:05.293+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 26 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T18:50:05.297+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T18:50:05.300+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T18:50:05.302+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T18:50:05.385+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 82 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 18:50:05','在线用户定时更新，执行成功') 
2025-09-05T19:00:05.182+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 14 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T19:00:05.185+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T19:00:05.189+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T19:00:05.190+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T19:00:05.249+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 57 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 19:00:05','在线用户定时更新，执行成功') 
2025-09-05T19:10:05.182+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 15 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T19:10:05.183+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T19:10:05.187+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T19:10:05.188+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T19:10:05.216+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 27 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 19:10:05','在线用户定时更新，执行成功') 
2025-09-05T19:40:05.587+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 20 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T19:40:05.590+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T19:40:05.595+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T19:40:05.596+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T19:40:05.627+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 30 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 19:40:05','在线用户定时更新，执行成功') 
2025-09-05T19:50:05.085+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 18 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T19:50:05.090+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T19:50:05.094+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T19:50:05.097+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T19:50:05.184+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 86 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 19:50:05','在线用户定时更新，执行成功') 
2025-09-05T20:00:05.205+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 38 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T20:00:05.213+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  3 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T20:00:05.217+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T20:00:05.219+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T20:00:05.292+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 71 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 20:00:05','在线用户定时更新，执行成功') 
2025-09-05T20:10:05.788+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 21 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T20:10:05.792+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T20:10:05.796+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T20:10:05.802+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T20:10:05.899+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 96 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 20:10:05','在线用户定时更新，执行成功') 
2025-09-05T20:20:12.780+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [1479 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T20:20:13.012+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 26 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T20:20:13.228+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T20:20:13.232+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T20:20:13.344+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 86 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 20:20:13','在线用户定时更新，执行成功') 
2025-09-05T20:30:05.810+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 43 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T20:30:05.815+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  3 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T20:30:05.821+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T20:30:05.824+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  2 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T20:30:05.863+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 39 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 20:30:05','在线用户定时更新，执行成功') 
2025-09-05T20:40:05.256+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 89 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T20:40:05.262+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T20:40:05.269+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T20:40:05.270+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T20:40:05.633+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [360 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 20:40:05','在线用户定时更新，执行成功') 
2025-09-05T20:50:08.014+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [1765 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T20:50:08.031+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 11 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T20:50:08.040+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T20:50:08.044+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  4 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T20:50:08.159+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [101 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 20:50:08','在线用户定时更新，执行成功') 
2025-09-05T21:00:05.509+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 42 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T21:00:05.520+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  9 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T21:00:05.526+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T21:00:05.527+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T21:00:05.560+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 32 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 21:00:05','在线用户定时更新，执行成功') 
2025-09-05T21:10:05.889+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 22 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T21:10:05.894+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T21:10:05.897+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T21:10:05.898+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T21:10:05.918+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 19 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 21:10:05','在线用户定时更新，执行成功') 
2025-09-05T21:20:05.696+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 29 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T21:20:05.698+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T21:20:05.701+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T21:20:05.702+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T21:20:05.731+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 29 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 21:20:05','在线用户定时更新，执行成功') 
2025-09-05T21:30:05.814+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 46 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T21:30:05.815+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T21:30:05.820+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T21:30:05.820+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T21:30:05.857+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 36 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 21:30:05','在线用户定时更新，执行成功') 
2025-09-05T21:40:05.985+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 18 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T21:40:05.996+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T21:40:06.000+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T21:40:06.002+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T21:40:06.072+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 70 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 21:40:06','在线用户定时更新，执行成功') 
2025-09-05T21:50:05.384+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 17 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T21:50:05.387+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  2 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T21:50:05.390+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T21:50:05.391+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T21:50:05.487+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 95 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 21:50:05','在线用户定时更新，执行成功') 
2025-09-05T22:36:59.907+08:00 [DEBU] {acd2bc69b269621827d1b849132c728d} [239 ms] [default] [sanxian1] [rows:15 ] SHOW FULL COLUMNS FROM `sxr_users`
2025-09-05T22:36:59.924+08:00 [DEBU] {acd2bc69b269621827d1b849132c728d} [ 15 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`username`,`phone`,`email`,`password_hash`,`nickname`,`avatar_url`,`gender`,`birth_date`,`location`,`bio`,`status`,`created_at`,`updated_at`,`last_login_at` FROM `sxr_users` WHERE `phone`='13800138000' LIMIT 1
2025-09-05T22:37:35.343+08:00 [DEBU] {0cec15b7ba69621834d1b849225c7d3d} [ 47 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`user_name`,`mobile`,`user_nickname`,`user_password`,`user_salt`,`user_status`,`is_admin`,`avatar`,`dept_id` FROM `sys_user` WHERE (`user_name`='demo') AND `deleted_at` IS NULL LIMIT 1
2025-09-05T22:37:35.407+08:00 [DEBU] {0cec15b7ba69621834d1b849225c7d3d} [ 60 ms] [default] [sanxian1] [rows:1  ] UPDATE `sys_user` SET `last_login_ip`='::1',`last_login_time`='2025-09-05 22:37:35' WHERE `id`=31
2025-09-05T22:37:35.454+08:00 [DEBU] {fc8b7ebeba69621835d1b8490a47e8c3} [ 26 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_login_log`(`login_name`,`ipaddr`,`login_location`,`browser`,`os`,`status`,`msg`,`login_time`,`module`) VALUES('demo','::1','内网IP','Edge','Windows 10',1,'登录成功','2025-09-05 22:37:35','系统后台') 
2025-09-05T22:37:35.478+08:00 [DEBU] {a8820ac2ba69621836d1b8499b2f71c4} [  5 ms] [default] [sanxian1] [rows:0  ] SELECT `id` FROM `sys_user_online` WHERE `token`='7ZUSfVIf2HyYjcv86SKPPs29v003ECPEScsdYsYYqO0UZxl82Rru4U8rKfP4TZcL97Q8WCgB+icNWiGQhBZootw+ZSCgen6z2Hfvi//ounRCGGyGl7NkcSL/i+6ulyuBUtzMXnYo2mzjBxTbEkaXww==' LIMIT 1
2025-09-05T22:37:35.491+08:00 [DEBU] {a8820ac2ba69621836d1b8499b2f71c4} [ 10 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_user_online`(`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os`) VALUES('b0228c9252ac13a7e8c9163a02028695','7ZUSfVIf2HyYjcv86SKPPs29v003ECPEScsdYsYYqO0UZxl82Rru4U8rKfP4TZcL97Q8WCgB+icNWiGQhBZootw+ZSCgen6z2Hfvi//ounRCGGyGl7NkcSL/i+6ulyuBUtzMXnYo2mzjBxTbEkaXww==','2025-09-05 22:37:35','demo','::1','Edge','Windows 10') 
2025-09-05T22:40:05.286+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 17 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T22:40:05.289+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  2 ms] [default] [sanxian1] [rows:2  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T22:40:05.381+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 87 ms] [default] [sanxian1] [rows:1  ] DELETE FROM `sys_user_online` WHERE `token`='7ZUSfVIf2HyYjcv86SKPPs29v003ECPEScsdYsYYqO0UZxl82Rru4U8rKfP4TZcL/vYHAEQw+N0KWm3rAatyIARAk1Wv+sx1pb8ufCJEo/gM7zAbfx2bAwMMLRHczAeLB2O3Cad1fGKz/R3+GlY4WA=='
2025-09-05T22:40:05.382+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T22:40:05.385+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  2 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T22:40:05.396+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 10 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 22:40:05','在线用户定时更新，执行成功') 
2025-09-05T22:50:05.084+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 16 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T22:50:05.086+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T22:50:05.089+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T22:50:05.091+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T22:50:05.169+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 77 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 22:50:05','在线用户定时更新，执行成功') 
2025-09-05T23:00:05.082+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 15 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T23:00:05.084+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T23:00:05.090+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T23:00:05.090+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T23:00:05.169+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 78 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 23:00:05','在线用户定时更新，执行成功') 
2025-09-05T23:10:05.585+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 16 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T23:10:05.588+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T23:10:05.591+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T23:10:05.592+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T23:10:05.687+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 94 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 23:10:05','在线用户定时更新，执行成功') 
2025-09-05T23:20:05.981+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 12 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T23:20:05.984+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T23:20:05.988+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T23:20:05.990+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T23:20:06.091+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [100 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 23:20:05','在线用户定时更新，执行成功') 
2025-09-05T23:30:05.980+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 13 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T23:30:05.981+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T23:30:05.985+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T23:30:05.986+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T23:30:06.014+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 27 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 23:30:05','在线用户定时更新，执行成功') 
2025-09-05T23:40:05.478+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 11 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T23:40:05.483+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T23:40:05.489+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T23:40:05.490+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T23:40:05.528+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 37 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 23:40:05','在线用户定时更新，执行成功') 
2025-09-05T23:50:05.182+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 15 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T23:50:05.184+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-09-05T23:50:05.188+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  1 ms] [default] [sanxian1] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-09-05T23:50:05.188+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [  0 ms] [default] [sanxian1] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-09-05T23:50:05.269+08:00 [DEBU] {f8fe9ad469556218f3d0b849b2520793} [ 80 ms] [default] [sanxian1] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-09-05 23:50:05','在线用户定时更新，执行成功') 
