<template>
  <div class="oauth-success">
    <div class="success-container">
      <el-card shadow="hover" class="success-card">
        <div class="success-content">
          <!-- 成功图标 -->
          <div class="success-icon">
            <el-icon :size="80" color="#67C23A">
              <SuccessFilled />
            </el-icon>
          </div>

          <!-- 成功标题 -->
          <h1 class="success-title">OAuth认证成功！</h1>
          
          <!-- 成功描述 -->
          <p class="success-description">
            恭喜您！您的gfast账户已成功与MediaWiki账户关联。
            <br>
            现在您可以在两个系统之间无缝切换，享受统一的身份认证体验。
          </p>

          <!-- 用户信息展示 -->
          <div v-if="oauthStore.userInfo" class="user-info-section">
            <h3>关联的MediaWiki账户信息</h3>
            <div class="user-card">
              <div class="user-avatar">
                <el-avatar :size="60" :src="avatarUrl">
                  <SvgIcon name="ele-User" :size="30" />
                </el-avatar>
              </div>
              <div class="user-details">
                <h4>{{ oauthStore.username }}</h4>
                <p class="user-email">{{ oauthStore.email || '未设置邮箱' }}</p>
                <div class="user-stats">
                  <el-tag type="info" size="small">
                    编辑 {{ oauthStore.editCount }} 次
                  </el-tag>
                  <el-tag 
                    v-for="group in oauthStore.groups.slice(0, 3)" 
                    :key="group"
                    type="primary" 
                    size="small"
                    class="ml5"
                  >
                    {{ group }}
                  </el-tag>
                  <span v-if="oauthStore.groups.length > 3" class="more-groups">
                    +{{ oauthStore.groups.length - 3 }} 更多
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- 功能介绍 -->
          <div class="features-section">
            <h3>现在您可以：</h3>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="feature-item">
                  <SvgIcon name="ele-Edit" :size="32" class="feature-icon" />
                  <h5>编辑页面</h5>
                  <p>直接在gfast中台编辑MediaWiki页面内容</p>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="feature-item">
                  <SvgIcon name="ele-Plus" :size="32" class="feature-icon" />
                  <h5>创建内容</h5>
                  <p>创建新的MediaWiki页面和内容</p>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="feature-item">
                  <SvgIcon name="ele-Upload" :size="32" class="feature-icon" />
                  <h5>文件管理</h5>
                  <p>上传和管理MediaWiki中的文件资源</p>
                </div>
              </el-col>
            </el-row>
          </div>

          <!-- 操作按钮 -->
          <div class="action-buttons">
            <el-button type="primary" size="large" @click="goToOAuthManagement">
              <SvgIcon name="ele-Setting" />
              管理OAuth设置
            </el-button>
            <el-button size="large" @click="goToWikipediaIntegration">
              <SvgIcon name="iconfont icon-wikipedia" />
              Wikipedia集成
            </el-button>
            <el-button size="large" @click="goToHome">
              <SvgIcon name="ele-HomeFilled" />
              返回首页
            </el-button>
          </div>

          <!-- 提示信息 -->
          <div class="tips-section">
            <el-alert
              title="温馨提示"
              type="info"
              :closable="false"
              show-icon
            >
              <template #default>
                <ul class="tips-list">
                  <li>OAuth认证会话将在 {{ oauthStore.formattedExpiresAt }} 过期</li>
                  <li>您可以随时在"OAuth设置"中管理您的认证状态</li>
                  <li>如遇到问题，请联系系统管理员</li>
                </ul>
              </template>
            </el-alert>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts" name="OAuthSuccess">
import { computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { SuccessFilled } from '@element-plus/icons-vue';
import { useOAuthStore } from '/@/stores/oauthStore';
import SvgIcon from '/@/components/svgIcon/index.vue';

// Router
const router = useRouter();

// Store
const oauthStore = useOAuthStore();

// 计算属性
const avatarUrl = computed(() => {
  if (oauthStore.userInfo?.email) {
    // 使用Gravatar头像
    const hash = btoa(oauthStore.userInfo.email.toLowerCase().trim());
    return `https://www.gravatar.com/avatar/${hash}?d=identicon&s=60`;
  }
  return '';
});

// 方法
const goToOAuthManagement = () => {
  router.push('/sanxianren/oauth');
};

const goToWikipediaIntegration = () => {
  router.push('/sanxianren/wikipediaIntegration');
};

const goToHome = () => {
  router.push('/home');
};

// 生命周期
onMounted(async () => {
  // 确保OAuth状态是最新的
  await oauthStore.getStatus();
  
  // 如果没有认证，重定向到OAuth管理页面
  if (!oauthStore.isAuthenticated) {
    router.push('/sanxianren/oauth');
  }
});
</script>

<style scoped lang="scss">
.oauth-success {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;

  .success-container {
    width: 100%;
    max-width: 800px;
  }

  .success-card {
    border-radius: 12px;
    overflow: hidden;
    border-top: 4px solid var(--el-color-success);
  }

  .success-content {
    text-align: center;
    padding: 40px 30px;
  }

  .success-icon {
    margin-bottom: 24px;
  }

  .success-title {
    margin: 0 0 16px 0;
    color: var(--el-text-color-primary);
    font-size: 28px;
    font-weight: 600;
  }

  .success-description {
    margin: 0 0 32px 0;
    color: var(--el-text-color-regular);
    font-size: 16px;
    line-height: 1.6;
  }

  .user-info-section {
    margin: 32px 0;
    text-align: left;

    h3 {
      margin-bottom: 16px;
      color: var(--el-text-color-primary);
      font-size: 18px;
      font-weight: 500;
      text-align: center;
    }

    .user-card {
      display: flex;
      align-items: center;
      padding: 20px;
      background: var(--el-fill-color-light);
      border-radius: 8px;
      margin: 0 auto;
      max-width: 400px;

      .user-avatar {
        margin-right: 16px;
      }

      .user-details {
        flex: 1;

        h4 {
          margin: 0 0 4px 0;
          color: var(--el-text-color-primary);
          font-size: 18px;
          font-weight: 500;
        }

        .user-email {
          margin: 0 0 8px 0;
          color: var(--el-text-color-regular);
          font-size: 14px;
        }

        .user-stats {
          .ml5 {
            margin-left: 5px;
          }

          .more-groups {
            margin-left: 8px;
            color: var(--el-text-color-secondary);
            font-size: 12px;
          }
        }
      }
    }
  }

  .features-section {
    margin: 32px 0;

    h3 {
      margin-bottom: 20px;
      color: var(--el-text-color-primary);
      font-size: 18px;
      font-weight: 500;
    }

    .feature-item {
      text-align: center;
      padding: 16px;

      .feature-icon {
        color: var(--el-color-primary);
        margin-bottom: 12px;
      }

      h5 {
        margin: 0 0 8px 0;
        color: var(--el-text-color-primary);
        font-size: 16px;
        font-weight: 500;
      }

      p {
        margin: 0;
        color: var(--el-text-color-regular);
        font-size: 14px;
        line-height: 1.4;
      }
    }
  }

  .action-buttons {
    margin: 32px 0;

    .el-button {
      margin: 8px;
    }
  }

  .tips-section {
    margin-top: 32px;
    text-align: left;

    .tips-list {
      margin: 0;
      padding-left: 20px;

      li {
        margin-bottom: 4px;
        color: var(--el-text-color-regular);
        font-size: 14px;
        line-height: 1.5;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .oauth-success {
    padding: 10px;

    .success-content {
      padding: 30px 20px;
    }

    .success-title {
      font-size: 24px;
    }

    .user-info-section .user-card {
      flex-direction: column;
      text-align: center;

      .user-avatar {
        margin-right: 0;
        margin-bottom: 12px;
      }
    }

    .features-section {
      .el-col {
        margin-bottom: 16px;
      }
    }

    .action-buttons {
      .el-button {
        width: 100%;
        margin: 4px 0;
      }
    }
  }
}
</style>
