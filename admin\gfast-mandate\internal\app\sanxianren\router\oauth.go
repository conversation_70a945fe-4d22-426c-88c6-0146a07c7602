/*
* @desc:OAuth认证路由配置
* @company:山东傲英网络科技股份有限公司
* @Author: AI Assistant
* @Date: 2025/01/22
 */

package router

import (
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/internal/app/sanxianren/controller"
)

// OAuth OAuth认证路由绑定
func OAuth(group *ghttp.RouterGroup) {
	// 创建OAuth控制器实例
	oauthController := controller.NewOAuthController()

	// OAuth认证相关路由
	group.Bind(
		// API接口
		oauthController.Authorize,     // POST /api/v1/sanxianren/oauth/authorize - 发起OAuth授权
		oauthController.Callback,      // GET/POST /api/v1/sanxianren/oauth/callback - 处理OAuth回调
		oauthController.RefreshToken,  // POST /api/v1/sanxianren/oauth/refresh - 刷新访问令牌
		oauthController.GetUserInfo,   // GET /api/v1/sanxianren/oauth/userinfo - 获取用户信息
		oauthController.ValidateToken, // POST /api/v1/sanxianren/oauth/validate - 验证访问令牌
		oauthController.RevokeToken,   // POST /api/v1/sanxianren/oauth/revoke - 撤销访问令牌
		oauthController.GetSession,    // GET /api/v1/sanxianren/oauth/session - 获取会话信息
		oauthController.DeleteSession, // DELETE /api/v1/sanxianren/oauth/session - 删除会话
	)

	// 浏览器重定向路由（不需要JSON响应）
	group.GET("/oauth/authorize-redirect", oauthController.AuthorizeRedirect)
	group.GET("/oauth/callback-redirect", oauthController.CallbackRedirect)
}

// OAuthPublic OAuth公共路由绑定（无需认证）
func OAuthPublic(group *ghttp.RouterGroup) {
	// 创建OAuth控制器实例
	oauthController := controller.NewOAuthController()

	// 公共OAuth路由（无需登录验证）
	group.Bind(
		oauthController.Callback,  // OAuth回调处理
		oauthController.GetStatus, // 获取OAuth状态
	)

	// 浏览器重定向路由
	group.GET("/oauth/callback-redirect", oauthController.CallbackRedirect)
}
