<template>
  <div class="oauth-status-widget">
    <!-- 简洁模式 -->
    <div v-if="mode === 'simple'" class="simple-mode">
      <el-tag 
        :type="oauthStore.isAuthenticated ? 'success' : 'warning'"
        :size="size"
        :effect="effect"
      >
        <SvgIcon 
          :name="oauthStore.isAuthenticated ? 'ele-Check' : 'ele-Warning'" 
          :size="14" 
          class="mr5"
        />
        {{ oauthStore.statusSummary }}
      </el-tag>
    </div>

    <!-- 卡片模式 -->
    <el-card v-else-if="mode === 'card'" shadow="hover" class="oauth-card">
      <template #header>
        <div class="card-header">
          <span>MediaWiki OAuth</span>
          <el-tag 
            :type="oauthStore.isAuthenticated ? 'success' : 'warning'"
            size="small"
          >
            {{ oauthStore.isAuthenticated ? '已连接' : '未连接' }}
          </el-tag>
        </div>
      </template>

      <div v-loading="oauthStore.loading" class="card-content">
        <div v-if="oauthStore.isAuthenticated" class="authenticated-info">
          <div class="user-avatar">
            <el-avatar :size="40" :src="avatarUrl">
              <SvgIcon name="ele-User" :size="20" />
            </el-avatar>
          </div>
          <div class="user-details">
            <h4>{{ oauthStore.username }}</h4>
            <p class="user-meta">
              {{ oauthStore.realName || oauthStore.email }}
            </p>
            <p class="user-stats">
              编辑 {{ oauthStore.editCount }} 次
            </p>
          </div>
        </div>

        <div v-else class="unauthenticated-info">
          <SvgIcon name="ele-Warning" :size="32" class="warning-icon" />
          <p>未连接MediaWiki账户</p>
        </div>

        <div class="card-actions">
          <el-button 
            v-if="!oauthStore.isAuthenticated"
            type="primary" 
            size="small"
            @click="startAuthentication"
            :loading="oauthStore.loading"
          >
            连接账户
          </el-button>
          <el-button 
            v-else
            size="small"
            @click="goToManagement"
          >
            管理设置
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 详细模式 -->
    <div v-else-if="mode === 'detailed'" class="detailed-mode">
      <el-descriptions :column="1" border size="small">
        <el-descriptions-item label="认证状态">
          <el-tag 
            :type="oauthStore.isAuthenticated ? 'success' : 'warning'"
            size="small"
          >
            {{ oauthStore.statusSummary }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item v-if="oauthStore.isAuthenticated" label="用户名">
          {{ oauthStore.username }}
        </el-descriptions-item>
        <el-descriptions-item v-if="oauthStore.isAuthenticated" label="邮箱">
          {{ oauthStore.email || '-' }}
        </el-descriptions-item>
        <el-descriptions-item v-if="oauthStore.isAuthenticated" label="过期时间">
          <span :class="{ 'text-danger': oauthStore.isTokenExpired }">
            {{ oauthStore.formattedExpiresAt || '-' }}
          </span>
        </el-descriptions-item>
      </el-descriptions>

      <div class="detailed-actions">
        <el-button 
          v-if="!oauthStore.isAuthenticated"
          type="primary" 
          size="small"
          @click="startAuthentication"
          :loading="oauthStore.loading"
        >
          <SvgIcon name="iconfont icon-link" />
          连接MediaWiki
        </el-button>
        <template v-else>
          <el-button 
            size="small"
            @click="refreshStatus"
            :loading="oauthStore.loading"
          >
            <SvgIcon name="ele-Refresh" />
            刷新
          </el-button>
          <el-button 
            size="small"
            @click="goToManagement"
          >
            <SvgIcon name="ele-Setting" />
            管理
          </el-button>
        </template>
      </div>
    </div>

    <!-- 内联模式 -->
    <div v-else class="inline-mode">
      <span class="status-text">
        MediaWiki: 
        <el-tag 
          :type="oauthStore.isAuthenticated ? 'success' : 'warning'"
          size="small"
          :effect="effect"
        >
          {{ oauthStore.isAuthenticated ? '已连接' : '未连接' }}
        </el-tag>
      </span>
      <el-button 
        v-if="showActions"
        type="text" 
        size="small"
        @click="oauthStore.isAuthenticated ? goToManagement() : startAuthentication()"
      >
        {{ oauthStore.isAuthenticated ? '管理' : '连接' }}
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts" name="OAuthStatus">
import { computed } from 'vue';
import { useRouter } from 'vue-router';
import { useOAuthStore } from '/@/stores/oauthStore';
import SvgIcon from '/@/components/svgIcon/index.vue';

// Props
interface Props {
  mode?: 'simple' | 'card' | 'detailed' | 'inline';
  size?: 'large' | 'default' | 'small';
  effect?: 'dark' | 'light' | 'plain';
  showActions?: boolean;
  autoRefresh?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'simple',
  size: 'default',
  effect: 'light',
  showActions: true,
  autoRefresh: false,
});

// Router
const router = useRouter();

// Store
const oauthStore = useOAuthStore();

// 计算属性
const avatarUrl = computed(() => {
  // 可以根据用户信息生成头像URL
  if (oauthStore.userInfo?.email) {
    // 使用Gravatar或其他头像服务
    return `https://www.gravatar.com/avatar/${btoa(oauthStore.userInfo.email)}?d=identicon&s=40`;
  }
  return '';
});

// 方法
const startAuthentication = async () => {
  await oauthStore.startAuthentication();
};

const refreshStatus = async () => {
  await oauthStore.getStatus();
};

const goToManagement = () => {
  router.push('/sanxianren/oauth');
};

// 自动刷新
if (props.autoRefresh) {
  // 可以添加定时刷新逻辑
  setInterval(() => {
    if (!oauthStore.loading) {
      oauthStore.getStatus();
    }
  }, 60000); // 每分钟刷新一次
}
</script>

<style scoped lang="scss">
.oauth-status-widget {
  .simple-mode {
    display: inline-block;
  }

  .oauth-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .card-content {
      min-height: 80px;
    }

    .authenticated-info {
      display: flex;
      align-items: center;
      margin-bottom: 16px;

      .user-avatar {
        margin-right: 12px;
      }

      .user-details {
        flex: 1;

        h4 {
          margin: 0 0 4px 0;
          font-size: 14px;
          font-weight: 500;
        }

        .user-meta {
          margin: 0 0 2px 0;
          color: var(--el-text-color-regular);
          font-size: 12px;
        }

        .user-stats {
          margin: 0;
          color: var(--el-text-color-secondary);
          font-size: 11px;
        }
      }
    }

    .unauthenticated-info {
      text-align: center;
      padding: 20px 0;

      .warning-icon {
        color: var(--el-color-warning);
        margin-bottom: 8px;
      }

      p {
        margin: 0;
        color: var(--el-text-color-regular);
        font-size: 14px;
      }
    }

    .card-actions {
      text-align: center;
    }
  }

  .detailed-mode {
    .detailed-actions {
      margin-top: 16px;
      text-align: center;

      .el-button {
        margin: 0 4px;
      }
    }
  }

  .inline-mode {
    display: flex;
    align-items: center;
    gap: 8px;

    .status-text {
      font-size: 14px;
      color: var(--el-text-color-regular);
    }
  }

  .mr5 {
    margin-right: 5px;
  }

  .text-danger {
    color: var(--el-color-danger);
  }
}
</style>
