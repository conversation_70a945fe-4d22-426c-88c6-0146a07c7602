// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// SxUserCollections is the golang structure for table sx_user_collections.
type SxUserCollections struct {
	Id          uint64      `json:"id"          orm:"id"           description:"收藏ID"`
	UserId      uint64      `json:"userId"      orm:"user_id"      description:"用户ID"`
	ContentType string      `json:"contentType" orm:"content_type" description:"内容类型"`
	ContentId   uint64      `json:"contentId"   orm:"content_id"   description:"内容ID"`
	Title       string      `json:"title"       orm:"title"        description:"内容标题"`
	Summary     string      `json:"summary"     orm:"summary"      description:"内容摘要"`
	CoverImage  string      `json:"coverImage"  orm:"cover_image"  description:"封面图片"`
	CreatedAt   *gtime.Time `json:"createdAt"   orm:"created_at"   description:"创建时间"`
}
