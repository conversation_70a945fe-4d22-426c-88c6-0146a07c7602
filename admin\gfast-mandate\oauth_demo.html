<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>gfast中台 - MediaWiki OAuth认证演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #666;
            font-size: 16px;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 24px;
            margin-bottom: 20px;
        }
        
        .card h3 {
            margin-bottom: 16px;
            color: #333;
            border-bottom: 2px solid #1890ff;
            padding-bottom: 8px;
        }
        
        .status-card {
            text-align: center;
        }
        
        .status-indicator {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 4px;
            margin-bottom: 16px;
            font-weight: 500;
        }
        
        .status-success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        
        .status-warning {
            background-color: #fffbe6;
            border: 1px solid #ffe58f;
            color: #faad14;
        }
        
        .status-error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        
        .btn {
            display: inline-block;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
            margin: 5px;
        }
        
        .btn-primary {
            background-color: #1890ff;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #40a9ff;
        }
        
        .btn-danger {
            background-color: #ff4d4f;
            color: white;
        }
        
        .btn-danger:hover {
            background-color: #ff7875;
        }
        
        .btn-default {
            background-color: #f5f5f5;
            color: #333;
            border: 1px solid #d9d9d9;
        }
        
        .btn-default:hover {
            background-color: #fff;
            border-color: #40a9ff;
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .user-info {
            text-align: left;
            margin: 20px 0;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }
        
        .info-item {
            padding: 12px;
            background-color: #fafafa;
            border-radius: 4px;
            border-left: 3px solid #1890ff;
        }
        
        .info-label {
            font-weight: 500;
            color: #666;
            margin-bottom: 4px;
        }
        
        .info-value {
            color: #333;
        }
        
        .tag {
            display: inline-block;
            padding: 2px 8px;
            background-color: #f0f0f0;
            border-radius: 3px;
            font-size: 12px;
            margin-right: 4px;
            margin-bottom: 4px;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .loading::before {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #1890ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .message {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 4px;
            color: white;
            z-index: 1000;
            animation: slideIn 0.3s ease-out;
        }
        
        .message-success { background-color: #52c41a; }
        .message-error { background-color: #ff4d4f; }
        .message-warning { background-color: #faad14; }
        .message-info { background-color: #1890ff; }
        
        @keyframes slideIn {
            from { transform: translateX(100%); }
            to { transform: translateX(0); }
        }
        
        .logs {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .log-item {
            padding: 8px 12px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .log-action {
            font-weight: 500;
            min-width: 100px;
        }
        
        .log-status {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
            min-width: 60px;
            text-align: center;
        }
        
        .log-status.success {
            background-color: #f6ffed;
            color: #52c41a;
        }
        
        .log-status.failed {
            background-color: #fff2f0;
            color: #ff4d4f;
        }
        
        .log-message {
            flex: 1;
            margin: 0 12px;
        }
        
        .log-time {
            color: #666;
            font-size: 12px;
            min-width: 150px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>gfast中台 - MediaWiki OAuth认证</h1>
            <p>通过OAuth认证连接您的MediaWiki账户，实现统一身份管理</p>
        </div>
        
        <!-- OAuth状态卡片 -->
        <div class="card status-card">
            <h3>认证状态</h3>
            <div id="status-content">
                <div class="loading">正在检查认证状态...</div>
            </div>
        </div>
        
        <!-- 用户信息卡片 -->
        <div class="card" id="user-info-card" style="display: none;">
            <h3>用户信息</h3>
            <div id="user-info-content"></div>
        </div>
        
        <!-- 操作日志卡片 -->
        <div class="card">
            <h3>操作日志</h3>
            <div class="logs" id="logs-container">
                <div style="text-align: center; padding: 20px; color: #666;">
                    暂无日志记录
                </div>
            </div>
        </div>
    </div>
    
    <!-- 引入OAuth管理器 -->
    <script src="frontend_oauth_integration.js"></script>
    
    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 创建OAuth管理器实例
            const oauth = new OAuthManager({
                baseUrl: '/api/v1/sanxianren',
                redirectPath: '/admin/oauth/success'
            });
            
            // 页面元素
            const statusContent = document.getElementById('status-content');
            const userInfoCard = document.getElementById('user-info-card');
            const userInfoContent = document.getElementById('user-info-content');
            const logsContainer = document.getElementById('logs-container');
            
            let logs = [];
            
            // 添加日志
            function addLog(action, status, message) {
                const log = {
                    action,
                    status,
                    message,
                    time: new Date().toLocaleString('zh-CN')
                };
                
                logs.unshift(log);
                if (logs.length > 50) {
                    logs = logs.slice(0, 50);
                }
                
                renderLogs();
            }
            
            // 渲染日志
            function renderLogs() {
                if (logs.length === 0) {
                    logsContainer.innerHTML = '<div style="text-align: center; padding: 20px; color: #666;">暂无日志记录</div>';
                    return;
                }
                
                const logsHtml = logs.map(log => `
                    <div class="log-item">
                        <div class="log-action">${log.action}</div>
                        <div class="log-status ${log.status}">${log.status}</div>
                        <div class="log-message">${log.message}</div>
                        <div class="log-time">${log.time}</div>
                    </div>
                `).join('');
                
                logsContainer.innerHTML = logsHtml;
            }
            
            // 获取OAuth状态
            async function getOAuthStatus() {
                try {
                    const response = await fetch('/api/v1/sanxianren/oauth/status', {
                        headers: {
                            'Authorization': `Bearer ${localStorage.getItem('gfast_token') || ''}`
                        }
                    });
                    
                    const data = await response.json();
                    renderStatus(data);
                    
                    if (data.is_authenticated) {
                        addLog('status_check', 'success', '已认证状态');
                    } else {
                        addLog('status_check', 'success', '未认证状态');
                    }
                } catch (error) {
                    console.error('获取OAuth状态失败:', error);
                    renderStatus({ is_authenticated: false, error: error.message });
                    addLog('status_check', 'failed', '获取状态失败: ' + error.message);
                }
            }
            
            // 渲染状态
            function renderStatus(status) {
                if (status.is_authenticated) {
                    statusContent.innerHTML = `
                        <div class="status-indicator status-success">
                            ✓ 已与MediaWiki账户关联
                        </div>
                        <div>
                            <button class="btn btn-danger" onclick="revokeOAuth()">撤销认证</button>
                            <button class="btn btn-default" onclick="getOAuthStatus()">刷新状态</button>
                        </div>
                    `;
                    
                    // 显示用户信息
                    if (status.user_info) {
                        renderUserInfo(status.user_info);
                        userInfoCard.style.display = 'block';
                    }
                } else {
                    statusContent.innerHTML = `
                        <div class="status-indicator status-warning">
                            ⚠ 未与MediaWiki账户关联
                        </div>
                        <p style="margin-bottom: 16px;">请点击下方按钮进行OAuth认证</p>
                        <div>
                            <button class="btn btn-primary" onclick="startOAuth()">连接MediaWiki账户</button>
                        </div>
                    `;
                    userInfoCard.style.display = 'none';
                }
            }
            
            // 渲染用户信息
            function renderUserInfo(userInfo) {
                const groups = userInfo.groups ? userInfo.groups.map(g => `<span class="tag">${g}</span>`).join('') : '';
                
                userInfoContent.innerHTML = `
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">用户名</div>
                            <div class="info-value">${userInfo.name || '-'}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">真实姓名</div>
                            <div class="info-value">${userInfo.real_name || '-'}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">邮箱</div>
                            <div class="info-value">${userInfo.email || '-'}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">编辑次数</div>
                            <div class="info-value">${userInfo.edit_count || 0}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">用户组</div>
                            <div class="info-value">${groups || '-'}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">注册时间</div>
                            <div class="info-value">${userInfo.registration ? new Date(userInfo.registration).toLocaleString('zh-CN') : '-'}</div>
                        </div>
                    </div>
                `;
            }
            
            // 全局函数
            window.startOAuth = async function() {
                try {
                    addLog('authorize', 'success', '发起OAuth认证');
                    await oauth.startOAuth();
                } catch (error) {
                    addLog('authorize', 'failed', '发起认证失败: ' + error.message);
                }
            };
            
            window.revokeOAuth = async function() {
                if (confirm('确定要撤销MediaWiki OAuth认证吗？')) {
                    try {
                        await oauth.revokeOAuth();
                        addLog('revoke', 'success', 'OAuth认证已撤销');
                        await getOAuthStatus();
                    } catch (error) {
                        addLog('revoke', 'failed', '撤销认证失败: ' + error.message);
                    }
                }
            };
            
            window.getOAuthStatus = getOAuthStatus;
            
            // 监听OAuth事件
            window.addEventListener('oauth:success', function(e) {
                addLog('callback', 'success', 'OAuth认证成功');
                getOAuthStatus();
            });
            
            window.addEventListener('oauth:logout', function() {
                addLog('logout', 'success', 'OAuth认证已注销');
                getOAuthStatus();
            });
            
            // 初始化加载状态
            getOAuthStatus();
        });
    </script>
</body>
</html>
