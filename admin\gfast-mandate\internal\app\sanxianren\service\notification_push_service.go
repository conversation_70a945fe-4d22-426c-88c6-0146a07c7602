/*
* @desc:消息推送服务
* @company:山东傲英网络科技股份有限公司
* @Author: wolfman
* @Date:   2024/12/19
 */

package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/tiger1103/gfast/v3/internal/app/sanxianren/dao"
)

type notificationPushService struct{}

func init() {
	RegisterNotificationPush(&notificationPushService{})
}

// SendNotification 发送通知
func (s *notificationPushService) SendNotification(ctx context.Context, message *PushMessage) error {
	// 1. 保存到数据库
	err := s.saveNotificationToDatabase(ctx, message)
	if err != nil {
		g.Log().Error(ctx, "保存通知到数据库失败:", err)
		return err
	}

	// 2. 实时推送（WebSocket/SSE）
	err = s.sendRealTimePush(ctx, message)
	if err != nil {
		g.Log().Error(ctx, "实时推送失败:", err)
		// 实时推送失败不影响整体流程
	}

	// 3. 移动端推送（如果有推送token）
	err = s.sendMobilePush(ctx, message)
	if err != nil {
		g.Log().Error(ctx, "移动端推送失败:", err)
		// 移动端推送失败不影响整体流程
	}

	return nil
}

// SendBatchNotifications 批量发送通知
func (s *notificationPushService) SendBatchNotifications(ctx context.Context, messages []*PushMessage) error {
	for _, message := range messages {
		err := s.SendNotification(ctx, message)
		if err != nil {
			g.Log().Error(ctx, fmt.Sprintf("发送通知失败 - 用户ID: %d, 错误: %v", message.UserId, err))
		}
	}
	return nil
}

// SendSystemNotification 发送系统通知
func (s *notificationPushService) SendSystemNotification(ctx context.Context, userIds []uint64, title, content string) error {
	var messages []*PushMessage

	for _, userId := range userIds {
		messages = append(messages, &PushMessage{
			UserId:           userId,
			NotificationType: "system",
			Title:            title,
			Content:          content,
			RelatedType:      "",
			RelatedId:        0,
		})
	}

	return s.SendBatchNotifications(ctx, messages)
}

// SendSubmissionNotification 发送投稿相关通知
func (s *notificationPushService) SendSubmissionNotification(ctx context.Context, userId uint64, submissionId uint64, status string) error {
	var title, content string

	switch status {
	case "approved":
		title = "投稿审核通过"
		content = "恭喜！您的投稿已通过审核并发布"
	case "rejected":
		title = "投稿审核未通过"
		content = "很抱歉，您的投稿未通过审核，请查看详细反馈"
	case "pending":
		title = "投稿提交成功"
		content = "您的投稿已提交，我们将尽快进行审核"
	default:
		title = "投稿状态更新"
		content = "您的投稿状态已更新"
	}

	message := &PushMessage{
		UserId:           userId,
		NotificationType: "submission",
		Title:            title,
		Content:          content,
		RelatedType:      "submission",
		RelatedId:        submissionId,
	}

	return s.SendNotification(ctx, message)
}

// SendInteractionNotification 发送互动通知
func (s *notificationPushService) SendInteractionNotification(ctx context.Context, userId uint64, interactionType, contentType string, contentId uint64, fromUserId uint64) error {
	var title, content string

	// 获取操作用户信息
	fromUserName := s.getUserName(ctx, fromUserId)

	switch interactionType {
	case "like":
		title = "收到点赞"
		content = fmt.Sprintf("%s 点赞了您的%s", fromUserName, s.getContentTypeLabel(contentType))
	case "comment":
		title = "收到评论"
		content = fmt.Sprintf("%s 评论了您的%s", fromUserName, s.getContentTypeLabel(contentType))
	case "follow":
		title = "新的关注者"
		content = fmt.Sprintf("%s 关注了您", fromUserName)
	case "mention":
		title = "有人提到了您"
		content = fmt.Sprintf("%s 在%s中提到了您", fromUserName, s.getContentTypeLabel(contentType))
	default:
		title = "新的互动"
		content = fmt.Sprintf("%s 与您进行了互动", fromUserName)
	}

	message := &PushMessage{
		UserId:           userId,
		NotificationType: "interaction",
		Title:            title,
		Content:          content,
		RelatedType:      contentType,
		RelatedId:        contentId,
		Extra: map[string]interface{}{
			"fromUserId":      fromUserId,
			"interactionType": interactionType,
		},
	}

	return s.SendNotification(ctx, message)
}

// ScheduleNotification 定时发送通知
func (s *notificationPushService) ScheduleNotification(ctx context.Context, message *PushMessage, scheduleTime time.Time) error {
	message.ScheduleTime = &scheduleTime

	// 这里可以集成定时任务系统（如：cron、延时队列等）
	// 简化实现：如果是未来时间，先保存到数据库，后续通过定时任务处理

	if scheduleTime.After(time.Now()) {
		// 保存为待发送状态
		return s.saveScheduledNotification(ctx, message)
	} else {
		// 立即发送
		return s.SendNotification(ctx, message)
	}
}

// ProcessScheduledNotifications 处理定时通知（定时任务调用）
func (s *notificationPushService) ProcessScheduledNotifications(ctx context.Context) error {
	// 获取到期的定时通知
	// 这里需要一个专门的定时通知表，简化实现暂时跳过
	g.Log().Info(ctx, "处理定时通知任务")
	return nil
}

// 私有方法

// saveNotificationToDatabase 保存通知到数据库
func (s *notificationPushService) saveNotificationToDatabase(ctx context.Context, message *PushMessage) error {
	_, err := dao.SxUserNotifications.Ctx(ctx).Insert(g.Map{
		"user_id":           message.UserId,
		"notification_type": message.NotificationType,
		"title":             message.Title,
		"content":           message.Content,
		"is_read":           0,
		"related_type":      message.RelatedType,
		"related_id":        message.RelatedId,
		"created_at":        time.Now(),
	})

	return err
}

// sendRealTimePush 发送实时推送
func (s *notificationPushService) sendRealTimePush(ctx context.Context, message *PushMessage) error {
	// 这里可以集成WebSocket或Server-Sent Events
	// 简化实现：记录日志
	g.Log().Info(ctx, fmt.Sprintf("实时推送通知 - 用户ID: %d, 标题: %s", message.UserId, message.Title))

	// 实际实现可以：
	// 1. 通过WebSocket连接池发送消息
	// 2. 通过Redis发布订阅模式
	// 3. 通过消息队列系统

	return nil
}

// sendMobilePush 发送移动端推送
func (s *notificationPushService) sendMobilePush(ctx context.Context, message *PushMessage) error {
	// 这里可以集成第三方推送服务（如：极光推送、友盟推送等）
	// 简化实现：记录日志
	g.Log().Info(ctx, fmt.Sprintf("移动端推送通知 - 用户ID: %d, 标题: %s", message.UserId, message.Title))

	// 实际实现需要：
	// 1. 获取用户的推送token
	// 2. 调用第三方推送API
	// 3. 处理推送结果和失败重试

	return nil
}

// saveScheduledNotification 保存定时通知
func (s *notificationPushService) saveScheduledNotification(ctx context.Context, message *PushMessage) error {
	// 这里需要一个专门的定时通知表
	// 简化实现：记录日志
	g.Log().Info(ctx, fmt.Sprintf("保存定时通知 - 用户ID: %d, 定时: %v", message.UserId, message.ScheduleTime))
	return nil
}

// getUserName 获取用户名称
func (s *notificationPushService) getUserName(ctx context.Context, userId uint64) string {
	// 简化实现：返回默认名称
	// 实际应该从用户表获取真实姓名
	return fmt.Sprintf("用户%d", userId)
}

// getContentTypeLabel 获取内容类型标签
func (s *notificationPushService) getContentTypeLabel(contentType string) string {
	labels := map[string]string{
		"story":   "故事",
		"factory": "工厂",
		"people":  "人物",
	}

	if label, exists := labels[contentType]; exists {
		return label
	}
	return "内容"
}

// GetUserNotificationSettings 获取用户通知设置
func (s *notificationPushService) GetUserNotificationSettings(ctx context.Context, userId uint64) (*NotificationSettings, error) {
	// 这里可以从用户设置表获取通知偏好
	// 简化实现：返回默认设置
	return &NotificationSettings{
		UserId:                   userId,
		SystemNotifications:      true,
		SubmissionNotifications:  true,
		InteractionNotifications: true,
		EmailNotifications:       false,
		PushNotifications:        true,
		QuietHours: QuietHours{
			Enabled:   false,
			StartTime: "22:00",
			EndTime:   "08:00",
		},
	}, nil
}

// NotificationSettings 通知设置
type NotificationSettings struct {
	UserId                   uint64     `json:"userId"`
	SystemNotifications      bool       `json:"systemNotifications"`
	SubmissionNotifications  bool       `json:"submissionNotifications"`
	InteractionNotifications bool       `json:"interactionNotifications"`
	EmailNotifications       bool       `json:"emailNotifications"`
	PushNotifications        bool       `json:"pushNotifications"`
	QuietHours               QuietHours `json:"quietHours"`
}

// QuietHours 免打扰时间
type QuietHours struct {
	Enabled   bool   `json:"enabled"`
	StartTime string `json:"startTime"`
	EndTime   string `json:"endTime"`
}

// UpdateUserNotificationSettings 更新用户通知设置
func (s *notificationPushService) UpdateUserNotificationSettings(ctx context.Context, userId uint64, settings *NotificationSettings) error {
	// 这里应该保存到用户设置表
	// 简化实现：记录日志
	settingsJson, _ := json.Marshal(settings)
	g.Log().Info(ctx, fmt.Sprintf("更新用户通知设置 - 用户ID: %d, 设置: %s", userId, string(settingsJson)))
	return nil
}
