// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/sanxianren/dao/internal"
)

// internalSxUserNotificationsDao is internal type for wrapping internal DAO implements.
type internalSxUserNotificationsDao = *internal.SxUserNotificationsDao

// sxUserNotificationsDao is the data access object for table sx_user_notifications.
// You can define custom methods on it to extend its functionality as you wish.
type sxUserNotificationsDao struct {
	internalSxUserNotificationsDao
}

var (
	// SxUserNotifications is globally public accessible object for table sx_user_notifications operations.
	SxUserNotifications = sxUserNotificationsDao{
		internal.NewSxUserNotificationsDao(),
	}
)

// Fill with you ideas below.
