/*
* @desc:用户浏览历史相关API
* @company:山东傲英网络科技股份有限公司
* @Author: wolfman
* @Date:   2024/12/19
 */

package sanxianren

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
)

// AddHistoryReq 添加浏览历史请求
type AddHistoryReq struct {
	g.Meta      `path:"/user/history/add" tags:"三线人/用户浏览历史" method:"post" summary:"添加浏览历史"`
	ContentType string `json:"contentType" description:"内容类型：story-故事，factory-工厂，people-人物" v:"required#内容类型不能为空"`
	ContentId   uint64 `json:"contentId" description:"内容ID" v:"required#内容ID不能为空"`
	commonApi.Author
}

// AddHistoryRes 添加浏览历史响应
type AddHistoryRes struct {
	g.Meta `mime:"application/json"`
}

// GetHistoryReq 获取浏览历史请求
type GetHistoryReq struct {
	g.Meta      `path:"/user/history" tags:"三线人/用户浏览历史" method:"get" summary:"获取浏览历史"`
	ContentType string `json:"contentType" description:"内容类型：story-故事，factory-工厂，people-人物，all-全部"`
	commonApi.PageReq
	commonApi.Author
}

// GetHistoryRes 获取浏览历史响应
type GetHistoryRes struct {
	g.Meta `mime:"application/json"`
	commonApi.ListRes
	List []HistoryInfo `json:"list" description:"浏览历史列表"`
}

// HistoryInfo 浏览历史信息
type HistoryInfo struct {
	Id          uint64 `json:"id" description:"历史记录ID"`
	ContentType string `json:"contentType" description:"内容类型"`
	ContentId   uint64 `json:"contentId" description:"内容ID"`
	Title       string `json:"title" description:"内容标题"`
	Summary     string `json:"summary" description:"内容摘要"`
	CoverImage  string `json:"coverImage" description:"封面图片"`
	ViewedAt    string `json:"viewedAt" description:"浏览时间"`
}

// ClearHistoryReq 清空浏览历史请求
type ClearHistoryReq struct {
	g.Meta      `path:"/user/history/clear" tags:"三线人/用户浏览历史" method:"delete" summary:"清空浏览历史"`
	ContentType string `json:"contentType" description:"内容类型：story-故事，factory-工厂，people-人物，all-全部"`
	commonApi.Author
}

// ClearHistoryRes 清空浏览历史响应
type ClearHistoryRes struct {
	g.Meta `mime:"application/json"`
}
