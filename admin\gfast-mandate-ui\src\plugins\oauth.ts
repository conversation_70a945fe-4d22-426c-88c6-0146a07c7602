/**
 * OAuth认证插件
 * <AUTHOR> Assistant
 * @date 2025-01-22
 */

import type { App } from 'vue';
import { useOAuthStore } from '/@/stores/oauthStore';
import { OAuthUtils } from '/@/api/sanxianren/oauth';
import router from '/@/router';

/**
 * OAuth插件配置
 */
export interface OAuthPluginOptions {
  // 是否自动初始化
  autoInit?: boolean;
  // 是否处理回调
  handleCallback?: boolean;
  // 回调路由名称
  callbackRouteName?: string;
  // 是否在路由守卫中检查OAuth状态
  checkInRouteGuard?: boolean;
  // 需要OAuth认证的路由
  protectedRoutes?: string[];
}

/**
 * 默认配置
 */
const defaultOptions: OAuthPluginOptions = {
  autoInit: true,
  handleCallback: true,
  callbackRouteName: 'sanxianrenOAuthCallback',
  checkInRouteGuard: false,
  protectedRoutes: [],
};

/**
 * OAuth插件
 */
export default {
  install(app: App, options: OAuthPluginOptions = {}) {
    const opts = { ...defaultOptions, ...options };
    
    // 合并配置到全局属性
    app.config.globalProperties.$oauth = {
      options: opts,
    };

    // 自动初始化
    if (opts.autoInit) {
      app.mixin({
        async created() {
          // 只在根组件中初始化一次
          if (this.$root === this) {
            await initializeOAuth();
          }
        },
      });
    }

    // 处理OAuth回调
    if (opts.handleCallback) {
      setupCallbackHandler(opts.callbackRouteName!);
    }

    // 路由守卫
    if (opts.checkInRouteGuard && opts.protectedRoutes!.length > 0) {
      setupRouteGuard(opts.protectedRoutes!);
    }
  },
};

/**
 * 初始化OAuth
 */
async function initializeOAuth(): Promise<void> {
  try {
    const oauthStore = useOAuthStore();
    
    // 检查URL中是否有OAuth回调参数
    const callbackParams = OAuthUtils.getCallbackParams();
    if (callbackParams) {
      // 如果有回调参数，跳转到回调页面处理
      const currentRoute = router.currentRoute.value;
      if (currentRoute.name !== 'sanxianrenOAuthCallback') {
        router.push({
          name: 'sanxianrenOAuthCallback',
          query: currentRoute.query,
        });
        return;
      }
    }

    // 初始化OAuth状态
    await oauthStore.initialize();
    
    console.log('OAuth插件初始化完成');
  } catch (error) {
    console.error('OAuth插件初始化失败:', error);
  }
}

/**
 * 设置回调处理器
 */
function setupCallbackHandler(callbackRouteName: string): void {
  router.beforeEach((to, from, next) => {
    // 检查是否有OAuth回调参数
    const callbackParams = OAuthUtils.getCallbackParams();
    
    if (callbackParams && to.name !== callbackRouteName) {
      // 重定向到回调页面
      next({
        name: callbackRouteName,
        query: to.query,
      });
    } else {
      next();
    }
  });
}

/**
 * 设置路由守卫
 */
function setupRouteGuard(protectedRoutes: string[]): void {
  router.beforeEach(async (to, from, next) => {
    // 检查是否是受保护的路由
    const isProtectedRoute = protectedRoutes.some(route => 
      to.path.startsWith(route) || to.name === route
    );

    if (isProtectedRoute) {
      const oauthStore = useOAuthStore();
      
      // 确保OAuth状态已初始化
      if (!oauthStore.lastUpdated) {
        await oauthStore.getStatus();
      }

      // 检查是否已认证
      if (!oauthStore.isAuthenticated) {
        // 未认证，重定向到OAuth认证页面
        next({
          name: 'sanxianrenOAuth',
          query: { return_url: to.fullPath },
        });
        return;
      }

      // 检查令牌是否过期
      if (oauthStore.isTokenExpired) {
        // 尝试刷新令牌
        const refreshed = await oauthStore.refreshToken();
        if (!refreshed) {
          // 刷新失败，重定向到认证页面
          next({
            name: 'sanxianrenOAuth',
            query: { return_url: to.fullPath },
          });
          return;
        }
      }
    }

    next();
  });
}

/**
 * OAuth工具函数
 */
export const oauthUtils = {
  /**
   * 检查当前用户是否有OAuth认证
   */
  async isAuthenticated(): Promise<boolean> {
    const oauthStore = useOAuthStore();
    if (!oauthStore.lastUpdated) {
      await oauthStore.getStatus();
    }
    return oauthStore.isAuthenticated && !oauthStore.isTokenExpired;
  },

  /**
   * 发起OAuth认证
   */
  async authenticate(returnUrl?: string): Promise<void> {
    const oauthStore = useOAuthStore();
    await oauthStore.startAuthentication(returnUrl);
  },

  /**
   * 撤销OAuth认证
   */
  async revoke(): Promise<boolean> {
    const oauthStore = useOAuthStore();
    return await oauthStore.revokeAuthentication();
  },

  /**
   * 获取用户信息
   */
  getUserInfo() {
    const oauthStore = useOAuthStore();
    return oauthStore.userInfo;
  },

  /**
   * 检查用户权限
   */
  hasPermission(permission: string): boolean {
    const oauthStore = useOAuthStore();
    return oauthStore.rights.includes(permission);
  },

  /**
   * 检查用户组
   */
  hasGroup(group: string): boolean {
    const oauthStore = useOAuthStore();
    return oauthStore.groups.includes(group);
  },
};

/**
 * OAuth指令
 */
export const oauthDirectives = {
  // v-oauth-required: 需要OAuth认证才能显示
  'oauth-required': {
    mounted(el: HTMLElement, binding: any) {
      const oauthStore = useOAuthStore();
      if (!oauthStore.isAuthenticated) {
        el.style.display = 'none';
      }
    },
    updated(el: HTMLElement, binding: any) {
      const oauthStore = useOAuthStore();
      el.style.display = oauthStore.isAuthenticated ? '' : 'none';
    },
  },

  // v-oauth-permission: 需要特定权限才能显示
  'oauth-permission': {
    mounted(el: HTMLElement, binding: any) {
      const permission = binding.value;
      if (!oauthUtils.hasPermission(permission)) {
        el.style.display = 'none';
      }
    },
    updated(el: HTMLElement, binding: any) {
      const permission = binding.value;
      el.style.display = oauthUtils.hasPermission(permission) ? '' : 'none';
    },
  },

  // v-oauth-group: 需要特定用户组才能显示
  'oauth-group': {
    mounted(el: HTMLElement, binding: any) {
      const group = binding.value;
      if (!oauthUtils.hasGroup(group)) {
        el.style.display = 'none';
      }
    },
    updated(el: HTMLElement, binding: any) {
      const group = binding.value;
      el.style.display = oauthUtils.hasGroup(group) ? '' : 'none';
    },
  },
};

/**
 * OAuth组合式API
 */
export function useOAuth() {
  const oauthStore = useOAuthStore();

  return {
    // 状态
    isAuthenticated: oauthStore.isAuthenticated,
    userInfo: oauthStore.userInfo,
    loading: oauthStore.loading,
    error: oauthStore.error,
    
    // 计算属性
    username: oauthStore.username,
    email: oauthStore.email,
    groups: oauthStore.groups,
    rights: oauthStore.rights,
    canEdit: oauthStore.canEdit,
    canCreatePage: oauthStore.canCreatePage,
    canUpload: oauthStore.canUpload,
    
    // 方法
    authenticate: oauthStore.startAuthentication,
    revoke: oauthStore.revokeAuthentication,
    refresh: oauthStore.refreshToken,
    getStatus: oauthStore.getStatus,
    
    // 工具方法
    hasPermission: oauthUtils.hasPermission,
    hasGroup: oauthUtils.hasGroup,
  };
}
