<template>
  <div class="oauth-management">
    <!-- 页面标题 -->
    <div class="system-user-title">
      <el-card shadow="hover" :body-style="{ paddingBottom: '0' }">
        <div class="system-user-search mb15">
          <el-row :gutter="20">
            <el-col :span="12">
              <h3 class="mb10">
                <SvgIcon name="iconfont icon-link" />
                MediaWiki OAuth认证管理
              </h3>
              <p class="text-muted">管理与MediaWiki的OAuth认证连接，实现统一身份认证</p>
            </el-col>
            <el-col :span="12" class="text-right">
              <el-button type="primary" @click="refreshStatus" :loading="oauthStore.loading">
                <SvgIcon name="ele-Refresh" />
                刷新状态
              </el-button>
            </el-col>
          </el-row>
        </div>
      </el-card>
    </div>

    <!-- OAuth状态卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="24">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>认证状态</span>
              <el-tag 
                :type="oauthStore.isAuthenticated ? 'success' : 'warning'"
                size="large"
              >
                {{ oauthStore.statusSummary }}
              </el-tag>
            </div>
          </template>

          <div v-loading="oauthStore.loading" class="oauth-status-content">
            <!-- 已认证状态 -->
            <div v-if="oauthStore.isAuthenticated" class="authenticated-section">
              <el-alert
                title="已成功连接MediaWiki"
                :description="`您的账户已与MediaWiki用户 ${oauthStore.username} 关联`"
                type="success"
                show-icon
                :closable="false"
                class="mb20"
              />

              <!-- 用户信息展示 -->
              <div class="user-info-section mb20">
                <h4 class="section-title">用户信息</h4>
                <el-descriptions :column="2" border>
                  <el-descriptions-item label="用户名">
                    <el-tag type="primary">{{ oauthStore.username }}</el-tag>
                  </el-descriptions-item>
                  <el-descriptions-item label="真实姓名">
                    {{ oauthStore.realName || '-' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="邮箱">
                    {{ oauthStore.email || '-' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="编辑次数">
                    <el-tag type="info">{{ oauthStore.editCount }}</el-tag>
                  </el-descriptions-item>
                  <el-descriptions-item label="用户组">
                    <el-tag
                      v-for="group in oauthStore.groups"
                      :key="group"
                      size="small"
                      class="mr5 mb5"
                    >
                      {{ group }}
                    </el-tag>
                    <span v-if="oauthStore.groups.length === 0">-</span>
                  </el-descriptions-item>
                  <el-descriptions-item label="会话过期时间">
                    <span :class="{ 'text-danger': oauthStore.isTokenExpired }">
                      {{ oauthStore.formattedExpiresAt || '-' }}
                    </span>
                  </el-descriptions-item>
                </el-descriptions>
              </div>

              <!-- 权限信息 -->
              <div class="permissions-section mb20">
                <h4 class="section-title">权限信息</h4>
                <div class="permissions-grid">
                  <el-card 
                    v-for="permission in permissionCards" 
                    :key="permission.key"
                    :class="['permission-card', { 'has-permission': permission.hasPermission }]"
                    shadow="hover"
                  >
                    <div class="permission-content">
                      <SvgIcon :name="permission.icon" :size="24" />
                      <div class="permission-info">
                        <h5>{{ permission.title }}</h5>
                        <p>{{ permission.description }}</p>
                      </div>
                      <el-tag 
                        :type="permission.hasPermission ? 'success' : 'info'"
                        size="small"
                      >
                        {{ permission.hasPermission ? '有权限' : '无权限' }}
                      </el-tag>
                    </div>
                  </el-card>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="actions-section">
                <el-button 
                  type="warning" 
                  @click="revokeAuthentication"
                  :loading="oauthStore.loading"
                >
                  <SvgIcon name="ele-Close" />
                  撤销认证
                </el-button>
                <el-button 
                  type="primary" 
                  @click="refreshToken"
                  :loading="oauthStore.loading"
                  :disabled="!oauthStore.refreshToken"
                >
                  <SvgIcon name="ele-Refresh" />
                  刷新令牌
                </el-button>
              </div>
            </div>

            <!-- 未认证状态 -->
            <div v-else class="unauthenticated-section">
              <el-alert
                title="未连接MediaWiki"
                description="您还没有与MediaWiki账户关联，请点击下方按钮进行OAuth认证"
                type="warning"
                show-icon
                :closable="false"
                class="mb20"
              />

              <!-- OAuth说明 -->
              <div class="oauth-description mb20">
                <h4 class="section-title">OAuth认证说明</h4>
                <el-row :gutter="20">
                  <el-col :span="8">
                    <div class="feature-item">
                      <SvgIcon name="ele-Lock" :size="32" class="feature-icon" />
                      <h5>安全认证</h5>
                      <p>使用OAuth 2.0标准协议，确保认证过程安全可靠</p>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="feature-item">
                      <SvgIcon name="ele-User" :size="32" class="feature-icon" />
                      <h5>统一身份</h5>
                      <p>实现gfast中台与MediaWiki的统一身份管理</p>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="feature-item">
                      <SvgIcon name="ele-Connection" :size="32" class="feature-icon" />
                      <h5>无缝集成</h5>
                      <p>一次认证，即可在两个系统间无缝切换</p>
                    </div>
                  </el-col>
                </el-row>
              </div>

              <!-- 认证按钮 -->
              <div class="auth-action">
                <el-button 
                  type="primary" 
                  size="large"
                  @click="startAuthentication"
                  :loading="oauthStore.loading"
                >
                  <SvgIcon name="iconfont icon-link" />
                  连接MediaWiki账户
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 错误提示 -->
    <el-alert
      v-if="oauthStore.error"
      :title="oauthStore.error"
      type="error"
      show-icon
      :closable="true"
      @close="oauthStore.clearError"
      class="mb20"
    />

    <!-- OAuth配置信息 -->
    <el-row :gutter="20" v-if="showConfig">
      <el-col :span="24">
        <el-card shadow="hover">
          <template #header>
            <span>OAuth配置信息</span>
          </template>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="客户端ID">
              {{ maskSensitiveInfo(config.client_id) }}
            </el-descriptions-item>
            <el-descriptions-item label="授权URL">
              {{ config.authorization_url }}
            </el-descriptions-item>
            <el-descriptions-item label="回调URL">
              {{ config.redirect_uri }}
            </el-descriptions-item>
            <el-descriptions-item label="权限范围">
              <el-tag
                v-for="scope in config.scopes"
                :key="scope"
                size="small"
                class="mr5"
              >
                {{ scope }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts" name="OAuthManagement">
import { ref, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { useOAuthStore } from '/@/stores/oauthStore';
import OAuthApi, { type OAuthConfigRes } from '/@/api/sanxianren/oauth';
import SvgIcon from '/@/components/svgIcon/index.vue';

// Store
const oauthStore = useOAuthStore();

// 响应式数据
const showConfig = ref(false);
const config = ref<OAuthConfigRes>({
  client_id: '',
  authorization_url: '',
  redirect_uri: '',
  scopes: [],
  message: '',
});

// 计算属性
const permissionCards = computed(() => [
  {
    key: 'edit',
    title: '编辑页面',
    description: '可以编辑现有的MediaWiki页面',
    icon: 'ele-Edit',
    hasPermission: oauthStore.canEdit,
  },
  {
    key: 'create',
    title: '创建页面',
    description: '可以创建新的MediaWiki页面',
    icon: 'ele-Plus',
    hasPermission: oauthStore.canCreatePage,
  },
  {
    key: 'upload',
    title: '上传文件',
    description: '可以上传文件到MediaWiki',
    icon: 'ele-Upload',
    hasPermission: oauthStore.canUpload,
  },
]);

// 方法
const refreshStatus = async () => {
  await oauthStore.getStatus();
  ElMessage.success('状态已刷新');
};

const startAuthentication = async () => {
  await oauthStore.startAuthentication('/sanxianren/oauth');
};

const revokeAuthentication = async () => {
  const success = await oauthStore.revokeAuthentication();
  if (success) {
    await refreshStatus();
  }
};

const refreshToken = async () => {
  await oauthStore.refreshToken();
};

const loadConfig = async () => {
  try {
    const response = await OAuthApi.getConfig();
    config.value = response;
  } catch (error) {
    console.error('加载OAuth配置失败:', error);
  }
};

const maskSensitiveInfo = (str: string): string => {
  if (!str || str.length <= 8) return str;
  return str.substring(0, 4) + '****' + str.substring(str.length - 4);
};

// 生命周期
onMounted(async () => {
  await oauthStore.initialize();
  await loadConfig();
});
</script>

<style scoped lang="scss">
.oauth-management {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .oauth-status-content {
    min-height: 200px;
  }

  .section-title {
    margin-bottom: 16px;
    color: var(--el-text-color-primary);
    font-weight: 500;
  }

  .permissions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 16px;
  }

  .permission-card {
    transition: all 0.3s ease;

    &.has-permission {
      border-color: var(--el-color-success);
    }

    .permission-content {
      display: flex;
      align-items: center;
      gap: 12px;

      .permission-info {
        flex: 1;

        h5 {
          margin: 0 0 4px 0;
          font-weight: 500;
        }

        p {
          margin: 0;
          color: var(--el-text-color-regular);
          font-size: 12px;
        }
      }
    }
  }

  .feature-item {
    text-align: center;
    padding: 20px;

    .feature-icon {
      color: var(--el-color-primary);
      margin-bottom: 12px;
    }

    h5 {
      margin: 0 0 8px 0;
      font-weight: 500;
    }

    p {
      margin: 0;
      color: var(--el-text-color-regular);
      font-size: 14px;
      line-height: 1.5;
    }
  }

  .auth-action {
    text-align: center;
    padding: 20px 0;
  }

  .actions-section {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid var(--el-border-color-light);
  }

  .text-danger {
    color: var(--el-color-danger);
  }

  .text-muted {
    color: var(--el-text-color-regular);
  }

  .mr5 {
    margin-right: 5px;
  }

  .mb5 {
    margin-bottom: 5px;
  }

  .mb10 {
    margin-bottom: 10px;
  }

  .mb15 {
    margin-bottom: 15px;
  }

  .mb20 {
    margin-bottom: 20px;
  }

  .text-right {
    text-align: right;
  }
}
</style>
