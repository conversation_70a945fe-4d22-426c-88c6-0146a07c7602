// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// SxUserSubmissions is the golang structure for table sx_user_submissions.
type SxUserSubmissions struct {
	Id             uint64      `json:"id"             orm:"id"             description:"投稿ID"`
	UserId         uint64      `json:"userId"         orm:"user_id"        description:"投稿用户ID"`
	SubmissionType string      `json:"submissionType" orm:"submission_type" description:"投稿类型"`
	Title          string      `json:"title"          orm:"title"          description:"投稿标题"`
	Content        string      `json:"content"        orm:"content"        description:"投稿内容"`
	MediaFiles     string      `json:"mediaFiles"     orm:"media_files"    description:"媒体文件JSON数组"`
	Tags           string      `json:"tags"           orm:"tags"           description:"标签，逗号分隔"`
	Status         int         `json:"status"         orm:"status"         description:"状态：1-待审核，2-审核中，3-已通过，4-已拒绝，5-已发布"`
	AdminId        uint64      `json:"adminId"        orm:"admin_id"       description:"审核管理员ID"`
	AdminComment   string      `json:"adminComment"   orm:"admin_comment"  description:"审核意见"`
	ReviewedAt     *gtime.Time `json:"reviewedAt"     orm:"reviewed_at"    description:"审核时间"`
	PublishedAt    *gtime.Time `json:"publishedAt"    orm:"published_at"   description:"发布时间"`
	PublishedId    uint64      `json:"publishedId"    orm:"published_id"   description:"发布后的内容ID"`
	IpAddress      string      `json:"ipAddress"      orm:"ip_address"     description:"IP地址"`
	CreatedAt      *gtime.Time `json:"createdAt"      orm:"created_at"     description:"创建时间"`
	UpdatedAt      *gtime.Time `json:"updatedAt"      orm:"updated_at"     description:"更新时间"`
}
