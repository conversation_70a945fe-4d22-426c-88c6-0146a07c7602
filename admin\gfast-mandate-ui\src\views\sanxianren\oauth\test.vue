<template>
  <div class="oauth-test">
    <div class="system-user-title">
      <el-card shadow="hover" :body-style="{ paddingBottom: '0' }">
        <div class="system-user-search mb15">
          <h3 class="mb10">
            <SvgIcon name="ele-Tools" />
            OAuth功能测试
          </h3>
          <p class="text-muted">测试OAuth认证的各项功能，验证集成是否正常工作</p>
        </div>
      </el-card>
    </div>

    <!-- 测试结果概览 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <el-card shadow="hover" class="test-stat-card">
          <div class="stat-content">
            <div class="stat-icon success">
              <SvgIcon name="ele-Check" :size="24" />
            </div>
            <div class="stat-info">
              <h4>{{ passedTests }}</h4>
              <p>通过测试</p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="test-stat-card">
          <div class="stat-content">
            <div class="stat-icon error">
              <SvgIcon name="ele-Close" :size="24" />
            </div>
            <div class="stat-info">
              <h4>{{ failedTests }}</h4>
              <p>失败测试</p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="test-stat-card">
          <div class="stat-content">
            <div class="stat-icon warning">
              <SvgIcon name="ele-Clock" :size="24" />
            </div>
            <div class="stat-info">
              <h4>{{ runningTests }}</h4>
              <p>运行中</p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="test-stat-card">
          <div class="stat-content">
            <div class="stat-icon info">
              <SvgIcon name="ele-List" :size="24" />
            </div>
            <div class="stat-info">
              <h4>{{ totalTests }}</h4>
              <p>总测试数</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 测试控制面板 -->
    <el-card shadow="hover" class="mb20">
      <template #header>
        <div class="card-header">
          <span>测试控制</span>
          <div>
            <el-button type="primary" @click="runAllTests" :loading="isRunning">
              <SvgIcon name="ele-VideoPlay" />
              运行所有测试
            </el-button>
            <el-button @click="clearResults">
              <SvgIcon name="ele-Delete" />
              清除结果
            </el-button>
          </div>
        </div>
      </template>

      <div class="test-controls">
        <el-checkbox-group v-model="selectedTests">
          <el-checkbox 
            v-for="test in testSuites" 
            :key="test.id"
            :label="test.id"
            :disabled="isRunning"
          >
            {{ test.name }}
          </el-checkbox>
        </el-checkbox-group>
      </div>
    </el-card>

    <!-- 测试结果 -->
    <el-card shadow="hover">
      <template #header>
        <span>测试结果</span>
      </template>

      <div class="test-results">
        <div 
          v-for="test in testSuites" 
          :key="test.id"
          class="test-item"
          :class="getTestStatusClass(test)"
        >
          <div class="test-header" @click="toggleTestDetails(test.id)">
            <div class="test-info">
              <SvgIcon 
                :name="getTestIcon(test)" 
                :size="16" 
                class="test-icon"
              />
              <span class="test-name">{{ test.name }}</span>
              <el-tag 
                :type="getTestTagType(test)"
                size="small"
                class="test-status"
              >
                {{ getTestStatusText(test) }}
              </el-tag>
            </div>
            <div class="test-actions">
              <el-button 
                type="text" 
                size="small"
                @click.stop="runSingleTest(test.id)"
                :loading="test.status === 'running'"
                :disabled="isRunning"
              >
                运行
              </el-button>
              <SvgIcon 
                :name="test.expanded ? 'ele-ArrowUp' : 'ele-ArrowDown'" 
                :size="14"
                class="expand-icon"
              />
            </div>
          </div>

          <div v-if="test.expanded" class="test-details">
            <div class="test-description">
              <p>{{ test.description }}</p>
            </div>
            
            <div v-if="test.result" class="test-result">
              <div class="result-header">
                <span>执行结果:</span>
                <span class="result-time">{{ test.result.duration }}ms</span>
              </div>
              
              <div v-if="test.result.success" class="result-success">
                <SvgIcon name="ele-Check" :size="16" />
                <span>{{ test.result.message }}</span>
              </div>
              
              <div v-else class="result-error">
                <SvgIcon name="ele-Close" :size="16" />
                <span>{{ test.result.message }}</span>
                <pre v-if="test.result.error" class="error-details">{{ test.result.error }}</pre>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts" name="OAuthTest">
import { ref, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { useOAuthStore } from '/@/stores/oauthStore';
import OAuthApi from '/@/api/sanxianren/oauth';
import SvgIcon from '/@/components/svgIcon/index.vue';

// Store
const oauthStore = useOAuthStore();

// 响应式数据
const isRunning = ref(false);
const selectedTests = ref<string[]>([]);

// 测试套件定义
const testSuites = ref([
  {
    id: 'config',
    name: 'OAuth配置测试',
    description: '测试OAuth配置是否正确加载',
    status: 'pending',
    expanded: false,
    result: null,
  },
  {
    id: 'status',
    name: 'OAuth状态测试',
    description: '测试获取OAuth认证状态',
    status: 'pending',
    expanded: false,
    result: null,
  },
  {
    id: 'store',
    name: 'Store状态测试',
    description: '测试OAuth Store的状态管理',
    status: 'pending',
    expanded: false,
    result: null,
  },
  {
    id: 'api',
    name: 'API接口测试',
    description: '测试OAuth相关API接口',
    status: 'pending',
    expanded: false,
    result: null,
  },
  {
    id: 'validation',
    name: '令牌验证测试',
    description: '测试访问令牌验证功能',
    status: 'pending',
    expanded: false,
    result: null,
  },
]);

// 计算属性
const totalTests = computed(() => testSuites.value.length);
const passedTests = computed(() => testSuites.value.filter(t => t.status === 'passed').length);
const failedTests = computed(() => testSuites.value.filter(t => t.status === 'failed').length);
const runningTests = computed(() => testSuites.value.filter(t => t.status === 'running').length);

// 方法
const getTestStatusClass = (test: any) => {
  return `test-${test.status}`;
};

const getTestIcon = (test: any) => {
  switch (test.status) {
    case 'passed': return 'ele-Check';
    case 'failed': return 'ele-Close';
    case 'running': return 'ele-Loading';
    default: return 'ele-Clock';
  }
};

const getTestTagType = (test: any) => {
  switch (test.status) {
    case 'passed': return 'success';
    case 'failed': return 'danger';
    case 'running': return 'warning';
    default: return 'info';
  }
};

const getTestStatusText = (test: any) => {
  switch (test.status) {
    case 'passed': return '通过';
    case 'failed': return '失败';
    case 'running': return '运行中';
    default: return '待运行';
  }
};

const toggleTestDetails = (testId: string) => {
  const test = testSuites.value.find(t => t.id === testId);
  if (test) {
    test.expanded = !test.expanded;
  }
};

// 测试执行函数
const runConfigTest = async () => {
  const startTime = Date.now();
  try {
    const config = await OAuthApi.getConfig();
    const duration = Date.now() - startTime;
    
    if (config.client_id && config.authorization_url) {
      return {
        success: true,
        message: '配置加载成功',
        duration,
        data: config,
      };
    } else {
      return {
        success: false,
        message: '配置不完整',
        duration,
        error: '缺少必要的配置项',
      };
    }
  } catch (error: any) {
    return {
      success: false,
      message: '配置加载失败',
      duration: Date.now() - startTime,
      error: error.message,
    };
  }
};

const runStatusTest = async () => {
  const startTime = Date.now();
  try {
    const status = await OAuthApi.getStatus();
    const duration = Date.now() - startTime;
    
    return {
      success: true,
      message: `状态获取成功，认证状态: ${status.is_authenticated ? '已认证' : '未认证'}`,
      duration,
      data: status,
    };
  } catch (error: any) {
    return {
      success: false,
      message: '状态获取失败',
      duration: Date.now() - startTime,
      error: error.message,
    };
  }
};

const runStoreTest = async () => {
  const startTime = Date.now();
  try {
    await oauthStore.getStatus();
    const duration = Date.now() - startTime;
    
    return {
      success: true,
      message: `Store状态更新成功，认证状态: ${oauthStore.statusSummary}`,
      duration,
      data: {
        isAuthenticated: oauthStore.isAuthenticated,
        username: oauthStore.username,
        loading: oauthStore.loading,
      },
    };
  } catch (error: any) {
    return {
      success: false,
      message: 'Store状态测试失败',
      duration: Date.now() - startTime,
      error: error.message,
    };
  }
};

const runApiTest = async () => {
  const startTime = Date.now();
  try {
    // 测试多个API接口
    const config = await OAuthApi.getConfig();
    const status = await OAuthApi.getStatus();
    const duration = Date.now() - startTime;
    
    return {
      success: true,
      message: 'API接口测试通过',
      duration,
      data: { config, status },
    };
  } catch (error: any) {
    return {
      success: false,
      message: 'API接口测试失败',
      duration: Date.now() - startTime,
      error: error.message,
    };
  }
};

const runValidationTest = async () => {
  const startTime = Date.now();
  try {
    if (oauthStore.accessToken) {
      const result = await OAuthApi.validateToken({
        access_token: oauthStore.accessToken,
      });
      const duration = Date.now() - startTime;
      
      return {
        success: true,
        message: `令牌验证完成，有效性: ${result.valid ? '有效' : '无效'}`,
        duration,
        data: result,
      };
    } else {
      return {
        success: true,
        message: '无访问令牌，跳过验证测试',
        duration: Date.now() - startTime,
      };
    }
  } catch (error: any) {
    return {
      success: false,
      message: '令牌验证失败',
      duration: Date.now() - startTime,
      error: error.message,
    };
  }
};

// 测试执行器映射
const testExecutors: Record<string, () => Promise<any>> = {
  config: runConfigTest,
  status: runStatusTest,
  store: runStoreTest,
  api: runApiTest,
  validation: runValidationTest,
};

const runSingleTest = async (testId: string) => {
  const test = testSuites.value.find(t => t.id === testId);
  if (!test || !testExecutors[testId]) return;
  
  test.status = 'running';
  test.result = null;
  
  try {
    const result = await testExecutors[testId]();
    test.result = result;
    test.status = result.success ? 'passed' : 'failed';
    
    if (result.success) {
      ElMessage.success(`${test.name} 测试通过`);
    } else {
      ElMessage.error(`${test.name} 测试失败`);
    }
  } catch (error: any) {
    test.result = {
      success: false,
      message: '测试执行异常',
      error: error.message,
      duration: 0,
    };
    test.status = 'failed';
    ElMessage.error(`${test.name} 测试异常`);
  }
};

const runAllTests = async () => {
  isRunning.value = true;
  
  try {
    const testsToRun = selectedTests.value.length > 0 
      ? testSuites.value.filter(t => selectedTests.value.includes(t.id))
      : testSuites.value;
    
    for (const test of testsToRun) {
      await runSingleTest(test.id);
      // 添加小延迟，避免请求过于频繁
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    const passed = testsToRun.filter(t => t.status === 'passed').length;
    const total = testsToRun.length;
    
    if (passed === total) {
      ElMessage.success(`所有测试通过 (${passed}/${total})`);
    } else {
      ElMessage.warning(`测试完成 (${passed}/${total} 通过)`);
    }
  } finally {
    isRunning.value = false;
  }
};

const clearResults = () => {
  testSuites.value.forEach(test => {
    test.status = 'pending';
    test.result = null;
    test.expanded = false;
  });
  ElMessage.info('测试结果已清除');
};

// 生命周期
onMounted(() => {
  // 默认选择所有测试
  selectedTests.value = testSuites.value.map(t => t.id);
});
</script>

<style scoped lang="scss">
.oauth-test {
  .test-stat-card {
    .stat-content {
      display: flex;
      align-items: center;
      gap: 12px;

      .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;

        &.success {
          background: var(--el-color-success-light-9);
          color: var(--el-color-success);
        }

        &.error {
          background: var(--el-color-danger-light-9);
          color: var(--el-color-danger);
        }

        &.warning {
          background: var(--el-color-warning-light-9);
          color: var(--el-color-warning);
        }

        &.info {
          background: var(--el-color-info-light-9);
          color: var(--el-color-info);
        }
      }

      .stat-info {
        h4 {
          margin: 0 0 4px 0;
          font-size: 24px;
          font-weight: 600;
          color: var(--el-text-color-primary);
        }

        p {
          margin: 0;
          color: var(--el-text-color-regular);
          font-size: 14px;
        }
      }
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .test-controls {
    .el-checkbox-group {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
    }
  }

  .test-results {
    .test-item {
      border: 1px solid var(--el-border-color-light);
      border-radius: 6px;
      margin-bottom: 12px;
      overflow: hidden;

      &.test-passed {
        border-color: var(--el-color-success);
      }

      &.test-failed {
        border-color: var(--el-color-danger);
      }

      &.test-running {
        border-color: var(--el-color-warning);
      }

      .test-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        cursor: pointer;
        background: var(--el-fill-color-lighter);

        &:hover {
          background: var(--el-fill-color-light);
        }

        .test-info {
          display: flex;
          align-items: center;
          gap: 8px;

          .test-name {
            font-weight: 500;
          }
        }

        .test-actions {
          display: flex;
          align-items: center;
          gap: 8px;
        }
      }

      .test-details {
        padding: 16px;
        border-top: 1px solid var(--el-border-color-lighter);

        .test-description {
          margin-bottom: 12px;

          p {
            margin: 0;
            color: var(--el-text-color-regular);
          }
        }

        .test-result {
          .result-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-weight: 500;

            .result-time {
              color: var(--el-text-color-secondary);
              font-size: 12px;
            }
          }

          .result-success,
          .result-error {
            display: flex;
            align-items: flex-start;
            gap: 8px;
            padding: 8px;
            border-radius: 4px;
          }

          .result-success {
            background: var(--el-color-success-light-9);
            color: var(--el-color-success);
          }

          .result-error {
            background: var(--el-color-danger-light-9);
            color: var(--el-color-danger);

            .error-details {
              margin-top: 8px;
              padding: 8px;
              background: var(--el-fill-color-light);
              border-radius: 4px;
              font-size: 12px;
              color: var(--el-text-color-regular);
              white-space: pre-wrap;
              max-height: 200px;
              overflow-y: auto;
            }
          }
        }
      }
    }
  }

  .mb10 {
    margin-bottom: 10px;
  }

  .mb15 {
    margin-bottom: 15px;
  }

  .mb20 {
    margin-bottom: 20px;
  }

  .text-muted {
    color: var(--el-text-color-regular);
  }
}
</style>
