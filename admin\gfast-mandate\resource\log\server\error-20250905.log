2025-09-05T00:02:39.133+08:00 {58c01d1aca1f6218a26e7c79fd3cced3} 200 "POST http localhost:8808 /api/v1/sanxianren/story/list HTTP/1.1" 1.637, 127.0.0.1, "http://localhost:5173/", "Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********"
2025-09-05T00:02:39.174+08:00 {c4b19f7bca1f6218a36e7c79786e630a} 200 "POST http localhost:8808 /api/v1/sanxianren/story/list HTTP/1.1" 0.029, 127.0.0.1, "http://localhost:5173/", "Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHT<PERSON>, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********"
2025-09-05T00:04:18.937+08:00 {786d61a1e11f6218a46e7c794f4a01f6} 200 "POST http localhost:8808 /api/v1/sanxianren/story/list HTTP/1.1" 0.374, 127.0.0.1, "http://localhost:5173/?ide_webview_request_time=1757000922409", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Trae/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36"
2025-09-05T00:04:21.914+08:00 {988cda5be21f6218a76e7c797c55189a} 200 "POST http localhost:8808 /api/v1/sanxianren/story/list HTTP/1.1" 0.222, 127.0.0.1, "http://localhost:5173/?ide_webview_request_time=1757000922409", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Trae/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36"
2025-09-05T00:04:36.804+08:00 {3c1bd5dfe51f6218aa6e7c7957604896} 200 "POST http localhost:8808 /api/v1/sanxianren/story/list HTTP/1.1" 0.012, 127.0.0.1, "http://localhost:5173/?ide_webview_request_time=1757000922409", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Trae/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36"
2025-09-05T00:04:37.073+08:00 {64c27eeee51f6218ad6e7c7903f5ca30} 200 "POST http localhost:8808 /api/v1/sanxianren/story/list HTTP/1.1" 0.036, 127.0.0.1, "http://localhost:5173/?ide_webview_request_time=1757000922409", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Trae/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36"
2025-09-05T00:04:42.586+08:00 {30b36233e71f6218b06e7c79beea7cb3} 200 "GET http localhost:8808 /api/v1/sanxianren/factoryPeople/get?id=3 HTTP/1.1" 98954.200, 127.0.0.1, "http://localhost:5173/?ide_webview_request_time=1757000922409", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Trae/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36", 1003, "获取信息失败", ""
Stack:
1. 获取信息失败
   1).  github.com/tiger1103/gfast/v3/library/liberr.NewCode
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/library/liberr/err.go:36
   2).  github.com/tiger1103/gfast/v3/library/liberr.ErrIsNil
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/library/liberr/err.go:22
   3).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/factoryPeople.(*sFactoryPeople).GetById.func1
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/factoryPeople/factory_people.go:104
   4).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/factoryPeople.(*sFactoryPeople).GetById
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/factoryPeople/factory_people.go:102
   5).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/controller.(*factoryPeopleController).Get
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/controller/factory_people.go:29
   6).  github.com/tiger1103/gfast/v3/internal/app/system/logic/middleware.(*sMiddleware).Auth
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/system/logic/middleware/middleware.go:77
   7).  github.com/tiger1103/gfast-token/gftoken.(*GfToken).authMiddleware
        C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/middleware.go:21
   8).  github.com/tiger1103/gfast/v3/internal/app/system/logic/middleware.(*sMiddleware).Ctx
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/system/logic/middleware/middleware.go:62
   9).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCharsetUTF8
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:39
   10). github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:30

2025-09-05T00:04:42.591+08:00 {30b36233e71f6218b06e7c79beea7cb3} 200 "GET http localhost:8808 /api/v1/sanxianren/factoryPeople/get?id=3 HTTP/1.1" 0.098, 127.0.0.1, "http://localhost:5173/?ide_webview_request_time=1757000922409", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Trae/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36"
2025-09-05T00:58:45.384+08:00 {cc66d83bda22621873ee1a4bd9294e76} 200 "POST http localhost:8808 /api/v1/sanxianren/story/stats HTTP/1.1" 54255.300, ::1, "", "curl/8.13.0", 1003, "查询统计失败", ""
Stack:
1. 查询统计失败
   1).  github.com/tiger1103/gfast/v3/library/liberr.NewCode
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/library/liberr/err.go:36
   2).  github.com/tiger1103/gfast/v3/library/liberr.ErrIsNil
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/library/liberr/err.go:22
   3).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/submissions.(*sSubmissions).SubmissionStats.func1
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/submissions/submissions.go:755
   4).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/submissions.(*sSubmissions).SubmissionStats
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/submissions/submissions.go:741
   5).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/controller.(*storyController).Stats
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/controller/story.go:239
   6).  github.com/tiger1103/gfast/v3/internal/app/system/logic/middleware.(*sMiddleware).Ctx
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/system/logic/middleware/middleware.go:47
   7).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCharsetUTF8
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:39
   8).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:30

2025-09-05T00:58:45.413+08:00 {cc66d83bda22621873ee1a4bd9294e76} 200 "POST http localhost:8808 /api/v1/sanxianren/story/stats HTTP/1.1" 0.054, ::1, "", "curl/8.13.0"
2025-09-05T01:26:22.204+08:00 {24f620015c24621874ee1a4bd4009340} 404 "POST http localhost:8808 /api/v1/sanxianren/user/score/list HTTP/1.1" 0.003, ::1, "", "curl/8.13.0"
2025-09-05T13:47:18.439+08:00 {681f98c6ca4c6218d35e6558357dc857} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/send-sms-code HTTP/1.1" 3073.400, 127.0.0.1, "", "Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********", -1, "", ""
Stack:
1. 发送过于频繁，请稍后再试
   1).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/userAuth.(*sUserAuth).SendSmsCode
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:157
   2).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/controller.(*userAuthController).SendSmsCode
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/controller/user_auth.go:67
   3).  github.com/tiger1103/gfast/v3/internal/app/system/logic/middleware.(*sMiddleware).Ctx
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/system/logic/middleware/middleware.go:47
   4).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCharsetUTF8
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:39
   5).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:30

2025-09-05T13:47:18.447+08:00 {681f98c6ca4c6218d35e6558357dc857} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/send-sms-code HTTP/1.1" 0.003, 127.0.0.1, "", "Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********"
2025-09-05T13:47:28.523+08:00 {80636720cd4c6218d45e65585bf1d7c4} 404 "OPTIONS http 127.0.0.1:8808 /api/v1/sanxianren/user/sms-login HTTP/1.1" 0.000, 127.0.0.1, "", "Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********"
2025-09-05T13:49:58.883+08:00 {ecb58322f04c6218d55e6558b9510e3b} 200 "OPTIONS http 127.0.0.1:8808 /api/v1/sanxianren/user/send-sms-code HTTP/1.1" 0.000, 127.0.0.1, "", "Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********"
2025-09-05T13:49:58.887+08:00 {dc47c522f04c6218d65e655858968053} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/send-sms-code HTTP/1.1" 0.000, 127.0.0.1, "", "Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********"
2025-09-05T13:50:09.306+08:00 {148ecd8ff24c6218d75e6558b9ef5830} 404 "OPTIONS http 127.0.0.1:8808 /api/v1/sanxianren/user/sms-login HTTP/1.1" 0.000, 127.0.0.1, "", "Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********"
2025-09-05T14:05:05.340+08:00 {44140d2dc34d6218db16db08698ddc91} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/sms-login HTTP/1.1" 39503.700, 127.0.0.1, "http://localhost:3000/", "Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********", -1, "", ""
Stack:
1. 生成令牌失败
   1).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/userAuth.(*sUserAuth).SmsLogin
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:204
2. 生成token失败
   1).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/userAuth.(*sUserAuth).generateToken
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:327
3. key length must more than 32
   1).  github.com/tiger1103/gfast-token/gftoken.(*GfToken).GenerateToken
        C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:56
   2).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/userAuth.(*sUserAuth).generateToken
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:325
   3).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/userAuth.(*sUserAuth).SmsLogin
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:202
   4).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/controller.(*userAuthController).SmsLogin
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/controller/user_auth.go:67
   5).  github.com/tiger1103/gfast/v3/internal/app/system/logic/middleware.(*sMiddleware).Ctx
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/system/logic/middleware/middleware.go:47
   6).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCharsetUTF8
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:48
   7).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:39

2025-09-05T14:05:05.344+08:00 {44140d2dc34d6218db16db08698ddc91} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/sms-login HTTP/1.1" 0.039, 127.0.0.1, "http://localhost:3000/", "Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********"
2025-09-05T14:05:20.163+08:00 {d026a0a2c64d6218dc16db08879ce02f} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/sms-login HTTP/1.1" 8215.800, 127.0.0.1, "http://localhost:3000/", "Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********", -1, "", ""
Stack:
1. 生成令牌失败
   1).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/userAuth.(*sUserAuth).SmsLogin
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:204
2. 生成token失败
   1).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/userAuth.(*sUserAuth).generateToken
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:327
3. key length must more than 32
   1).  github.com/tiger1103/gfast-token/gftoken.(*GfToken).GenerateToken
        C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:56
   2).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/userAuth.(*sUserAuth).generateToken
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:325
   3).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/userAuth.(*sUserAuth).SmsLogin
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:202
   4).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/controller.(*userAuthController).SmsLogin
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/controller/user_auth.go:67
   5).  github.com/tiger1103/gfast/v3/internal/app/system/logic/middleware.(*sMiddleware).Ctx
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/system/logic/middleware/middleware.go:47
   6).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCharsetUTF8
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:48
   7).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:39

2025-09-05T14:05:20.205+08:00 {d026a0a2c64d6218dc16db08879ce02f} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/sms-login HTTP/1.1" 0.008, 127.0.0.1, "http://localhost:3000/", "Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********"
2025-09-05T14:05:40.879+08:00 {c05fea75cb4d6218dd16db08d871fef7} 200 "OPTIONS http 127.0.0.1:8808 /api/v1/sanxianren/user/login HTTP/1.1" 0.000, 127.0.0.1, "http://localhost:3000/", "Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********"
2025-09-05T14:05:40.898+08:00 {80d31076cb4d6218de16db0872cb8a70} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/login HTTP/1.1" 15817.500, 127.0.0.1, "http://localhost:3000/", "Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********", -1, "", ""
Stack:
1. 生成令牌失败
   1).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/userAuth.(*sUserAuth).Login
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:124
2. 生成token失败
   1).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/userAuth.(*sUserAuth).generateToken
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:327
3. key length must more than 32
   1).  github.com/tiger1103/gfast-token/gftoken.(*GfToken).GenerateToken
        C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:56
   2).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/userAuth.(*sUserAuth).generateToken
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:325
   3).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/userAuth.(*sUserAuth).Login
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:122
   4).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/controller.(*userAuthController).Login
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/controller/user_auth.go:52
   5).  github.com/tiger1103/gfast/v3/internal/app/system/logic/middleware.(*sMiddleware).Ctx
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/system/logic/middleware/middleware.go:47
   6).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCharsetUTF8
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:48
   7).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:39

2025-09-05T14:05:40.902+08:00 {80d31076cb4d6218de16db0872cb8a70} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/login HTTP/1.1" 0.015, 127.0.0.1, "http://localhost:3000/", "Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********"
2025-09-05T14:05:58.088+08:00 {b0db8277cf4d6218df16db08b80fb0cc} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/send-sms-code HTTP/1.1" 2000.600, 127.0.0.1, "", "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.17763.7671", -1, "", ""
Stack:
1. 发送过于频繁，请稍后再试
   1).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/userAuth.(*sUserAuth).SendSmsCode
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:239
   2).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/controller.(*userAuthController).SendSmsCode
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/controller/user_auth.go:82
   3).  github.com/tiger1103/gfast/v3/internal/app/system/logic/middleware.(*sMiddleware).Ctx
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/system/logic/middleware/middleware.go:47
   4).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCharsetUTF8
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:48
   5).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:39

2025-09-05T14:05:58.100+08:00 {b0db8277cf4d6218df16db08b80fb0cc} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/send-sms-code HTTP/1.1" 0.002, 127.0.0.1, "", "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.17763.7671"
2025-09-05T14:05:58.229+08:00 {98300180cf4d6218e016db08ffe8f34f} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/sms-login HTTP/1.1" 593.200, 127.0.0.1, "", "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.17763.7671", 51, "Validation Failed", ""
Stack:
The SmsCode field is required
2025-09-05T14:05:58.233+08:00 {98300180cf4d6218e016db08ffe8f34f} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/sms-login HTTP/1.1" 0.000, 127.0.0.1, "", "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.17763.7671"
2025-09-05T14:06:21.741+08:00 {e03819f9d44d6218e116db08e1f5438f} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/login HTTP/1.1" 6001.300, 127.0.0.1, "", "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.17763.7671", -1, "", ""
Stack:
1. 生成令牌失败
   1).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/userAuth.(*sUserAuth).Login
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:124
2. 生成token失败
   1).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/userAuth.(*sUserAuth).generateToken
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:327
3. key length must more than 32
   1).  github.com/tiger1103/gfast-token/gftoken.(*GfToken).GenerateToken
        C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:56
   2).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/userAuth.(*sUserAuth).generateToken
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:325
   3).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/userAuth.(*sUserAuth).Login
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:122
   4).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/controller.(*userAuthController).Login
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/controller/user_auth.go:52
   5).  github.com/tiger1103/gfast/v3/internal/app/system/logic/middleware.(*sMiddleware).Ctx
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/system/logic/middleware/middleware.go:47
   6).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCharsetUTF8
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:48
   7).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:39

2025-09-05T14:06:21.751+08:00 {e03819f9d44d6218e116db08e1f5438f} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/login HTTP/1.1" 0.006, 127.0.0.1, "", "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.17763.7671"
2025-09-05T14:06:47.887+08:00 {70afdc0fdb4d6218e216db0841d11715} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/register HTTP/1.1" 998.900, 127.0.0.1, "", "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.17763.7671", 51, "Validation Failed", ""
Stack:
The SmsCode field is required
2025-09-05T14:06:47.898+08:00 {70afdc0fdb4d6218e216db0841d11715} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/register HTTP/1.1" 0.000, 127.0.0.1, "", "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.17763.7671"
2025-09-05T14:06:48.084+08:00 {d0958f11db4d6218e316db08b208cb82} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/login HTTP/1.1" 168704.400, 127.0.0.1, "", "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.17763.7671", -1, "", ""
Stack:
1. 生成令牌失败
   1).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/userAuth.(*sUserAuth).Login
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:124
2. 生成token失败
   1).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/userAuth.(*sUserAuth).generateToken
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:327
3. key length must more than 32
   1).  github.com/tiger1103/gfast-token/gftoken.(*GfToken).GenerateToken
        C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:56
   2).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/userAuth.(*sUserAuth).generateToken
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:325
   3).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/userAuth.(*sUserAuth).Login
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:122
   4).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/controller.(*userAuthController).Login
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/controller/user_auth.go:52
   5).  github.com/tiger1103/gfast/v3/internal/app/system/logic/middleware.(*sMiddleware).Ctx
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/system/logic/middleware/middleware.go:47
   6).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCharsetUTF8
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:48
   7).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:39

2025-09-05T14:06:48.387+08:00 {d0958f11db4d6218e316db08b208cb82} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/login HTTP/1.1" 0.168, 127.0.0.1, "", "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.17763.7671"
2025-09-05T14:09:38.815+08:00 {c8336aa6024e621852acbc2b9db502e6} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/sms-login HTTP/1.1" 934500.800, 127.0.0.1, "", "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.17763.7671", -1, "", ""
Stack:
1. 生成令牌失败
   1).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/userAuth.(*sUserAuth).SmsLogin
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:204
2. 生成token失败
   1).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/userAuth.(*sUserAuth).generateToken
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:332
3. key length must more than 32
   1).  github.com/tiger1103/gfast-token/gftoken.(*GfToken).GenerateToken
        C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:56
   2).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/userAuth.(*sUserAuth).generateToken
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:330
   3).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/userAuth.(*sUserAuth).SmsLogin
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:202
   4).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/controller.(*userAuthController).SmsLogin
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/controller/user_auth.go:67
   5).  github.com/tiger1103/gfast/v3/internal/app/system/logic/middleware.(*sMiddleware).Ctx
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/system/logic/middleware/middleware.go:47
   6).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCharsetUTF8
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:48
   7).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:39

2025-09-05T14:29:15.540+08:00 {24e273d4144f6218844f605f4531917b} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/login HTTP/1.1" 21000.700, 127.0.0.1, "http://localhost:3000/", "Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********", -1, "", ""
Stack:
1. 密码错误
   1).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/userAuth.(*sUserAuth).Login
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:113
   2).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/controller.(*userAuthController).Login
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/controller/user_auth.go:52
   3).  github.com/tiger1103/gfast/v3/internal/app/system/logic/middleware.(*sMiddleware).Ctx
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/system/logic/middleware/middleware.go:47
   4).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCharsetUTF8
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:48
   5).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:39

2025-09-05T14:29:15.558+08:00 {24e273d4144f6218844f605f4531917b} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/login HTTP/1.1" 0.021, 127.0.0.1, "http://localhost:3000/", "Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********"
2025-09-05T14:29:27.194+08:00 {98ceb28c174f6218854f605f76cca677} 404 "OPTIONS http 127.0.0.1:8808 /api/v1/sanxianren/factory-people HTTP/1.1" 0.004, 127.0.0.1, "http://localhost:3000/", "Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********"
2025-09-05T14:38:33.626+08:00 {50d037c4964f6218864f605f15128224} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/login HTTP/1.1" 43002.400, 127.0.0.1, "", "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.17763.7671", -1, "", ""
Stack:
1. 密码错误
   1).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/userAuth.(*sUserAuth).Login
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:113
   2).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/controller.(*userAuthController).Login
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/controller/user_auth.go:52
   3).  github.com/tiger1103/gfast/v3/internal/app/system/logic/middleware.(*sMiddleware).Ctx
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/system/logic/middleware/middleware.go:47
   4).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCharsetUTF8
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:48
   5).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:39

2025-09-05T14:38:33.870+08:00 {50d037c4964f6218864f605f15128224} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/login HTTP/1.1" 0.043, 127.0.0.1, "", "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.17763.7671"
2025-09-05T14:40:10.444+08:00 {20ca164bad4f6218874f605f356da8c2} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/sms-login HTTP/1.1" 0.109, 127.0.0.1, "", "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.17763.7671"
2025-09-05T14:40:12.748+08:00 {1cd261d8ad4f6218894f605f220e06f0} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/login HTTP/1.1" 42496.600, 127.0.0.1, "", "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.17763.7671", -1, "", ""
Stack:
1. 密码错误
   1).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/userAuth.(*sUserAuth).Login
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:113
   2).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/controller.(*userAuthController).Login
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/controller/user_auth.go:52
   3).  github.com/tiger1103/gfast/v3/internal/app/system/logic/middleware.(*sMiddleware).Ctx
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/system/logic/middleware/middleware.go:47
   4).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCharsetUTF8
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:48
   5).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:39

2025-09-05T14:40:12.794+08:00 {1cd261d8ad4f6218894f605f220e06f0} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/login HTTP/1.1" 0.042, 127.0.0.1, "", "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.17763.7671"
2025-09-05T14:40:48.194+08:00 {58f82a18b64f62188a4f605f93fb7a89} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/sms-login HTTP/1.1" 0.060, 127.0.0.1, "", "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.17763.7671"
2025-09-05T14:40:49.252+08:00 {245fac59b64f62188c4f605f0c5075df} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/login HTTP/1.1" 13000.100, 127.0.0.1, "", "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.17763.7671", -1, "", ""
Stack:
1. 密码错误
   1).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/userAuth.(*sUserAuth).Login
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:113
   2).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/controller.(*userAuthController).Login
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/controller/user_auth.go:52
   3).  github.com/tiger1103/gfast/v3/internal/app/system/logic/middleware.(*sMiddleware).Ctx
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/system/logic/middleware/middleware.go:47
   4).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCharsetUTF8
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:48
   5).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:39

2025-09-05T14:40:49.317+08:00 {245fac59b64f62188c4f605f0c5075df} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/login HTTP/1.1" 0.013, 127.0.0.1, "", "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.17763.7671"
2025-09-05T14:42:53.634+08:00 {4c262750d34f6218ec6bef3659a1aaca} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/login HTTP/1.1" 5864.100, 127.0.0.1, "", "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.17763.7671", -1, "", ""
Stack:
1. 密码错误
   1).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/userAuth.(*sUserAuth).Login
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:113
   2).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/controller.(*userAuthController).Login
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/controller/user_auth.go:52
   3).  github.com/tiger1103/gfast/v3/internal/app/system/logic/middleware.(*sMiddleware).Ctx
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/system/logic/middleware/middleware.go:47
   4).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCharsetUTF8
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:48
   5).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:39

2025-09-05T14:42:53.666+08:00 {4c262750d34f6218ec6bef3659a1aaca} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/login HTTP/1.1" 0.005, 127.0.0.1, "", "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.17763.7671"
2025-09-05T14:44:44.612+08:00 {4c237626ed4f6218f8c360325479a89c} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/login HTTP/1.1" 10936.900, 127.0.0.1, "", "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.17763.7671", -1, "", ""
Stack:
1. 密码错误
   1).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/userAuth.(*sUserAuth).Login
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:113
   2).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/controller.(*userAuthController).Login
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/controller/user_auth.go:52
   3).  github.com/tiger1103/gfast/v3/internal/app/system/logic/middleware.(*sMiddleware).Ctx
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/system/logic/middleware/middleware.go:47
   4).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCharsetUTF8
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:48
   5).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:39

2025-09-05T14:44:44.664+08:00 {4c237626ed4f6218f8c360325479a89c} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/login HTTP/1.1" 0.010, 127.0.0.1, "", "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.17763.7671"
2025-09-05T14:45:19.687+08:00 {d4b5ab46f54f6218f9c36032b20d2cce} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/sms-login HTTP/1.1" 0.191, 127.0.0.1, "", "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.17763.7671"
2025-09-05T14:45:20.522+08:00 {e4cf1c83f54f6218fbc36032caf462e9} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/login HTTP/1.1" 8454.500, 127.0.0.1, "", "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.17763.7671", -1, "", ""
Stack:
1. 密码错误
   1).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/userAuth.(*sUserAuth).Login
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:113
   2).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/controller.(*userAuthController).Login
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/controller/user_auth.go:52
   3).  github.com/tiger1103/gfast/v3/internal/app/system/logic/middleware.(*sMiddleware).Ctx
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/system/logic/middleware/middleware.go:47
   4).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCharsetUTF8
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:48
   5).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:39

2025-09-05T14:45:20.571+08:00 {e4cf1c83f54f6218fbc36032caf462e9} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/login HTTP/1.1" 0.008, 127.0.0.1, "", "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.17763.7671"
2025-09-05T15:28:39.928+08:00 {582cc8bb52526218fcc360322512e375} 200 "OPTIONS http 127.0.0.1:8808 /api/v1/sanxianren/user/login HTTP/1.1" 0.025, 127.0.0.1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-09-05T15:28:40.018+08:00 {14fddabc52526218fdc3603225b3a139} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/login HTTP/1.1" 67761.600, 127.0.0.1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", -1, "", ""
Stack:
1. 密码错误
   1).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/userAuth.(*sUserAuth).Login
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:113
   2).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/controller.(*userAuthController).Login
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/controller/user_auth.go:52
   3).  github.com/tiger1103/gfast/v3/internal/app/system/logic/middleware.(*sMiddleware).Ctx
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/system/logic/middleware/middleware.go:47
   4).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCharsetUTF8
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:48
   5).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:39

2025-09-05T15:28:40.021+08:00 {14fddabc52526218fdc3603225b3a139} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/login HTTP/1.1" 0.067, 127.0.0.1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-09-05T15:28:55.429+08:00 {10451f4f56526218fec360325252b104} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/sms-login HTTP/1.1" 0.152, 127.0.0.1, "", "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.17763.7671"
2025-09-05T15:28:55.548+08:00 {9093185f5652621800c46032216a8cbc} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/login HTTP/1.1" 4151.400, 127.0.0.1, "", "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.17763.7671", -1, "", ""
Stack:
1. 密码错误
   1).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/userAuth.(*sUserAuth).Login
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:113
   2).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/controller.(*userAuthController).Login
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/controller/user_auth.go:52
   3).  github.com/tiger1103/gfast/v3/internal/app/system/logic/middleware.(*sMiddleware).Ctx
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/system/logic/middleware/middleware.go:47
   4).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCharsetUTF8
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:48
   5).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:39

2025-09-05T15:28:55.553+08:00 {9093185f5652621800c46032216a8cbc} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/login HTTP/1.1" 0.004, 127.0.0.1, "", "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.17763.7671"
2025-09-05T15:30:30.409+08:00 {1844a0746c526218ed4ffe0661895525} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/login HTTP/1.1" 8652.700, 127.0.0.1, "", "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.17763.7671", -1, "", ""
Stack:
1. 密码错误
   1).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/userAuth.(*sUserAuth).Login
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:113
   2).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/controller.(*userAuthController).Login
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/controller/user_auth.go:52
   3).  github.com/tiger1103/gfast/v3/internal/app/system/logic/middleware.(*sMiddleware).Ctx
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/system/logic/middleware/middleware.go:47
   4).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCharsetUTF8
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:48
   5).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:39

2025-09-05T15:30:30.424+08:00 {1844a0746c526218ed4ffe0661895525} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/login HTTP/1.1" 0.008, 127.0.0.1, "", "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.17763.7671"
2025-09-05T15:30:30.510+08:00 {c4801f7b6c526218ee4ffe06b00f9f13} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/login HTTP/1.1" 7990.300, 127.0.0.1, "", "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.17763.7671", -1, "", ""
Stack:
1. 密码错误
   1).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/userAuth.(*sUserAuth).Login
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:113
   2).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/controller.(*userAuthController).Login
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/controller/user_auth.go:52
   3).  github.com/tiger1103/gfast/v3/internal/app/system/logic/middleware.(*sMiddleware).Ctx
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/system/logic/middleware/middleware.go:47
   4).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCharsetUTF8
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:48
   5).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:39

2025-09-05T15:30:30.522+08:00 {c4801f7b6c526218ee4ffe06b00f9f13} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/login HTTP/1.1" 0.007, 127.0.0.1, "", "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.17763.7671"
2025-09-05T15:30:30.546+08:00 {8c670f7d6c526218ef4ffe0600875c2a} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/login HTTP/1.1" 10501.000, 127.0.0.1, "", "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.17763.7671", -1, "", ""
Stack:
1. 密码错误
   1).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/userAuth.(*sUserAuth).Login
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:113
   2).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/controller.(*userAuthController).Login
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/controller/user_auth.go:52
   3).  github.com/tiger1103/gfast/v3/internal/app/system/logic/middleware.(*sMiddleware).Ctx
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/system/logic/middleware/middleware.go:47
   4).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCharsetUTF8
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:48
   5).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:39

2025-09-05T15:30:30.560+08:00 {8c670f7d6c526218ef4ffe0600875c2a} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/login HTTP/1.1" 0.010, 127.0.0.1, "", "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.17763.7671"
2025-09-05T15:30:30.590+08:00 {0829a77f6c526218f04ffe0668b02a5d} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/login HTTP/1.1" 11497.400, 127.0.0.1, "", "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.17763.7671", -1, "", ""
Stack:
1. 密码错误
   1).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/userAuth.(*sUserAuth).Login
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:113
   2).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/controller.(*userAuthController).Login
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/controller/user_auth.go:52
   3).  github.com/tiger1103/gfast/v3/internal/app/system/logic/middleware.(*sMiddleware).Ctx
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/system/logic/middleware/middleware.go:47
   4).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCharsetUTF8
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:48
   5).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:39

2025-09-05T15:30:30.605+08:00 {0829a77f6c526218f04ffe0668b02a5d} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/login HTTP/1.1" 0.011, 127.0.0.1, "", "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.17763.7671"
2025-09-05T15:31:10.963+08:00 {4827a8e675526218f14ffe06be5ef9c5} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/sms-login HTTP/1.1" 3997.700, 127.0.0.1, "", "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.17763.7671", 51, "Validation Failed", ""
Stack:
The Phone field is required
2025-09-05T15:31:10.964+08:00 {4827a8e675526218f14ffe06be5ef9c5} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/sms-login HTTP/1.1" 0.003, 127.0.0.1, "", "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.17763.7671"
2025-09-05T15:31:43.014+08:00 {8c7e5d5c7d526218f24ffe06fbf165cc} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/sms-login HTTP/1.1" 0.012, 127.0.0.1, "", "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.17763.7671"
2025-09-05T15:32:56.210+08:00 {447ddb668e526218f44ffe0622311103} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/login HTTP/1.1" 18499.800, 127.0.0.1, "", "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.17763.7671", -1, "", ""
Stack:
1. 密码错误
   1).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/userAuth.(*sUserAuth).Login
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:113
   2).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/controller.(*userAuthController).Login
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/controller/user_auth.go:52
   3).  github.com/tiger1103/gfast/v3/internal/app/system/logic/middleware.(*sMiddleware).Ctx
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/system/logic/middleware/middleware.go:47
   4).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCharsetUTF8
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:48
   5).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:39

2025-09-05T15:32:56.234+08:00 {447ddb668e526218f44ffe0622311103} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/login HTTP/1.1" 0.018, 127.0.0.1, "", "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.17763.7671"
2025-09-05T15:53:15.470+08:00 {50bdb046aa536218f64ffe06243186bf} 200 "OPTIONS http 127.0.0.1:8808 /api/v1/sanxianren/story/list HTTP/1.1" 0.022, 127.0.0.1, "http://localhost:5173/", "Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********"
2025-09-05T15:53:15.504+08:00 {0081d729aa536218f54ffe069b04e917} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/story/list HTTP/1.1" 0.669, 127.0.0.1, "http://localhost:5173/", "Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36 HBuilderX"
2025-09-05T15:53:15.636+08:00 {24f9fd4daa536218f74ffe06fcab435e} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/story/list HTTP/1.1" 0.090, 127.0.0.1, "http://localhost:5173/", "Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36 HBuilderX"
2025-09-05T15:53:15.658+08:00 {a85f9d50aa536218f84ffe06138a9c1f} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/story/list HTTP/1.1" 0.068, 127.0.0.1, "http://localhost:5173/", "Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********"
2025-09-05T15:53:15.857+08:00 {dc132356aa536218f94ffe06d968513f} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/story/list HTTP/1.1" 0.175, 127.0.0.1, "http://localhost:5173/", "Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********"
2025-09-05T15:53:40.571+08:00 {7416db20b0536218fa4ffe06d334e97c} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/story/list HTTP/1.1" 0.014, 127.0.0.1, "http://localhost:5173/", "Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36 HBuilderX"
2025-09-05T15:53:40.611+08:00 {5cdf7223b0536218fb4ffe0677b10fcf} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/story/list HTTP/1.1" 0.010, 127.0.0.1, "http://localhost:5173/", "Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********"
2025-09-05T15:53:40.814+08:00 {24ab9b2fb0536218fc4ffe06e74eb967} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/story/list HTTP/1.1" 0.009, 127.0.0.1, "http://localhost:5173/", "Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36 HBuilderX"
2025-09-05T15:53:40.918+08:00 {6ca67a35b0536218fd4ffe06cca512e4} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/story/list HTTP/1.1" 0.015, 127.0.0.1, "http://localhost:5173/", "Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********"
2025-09-05T16:15:45.439+08:00 pid[25940]: all servers shutdown
2025-09-05T22:36:59.970+08:00 {acd2bc69b269621827d1b849132c728d} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/login HTTP/1.1" 327996.000, 127.0.0.1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", -1, "", ""
Stack:
1. 密码错误
   1).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/logic/userAuth.(*sUserAuth).Login
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/logic/userAuth/user_auth.go:113
   2).  github.com/tiger1103/gfast/v3/internal/app/sanxianren/controller.(*userAuthController).Login
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/sanxianren/controller/user_auth.go:52
   3).  github.com/tiger1103/gfast/v3/internal/app/system/logic/middleware.(*sMiddleware).Ctx
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/system/logic/middleware/middleware.go:47
   4).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCharsetUTF8
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:48
   5).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
        C:/Users/<USER>/Documents/sanxianren/admin/gfast-mandate/internal/app/common/logic/middleware/middleware.go:39

2025-09-05T22:36:59.982+08:00 {acd2bc69b269621827d1b849132c728d} 200 "POST http 127.0.0.1:8808 /api/v1/sanxianren/user/login HTTP/1.1" 0.327, 127.0.0.1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-09-05T22:37:24.541+08:00 {b0c20635b869621828d1b849ae21e9ce} 200 "GET http localhost:8808 /api/v1/websocket?token=7ZUSfVIf2HyYjcv86SKPPs29v003ECPEScsdYsYYqO0UZxl82Rru4U8rKfP4TZcL%2FvYHAEQw%2BN0KWm3rAatyIARAk1Wv%2Bsx1pb8ufCJEo%2FgM7zAbfx2bAwMMLRHczAeLB2O3Cad1fGKz%2FR3%2BGlY4WA%3D%3D HTTP/1.1" 0.031, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-09-05T22:37:30.849+08:00 {ac41c0aeb969621829d1b849faddc0d1} 200 "OPTIONS http localhost:8808 /api/v1/system/cache/remove HTTP/1.1" 0.000, ::1, "http://localhost:8888/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-09-05T22:37:30.854+08:00 {b82ffdaeb96962182ad1b8499e58d371} 200 "DELETE http localhost:8808 /api/v1/system/cache/remove HTTP/1.1" 0.000, ::1, "http://localhost:8888/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-09-05T22:37:31.974+08:00 {7c40b3f1b96962182bd1b849e6e2b915} 200 "GET http localhost:8808 /api/v1/websocket?token=7ZUSfVIf2HyYjcv86SKPPs29v003ECPEScsdYsYYqO0UZxl82Rru4U8rKfP4TZcL%2FvYHAEQw%2BN0KWm3rAatyIARAk1Wv%2Bsx1pb8ufCJEo%2FgM7zAbfx2bAwMMLRHczAeLB2O3Cad1fGKz%2FR3%2BGlY4WA%3D%3D HTTP/1.1" 0.001, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-09-05T22:37:31.994+08:00 {ecb6e4f2b96962182cd1b8493c5a39be} 404 "OPTIONS http localhost:8808 /api/v1/sanxianren/oauth/status HTTP/1.1" 0.001, ::1, "http://localhost:8888/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-09-05T22:37:32.000+08:00 {58cc4ef3b96962182dd1b849e7eca551} 200 "OPTIONS http localhost:8808 /api/v1/system/user/getUserMenus HTTP/1.1" 0.000, ::1, "http://localhost:8888/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-09-05T22:37:32.004+08:00 {0cd18bf3b96962182ed1b84934c28148} 200 "GET http localhost:8808 /api/v1/system/user/getUserMenus HTTP/1.1" 0.000, ::1, "http://localhost:8888/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-09-05T22:37:32.041+08:00 {1c0ac8f5b96962182fd1b8495cd5a7c7} 404 "OPTIONS http localhost:8808 /api/v1/sanxianren/oauth/status HTTP/1.1" 0.000, ::1, "http://localhost:8888/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-09-05T22:37:33.551+08:00 {08cc9c4fba69621830d1b849f6b3f83b} 200 "GET http localhost:8808 /api/v1/websocket?token=undefined HTTP/1.1" 0.005, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-09-05T22:37:33.565+08:00 {5430a050ba69621831d1b84987f2043f} 404 "GET http localhost:8808 /api/v1/sanxianren/oauth/status HTTP/1.1" 0.000, ::1, "http://localhost:8888/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-09-05T22:37:33.807+08:00 {0c46dd59ba69621832d1b84998176386} 200 "GET http localhost:8808 /api/v1/pub/captcha/get HTTP/1.1" 0.087, ::1, "http://localhost:8888/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-09-05T22:37:35.282+08:00 {8069f7b6ba69621833d1b849b46b93d1} 200 "OPTIONS http localhost:8808 /api/v1/system/login HTTP/1.1" 0.000, ::1, "http://localhost:8888/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-09-05T22:37:35.479+08:00 {0cec15b7ba69621834d1b849225c7d3d} 200 "POST http localhost:8808 /api/v1/system/login HTTP/1.1" 0.195, ::1, "http://localhost:8888/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-09-05T22:38:03.665+08:00 {a06c2d52c169621837d1b849c1280709} 200 "GET http localhost:8808 /api/v1/websocket?token=7ZUSfVIf2HyYjcv86SKPPs29v003ECPEScsdYsYYqO0UZxl82Rru4U8rKfP4TZcL97Q8WCgB%2BicNWiGQhBZootw%2BZSCgen6z2Hfvi%2F%2FounRCGGyGl7NkcSL%2Fi%2B6ulyuBUtzMXnYo2mzjBxTbEkaXww%3D%3D HTTP/1.1" 0.009, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
2025-09-05T23:07:39.815+08:00 {008c4ada5e6b621838d1b8495e5e3d61} 200 "GET http localhost:8808 /api/v1/websocket?token=7ZUSfVIf2HyYjcv86SKPPs29v003ECPEScsdYsYYqO0UZxl82Rru4U8rKfP4TZcL97Q8WCgB%2BicNWiGQhBZootw%2BZSCgen6z2Hfvi%2F%2FounRCGGyGl7NkcSL%2Fi%2B6ulyuBUtzMXnYo2mzjBxTbEkaXww%3D%3D HTTP/1.1" 0.073, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
