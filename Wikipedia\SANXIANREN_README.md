# 天南地北三线人 - Wikipedia现代化首页

## 🎯 项目概述

这是一个为MediaWiki Wikipedia系统设计的现代化前端首页，专门为"天南地北三线人"主题打造。项目采用Vue 3 + Element Plus技术栈，提供瀑布流布局、无限滚动、响应式设计等现代化功能。

## 📁 项目结构

```
Wikipedia/
├── frontend/                    # 前端文件
│   ├── index.html              # 主页面
│   └── assets/
│       ├── css/
│       │   ├── main.css        # 主样式
│       │   ├── components.css  # 组件样式
│       │   └── responsive.css  # 响应式样式
│       └── js/
│           ├── data.js         # 数据文件
│           ├── utils.js        # 工具函数
│           └── main.js         # 主应用
├── api/
│   └── sanxianren.php          # API接口
├── LocalSettings.custom.php    # MediaWiki自定义配置
└── SANXIANREN_README.md        # 项目说明文档
```

## 🚀 快速开始

### 1. 部署前端文件

将 `frontend` 目录下的所有文件复制到您的Web服务器：

```bash
# 复制前端文件到Web根目录
cp -r frontend/* /var/www/html/wikipedia/frontend/

# 设置适当的权限
chmod -R 755 /var/www/html/wikipedia/frontend/
```

### 2. 配置MediaWiki

#### 方法一：直接修改LocalSettings.php
将 `LocalSettings.custom.php` 的内容添加到您的 `LocalSettings.php` 文件末尾：

```php
// 在LocalSettings.php末尾添加
require_once 'LocalSettings.custom.php';
```

#### 方法二：包含自定义配置文件
```php
// 在LocalSettings.php末尾添加
if (file_exists(__DIR__ . '/LocalSettings.custom.php')) {
    require_once __DIR__ . '/LocalSettings.custom.php';
}
```

### 3. 部署API接口

```bash
# 创建API目录
mkdir -p /var/www/html/wikipedia/api/

# 复制API文件
cp api/sanxianren.php /var/www/html/wikipedia/api/

# 设置权限
chmod 755 /var/www/html/wikipedia/api/sanxianren.php
```

### 4. 配置Web服务器重定向

#### Apache配置
在您的Apache虚拟主机配置中添加：

```apache
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /var/www/html/wikipedia
    
    # 重定向根目录到现代化首页
    RedirectMatch ^/$ /frontend/index.html
    
    # API重写规则
    RewriteEngine On
    RewriteRule ^api/v1/sanxianren/(.*)$ /api/sanxianren.php?action=$1 [QSA,L]
</VirtualHost>
```

#### Nginx配置
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/html/wikipedia;
    
    # 重定向根目录到现代化首页
    location = / {
        return 301 /frontend/index.html;
    }
    
    # API接口
    location ~ ^/api/v1/sanxianren/(.*)$ {
        try_files $uri /api/sanxianren.php?action=$1;
    }
}
```

## 🎨 功能特性

### 1. 现代化导航栏
- **品牌标识**：天南地北三线人
- **主导航**：三线人、三线厂、故事馆、口述历史、遗址馆
- **智能搜索**：实时搜索建议
- **响应式设计**：移动端抽屉菜单

### 2. 瀑布流布局
- **自适应列数**：1-5列响应式布局
- **智能分布**：内容智能分配到各列
- **流畅动画**：卡片悬浮和加载效果

### 3. 无限滚动
- **自动加载**：滚动到底部自动加载更多
- **加载状态**：友好的加载指示器
- **性能优化**：防抖和节流处理

### 4. 内容管理
- **分类筛选**：按栏目快速筛选内容
- **搜索功能**：全文搜索和关键词高亮
- **收藏功能**：文章收藏和管理

## 📱 响应式断点

| 设备类型 | 屏幕宽度 | 列数 | 特殊适配 |
|---------|---------|------|---------|
| 大屏幕 | ≥1400px | 5列 | 最大内容展示 |
| 桌面 | ≥1200px | 4列 | 标准布局 |
| 平板 | ≥992px | 3列 | 中等密度 |
| 小平板 | ≥768px | 2列 | 简化布局 |
| 手机 | <768px | 1列 | 移动优化 |

## 🔌 API接口

### 基础接口
```
GET /api/sanxianren.php?action=articles&category=sanxianren&page=1&limit=20
GET /api/sanxianren.php?action=article&id=123
GET /api/sanxianren.php?action=search&query=关键词
GET /api/sanxianren.php?action=suggestions&q=搜索词
GET /api/sanxianren.php?action=categories
```

### 响应格式
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "list": [...],
        "total": 100,
        "page": 1,
        "limit": 20
    }
}
```

## 🔧 自定义配置

### 修改网站信息
编辑 `frontend/assets/js/data.js`：

```javascript
// 修改导航项
const navigationItems = [
    {
        key: 'custom',
        label: '自定义栏目',
        icon: 'el-icon-custom',
        description: '自定义描述'
    }
    // ... 更多栏目
];
```

### 修改样式主题
编辑 `frontend/assets/css/main.css`：

```css
/* 修改主色调 */
:root {
    --primary-color: #409eff;
    --success-color: #67c23a;
    --warning-color: #e6a23c;
    --danger-color: #f56c6c;
}
```

## 🛠️ 开发指南

### 本地开发
1. 启动本地Web服务器
2. 访问 `http://localhost/frontend/index.html`
3. 修改代码并刷新页面查看效果

### 调试模式
在浏览器控制台中启用调试：

```javascript
// 启用调试模式
localStorage.setItem('debug', 'true');

// 查看应用状态
console.log(window.WikipediaApp);
```

## 🚨 故障排除

### 常见问题

1. **页面无法加载**
   - 检查Web服务器配置
   - 确认文件权限正确
   - 查看浏览器控制台错误

2. **API接口报错**
   - 检查数据库连接
   - 确认MediaWiki配置正确
   - 查看服务器错误日志

3. **样式显示异常**
   - 清除浏览器缓存
   - 检查CSS文件路径
   - 确认CDN资源可访问

### 日志查看
```bash
# Apache错误日志
tail -f /var/log/apache2/error.log

# Nginx错误日志
tail -f /var/log/nginx/error.log

# PHP错误日志
tail -f /var/log/php/error.log
```

## 🎉 开始使用

现在您可以访问您的Wikipedia站点，体验全新的现代化首页！

### 访问地址
- **现代化首页**：`http://your-domain.com/frontend/index.html`
- **传统Wiki首页**：`http://your-domain.com/index.php`
- **管理后台**：`http://your-domain.com/index.php/Special:SpecialPages`

### 导航栏功能
- **三线人** - 浏览三线建设者人物故事
- **三线厂** - 查看三线建设工厂企业信息
- **故事馆** - 阅读三线建设历史故事
- **口述历史** - 聆听亲历者回忆
- **遗址馆** - 探索三线建设历史遗址
- **搜索** - 全站内容搜索

### 使用技巧
1. **瀑布流浏览**：页面会根据屏幕大小自动调整列数
2. **无限滚动**：向下滚动自动加载更多内容
3. **分类筛选**：点击分类标签快速筛选内容
4. **移动端**：在手机上使用抽屉菜单导航

## 📄 许可证

本项目采用 MIT 许可证，详情请参阅 LICENSE 文件。

---

**祝您使用愉快！🚀**

如果您在使用过程中遇到任何问题，请参考故障排除部分或联系技术支持。
