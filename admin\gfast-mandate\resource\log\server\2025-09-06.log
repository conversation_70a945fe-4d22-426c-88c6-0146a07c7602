2025-09-06T16:51:55.089+08:00 [FATA] net.Listen address ":8808" failed: listen tcp :8808: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted.
1. net.Listen address ":8808" failed
2. listen tcp :8808: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted.

2025-09-06T16:53:58.535+08:00 [INFO] pid[9796]: http server started listening on [:8809]
2025-09-06T16:53:58.535+08:00 [INFO] swagger ui is serving at address: http://127.0.0.1:8809/swagger/
2025-09-06T16:53:58.538+08:00 [INFO] openapi specification is serving at address: http://127.0.0.1:8809/api.json
2025-09-06T16:56:29.810+08:00 [INFO] pid[33328]: http server started listening on [:8809]
2025-09-06T16:56:29.810+08:00 [INFO] swagger ui is serving at address: http://127.0.0.1:8809/swagger/
2025-09-06T16:56:29.813+08:00 [INFO] openapi specification is serving at address: http://127.0.0.1:8809/api.json
2025-09-06T16:58:06.548+08:00 [INFO] pid[15904]: http server started listening on [:8809]
2025-09-06T16:58:06.548+08:00 [INFO] swagger ui is serving at address: http://127.0.0.1:8809/swagger/
2025-09-06T16:58:06.575+08:00 [INFO] openapi specification is serving at address: http://127.0.0.1:8809/api.json
2025-09-06T16:58:40.903+08:00 [INFO] pid[15904]: all servers shutdown
2025-09-06T16:59:02.094+08:00 [INFO] pid[20972]: http server started listening on [:8808]
2025-09-06T16:59:02.094+08:00 [INFO] swagger ui is serving at address: http://127.0.0.1:8808/swagger/
2025-09-06T16:59:02.107+08:00 [INFO] openapi specification is serving at address: http://127.0.0.1:8808/api.json
2025-09-06T17:00:23.229+08:00 [INFO] pid[20132]: http server started listening on [:8808]
2025-09-06T17:00:23.229+08:00 [INFO] swagger ui is serving at address: http://127.0.0.1:8808/swagger/
2025-09-06T17:00:23.232+08:00 [INFO] openapi specification is serving at address: http://127.0.0.1:8808/api.json
2025-09-06T18:50:09.082+08:00 [INFO] pid[25820]: http server started listening on [:8808]
2025-09-06T18:50:09.082+08:00 [INFO] swagger ui is serving at address: http://127.0.0.1:8808/swagger/
2025-09-06T18:50:09.424+08:00 [INFO] openapi specification is serving at address: http://127.0.0.1:8808/api.json
2025-09-06T19:29:05.303+08:00 [INFO] pid[32288]: http server started listening on [:8808]
2025-09-06T19:29:05.304+08:00 [INFO] swagger ui is serving at address: http://127.0.0.1:8808/swagger/
2025-09-06T19:29:05.310+08:00 [INFO] openapi specification is serving at address: http://127.0.0.1:8808/api.json
