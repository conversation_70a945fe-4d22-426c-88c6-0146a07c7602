// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SxUserBadgesDao is the data access object for table sx_user_badges.
type SxUserBadgesDao struct {
	table   string              // table is the underlying table name of the DAO.
	group   string              // group is the database configuration group name of current DAO.
	columns SxUserBadgesColumns // columns contains all the column names of Table for convenient usage.
}

// SxUserBadgesColumns defines and stores column names for table sx_user_badges.
type SxUserBadgesColumns struct {
	Id               string // 徽章ID
	UserId           string // 用户ID
	BadgeType        string // 徽章类型：contributor-贡献者，storyteller-故事家，collector-收藏家，active-活跃用户
	BadgeName        string // 徽章名称
	BadgeIcon        string // 徽章图标
	BadgeDescription string // 徽章描述
	EarnedAt         string // 获得时间
	IsDisplayed      string // 是否显示：1-显示，0-隐藏
}

// sxUserBadgesColumns holds the columns for table sx_user_badges.
var sxUserBadgesColumns = SxUserBadgesColumns{
	Id:               "id",
	UserId:           "user_id",
	BadgeType:        "badge_type",
	BadgeName:        "badge_name",
	BadgeIcon:        "badge_icon",
	BadgeDescription: "badge_description",
	EarnedAt:         "earned_at",
	IsDisplayed:      "is_displayed",
}

// NewSxUserBadgesDao creates and returns a new DAO object for table data access.
func NewSxUserBadgesDao() *SxUserBadgesDao {
	return &SxUserBadgesDao{
		group:   "default",
		table:   "sx_user_badges",
		columns: sxUserBadgesColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *SxUserBadgesDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *SxUserBadgesDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *SxUserBadgesDao) Columns() SxUserBadgesColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *SxUserBadgesDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *SxUserBadgesDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}
