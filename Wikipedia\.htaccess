# MediaWiki Security Configuration
# 为 MediaWiki 站点添加安全标头和配置

<IfModule mod_headers.c>
    # 防止浏览器进行 MIME 类型嗅探
    Header always set X-Content-Type-Options "nosniff"
    
    # 防止点击劫持攻击
    Header always set X-Frame-Options "SAMEORIGIN"
    
    # 启用 XSS 保护
    Header always set X-XSS-Protection "1; mode=block"
    
    # 推荐使用 HTTPS
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
    
    # 隐藏服务器信息
    Header always unset Server
    Header always unset X-Powered-By
</IfModule>

# 禁用服务器签名
ServerSignature Off

# 防止访问敏感文件和目录
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|inc|bak|backup|old|tmp)$">
    Order Deny,Allow
    Deny from all
</FilesMatch>

# 保护配置文件（但允许 MediaWiki 内部访问）
# <Files "LocalSettings.php">
#     Order Deny,Allow
#     Deny from all
# </Files>

# 保护维护脚本目录
<Directory "maintenance">
    Order Deny,Allow
    Deny from all
</Directory>

# 保护测试目录
<Directory "tests">
    Order Deny,Allow
    Deny from all
</Directory>

# 保护 vendor 目录中的敏感文件（但允许 MediaWiki 必需的文件）
<DirectoryMatch "vendor/.*">
    <FilesMatch "\.(md|txt|yml|yaml)$">
        Order Deny,Allow
        Deny from all
    </FilesMatch>
</DirectoryMatch>

# 启用压缩以提高性能
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# 设置缓存策略
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
</IfModule>

# URL 重写规则
<IfModule mod_rewrite.c>
    RewriteEngine On

    # 重定向到 HTTPS（如果配置了 SSL）
    # RewriteCond %{HTTPS} off
    # RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

    # 天南地北三线人 - 现代化首页重定向
    # 当访问根目录时，直接使用modern.php而不重定向
    RewriteCond %{REQUEST_URI} ^/$
    RewriteRule ^$ /modern.php [L]

    # 当访问index.php且没有特殊参数时，直接使用modern.php而不重定向
    RewriteCond %{REQUEST_URI} ^/index\.php$
    RewriteCond %{QUERY_STRING} !classic=1
    RewriteCond %{QUERY_STRING} !title=
    RewriteCond %{QUERY_STRING} !action=
    RewriteCond %{QUERY_STRING} !oldid=
    RewriteCond %{QUERY_STRING} !diff=
    RewriteRule ^index\.php$ /modern.php [L]

    # API路由重写
    RewriteRule ^api/v1/sanxianren/(.*)$ /api/sanxianren.php?action=$1 [QSA,L]

    # 搜索引擎友好的URL
    RewriteRule ^search/(.*)$ /modern.php?search=$1 [L,QSA]
    RewriteRule ^category/(.*)$ /modern.php?category=$1 [L,QSA]

    # 保持MediaWiki的原有功能
    RewriteCond %{REQUEST_URI} ^/index\.php$
    RewriteCond %{QUERY_STRING} classic=1 [OR]
    RewriteCond %{QUERY_STRING} title= [OR]
    RewriteCond %{QUERY_STRING} action= [OR]
    RewriteCond %{QUERY_STRING} oldid= [OR]
    RewriteCond %{QUERY_STRING} diff=
    RewriteRule ^index\.php$ - [L]

    # 处理MediaWiki的特殊页面和资源
    RewriteCond %{REQUEST_URI} ^/(api|load|thumb|images|skins|resources|extensions|maintenance|tests)/ [OR]
    RewriteCond %{REQUEST_FILENAME} -f [OR]
    RewriteCond %{REQUEST_FILENAME} -d
    RewriteRule ^ - [L]

    # 友好的 URL 重写（MediaWiki短URL支持）
    # RewriteCond %{REQUEST_FILENAME} !-f
    # RewriteCond %{REQUEST_FILENAME} !-d
    # RewriteRule ^wiki/(.*)$ /index.php?title=$1 [L,QSA]
</IfModule>

# 错误页面重定向到现代化首页
ErrorDocument 404 /modern.php?error=404
ErrorDocument 403 /modern.php?error=403