// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/sanxianren/dao/internal"
)

// internalSxUserCollectionsDao is internal type for wrapping internal DAO implements.
type internalSxUserCollectionsDao = *internal.SxUserCollectionsDao

// sxUserCollectionsDao is the data access object for table sx_user_collections.
// You can define custom methods on it to extend its functionality as you wish.
type sxUserCollectionsDao struct {
	internalSxUserCollectionsDao
}

var (
	// SxUserCollections is globally public accessible object for table sx_user_collections operations.
	SxUserCollections = sxUserCollectionsDao{
		internal.NewSxUserCollectionsDao(),
	}
)

// Fill with you ideas below.
