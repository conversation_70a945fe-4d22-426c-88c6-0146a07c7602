// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/sanxianren/dao/internal"
)

// internalSxUserVipDao is internal type for wrapping internal DAO implements.
type internalSxUserVipDao = *internal.SxUserVipDao

// sxUserVipDao is the data access object for table sx_user_vip.
// You can define custom methods on it to extend its functionality as you wish.
type sxUserVipDao struct {
	internalSxUserVipDao
}

var (
	// SxUserVip is globally public accessible object for table sx_user_vip operations.
	SxUserVip = sxUserVipDao{
		internal.NewSxUserVipDao(),
	}
)

// Fill with you ideas below.
