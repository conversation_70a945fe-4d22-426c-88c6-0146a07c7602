<template>
	<view class="history-manager">
		<view class="header">
			<text class="title">浏览历史</text>
			<view class="actions">
				<text class="clear-btn" @click="showClearDialog">清空历史</text>
			</view>
		</view>
		
		<view class="filter-tabs">
			<text 
				v-for="tab in filterTabs" 
				:key="tab.value"
				:class="['tab', { active: currentFilter === tab.value }]"
				@click="switchFilter(tab.value)"
			>
				{{ tab.label }}
			</text>
		</view>
		
		<view class="history-list" v-if="histories.length > 0">
			<view 
				v-for="item in histories" 
				:key="item.id"
				class="history-item"
				@click="viewContent(item)"
			>
				<image 
					:src="item.coverImage || '/static/default-cover.jpg'" 
					class="cover-image"
					mode="aspectFill"
				/>
				<view class="content">
					<text class="title">{{ item.title }}</text>
					<text class="summary">{{ item.summary }}</text>
					<view class="meta">
						<text class="type">{{ getTypeLabel(item.contentType) }}</text>
						<text class="time">{{ formatTime(item.viewedAt) }}</text>
					</view>
				</view>
			</view>
		</view>
		
		<view class="empty-state" v-else>
			<image src="/static/empty-history.png" class="empty-icon" />
			<text class="empty-text">暂无浏览历史</text>
			<text class="empty-hint">开始探索精彩内容吧</text>
		</view>
		
		<view class="load-more" v-if="hasMore && histories.length > 0">
			<text class="load-more-btn" @click="loadMore">加载更多</text>
		</view>
		
		<!-- 清空确认对话框 -->
		<uni-popup ref="clearDialog" type="dialog">
			<uni-popup-dialog 
				type="warn"
				title="确认清空"
				content="确定要清空所有浏览历史吗？此操作不可恢复。"
				:before-close="true"
				@close="handleClearDialogClose"
				@confirm="confirmClear"
			></uni-popup-dialog>
		</uni-popup>
	</view>
</template>

<script>
import { getHistory, clearHistory } from '@/utils/apiUtils.js'

export default {
	name: 'HistoryManager',
	data() {
		return {
			histories: [],
			currentFilter: 'all',
			currentPage: 1,
			pageSize: 10,
			hasMore: true,
			loading: false,
			filterTabs: [
				{ label: '全部', value: 'all' },
				{ label: '故事', value: 'story' },
				{ label: '工厂', value: 'factory' },
				{ label: '人物', value: 'people' }
			]
		}
	},
	mounted() {
		this.loadHistories()
	},
	methods: {
		async loadHistories(reset = true) {
			if (this.loading) return
			
			this.loading = true
			
			try {
				const params = {
					contentType: this.currentFilter,
					pageNum: reset ? 1 : this.currentPage,
					pageSize: this.pageSize
				}
				
				const result = await getHistory(params)
				
				if (result.success && result.data) {
					const newHistories = result.data.list || []
					
					if (reset) {
						this.histories = newHistories
						this.currentPage = 1
					} else {
						this.histories.push(...newHistories)
					}
					
					this.hasMore = newHistories.length === this.pageSize
				} else {
					uni.showToast({
						title: result.error || '获取浏览历史失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('加载浏览历史失败:', error)
				uni.showToast({
					title: '网络错误',
					icon: 'none'
				})
			} finally {
				this.loading = false
			}
		},
		
		switchFilter(filter) {
			if (this.currentFilter === filter) return
			
			this.currentFilter = filter
			this.loadHistories(true)
		},
		
		async loadMore() {
			if (!this.hasMore || this.loading) return
			
			this.currentPage++
			await this.loadHistories(false)
		},
		
		showClearDialog() {
			this.$refs.clearDialog.open()
		},
		
		handleClearDialogClose() {
			this.$refs.clearDialog.close()
		},
		
		async confirmClear() {
			try {
				const result = await clearHistory({
					contentType: this.currentFilter
				})
				
				if (result.success) {
					this.histories = []
					this.hasMore = false
					
					uni.showToast({
						title: '清空成功',
						icon: 'success'
					})
				} else {
					uni.showToast({
						title: result.error || '清空失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('清空浏览历史失败:', error)
				uni.showToast({
					title: '网络错误',
					icon: 'none'
				})
			}
			
			this.$refs.clearDialog.close()
		},
		
		viewContent(item) {
			// 根据内容类型跳转到对应页面
			let url = ''
			switch (item.contentType) {
				case 'story':
					url = `/pages/story/detail?id=${item.contentId}`
					break
				case 'factory':
					url = `/pages/factory/detail?id=${item.contentId}`
					break
				case 'people':
					url = `/pages/people/detail?id=${item.contentId}`
					break
				default:
					return
			}
			
			uni.navigateTo({ url })
		},
		
		getTypeLabel(type) {
			const typeMap = {
				story: '故事',
				factory: '工厂',
				people: '人物'
			}
			return typeMap[type] || '未知'
		},
		
		formatTime(timeStr) {
			if (!timeStr) return ''
			
			const time = new Date(timeStr)
			const now = new Date()
			const diff = now - time
			
			if (diff < 60000) { // 1分钟内
				return '刚刚'
			} else if (diff < 3600000) { // 1小时内
				return `${Math.floor(diff / 60000)}分钟前`
			} else if (diff < 86400000) { // 1天内
				return `${Math.floor(diff / 3600000)}小时前`
			} else if (diff < 2592000000) { // 30天内
				return `${Math.floor(diff / 86400000)}天前`
			} else {
				return time.toLocaleDateString()
			}
		}
	}
}
</script>

<style scoped>
.history-manager {
	padding: 20rpx;
}

.header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.clear-btn {
	color: #ff4757;
	font-size: 26rpx;
	padding: 10rpx 20rpx;
	border: 1rpx solid #ff4757;
	border-radius: 20rpx;
	background: transparent;
}

.filter-tabs {
	display: flex;
	background: #f5f5f5;
	border-radius: 10rpx;
	padding: 6rpx;
	margin-bottom: 30rpx;
}

.tab {
	flex: 1;
	text-align: center;
	padding: 16rpx 0;
	font-size: 28rpx;
	color: #666;
	border-radius: 6rpx;
	transition: all 0.3s;
}

.tab.active {
	background: #007bff;
	color: white;
}

.history-item {
	display: flex;
	background: white;
	border-radius: 12rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.cover-image {
	width: 120rpx;
	height: 120rpx;
	border-radius: 8rpx;
	margin-right: 20rpx;
}

.content {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.content .title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.summary {
	font-size: 26rpx;
	color: #666;
	line-height: 1.4;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	margin-bottom: 10rpx;
}

.meta {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.type {
	background: #e8f5e8;
	color: #4caf50;
	padding: 4rpx 12rpx;
	border-radius: 20rpx;
	font-size: 22rpx;
}

.time {
	font-size: 22rpx;
	color: #999;
}

.empty-state {
	text-align: center;
	padding: 100rpx 0;
}

.empty-icon {
	width: 200rpx;
	height: 200rpx;
	margin-bottom: 30rpx;
}

.empty-text {
	display: block;
	font-size: 32rpx;
	color: #666;
	margin-bottom: 10rpx;
}

.empty-hint {
	display: block;
	font-size: 26rpx;
	color: #999;
}

.load-more {
	text-align: center;
	padding: 30rpx 0;
}

.load-more-btn {
	color: #007bff;
	font-size: 28rpx;
	padding: 20rpx 40rpx;
	border: 1rpx solid #007bff;
	border-radius: 30rpx;
	background: transparent;
}
</style>
