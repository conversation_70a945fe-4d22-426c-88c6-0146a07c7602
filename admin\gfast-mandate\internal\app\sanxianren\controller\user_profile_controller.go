/*
* @desc:用户个人中心控制器
* @company:山东傲英网络科技股份有限公司
* @Author: wolfman
* @Date:   2024/12/19
 */

package controller

import (
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	v1 "github.com/tiger1103/gfast/v3/api/v1/sanxianren"
	"github.com/tiger1103/gfast/v3/internal/app/sanxianren/service"
	systemService "github.com/tiger1103/gfast/v3/internal/app/system/service"
)

// UserProfileController 用户个人中心控制器
type UserProfileController struct{}

// GetUserProfile 获取用户个人资料
func (c *UserProfileController) GetUserProfile(ctx context.Context, req *v1.UserProfileReq) (res *v1.UserProfileRes, err error) {
	// 获取当前用户ID
	userId := systemService.Context().GetUserId(ctx)
	if userId == 0 {
		return nil, gerror.New("用户未登录")
	}

	// 调用服务获取用户资料
	res, err = service.UserProfile().GetUserProfile(ctx, userId)
	if err != nil {
		g.Log().Error(ctx, "获取用户个人资料失败:", err)
		return nil, gerror.New("获取用户资料失败")
	}

	return res, nil
}

// EditUserProfile 编辑用户个人资料
func (c *UserProfileController) EditUserProfile(ctx context.Context, req *v1.EditUserProfileReq) (res *v1.EditUserProfileRes, err error) {
	// 获取当前用户ID
	userId := systemService.Context().GetUserId(ctx)
	if userId == 0 {
		return nil, gerror.New("用户未登录")
	}

	// 调用服务编辑用户资料
	err = service.UserProfile().EditUserProfile(ctx, userId, req)
	if err != nil {
		g.Log().Error(ctx, "编辑用户个人资料失败:", err)
		return nil, gerror.New("编辑用户资料失败")
	}

	return &v1.EditUserProfileRes{}, nil
}

// GetUserSubmissions 获取用户投稿列表
func (c *UserProfileController) GetUserSubmissions(ctx context.Context, req *v1.UserSubmissionsReq) (res *v1.UserSubmissionsRes, err error) {
	// 获取当前用户ID
	userId := systemService.Context().GetUserId(ctx)
	if userId == 0 {
		return nil, gerror.New("用户未登录")
	}

	// 调用服务获取用户投稿列表
	res, err = service.UserProfile().GetUserSubmissions(ctx, userId, req)
	if err != nil {
		g.Log().Error(ctx, "获取用户投稿列表失败:", err)
		return nil, gerror.New("获取用户投稿列表失败")
	}

	return res, nil
}

// GetUserSubmissionStats 获取用户投稿统计
func (c *UserProfileController) GetUserSubmissionStats(ctx context.Context, req *v1.UserSubmissionStatsReq) (res *v1.UserSubmissionStatsRes, err error) {
	// 获取当前用户ID
	userId := systemService.Context().GetUserId(ctx)
	if userId == 0 {
		return nil, gerror.New("用户未登录")
	}

	// 调用服务获取用户投稿统计
	res, err = service.UserProfile().GetUserSubmissionStats(ctx, userId)
	if err != nil {
		g.Log().Error(ctx, "获取用户投稿统计失败:", err)
		return nil, gerror.New("获取用户投稿统计失败")
	}

	return res, nil
}

// GetUserVipInfo 获取用户VIP信息
func (c *UserProfileController) GetUserVipInfo(ctx context.Context, req *v1.UserVipInfoReq) (res *v1.UserVipInfoRes, err error) {
	// 获取当前用户ID
	userId := systemService.Context().GetUserId(ctx)
	if userId == 0 {
		return nil, gerror.New("用户未登录")
	}

	// 调用服务获取用户VIP信息
	res, err = service.UserProfile().GetUserVipInfo(ctx, userId)
	if err != nil {
		g.Log().Error(ctx, "获取用户VIP信息失败:", err)
		return nil, gerror.New("获取用户VIP信息失败")
	}

	return res, nil
}

// GetUserBadges 获取用户徽章列表
func (c *UserProfileController) GetUserBadges(ctx context.Context, req *v1.UserBadgesReq) (res *v1.UserBadgesRes, err error) {
	// 获取当前用户ID
	userId := systemService.Context().GetUserId(ctx)
	if userId == 0 {
		return nil, gerror.New("用户未登录")
	}

	// 调用服务获取用户徽章列表
	res, err = service.UserProfile().GetUserBadges(ctx, userId)
	if err != nil {
		g.Log().Error(ctx, "获取用户徽章列表失败:", err)
		return nil, gerror.New("获取用户徽章列表失败")
	}

	return res, nil
}
