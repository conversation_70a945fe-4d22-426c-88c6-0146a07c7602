// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// SxUserVip is the golang structure for table sx_user_vip.
type SxUserVip struct {
	Id            uint64      `json:"id"            orm:"id"             description:"VIP记录ID"`
	UserId        uint64      `json:"userId"        orm:"user_id"        description:"用户ID"`
	VipType       string      `json:"vipType"       orm:"vip_type"       description:"VIP类型：monthly-月度，yearly-年度，lifetime-终身"`
	StartDate     *gtime.Time `json:"startDate"     orm:"start_date"     description:"VIP开始时间"`
	EndDate       *gtime.Time `json:"endDate"       orm:"end_date"       description:"VIP结束时间"`
	IsActive      int         `json:"isActive"      orm:"is_active"      description:"是否激活：1-激活，0-未激活"`
	PaymentAmount float64     `json:"paymentAmount" orm:"payment_amount" description:"支付金额"`
	PaymentMethod string      `json:"paymentMethod" orm:"payment_method" description:"支付方式"`
	OrderNo       string      `json:"orderNo"       orm:"order_no"       description:"订单号"`
	CreatedAt     *gtime.Time `json:"createdAt"     orm:"created_at"     description:"创建时间"`
	UpdatedAt     *gtime.Time `json:"updatedAt"     orm:"updated_at"     description:"更新时间"`
}
