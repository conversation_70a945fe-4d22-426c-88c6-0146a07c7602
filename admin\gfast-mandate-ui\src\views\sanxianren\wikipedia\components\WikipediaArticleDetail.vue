<template>
  <div class="article-detail" v-loading="loading">
    <!-- 文章头部 -->
    <div class="article-header">
      <div class="header-content">
        <h1 class="article-title">{{ article.title }}</h1>
        
        <!-- 文章元信息 -->
        <div class="article-meta">
          <div class="meta-item">
            <SvgIcon name="ele-User" :size="14" />
            <span>{{ article.author?.name || '匿名' }}</span>
          </div>
          <div class="meta-item">
            <SvgIcon name="ele-Clock" :size="14" />
            <span>{{ formatTime(article.updatedAt) }}</span>
          </div>
          <div class="meta-item">
            <SvgIcon name="ele-View" :size="14" />
            <span>{{ formatNumber(article.views) }} 次浏览</span>
          </div>
          <div class="meta-item">
            <SvgIcon name="ele-Edit" :size="14" />
            <span>{{ formatNumber(article.edits) }} 次编辑</span>
          </div>
        </div>

        <!-- 分类和标签 -->
        <div class="article-tags">
          <el-tag 
            :type="getCategoryType(article.category)"
            size="small"
            effect="dark"
          >
            <SvgIcon :name="getCategoryIcon(article.category)" :size="12" class="mr4" />
            {{ getCategoryName(article.category) }}
          </el-tag>
          
          <el-tag
            v-for="tag in article.tags"
            :key="tag"
            size="small"
            type="info"
            effect="plain"
            class="tag-item"
          >
            {{ tag }}
          </el-tag>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="header-actions">
        <el-button 
          :type="article.isFavorite ? 'danger' : 'default'"
          @click="toggleFavorite"
        >
          <SvgIcon 
            :name="article.isFavorite ? 'ele-StarFilled' : 'ele-Star'" 
            :size="16" 
          />
          {{ article.isFavorite ? '取消收藏' : '收藏' }}
        </el-button>
        
        <el-button @click="shareArticle">
          <SvgIcon name="ele-Share" :size="16" />
          分享
        </el-button>
        
        <el-button 
          v-if="canEdit"
          type="primary" 
          @click="editArticle"
        >
          <SvgIcon name="ele-Edit" :size="16" />
          编辑
        </el-button>
      </div>
    </div>

    <!-- 文章图片 -->
    <div v-if="article.image" class="article-image">
      <el-image
        :src="article.image"
        :alt="article.title"
        fit="cover"
        :preview-src-list="[article.image]"
        class="main-image"
      >
        <template #placeholder>
          <div class="image-placeholder">
            <SvgIcon name="ele-Picture" :size="48" />
          </div>
        </template>
        <template #error>
          <div class="image-error">
            <SvgIcon name="ele-PictureFilled" :size="48" />
          </div>
        </template>
      </el-image>
    </div>

    <!-- 文章摘要 -->
    <div class="article-summary">
      <h3>摘要</h3>
      <p>{{ article.summary }}</p>
    </div>

    <!-- 目录 -->
    <div v-if="tableOfContents.length > 0" class="table-of-contents">
      <h3>目录</h3>
      <ul class="toc-list">
        <li 
          v-for="item in tableOfContents"
          :key="item.id"
          :class="`toc-level-${item.level}`"
          @click="scrollToSection(item.id)"
        >
          <a :href="`#${item.id}`">{{ item.title }}</a>
        </li>
      </ul>
    </div>

    <!-- 文章内容 -->
    <div class="article-content">
      <div 
        v-html="processedContent"
        class="content-body"
      ></div>
    </div>

    <!-- 参考资料 -->
    <div v-if="article.references && article.references.length > 0" class="article-references">
      <h3>参考资料</h3>
      <ol class="references-list">
        <li 
          v-for="(ref, index) in article.references"
          :key="index"
          class="reference-item"
        >
          <a 
            v-if="ref.url"
            :href="ref.url"
            target="_blank"
            rel="noopener noreferrer"
          >
            {{ ref.title }}
          </a>
          <span v-else>{{ ref.title }}</span>
          <span v-if="ref.author" class="ref-author">. {{ ref.author }}</span>
          <span v-if="ref.date" class="ref-date">. {{ ref.date }}</span>
        </li>
      </ol>
    </div>

    <!-- 相关文章 -->
    <div v-if="relatedArticles.length > 0" class="related-articles">
      <h3>相关文章</h3>
      <div class="related-grid">
        <div 
          v-for="related in relatedArticles"
          :key="related.id"
          class="related-item"
          @click="openRelatedArticle(related)"
        >
          <div class="related-image">
            <el-image
              :src="related.image"
              :alt="related.title"
              fit="cover"
            >
              <template #placeholder>
                <SvgIcon name="ele-Picture" :size="24" />
              </template>
              <template #error>
                <SvgIcon name="ele-PictureFilled" :size="24" />
              </template>
            </el-image>
          </div>
          <div class="related-content">
            <h4>{{ related.title }}</h4>
            <p>{{ related.summary }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 文章统计 -->
    <div class="article-stats">
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-value">{{ formatNumber(article.views) }}</div>
          <div class="stat-label">浏览量</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ formatNumber(article.edits) }}</div>
          <div class="stat-label">编辑次数</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ formatNumber(article.favorites) }}</div>
          <div class="stat-label">收藏数</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ getQualityText(article.quality) }}</div>
          <div class="stat-label">质量评级</div>
        </div>
      </div>
    </div>

    <!-- 版权信息 -->
    <div class="article-footer">
      <div class="copyright-info">
        <p>
          本文内容遵循 
          <a href="#" target="_blank">知识共享 署名-相同方式共享 3.0协议</a>
          ，转载请注明出处。
        </p>
        <p>
          最后编辑时间：{{ formatTime(article.updatedAt) }}
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="WikipediaArticleDetail">
import { ref, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { useRouter } from 'vue-router';
import { useUserStore } from '/@/stores/userStore';
import { useWikipediaStore } from '/@/stores/wikipediaStore';
import SvgIcon from '/@/components/svgIcon/index.vue';

// Props
interface Props {
  article: any;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  close: [];
  edit: [article: any];
  favorite: [article: any];
  share: [article: any];
}>();

// Router
const router = useRouter();

// Stores
const userStore = useUserStore();
const wikipediaStore = useWikipediaStore();

// 响应式数据
const loading = ref(false);
const tableOfContents = ref([]);
const relatedArticles = ref([]);

// 计算属性
const canEdit = computed(() => {
  return userStore.hasPermission('edit') || props.article.author?.id === userStore.userInfo?.id;
});

const processedContent = computed(() => {
  if (!props.article.content) return '';
  
  // 处理内容，添加目录锚点等
  let content = props.article.content;
  
  // 提取标题生成目录
  const headings = content.match(/<h[1-6][^>]*>.*?<\/h[1-6]>/gi) || [];
  tableOfContents.value = headings.map((heading, index) => {
    const level = parseInt(heading.match(/<h([1-6])/)[1]);
    const title = heading.replace(/<[^>]*>/g, '');
    const id = `heading-${index}`;
    
    // 为标题添加id
    content = content.replace(heading, heading.replace('>', ` id="${id}">`));
    
    return { id, title, level };
  });
  
  return content;
});

// 方法
const toggleFavorite = () => {
  emit('favorite', props.article);
};

const shareArticle = () => {
  emit('share', props.article);
};

const editArticle = () => {
  emit('edit', props.article);
};

const scrollToSection = (id: string) => {
  const element = document.getElementById(id);
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' });
  }
};

const openRelatedArticle = (article: any) => {
  router.push(`/sanxianren/wikipedia/article/${article.id}`);
};

const getCategoryType = (category: string) => {
  const typeMap: Record<string, string> = {
    history: 'warning',
    science: 'success',
    technology: 'primary',
    culture: 'danger',
    geography: 'info',
    biography: 'success',
    arts: 'danger',
  };
  return typeMap[category] || 'primary';
};

const getCategoryIcon = (category: string) => {
  const iconMap: Record<string, string> = {
    history: 'ele-Clock',
    science: 'ele-Experiment',
    technology: 'ele-Cpu',
    culture: 'ele-Picture',
    geography: 'ele-Location',
    biography: 'ele-User',
    arts: 'ele-Brush',
  };
  return iconMap[category] || 'ele-Document';
};

const getCategoryName = (category: string) => {
  const nameMap: Record<string, string> = {
    history: '历史',
    science: '科学',
    technology: '技术',
    culture: '文化',
    geography: '地理',
    biography: '人物',
    arts: '艺术',
  };
  return nameMap[category] || '其他';
};

const getQualityText = (quality: string) => {
  const textMap: Record<string, string> = {
    stub: '小作品',
    start: '初级',
    c: '良好',
    b: '优秀',
    ga: '优良',
    fa: '特色',
  };
  return textMap[quality] || '一般';
};

const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
};

const formatTime = (time: string) => {
  return new Date(time).toLocaleString('zh-CN');
};

// 加载相关文章
const loadRelatedArticles = async () => {
  try {
    // 这里应该调用API获取相关文章
    // 暂时使用模拟数据
    relatedArticles.value = [
      {
        id: '1',
        title: '相关文章1',
        summary: '这是一篇相关文章的摘要...',
        image: 'https://via.placeholder.com/150x100',
      },
      {
        id: '2',
        title: '相关文章2',
        summary: '这是另一篇相关文章的摘要...',
        image: 'https://via.placeholder.com/150x100',
      },
    ];
  } catch (error) {
    console.error('加载相关文章失败:', error);
  }
};

// 生命周期
onMounted(() => {
  loadRelatedArticles();
});
</script>

<style scoped lang="scss">
.article-detail {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  line-height: 1.6;

  .article-header {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid var(--el-border-color-light);

    .header-content {
      margin-bottom: 16px;
    }

    .article-title {
      margin: 0 0 16px 0;
      font-size: 28px;
      font-weight: 700;
      color: var(--el-text-color-primary);
      line-height: 1.3;
    }

    .article-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      margin-bottom: 12px;

      .meta-item {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 14px;
        color: var(--el-text-color-secondary);
      }
    }

    .article-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .tag-item {
        font-size: 12px;
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;
    }
  }

  .article-image {
    margin-bottom: 30px;

    .main-image {
      width: 100%;
      max-height: 400px;
      border-radius: 8px;
      overflow: hidden;
    }

    .image-placeholder,
    .image-error {
      width: 100%;
      height: 300px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: var(--el-fill-color-light);
      color: var(--el-text-color-placeholder);
    }
  }

  .article-summary {
    margin-bottom: 30px;
    padding: 20px;
    background: var(--el-fill-color-lighter);
    border-radius: 8px;
    border-left: 4px solid var(--el-color-primary);

    h3 {
      margin: 0 0 12px 0;
      font-size: 18px;
      color: var(--el-text-color-primary);
    }

    p {
      margin: 0;
      font-size: 16px;
      color: var(--el-text-color-regular);
      line-height: 1.6;
    }
  }

  .table-of-contents {
    margin-bottom: 30px;
    padding: 20px;
    background: var(--el-fill-color-lighter);
    border-radius: 8px;

    h3 {
      margin: 0 0 16px 0;
      font-size: 18px;
      color: var(--el-text-color-primary);
    }

    .toc-list {
      margin: 0;
      padding: 0;
      list-style: none;

      li {
        margin-bottom: 8px;
        cursor: pointer;

        &.toc-level-1 { padding-left: 0; }
        &.toc-level-2 { padding-left: 20px; }
        &.toc-level-3 { padding-left: 40px; }
        &.toc-level-4 { padding-left: 60px; }
        &.toc-level-5 { padding-left: 80px; }
        &.toc-level-6 { padding-left: 100px; }

        a {
          color: var(--el-color-primary);
          text-decoration: none;
          font-size: 14px;

          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
  }

  .article-content {
    margin-bottom: 40px;

    .content-body {
      font-size: 16px;
      line-height: 1.8;
      color: var(--el-text-color-primary);

      :deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
        margin: 24px 0 16px 0;
        font-weight: 600;
        line-height: 1.4;
      }

      :deep(p) {
        margin: 16px 0;
      }

      :deep(img) {
        max-width: 100%;
        height: auto;
        border-radius: 4px;
      }

      :deep(blockquote) {
        margin: 20px 0;
        padding: 16px 20px;
        background: var(--el-fill-color-lighter);
        border-left: 4px solid var(--el-color-primary);
        font-style: italic;
      }

      :deep(code) {
        padding: 2px 6px;
        background: var(--el-fill-color-light);
        border-radius: 3px;
        font-family: 'Courier New', monospace;
        font-size: 14px;
      }

      :deep(pre) {
        padding: 16px;
        background: var(--el-fill-color-light);
        border-radius: 6px;
        overflow-x: auto;
      }
    }
  }

  .article-references {
    margin-bottom: 30px;

    h3 {
      margin: 0 0 16px 0;
      font-size: 18px;
      color: var(--el-text-color-primary);
    }

    .references-list {
      margin: 0;
      padding-left: 20px;

      .reference-item {
        margin-bottom: 8px;
        font-size: 14px;
        line-height: 1.5;

        a {
          color: var(--el-color-primary);
          text-decoration: none;

          &:hover {
            text-decoration: underline;
          }
        }

        .ref-author,
        .ref-date {
          color: var(--el-text-color-secondary);
        }
      }
    }
  }

  .related-articles {
    margin-bottom: 30px;

    h3 {
      margin: 0 0 16px 0;
      font-size: 18px;
      color: var(--el-text-color-primary);
    }

    .related-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 16px;

      .related-item {
        display: flex;
        gap: 12px;
        padding: 12px;
        background: var(--el-fill-color-lighter);
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          background: var(--el-fill-color-light);
          transform: translateY(-2px);
        }

        .related-image {
          width: 60px;
          height: 60px;
          flex-shrink: 0;
          border-radius: 4px;
          overflow: hidden;
        }

        .related-content {
          flex: 1;

          h4 {
            margin: 0 0 4px 0;
            font-size: 14px;
            font-weight: 500;
            color: var(--el-text-color-primary);
          }

          p {
            margin: 0;
            font-size: 12px;
            color: var(--el-text-color-secondary);
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }
        }
      }
    }
  }

  .article-stats {
    margin-bottom: 30px;

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: 16px;

      .stat-item {
        text-align: center;
        padding: 16px;
        background: var(--el-fill-color-lighter);
        border-radius: 8px;

        .stat-value {
          font-size: 20px;
          font-weight: 600;
          color: var(--el-color-primary);
          margin-bottom: 4px;
        }

        .stat-label {
          font-size: 12px;
          color: var(--el-text-color-secondary);
        }
      }
    }
  }

  .article-footer {
    padding-top: 20px;
    border-top: 1px solid var(--el-border-color-light);

    .copyright-info {
      font-size: 12px;
      color: var(--el-text-color-secondary);
      line-height: 1.5;

      p {
        margin: 8px 0;
      }

      a {
        color: var(--el-color-primary);
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }

  .mr4 {
    margin-right: 4px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .article-detail {
    padding: 16px;

    .article-header {
      .article-title {
        font-size: 24px;
      }

      .article-meta {
        flex-direction: column;
        gap: 8px;
      }

      .header-actions {
        flex-direction: column;

        .el-button {
          width: 100%;
        }
      }
    }

    .related-articles .related-grid {
      grid-template-columns: 1fr;
    }

    .article-stats .stats-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }
}
</style>
