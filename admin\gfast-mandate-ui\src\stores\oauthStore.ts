/**
 * OAuth认证状态管理
 * <AUTHOR> Assistant
 * @date 2025-01-22
 */

import { defineStore } from 'pinia';
import { ElMessage, ElMessageBox } from 'element-plus';
import OAuthApi, { type MediaWikiUserInfo, type OAuthStatusRes } from '/@/api/sanxianren/oauth';
import { Session } from '/@/utils/storage';

/**
 * OAuth状态接口
 */
export interface OAuthState {
  // 认证状态
  isAuthenticated: boolean;
  // MediaWiki用户信息
  userInfo: MediaWikiUserInfo | null;
  // 会话ID
  sessionId: string;
  // 过期时间
  expiresAt: string;
  // 访问令牌
  accessToken: string;
  // 刷新令牌
  refreshToken: string;
  // 加载状态
  loading: boolean;
  // 错误信息
  error: string;
  // 最后更新时间
  lastUpdated: number;
}

/**
 * OAuth Store
 */
export const useOAuthStore = defineStore('oauth', {
  state: (): OAuthState => ({
    isAuthenticated: false,
    userInfo: null,
    sessionId: '',
    expiresAt: '',
    accessToken: '',
    refreshToken: '',
    loading: false,
    error: '',
    lastUpdated: 0,
  }),

  getters: {
    /**
     * 是否已认证
     */
    isLoggedIn: (state) => state.isAuthenticated && !!state.userInfo,

    /**
     * 用户名
     */
    username: (state) => state.userInfo?.name || '',

    /**
     * 真实姓名
     */
    realName: (state) => state.userInfo?.real_name || '',

    /**
     * 邮箱
     */
    email: (state) => state.userInfo?.email || '',

    /**
     * 用户组
     */
    groups: (state) => state.userInfo?.groups || [],

    /**
     * 用户权限
     */
    rights: (state) => state.userInfo?.rights || [],

    /**
     * 编辑次数
     */
    editCount: (state) => state.userInfo?.edit_count || 0,

    /**
     * 是否有编辑权限
     */
    canEdit: (state) => state.userInfo?.rights?.includes('edit') || false,

    /**
     * 是否有创建页面权限
     */
    canCreatePage: (state) => state.userInfo?.rights?.includes('createpage') || false,

    /**
     * 是否有上传文件权限
     */
    canUpload: (state) => state.userInfo?.rights?.includes('upload') || false,

    /**
     * 令牌是否过期
     */
    isTokenExpired: (state) => {
      if (!state.expiresAt) return true;
      return new Date(state.expiresAt) <= new Date();
    },

    /**
     * 格式化的过期时间
     */
    formattedExpiresAt: (state) => {
      if (!state.expiresAt) return '';
      return new Date(state.expiresAt).toLocaleString('zh-CN');
    },

    /**
     * 认证状态摘要
     */
    statusSummary: (state) => {
      if (!state.isAuthenticated) {
        return '未认证';
      }
      if (state.isTokenExpired) {
        return '令牌已过期';
      }
      return `已认证 (${state.userInfo?.name})`;
    },
  },

  actions: {
    /**
     * 获取OAuth状态
     */
    async getStatus(): Promise<void> {
      this.loading = true;
      this.error = '';

      try {
        const response = await OAuthApi.getStatus();
        this.updateState(response);
        this.lastUpdated = Date.now();
      } catch (error: any) {
        this.error = error.message || '获取OAuth状态失败';
        console.error('获取OAuth状态失败:', error);
      } finally {
        this.loading = false;
      }
    },

    /**
     * 发起OAuth认证
     */
    async startAuthentication(returnUrl?: string): Promise<void> {
      this.loading = true;
      this.error = '';

      try {
        const response = await OAuthApi.authorize({
          return_url: returnUrl || window.location.origin + '/sanxianren/oauth/success',
        });

        if (response.auth_url) {
          // 保存当前状态到sessionStorage
          Session.set('oauth_return_url', returnUrl);
          
          // 重定向到MediaWiki授权页面
          window.location.href = response.auth_url;
        } else {
          throw new Error('获取授权URL失败');
        }
      } catch (error: any) {
        this.error = error.message || '发起OAuth认证失败';
        ElMessage.error(this.error);
        console.error('发起OAuth认证失败:', error);
      } finally {
        this.loading = false;
      }
    },

    /**
     * 处理OAuth回调
     */
    async handleCallback(code: string, state: string, error?: string, errorDescription?: string): Promise<boolean> {
      this.loading = true;
      this.error = '';

      try {
        if (error) {
          throw new Error(`OAuth认证失败: ${errorDescription || error}`);
        }

        const response = await OAuthApi.callback({
          code,
          state,
          error,
          error_description: errorDescription,
        });

        if (response.success) {
          // 更新状态
          if (response.user_info) {
            this.userInfo = response.user_info;
            this.isAuthenticated = true;
          }

          // 保存令牌
          if (response.token) {
            Session.set('oauth_token', response.token);
          }

          ElMessage.success('OAuth认证成功！');
          return true;
        } else {
          throw new Error(response.message || 'OAuth认证失败');
        }
      } catch (error: any) {
        this.error = error.message || 'OAuth回调处理失败';
        ElMessage.error(this.error);
        console.error('OAuth回调处理失败:', error);
        return false;
      } finally {
        this.loading = false;
      }
    },

    /**
     * 刷新访问令牌
     */
    async refreshToken(): Promise<boolean> {
      if (!this.refreshToken) {
        this.error = '没有可用的刷新令牌';
        return false;
      }

      this.loading = true;
      this.error = '';

      try {
        const response = await OAuthApi.refreshToken({
          refresh_token: this.refreshToken,
        });

        this.accessToken = response.access_token;
        this.refreshToken = response.refresh_token;
        this.expiresAt = new Date(Date.now() + response.expires_in * 1000).toISOString();

        ElMessage.success('令牌刷新成功');
        return true;
      } catch (error: any) {
        this.error = error.message || '刷新令牌失败';
        ElMessage.error(this.error);
        console.error('刷新令牌失败:', error);
        return false;
      } finally {
        this.loading = false;
      }
    },

    /**
     * 撤销OAuth认证
     */
    async revokeAuthentication(): Promise<boolean> {
      try {
        await ElMessageBox.confirm(
          '确定要撤销MediaWiki OAuth认证吗？撤销后将无法访问MediaWiki相关功能。',
          '确认撤销',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        );

        this.loading = true;
        this.error = '';

        try {
          if (this.accessToken) {
            await OAuthApi.revokeToken({
              access_token: this.accessToken,
            });
          }

          // 清除本地状态
          this.clearState();
          
          ElMessage.success('OAuth认证已撤销');
          return true;
        } catch (error: any) {
          this.error = error.message || '撤销OAuth认证失败';
          ElMessage.error(this.error);
          console.error('撤销OAuth认证失败:', error);
          return false;
        } finally {
          this.loading = false;
        }
      } catch {
        // 用户取消操作
        return false;
      }
    },

    /**
     * 验证访问令牌
     */
    async validateToken(): Promise<boolean> {
      if (!this.accessToken) {
        return false;
      }

      try {
        const response = await OAuthApi.validateToken({
          access_token: this.accessToken,
        });

        return response.valid;
      } catch (error) {
        console.error('验证令牌失败:', error);
        return false;
      }
    },

    /**
     * 更新状态
     */
    updateState(data: OAuthStatusRes): void {
      this.isAuthenticated = data.is_authenticated;
      this.userInfo = data.user_info || null;
      this.sessionId = data.session_id || '';
      this.expiresAt = data.expires_at || '';
    },

    /**
     * 清除状态
     */
    clearState(): void {
      this.isAuthenticated = false;
      this.userInfo = null;
      this.sessionId = '';
      this.expiresAt = '';
      this.accessToken = '';
      this.refreshToken = '';
      this.error = '';
      this.lastUpdated = 0;

      // 清除本地存储
      Session.remove('oauth_token');
      Session.remove('oauth_return_url');
    },

    /**
     * 检查并自动刷新令牌
     */
    async checkAndRefreshToken(): Promise<void> {
      if (this.isAuthenticated && this.isTokenExpired && this.refreshToken) {
        await this.refreshToken();
      }
    },

    /**
     * 初始化OAuth状态
     */
    async initialize(): Promise<void> {
      // 检查本地存储的令牌
      const token = Session.get('oauth_token');
      if (token) {
        this.accessToken = token;
      }

      // 获取最新状态
      await this.getStatus();

      // 检查并刷新令牌
      await this.checkAndRefreshToken();
    },

    /**
     * 重置错误状态
     */
    clearError(): void {
      this.error = '';
    },
  },
});
