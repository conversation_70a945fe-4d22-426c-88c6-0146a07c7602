# GFast-v3
**简介**:GFast-v3
**HOST**:
**联系人**:GFast
**Version**:
**接口路径**:
[TOC]
# AI处理
## 备份原始数据
**接口地址**:`/api/v1/ai-processing/ai-processing/backup`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "submission_uuid": "",
  "data_type": "",
  "content": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BackupOriginalDataReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BackupOriginalDataReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BackupOriginalDataReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BackupOriginalDataReq|
|&emsp;&emsp;submission_uuid|稿件UUID||true|string(string)||
|&emsp;&emsp;data_type|数据类型：factory/people/story||true|string(string)||
|&emsp;&emsp;content|原始内容||true|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 获取原始数据备份信息
**接口地址**:`/api/v1/ai-processing/ai-processing/backup/info`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|submission_uuid|稿件UUID|query|true|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 提取数据
**接口地址**:`/api/v1/ai-processing/ai-processing/extract`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "submission_uuid": "",
  "extraction_type": "",
  "source_content": "",
  "options": {
    "additionalProperties1": {}
  }
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ExtractDataReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ExtractDataReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ExtractDataReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ExtractDataReq|
|&emsp;&emsp;submission_uuid|稿件UUID||true|string(string)||
|&emsp;&emsp;extraction_type|提取类型：factory/people/story||true|string(string)||
|&emsp;&emsp;source_content|源内容||true|string(string)||
|&emsp;&emsp;options|提取选项||false|interface|interface|
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 获取AI处理结果
**接口地址**:`/api/v1/ai-processing/ai-processing/result`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|task_id|任务ID|query|true|string(string)||
|submission_uuid|稿件UUID|query|true|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 创建AI处理任务
**接口地址**:`/api/v1/ai-processing/ai-processing/task`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "submission_uuid": "",
  "processing_type": "",
  "options": {
    "additionalProperties1": {}
  }
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.CreateProcessingTaskReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.CreateProcessingTaskReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.CreateProcessingTaskReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.CreateProcessingTaskReq|
|&emsp;&emsp;submission_uuid|稿件UUID||true|string(string)||
|&emsp;&emsp;processing_type|处理类型：factory/people/story||true|string(string)||
|&emsp;&emsp;options|处理选项||false|interface|interface|
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 提交到Wikipedia
**接口地址**:`/api/v1/ai-processing/ai-processing/wikipedia`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "submission_uuid": "",
  "task_id": "",
  "page_title": "",
  "page_content": "",
  "namespace": "",
  "summary": "",
  "categories": [],
  "tags": [],
  "category": "",
  "name": "",
  "factory_code": "",
  "author": "",
  "modified_at": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.SubmitToWikipediaReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.SubmitToWikipediaReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.SubmitToWikipediaReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.SubmitToWikipediaReq|
|&emsp;&emsp;submission_uuid|稿件唯一标识||true|string(string)||
|&emsp;&emsp;task_id|AI处理任务ID||true|string(string)||
|&emsp;&emsp;page_title|页面标题（可选，不传将由服务层自动生成）||false|string(string)||
|&emsp;&emsp;page_content|页面内容（可选，不传将由服务层自动生成）||false|string(string)||
|&emsp;&emsp;namespace|命名空间，如Main/Article/User等||false|string(string)||
|&emsp;&emsp;summary|提交摘要/编辑说明||false|string(string)||
|&emsp;&emsp;categories|页面分类标签||false|array([]string)|string|
|&emsp;&emsp;tags|业务标签||false|array([]string)|string|
|&emsp;&emsp;category|业务分类：factory/people/story/oral_history/heritage_site||false|string(string)||
|&emsp;&emsp;name|主体名称，如工厂名/人物名/故事名||false|string(string)||
|&emsp;&emsp;factory_code|工厂代码，用于工厂类命名拼接||false|string(string)||
|&emsp;&emsp;author|内容作者，用于内容署名||false|string(string)||
|&emsp;&emsp;modified_at|内容修改时间，ISO8601字符串，用于内容说明||false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 启动处理工作流
**接口地址**:`/api/v1/ai-processing/ai-processing/workflow`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "submission_uuid": "",
  "workflow_type": "",
  "options": {
    "additionalProperties1": {}
  }
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.StartProcessingWorkflowReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.StartProcessingWorkflowReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.StartProcessingWorkflowReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.StartProcessingWorkflowReq|
|&emsp;&emsp;submission_uuid|稿件UUID||true|string(string)||
|&emsp;&emsp;workflow_type|工作流类型：full/ai_only/extract_only/wiki_only||true|string(string)||
|&emsp;&emsp;options|工作流选项||false|interface|interface|
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 获取工作流状态
**接口地址**:`/api/v1/ai-processing/ai-processing/workflow/status`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|workflow_id|工作流ID|query|true|string(string)||
|submission_uuid|稿件UUID|query|true|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 备份原始数据
**接口地址**:`/api/v1/sanxianren/ai-processing/ai-processing/backup`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "submission_uuid": "",
  "data_type": "",
  "content": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BackupOriginalDataReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BackupOriginalDataReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BackupOriginalDataReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BackupOriginalDataReq|
|&emsp;&emsp;submission_uuid|稿件UUID||true|string(string)||
|&emsp;&emsp;data_type|数据类型：factory/people/story||true|string(string)||
|&emsp;&emsp;content|原始内容||true|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 获取原始数据备份信息
**接口地址**:`/api/v1/sanxianren/ai-processing/ai-processing/backup/info`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|submission_uuid|稿件UUID|query|true|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 提取数据
**接口地址**:`/api/v1/sanxianren/ai-processing/ai-processing/extract`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "submission_uuid": "",
  "extraction_type": "",
  "source_content": "",
  "options": {
    "additionalProperties1": {}
  }
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ExtractDataReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ExtractDataReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ExtractDataReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ExtractDataReq|
|&emsp;&emsp;submission_uuid|稿件UUID||true|string(string)||
|&emsp;&emsp;extraction_type|提取类型：factory/people/story||true|string(string)||
|&emsp;&emsp;source_content|源内容||true|string(string)||
|&emsp;&emsp;options|提取选项||false|interface|interface|
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 获取AI处理结果
**接口地址**:`/api/v1/sanxianren/ai-processing/ai-processing/result`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|task_id|任务ID|query|true|string(string)||
|submission_uuid|稿件UUID|query|true|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 创建AI处理任务
**接口地址**:`/api/v1/sanxianren/ai-processing/ai-processing/task`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "submission_uuid": "",
  "processing_type": "",
  "options": {
    "additionalProperties1": {}
  }
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.CreateProcessingTaskReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.CreateProcessingTaskReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.CreateProcessingTaskReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.CreateProcessingTaskReq|
|&emsp;&emsp;submission_uuid|稿件UUID||true|string(string)||
|&emsp;&emsp;processing_type|处理类型：factory/people/story||true|string(string)||
|&emsp;&emsp;options|处理选项||false|interface|interface|
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 提交到Wikipedia
**接口地址**:`/api/v1/sanxianren/ai-processing/ai-processing/wikipedia`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "submission_uuid": "",
  "task_id": "",
  "page_title": "",
  "page_content": "",
  "namespace": "",
  "summary": "",
  "categories": [],
  "tags": [],
  "category": "",
  "name": "",
  "factory_code": "",
  "author": "",
  "modified_at": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.SubmitToWikipediaReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.SubmitToWikipediaReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.SubmitToWikipediaReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.SubmitToWikipediaReq|
|&emsp;&emsp;submission_uuid|稿件唯一标识||true|string(string)||
|&emsp;&emsp;task_id|AI处理任务ID||true|string(string)||
|&emsp;&emsp;page_title|页面标题（可选，不传将由服务层自动生成）||false|string(string)||
|&emsp;&emsp;page_content|页面内容（可选，不传将由服务层自动生成）||false|string(string)||
|&emsp;&emsp;namespace|命名空间，如Main/Article/User等||false|string(string)||
|&emsp;&emsp;summary|提交摘要/编辑说明||false|string(string)||
|&emsp;&emsp;categories|页面分类标签||false|array([]string)|string|
|&emsp;&emsp;tags|业务标签||false|array([]string)|string|
|&emsp;&emsp;category|业务分类：factory/people/story/oral_history/heritage_site||false|string(string)||
|&emsp;&emsp;name|主体名称，如工厂名/人物名/故事名||false|string(string)||
|&emsp;&emsp;factory_code|工厂代码，用于工厂类命名拼接||false|string(string)||
|&emsp;&emsp;author|内容作者，用于内容署名||false|string(string)||
|&emsp;&emsp;modified_at|内容修改时间，ISO8601字符串，用于内容说明||false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 启动处理工作流
**接口地址**:`/api/v1/sanxianren/ai-processing/ai-processing/workflow`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "submission_uuid": "",
  "workflow_type": "",
  "options": {
    "additionalProperties1": {}
  }
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.StartProcessingWorkflowReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.StartProcessingWorkflowReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.StartProcessingWorkflowReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.StartProcessingWorkflowReq|
|&emsp;&emsp;submission_uuid|稿件UUID||true|string(string)||
|&emsp;&emsp;workflow_type|工作流类型：full/ai_only/extract_only/wiki_only||true|string(string)||
|&emsp;&emsp;options|工作流选项||false|interface|interface|
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 获取工作流状态
**接口地址**:`/api/v1/sanxianren/ai-processing/ai-processing/workflow/status`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|workflow_id|工作流ID|query|true|string(string)||
|submission_uuid|稿件UUID|query|true|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
# 轮播图管理
## 添加轮播图
**接口地址**:`/api/v1/banner/banner/add`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "title": "",
  "imageUrl": "",
  "linkUrl": "",
  "linkType": 0,
  "position": "",
  "sortOrder": 0,
  "status": 0,
  "startTime": "",
  "endTime": "",
  "description": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerAddReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerAddReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerAddReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerAddReq|
|&emsp;&emsp;title|轮播图标题||true|string(string)||
|&emsp;&emsp;imageUrl|图片URL||true|string(string)||
|&emsp;&emsp;linkUrl|跳转链接||false|string(string)||
|&emsp;&emsp;linkType|链接类型：1-内部页面，2-外部链接，3-无链接,可用值:1,2,3||false|integer(int)||
|&emsp;&emsp;position|展示位置：home_banner-首页轮播，story_banner-故事横幅||true|string(string)||
|&emsp;&emsp;sortOrder|排序权重，数字越大越靠前||false|integer(int)||
|&emsp;&emsp;status|状态：1-启用，0-禁用,可用值:0,1||false|integer(int)||
|&emsp;&emsp;startTime|开始时间||false|string(*gtime.Time)||
|&emsp;&emsp;endTime|结束时间||false|string(*gtime.Time)||
|&emsp;&emsp;description|轮播图描述||false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerAddRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 删除轮播图
**接口地址**:`/api/v1/banner/banner/delete`
**请求方式**:`DELETE`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|ids|轮播图ID数组|query|true|array|integer|
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerDeleteRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 编辑轮播图
**接口地址**:`/api/v1/banner/banner/edit`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "id": 0,
  "title": "",
  "imageUrl": "",
  "linkUrl": "",
  "linkType": 0,
  "position": "",
  "sortOrder": 0,
  "status": 0,
  "startTime": "",
  "endTime": "",
  "description": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerEditReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerEditReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerEditReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerEditReq|
|&emsp;&emsp;id|轮播图ID||true|integer(uint64)||
|&emsp;&emsp;title|轮播图标题||true|string(string)||
|&emsp;&emsp;imageUrl|图片URL||true|string(string)||
|&emsp;&emsp;linkUrl|跳转链接||false|string(string)||
|&emsp;&emsp;linkType|链接类型：1-内部页面，2-外部链接，3-无链接,可用值:1,2,3||false|integer(int)||
|&emsp;&emsp;position|展示位置：home_banner-首页轮播，story_banner-故事横幅||true|string(string)||
|&emsp;&emsp;sortOrder|排序权重，数字越大越靠前||false|integer(int)||
|&emsp;&emsp;status|状态：1-启用，0-禁用,可用值:0,1||false|integer(int)||
|&emsp;&emsp;startTime|开始时间||false|string(*gtime.Time)||
|&emsp;&emsp;endTime|结束时间||false|string(*gtime.Time)||
|&emsp;&emsp;description|轮播图描述||false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerEditRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 获取轮播图详情
**接口地址**:`/api/v1/banner/banner/get`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id|轮播图ID|query|true|integer(uint64)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerGetRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|id|轮播图ID|integer(uint64)|integer(uint64)|
|title|轮播图标题|string(string)|string(string)|
|imageUrl|图片URL|string(string)|string(string)|
|linkUrl|跳转链接|string(string)|string(string)|
|linkType|链接类型：1-内部页面，2-外部链接，3-无链接|integer(int)|integer(int)|
|position|展示位置：home_banner-首页轮播，story_banner-故事横幅|string(string)|string(string)|
|sortOrder|排序权重，数字越大越靠前|integer(int)|integer(int)|
|status|状态：1-启用，0-禁用|integer(int)|integer(int)|
|startTime|开始时间|string(*gtime.Time)|string(*gtime.Time)|
|endTime|结束时间|string(*gtime.Time)|string(*gtime.Time)|
|clickCount|点击次数|integer(int)|integer(int)|
|viewCount|展示次数|integer(int)|integer(int)|
|description|轮播图描述|string(string)|string(string)|
|createdAt|创建时间|string(*gtime.Time)|string(*gtime.Time)|
|updatedAt|更新时间|string(*gtime.Time)|string(*gtime.Time)|
**响应示例**:
```javascript
{
	"id": 0,
	"title": "",
	"imageUrl": "",
	"linkUrl": "",
	"linkType": 0,
	"position": "",
	"sortOrder": 0,
	"status": 0,
	"startTime": "",
	"endTime": "",
	"clickCount": 0,
	"viewCount": 0,
	"description": "",
	"createdAt": "",
	"updatedAt": ""
}
```
## 轮播图列表
**接口地址**:`/api/v1/banner/banner/list`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|title|轮播图标题|query|false|string(string)||
|position|展示位置|query|false|string(string)||
|status|状态：1-启用，0-禁用|query|false|integer(*int)||
|pageNum|页码|query|false|integer(int)||
|pageSize|每页数量|query|false|integer(int)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerSearchRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|list||interface|interface|
|page||integer(int)|integer(int)|
|size||integer(int)|integer(int)|
|total||integer(int)|integer(int)|
|pageSize||integer(int)|integer(int)|
|currentPage||integer(int)|integer(int)|
**响应示例**:
```javascript
{
	"list": {},
	"page": 0,
	"size": 0,
	"total": 0,
	"pageSize": 0,
	"currentPage": 0
}
```
## 更新轮播图状态
**接口地址**:`/api/v1/banner/banner/status`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "id": 0,
  "status": 0
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerStatusReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerStatusReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerStatusReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerStatusReq|
|&emsp;&emsp;id|轮播图ID||true|integer(uint64)||
|&emsp;&emsp;status|状态：1-启用，0-禁用,可用值:0,1||false|integer(int)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerStatusRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 轮播图点击统计
**接口地址**:`/api/v1/public/public/banner/click`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "id": 0
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerClickReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerClickReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerClickReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerClickReq|
|&emsp;&emsp;id|轮播图ID||true|integer(uint64)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerClickRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 公开轮播图列表
**接口地址**:`/api/v1/public/public/banner/list`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|position|展示位置：home_banner-首页轮播，story_banner-故事横幅|query|true|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerPublicListRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|list|轮播图列表|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerPublicItem|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerPublicItem|
|&emsp;&emsp;id|轮播图ID|integer(uint64)||
|&emsp;&emsp;title|轮播图标题|string(string)||
|&emsp;&emsp;imageUrl|图片URL|string(string)||
|&emsp;&emsp;linkUrl|跳转链接|string(string)||
|&emsp;&emsp;linkType|链接类型：1-内部页面，2-外部链接，3-无链接|integer(int)||
|&emsp;&emsp;description|轮播图描述|string(string)||
**响应示例**:
```javascript
{
	"list": [
		{
			"id": 0,
			"title": "",
			"imageUrl": "",
			"linkUrl": "",
			"linkType": 0,
			"description": ""
		}
	]
}
```
## 添加轮播图
**接口地址**:`/api/v1/sanxianren/banner/banner/add`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "title": "",
  "imageUrl": "",
  "linkUrl": "",
  "linkType": 0,
  "position": "",
  "sortOrder": 0,
  "status": 0,
  "startTime": "",
  "endTime": "",
  "description": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerAddReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerAddReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerAddReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerAddReq|
|&emsp;&emsp;title|轮播图标题||true|string(string)||
|&emsp;&emsp;imageUrl|图片URL||true|string(string)||
|&emsp;&emsp;linkUrl|跳转链接||false|string(string)||
|&emsp;&emsp;linkType|链接类型：1-内部页面，2-外部链接，3-无链接,可用值:1,2,3||false|integer(int)||
|&emsp;&emsp;position|展示位置：home_banner-首页轮播，story_banner-故事横幅||true|string(string)||
|&emsp;&emsp;sortOrder|排序权重，数字越大越靠前||false|integer(int)||
|&emsp;&emsp;status|状态：1-启用，0-禁用,可用值:0,1||false|integer(int)||
|&emsp;&emsp;startTime|开始时间||false|string(*gtime.Time)||
|&emsp;&emsp;endTime|结束时间||false|string(*gtime.Time)||
|&emsp;&emsp;description|轮播图描述||false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerAddRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 删除轮播图
**接口地址**:`/api/v1/sanxianren/banner/banner/delete`
**请求方式**:`DELETE`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|ids|轮播图ID数组|query|true|array|integer|
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerDeleteRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 编辑轮播图
**接口地址**:`/api/v1/sanxianren/banner/banner/edit`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "id": 0,
  "title": "",
  "imageUrl": "",
  "linkUrl": "",
  "linkType": 0,
  "position": "",
  "sortOrder": 0,
  "status": 0,
  "startTime": "",
  "endTime": "",
  "description": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerEditReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerEditReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerEditReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerEditReq|
|&emsp;&emsp;id|轮播图ID||true|integer(uint64)||
|&emsp;&emsp;title|轮播图标题||true|string(string)||
|&emsp;&emsp;imageUrl|图片URL||true|string(string)||
|&emsp;&emsp;linkUrl|跳转链接||false|string(string)||
|&emsp;&emsp;linkType|链接类型：1-内部页面，2-外部链接，3-无链接,可用值:1,2,3||false|integer(int)||
|&emsp;&emsp;position|展示位置：home_banner-首页轮播，story_banner-故事横幅||true|string(string)||
|&emsp;&emsp;sortOrder|排序权重，数字越大越靠前||false|integer(int)||
|&emsp;&emsp;status|状态：1-启用，0-禁用,可用值:0,1||false|integer(int)||
|&emsp;&emsp;startTime|开始时间||false|string(*gtime.Time)||
|&emsp;&emsp;endTime|结束时间||false|string(*gtime.Time)||
|&emsp;&emsp;description|轮播图描述||false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerEditRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 获取轮播图详情
**接口地址**:`/api/v1/sanxianren/banner/banner/get`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id|轮播图ID|query|true|integer(uint64)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerGetRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|id|轮播图ID|integer(uint64)|integer(uint64)|
|title|轮播图标题|string(string)|string(string)|
|imageUrl|图片URL|string(string)|string(string)|
|linkUrl|跳转链接|string(string)|string(string)|
|linkType|链接类型：1-内部页面，2-外部链接，3-无链接|integer(int)|integer(int)|
|position|展示位置：home_banner-首页轮播，story_banner-故事横幅|string(string)|string(string)|
|sortOrder|排序权重，数字越大越靠前|integer(int)|integer(int)|
|status|状态：1-启用，0-禁用|integer(int)|integer(int)|
|startTime|开始时间|string(*gtime.Time)|string(*gtime.Time)|
|endTime|结束时间|string(*gtime.Time)|string(*gtime.Time)|
|clickCount|点击次数|integer(int)|integer(int)|
|viewCount|展示次数|integer(int)|integer(int)|
|description|轮播图描述|string(string)|string(string)|
|createdAt|创建时间|string(*gtime.Time)|string(*gtime.Time)|
|updatedAt|更新时间|string(*gtime.Time)|string(*gtime.Time)|
**响应示例**:
```javascript
{
	"id": 0,
	"title": "",
	"imageUrl": "",
	"linkUrl": "",
	"linkType": 0,
	"position": "",
	"sortOrder": 0,
	"status": 0,
	"startTime": "",
	"endTime": "",
	"clickCount": 0,
	"viewCount": 0,
	"description": "",
	"createdAt": "",
	"updatedAt": ""
}
```
## 轮播图列表
**接口地址**:`/api/v1/sanxianren/banner/banner/list`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|title|轮播图标题|query|false|string(string)||
|position|展示位置|query|false|string(string)||
|status|状态：1-启用，0-禁用|query|false|integer(*int)||
|pageNum|页码|query|false|integer(int)||
|pageSize|每页数量|query|false|integer(int)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerSearchRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|list||interface|interface|
|page||integer(int)|integer(int)|
|size||integer(int)|integer(int)|
|total||integer(int)|integer(int)|
|pageSize||integer(int)|integer(int)|
|currentPage||integer(int)|integer(int)|
**响应示例**:
```javascript
{
	"list": {},
	"page": 0,
	"size": 0,
	"total": 0,
	"pageSize": 0,
	"currentPage": 0
}
```
## 更新轮播图状态
**接口地址**:`/api/v1/sanxianren/banner/banner/status`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "id": 0,
  "status": 0
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerStatusReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerStatusReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerStatusReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerStatusReq|
|&emsp;&emsp;id|轮播图ID||true|integer(uint64)||
|&emsp;&emsp;status|状态：1-启用，0-禁用,可用值:0,1||false|integer(int)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerStatusRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 轮播图点击统计
**接口地址**:`/api/v1/sanxianren/public/public/banner/click`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "id": 0
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerClickReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerClickReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerClickReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerClickReq|
|&emsp;&emsp;id|轮播图ID||true|integer(uint64)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerClickRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 公开轮播图列表
**接口地址**:`/api/v1/sanxianren/public/public/banner/list`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|position|展示位置：home_banner-首页轮播，story_banner-故事横幅|query|true|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerPublicListRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|list|轮播图列表|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerPublicItem|github.com.tiger1103.gfast.v3.api.v1.sanxianren.BannerPublicItem|
|&emsp;&emsp;id|轮播图ID|integer(uint64)||
|&emsp;&emsp;title|轮播图标题|string(string)||
|&emsp;&emsp;imageUrl|图片URL|string(string)||
|&emsp;&emsp;linkUrl|跳转链接|string(string)||
|&emsp;&emsp;linkType|链接类型：1-内部页面，2-外部链接，3-无链接|integer(int)||
|&emsp;&emsp;description|轮播图描述|string(string)||
**响应示例**:
```javascript
{
	"list": [
		{
			"id": 0,
			"title": "",
			"imageUrl": "",
			"linkUrl": "",
			"linkType": 0,
			"description": ""
		}
	]
}
```
# 分类管理
## 添加分类
**接口地址**:`/api/v1/category/category/add`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "name": "",
  "slug": "",
  "type": "",
  "parent_id": 0,
  "sort_order": 0,
  "icon": "",
  "color": "",
  "description": "",
  "keywords": "",
  "is_hot": 0,
  "is_recommend": 0,
  "status": 1
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.CategoryAddReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.CategoryAddReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.CategoryAddReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.CategoryAddReq|
|&emsp;&emsp;name|分类名称||true|string(string)||
|&emsp;&emsp;slug|分类标识（英文）||true|string(string)||
|&emsp;&emsp;type|分类类型,可用值:story,factory,people,general||true|string(string)||
|&emsp;&emsp;parent_id|父分类ID（0为顶级分类）||false|integer(uint64)||
|&emsp;&emsp;sort_order|排序权重||false|integer(int)||
|&emsp;&emsp;icon|分类图标||false|string(string)||
|&emsp;&emsp;color|分类颜色||false|string(string)||
|&emsp;&emsp;description|分类描述||false|string(string)||
|&emsp;&emsp;keywords|关键词||false|string(string)||
|&emsp;&emsp;is_hot|是否热门,可用值:0,1||false|integer(int)||
|&emsp;&emsp;is_recommend|是否推荐,可用值:0,1||false|integer(int)||
|&emsp;&emsp;status|状态,可用值:0,1||true|integer(int)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 删除分类
**接口地址**:`/api/v1/category/category/delete`
**请求方式**:`DELETE`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|ids|分类ID数组|query|true|array|integer|
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 编辑分类
**接口地址**:`/api/v1/category/category/edit`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "id": 0,
  "name": "",
  "slug": "",
  "type": "",
  "parent_id": 0,
  "sort_order": 0,
  "icon": "",
  "color": "",
  "description": "",
  "keywords": "",
  "is_hot": 0,
  "is_recommend": 0,
  "status": 1
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.CategoryEditReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.CategoryEditReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.CategoryEditReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.CategoryEditReq|
|&emsp;&emsp;id|分类ID||true|integer(uint64)||
|&emsp;&emsp;name|分类名称||true|string(string)||
|&emsp;&emsp;slug|分类标识（英文）||true|string(string)||
|&emsp;&emsp;type|分类类型,可用值:story,factory,people,general||true|string(string)||
|&emsp;&emsp;parent_id|父分类ID（0为顶级分类）||false|integer(uint64)||
|&emsp;&emsp;sort_order|排序权重||false|integer(int)||
|&emsp;&emsp;icon|分类图标||false|string(string)||
|&emsp;&emsp;color|分类颜色||false|string(string)||
|&emsp;&emsp;description|分类描述||false|string(string)||
|&emsp;&emsp;keywords|关键词||false|string(string)||
|&emsp;&emsp;is_hot|是否热门,可用值:0,1||false|integer(int)||
|&emsp;&emsp;is_recommend|是否推荐,可用值:0,1||false|integer(int)||
|&emsp;&emsp;status|状态,可用值:0,1||true|integer(int)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 获取分类详情
**接口地址**:`/api/v1/category/category/get`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id|分类ID|query|true|integer(uint64)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 分类列表
**接口地址**:`/api/v1/category/category/list`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|name|分类名称|query|false|string(string)||
|type|分类类型（story/factory/people/general）|query|false|string(string)||
|parent_id|父分类ID|query|false|integer(*uint64)||
|level|分类层级|query|false|integer(*int)||
|is_hot|是否热门（0否 1是）|query|false|integer(*int)||
|is_recommend|是否推荐（0否 1是）|query|false|integer(*int)||
|status|状态（0禁用 1启用）|query|false|integer(*int)||
|page_num|页码|query|false|integer(int)||
|page_size|每页数量|query|false|integer(int)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 更新分类状态
**接口地址**:`/api/v1/category/category/status`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "ids": [],
  "status": 0
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.CategoryStatusReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.CategoryStatusReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.CategoryStatusReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.CategoryStatusReq|
|&emsp;&emsp;ids|分类ID数组||true|array([]uint64)|integer|
|&emsp;&emsp;status|状态（0禁用 1启用）,可用值:0,1||true|integer(int)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 获取分类树
**接口地址**:`/api/v1/category/category/tree`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|type|分类类型（story/factory/people/general）|query|false|string(string)||
|status|状态（0禁用 1启用）|query|false|integer(*int)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
# 推荐系统管理
## 添加推荐算法配置
**接口地址**:`/api/v1/content-recommendation/config/config/add`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "configKey": "",
  "configValue": "",
  "name": "",
  "type": "",
  "algorithm": "",
  "weight": 0,
  "parameters": "",
  "isActive": 0,
  "status": 0,
  "description": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.RecommendationConfigAddReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.RecommendationConfigAddReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.RecommendationConfigAddReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.RecommendationConfigAddReq|
|&emsp;&emsp;configKey|配置键||true|string(string)||
|&emsp;&emsp;configValue|配置值||true|string(string)||
|&emsp;&emsp;name|配置名称||true|string(string)||
|&emsp;&emsp;type|推荐类型,可用值:content,user,factory||true|string(string)||
|&emsp;&emsp;algorithm|算法类型,可用值:collaborative,content_based,hybrid||true|string(string)||
|&emsp;&emsp;weight|权重值||true|number(float64)||
|&emsp;&emsp;parameters|算法参数||false|string(string)||
|&emsp;&emsp;isActive|是否激活,可用值:0,1||false|integer(*int)||
|&emsp;&emsp;status|状态,可用值:0,1||true|integer(int)||
|&emsp;&emsp;description|配置描述||false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.RecommendationConfigAddRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 删除推荐算法配置
**接口地址**:`/api/v1/content-recommendation/config/config/delete`
**请求方式**:`DELETE`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|ids|配置ID列表|query|true|array|integer|
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.RecommendationConfigDeleteRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 获取推荐算法配置详情
**接口地址**:`/api/v1/content-recommendation/config/config/detail`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id|配置ID|query|true|integer(uint64)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 编辑推荐算法配置
**接口地址**:`/api/v1/content-recommendation/config/config/edit`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "id": 0,
  "configKey": "",
  "configValue": "",
  "name": "",
  "type": "",
  "algorithm": "",
  "weight": 0,
  "parameters": "",
  "isActive": 0,
  "status": 0,
  "description": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.RecommendationConfigEditReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.RecommendationConfigEditReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.RecommendationConfigEditReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.RecommendationConfigEditReq|
|&emsp;&emsp;id|配置ID||true|integer(uint64)||
|&emsp;&emsp;configKey|配置键||true|string(string)||
|&emsp;&emsp;configValue|配置值||true|string(string)||
|&emsp;&emsp;name|配置名称||true|string(string)||
|&emsp;&emsp;type|推荐类型,可用值:content,user,factory||true|string(string)||
|&emsp;&emsp;algorithm|算法类型,可用值:collaborative,content_based,hybrid||true|string(string)||
|&emsp;&emsp;weight|权重值||true|number(float64)||
|&emsp;&emsp;parameters|算法参数||false|string(string)||
|&emsp;&emsp;isActive|是否激活,可用值:0,1||false|integer(*int)||
|&emsp;&emsp;status|状态,可用值:0,1||true|integer(int)||
|&emsp;&emsp;description|配置描述||false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.RecommendationConfigEditRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 获取推荐算法配置列表
**接口地址**:`/api/v1/content-recommendation/config/config/list`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|name|配置名称|query|false|string(string)||
|configKey|配置键|query|false|string(string)||
|type|推荐类型,可用值:content,user,factory|query|false|string(string)||
|algorithm|算法类型,可用值:collaborative,content_based,hybrid|query|false|string(string)||
|is_active|是否激活：1-激活，0-未激活,可用值:0,1|query|false|integer(*int)||
|status|状态：0-禁用,1-启用,可用值:0,1|query|false|integer(*int)||
|page|页码|query|false|integer(int)||
|page_num|页码|query|false|integer(int)||
|page_size|每页数量|query|false|integer(int)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 添加推荐算法配置
**接口地址**:`/api/v1/sanxianren/content-recommendation/config/config/add`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "configKey": "",
  "configValue": "",
  "name": "",
  "type": "",
  "algorithm": "",
  "weight": 0,
  "parameters": "",
  "isActive": 0,
  "status": 0,
  "description": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.RecommendationConfigAddReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.RecommendationConfigAddReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.RecommendationConfigAddReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.RecommendationConfigAddReq|
|&emsp;&emsp;configKey|配置键||true|string(string)||
|&emsp;&emsp;configValue|配置值||true|string(string)||
|&emsp;&emsp;name|配置名称||true|string(string)||
|&emsp;&emsp;type|推荐类型,可用值:content,user,factory||true|string(string)||
|&emsp;&emsp;algorithm|算法类型,可用值:collaborative,content_based,hybrid||true|string(string)||
|&emsp;&emsp;weight|权重值||true|number(float64)||
|&emsp;&emsp;parameters|算法参数||false|string(string)||
|&emsp;&emsp;isActive|是否激活,可用值:0,1||false|integer(*int)||
|&emsp;&emsp;status|状态,可用值:0,1||true|integer(int)||
|&emsp;&emsp;description|配置描述||false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.RecommendationConfigAddRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 删除推荐算法配置
**接口地址**:`/api/v1/sanxianren/content-recommendation/config/config/delete`
**请求方式**:`DELETE`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|ids|配置ID列表|query|true|array|integer|
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.RecommendationConfigDeleteRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 获取推荐算法配置详情
**接口地址**:`/api/v1/sanxianren/content-recommendation/config/config/detail`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id|配置ID|query|true|integer(uint64)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 编辑推荐算法配置
**接口地址**:`/api/v1/sanxianren/content-recommendation/config/config/edit`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "id": 0,
  "configKey": "",
  "configValue": "",
  "name": "",
  "type": "",
  "algorithm": "",
  "weight": 0,
  "parameters": "",
  "isActive": 0,
  "status": 0,
  "description": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.RecommendationConfigEditReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.RecommendationConfigEditReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.RecommendationConfigEditReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.RecommendationConfigEditReq|
|&emsp;&emsp;id|配置ID||true|integer(uint64)||
|&emsp;&emsp;configKey|配置键||true|string(string)||
|&emsp;&emsp;configValue|配置值||true|string(string)||
|&emsp;&emsp;name|配置名称||true|string(string)||
|&emsp;&emsp;type|推荐类型,可用值:content,user,factory||true|string(string)||
|&emsp;&emsp;algorithm|算法类型,可用值:collaborative,content_based,hybrid||true|string(string)||
|&emsp;&emsp;weight|权重值||true|number(float64)||
|&emsp;&emsp;parameters|算法参数||false|string(string)||
|&emsp;&emsp;isActive|是否激活,可用值:0,1||false|integer(*int)||
|&emsp;&emsp;status|状态,可用值:0,1||true|integer(int)||
|&emsp;&emsp;description|配置描述||false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.RecommendationConfigEditRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 获取推荐算法配置列表
**接口地址**:`/api/v1/sanxianren/content-recommendation/config/config/list`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|name|配置名称|query|false|string(string)||
|configKey|配置键|query|false|string(string)||
|type|推荐类型,可用值:content,user,factory|query|false|string(string)||
|algorithm|算法类型,可用值:collaborative,content_based,hybrid|query|false|string(string)||
|is_active|是否激活：1-激活，0-未激活,可用值:0,1|query|false|integer(*int)||
|status|状态：0-禁用,1-启用,可用值:0,1|query|false|integer(*int)||
|page|页码|query|false|integer(int)||
|page_num|页码|query|false|integer(int)||
|page_size|每页数量|query|false|integer(int)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
# 内容推荐管理
## 添加内容推荐
**接口地址**:`/api/v1/content-recommendation/content-recommendation/add`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "title": "",
  "contentType": "",
  "contentId": 0,
  "position": "",
  "sortOrder": 0,
  "status": 0,
  "startTime": "",
  "endTime": "",
  "reason": "",
  "tags": "",
  "weight": 0,
  "algorithm": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationAddReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationAddReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationAddReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationAddReq|
|&emsp;&emsp;title|推荐标题||true|string(string)||
|&emsp;&emsp;contentType|内容类型：story-故事，factory-工厂，people-人物,可用值:story,factory,people||true|string(string)||
|&emsp;&emsp;contentId|内容ID||true|integer(uint64)||
|&emsp;&emsp;position|推荐位置：home_featured-首页精选，story_hot-热门故事，factory_hot-热门工厂，people_hot-热门人物||true|string(string)||
|&emsp;&emsp;sortOrder|排序权重，数字越大越靠前||false|integer(int)||
|&emsp;&emsp;status|状态：1-启用，0-禁用,可用值:0,1||false|integer(int)||
|&emsp;&emsp;startTime|开始时间||false|string(*gtime.Time)||
|&emsp;&emsp;endTime|结束时间||false|string(*gtime.Time)||
|&emsp;&emsp;reason|推荐理由||false|string(string)||
|&emsp;&emsp;tags|推荐标签，JSON格式||false|string(string)||
|&emsp;&emsp;weight|推荐权重||false|number(float64)||
|&emsp;&emsp;algorithm|推荐算法：manual-手动，hot-热度，new-最新，recommend-智能推荐,可用值:manual,hot,new,recommend||true|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationAddRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 删除内容推荐
**接口地址**:`/api/v1/content-recommendation/content-recommendation/delete`
**请求方式**:`DELETE`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|ids|推荐ID数组|query|true|array|integer|
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationDeleteRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 编辑内容推荐
**接口地址**:`/api/v1/content-recommendation/content-recommendation/edit`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "id": 0,
  "title": "",
  "contentType": "",
  "contentId": 0,
  "position": "",
  "sortOrder": 0,
  "status": 0,
  "startTime": "",
  "endTime": "",
  "reason": "",
  "tags": "",
  "weight": 0,
  "algorithm": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationEditReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationEditReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationEditReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationEditReq|
|&emsp;&emsp;id|推荐ID||true|integer(uint64)||
|&emsp;&emsp;title|推荐标题||true|string(string)||
|&emsp;&emsp;contentType|内容类型：story-故事，factory-工厂，people-人物,可用值:story,factory,people||true|string(string)||
|&emsp;&emsp;contentId|内容ID||true|integer(uint64)||
|&emsp;&emsp;position|推荐位置：home_featured-首页精选，story_hot-热门故事，factory_hot-热门工厂，people_hot-热门人物||true|string(string)||
|&emsp;&emsp;sortOrder|排序权重，数字越大越靠前||false|integer(int)||
|&emsp;&emsp;status|状态：1-启用，0-禁用,可用值:0,1||false|integer(int)||
|&emsp;&emsp;startTime|开始时间||false|string(*gtime.Time)||
|&emsp;&emsp;endTime|结束时间||false|string(*gtime.Time)||
|&emsp;&emsp;reason|推荐理由||false|string(string)||
|&emsp;&emsp;tags|推荐标签，JSON格式||false|string(string)||
|&emsp;&emsp;weight|推荐权重||false|number(float64)||
|&emsp;&emsp;algorithm|推荐算法：manual-手动，hot-热度，new-最新，recommend-智能推荐,可用值:manual,hot,new,recommend||true|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationEditRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 获取内容推荐详情
**接口地址**:`/api/v1/content-recommendation/content-recommendation/get`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id|推荐ID|query|true|integer(uint64)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationGetRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|id|推荐ID|integer(uint64)|integer(uint64)|
|title|推荐标题|string(string)|string(string)|
|contentType|内容类型：story-故事，factory-工厂，people-人物|string(string)|string(string)|
|contentId|内容ID|integer(uint64)|integer(uint64)|
|position|推荐位置|string(string)|string(string)|
|sortOrder|排序权重，数字越大越靠前|integer(int)|integer(int)|
|status|状态：1-启用，0-禁用|integer(int)|integer(int)|
|startTime|开始时间|string(*gtime.Time)|string(*gtime.Time)|
|endTime|结束时间|string(*gtime.Time)|string(*gtime.Time)|
|clickCount|点击次数|integer(int)|integer(int)|
|viewCount|展示次数|integer(int)|integer(int)|
|reason|推荐理由|string(string)|string(string)|
|tags|推荐标签，JSON格式|string(string)|string(string)|
|weight|推荐权重|number(float64)|number(float64)|
|algorithm|推荐算法|string(string)|string(string)|
|createdAt|创建时间|string(*gtime.Time)|string(*gtime.Time)|
|updatedAt|更新时间|string(*gtime.Time)|string(*gtime.Time)|
**响应示例**:
```javascript
{
	"id": 0,
	"title": "",
	"contentType": "",
	"contentId": 0,
	"position": "",
	"sortOrder": 0,
	"status": 0,
	"startTime": "",
	"endTime": "",
	"clickCount": 0,
	"viewCount": 0,
	"reason": "",
	"tags": "",
	"weight": 0,
	"algorithm": "",
	"createdAt": "",
	"updatedAt": ""
}
```
## 内容推荐列表
**接口地址**:`/api/v1/content-recommendation/content-recommendation/list`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|title|推荐标题|query|false|string(string)||
|contentType|内容类型：story-故事，factory-工厂，people-人物|query|false|string(string)||
|position|推荐位置|query|false|string(string)||
|algorithm|推荐算法|query|false|string(string)||
|status|状态：1-启用，0-禁用|query|false|integer(*int)||
|startTime|开始时间|query|false|string(string)||
|endTime|结束时间|query|false|string(string)||
|pageNum|页码|query|false|integer(int)||
|pageSize|每页数量|query|false|integer(int)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationSearchRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|list||interface|interface|
|page||integer(int)|integer(int)|
|size||integer(int)|integer(int)|
|total||integer(int)|integer(int)|
|currentPage||integer(int)|integer(int)|
**响应示例**:
```javascript
{
	"list": {},
	"page": 0,
	"size": 0,
	"total": 0,
	"currentPage": 0
}
```
## 更新内容推荐状态
**接口地址**:`/api/v1/content-recommendation/content-recommendation/status`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "ids": [],
  "status": 0
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationStatusReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationStatusReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationStatusReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationStatusReq|
|&emsp;&emsp;ids|推荐ID数组||true|array([]uint64)|integer|
|&emsp;&emsp;status|状态：1-启用，0-禁用,可用值:0,1||false|integer(int)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationStatusRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 内容推荐点击统计
**接口地址**:`/api/v1/public/content-recommendation/public/content-recommendation/click`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "id": 0
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationClickReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationClickReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationClickReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationClickReq|
|&emsp;&emsp;id|推荐ID||true|integer(uint64)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationClickRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 公开内容推荐列表
**接口地址**:`/api/v1/public/content-recommendation/public/content-recommendation/list`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|position|推荐位置|query|true|string(string)||
|contentType|内容类型|query|false|string(string)||
|pageNum|页码|query|false|integer(int)||
|pageSize|每页数量|query|false|integer(int)||
|limit|限制数量|query|false|integer(int)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.PublicContentRecommendationRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|list|推荐列表|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationPublicItem|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationPublicItem|
|&emsp;&emsp;id|推荐ID|integer(uint64)||
|&emsp;&emsp;title|推荐标题|string(string)||
|&emsp;&emsp;contentType|内容类型|string(string)||
|&emsp;&emsp;contentId|内容ID|integer(uint64)||
|&emsp;&emsp;reason|推荐理由|string(string)||
|&emsp;&emsp;tags|推荐标签|string(string)||
|&emsp;&emsp;weight|推荐权重|number(float64)||
|&emsp;&emsp;algorithm|推荐算法|string(string)||
**响应示例**:
```javascript
{
	"list": [
		{
			"id": 0,
			"title": "",
			"contentType": "",
			"contentId": 0,
			"reason": "",
			"tags": "",
			"weight": 0,
			"algorithm": ""
		}
	]
}
```
## 添加内容推荐
**接口地址**:`/api/v1/sanxianren/content-recommendation/content-recommendation/add`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "title": "",
  "contentType": "",
  "contentId": 0,
  "position": "",
  "sortOrder": 0,
  "status": 0,
  "startTime": "",
  "endTime": "",
  "reason": "",
  "tags": "",
  "weight": 0,
  "algorithm": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationAddReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationAddReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationAddReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationAddReq|
|&emsp;&emsp;title|推荐标题||true|string(string)||
|&emsp;&emsp;contentType|内容类型：story-故事，factory-工厂，people-人物,可用值:story,factory,people||true|string(string)||
|&emsp;&emsp;contentId|内容ID||true|integer(uint64)||
|&emsp;&emsp;position|推荐位置：home_featured-首页精选，story_hot-热门故事，factory_hot-热门工厂，people_hot-热门人物||true|string(string)||
|&emsp;&emsp;sortOrder|排序权重，数字越大越靠前||false|integer(int)||
|&emsp;&emsp;status|状态：1-启用，0-禁用,可用值:0,1||false|integer(int)||
|&emsp;&emsp;startTime|开始时间||false|string(*gtime.Time)||
|&emsp;&emsp;endTime|结束时间||false|string(*gtime.Time)||
|&emsp;&emsp;reason|推荐理由||false|string(string)||
|&emsp;&emsp;tags|推荐标签，JSON格式||false|string(string)||
|&emsp;&emsp;weight|推荐权重||false|number(float64)||
|&emsp;&emsp;algorithm|推荐算法：manual-手动，hot-热度，new-最新，recommend-智能推荐,可用值:manual,hot,new,recommend||true|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationAddRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 删除内容推荐
**接口地址**:`/api/v1/sanxianren/content-recommendation/content-recommendation/delete`
**请求方式**:`DELETE`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|ids|推荐ID数组|query|true|array|integer|
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationDeleteRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 编辑内容推荐
**接口地址**:`/api/v1/sanxianren/content-recommendation/content-recommendation/edit`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "id": 0,
  "title": "",
  "contentType": "",
  "contentId": 0,
  "position": "",
  "sortOrder": 0,
  "status": 0,
  "startTime": "",
  "endTime": "",
  "reason": "",
  "tags": "",
  "weight": 0,
  "algorithm": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationEditReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationEditReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationEditReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationEditReq|
|&emsp;&emsp;id|推荐ID||true|integer(uint64)||
|&emsp;&emsp;title|推荐标题||true|string(string)||
|&emsp;&emsp;contentType|内容类型：story-故事，factory-工厂，people-人物,可用值:story,factory,people||true|string(string)||
|&emsp;&emsp;contentId|内容ID||true|integer(uint64)||
|&emsp;&emsp;position|推荐位置：home_featured-首页精选，story_hot-热门故事，factory_hot-热门工厂，people_hot-热门人物||true|string(string)||
|&emsp;&emsp;sortOrder|排序权重，数字越大越靠前||false|integer(int)||
|&emsp;&emsp;status|状态：1-启用，0-禁用,可用值:0,1||false|integer(int)||
|&emsp;&emsp;startTime|开始时间||false|string(*gtime.Time)||
|&emsp;&emsp;endTime|结束时间||false|string(*gtime.Time)||
|&emsp;&emsp;reason|推荐理由||false|string(string)||
|&emsp;&emsp;tags|推荐标签，JSON格式||false|string(string)||
|&emsp;&emsp;weight|推荐权重||false|number(float64)||
|&emsp;&emsp;algorithm|推荐算法：manual-手动，hot-热度，new-最新，recommend-智能推荐,可用值:manual,hot,new,recommend||true|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationEditRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 获取内容推荐详情
**接口地址**:`/api/v1/sanxianren/content-recommendation/content-recommendation/get`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id|推荐ID|query|true|integer(uint64)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationGetRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|id|推荐ID|integer(uint64)|integer(uint64)|
|title|推荐标题|string(string)|string(string)|
|contentType|内容类型：story-故事，factory-工厂，people-人物|string(string)|string(string)|
|contentId|内容ID|integer(uint64)|integer(uint64)|
|position|推荐位置|string(string)|string(string)|
|sortOrder|排序权重，数字越大越靠前|integer(int)|integer(int)|
|status|状态：1-启用，0-禁用|integer(int)|integer(int)|
|startTime|开始时间|string(*gtime.Time)|string(*gtime.Time)|
|endTime|结束时间|string(*gtime.Time)|string(*gtime.Time)|
|clickCount|点击次数|integer(int)|integer(int)|
|viewCount|展示次数|integer(int)|integer(int)|
|reason|推荐理由|string(string)|string(string)|
|tags|推荐标签，JSON格式|string(string)|string(string)|
|weight|推荐权重|number(float64)|number(float64)|
|algorithm|推荐算法|string(string)|string(string)|
|createdAt|创建时间|string(*gtime.Time)|string(*gtime.Time)|
|updatedAt|更新时间|string(*gtime.Time)|string(*gtime.Time)|
**响应示例**:
```javascript
{
	"id": 0,
	"title": "",
	"contentType": "",
	"contentId": 0,
	"position": "",
	"sortOrder": 0,
	"status": 0,
	"startTime": "",
	"endTime": "",
	"clickCount": 0,
	"viewCount": 0,
	"reason": "",
	"tags": "",
	"weight": 0,
	"algorithm": "",
	"createdAt": "",
	"updatedAt": ""
}
```
## 内容推荐列表
**接口地址**:`/api/v1/sanxianren/content-recommendation/content-recommendation/list`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|title|推荐标题|query|false|string(string)||
|contentType|内容类型：story-故事，factory-工厂，people-人物|query|false|string(string)||
|position|推荐位置|query|false|string(string)||
|algorithm|推荐算法|query|false|string(string)||
|status|状态：1-启用，0-禁用|query|false|integer(*int)||
|startTime|开始时间|query|false|string(string)||
|endTime|结束时间|query|false|string(string)||
|pageNum|页码|query|false|integer(int)||
|pageSize|每页数量|query|false|integer(int)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationSearchRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|list||interface|interface|
|page||integer(int)|integer(int)|
|size||integer(int)|integer(int)|
|total||integer(int)|integer(int)|
|currentPage||integer(int)|integer(int)|
**响应示例**:
```javascript
{
	"list": {},
	"page": 0,
	"size": 0,
	"total": 0,
	"currentPage": 0
}
```
## 更新内容推荐状态
**接口地址**:`/api/v1/sanxianren/content-recommendation/content-recommendation/status`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "ids": [],
  "status": 0
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationStatusReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationStatusReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationStatusReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationStatusReq|
|&emsp;&emsp;ids|推荐ID数组||true|array([]uint64)|integer|
|&emsp;&emsp;status|状态：1-启用，0-禁用,可用值:0,1||false|integer(int)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationStatusRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 内容推荐点击统计
**接口地址**:`/api/v1/sanxianren/public/content-recommendation/public/content-recommendation/click`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "id": 0
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationClickReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationClickReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationClickReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationClickReq|
|&emsp;&emsp;id|推荐ID||true|integer(uint64)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationClickRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 公开内容推荐列表
**接口地址**:`/api/v1/sanxianren/public/content-recommendation/public/content-recommendation/list`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|position|推荐位置|query|true|string(string)||
|contentType|内容类型|query|false|string(string)||
|pageNum|页码|query|false|integer(int)||
|pageSize|每页数量|query|false|integer(int)||
|limit|限制数量|query|false|integer(int)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.PublicContentRecommendationRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|list|推荐列表|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationPublicItem|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentRecommendationPublicItem|
|&emsp;&emsp;id|推荐ID|integer(uint64)||
|&emsp;&emsp;title|推荐标题|string(string)||
|&emsp;&emsp;contentType|内容类型|string(string)||
|&emsp;&emsp;contentId|内容ID|integer(uint64)||
|&emsp;&emsp;reason|推荐理由|string(string)||
|&emsp;&emsp;tags|推荐标签|string(string)||
|&emsp;&emsp;weight|推荐权重|number(float64)||
|&emsp;&emsp;algorithm|推荐算法|string(string)||
**响应示例**:
```javascript
{
	"list": [
		{
			"id": 0,
			"title": "",
			"contentType": "",
			"contentId": 0,
			"reason": "",
			"tags": "",
			"weight": 0,
			"algorithm": ""
		}
	]
}
```
# 内容管理
## 设置内容分类标签
**接口地址**:`/api/v1/content/content/category-tag`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "content_id": 0,
  "content_type": "",
  "category_ids": [],
  "tag_ids": []
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentCategoryTagReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentCategoryTagReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentCategoryTagReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ContentCategoryTagReq|
|&emsp;&emsp;content_id|内容ID||true|integer(uint64)||
|&emsp;&emsp;content_type|内容类型,可用值:story,factory,people||true|string(string)||
|&emsp;&emsp;category_ids|分类ID数组||false|array([]uint64)|integer|
|&emsp;&emsp;tag_ids|标签ID数组||false|array([]uint64)|integer|
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 获取内容分类标签
**接口地址**:`/api/v1/content/content/category-tag/get`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|content_id|内容ID|query|true|integer(uint64)||
|content_type|内容类型,可用值:story,factory,people|query|true|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
# 短信管理
## 添加短信配置
**接口地址**:`/api/v1/plugins/sms/pluginSmsConfig/config/add`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "name": "",
  "driver": "",
  "config": "",
  "status": 0
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.plugins.sms.PluginSmsConfigAddReq|github.com.tiger1103.gfast.v3.api.v1.plugins.sms.PluginSmsConfigAddReq|body|true|github.com.tiger1103.gfast.v3.api.v1.plugins.sms.PluginSmsConfigAddReq|github.com.tiger1103.gfast.v3.api.v1.plugins.sms.PluginSmsConfigAddReq|
|&emsp;&emsp;name|配置名称||true|string(string)||
|&emsp;&emsp;driver|短信驱动||true|string(string)||
|&emsp;&emsp;config|配置信息||false|string(string)||
|&emsp;&emsp;status|状态||false|integer(int)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.plugins.sms.PluginSmsConfigAddRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 修改短信配置状态
**接口地址**:`/api/v1/plugins/sms/pluginSmsConfig/config/changeStatus`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "id": 0,
  "status": 0
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.plugins.sms.PluginsSmsConfigChangeStatusReq|github.com.tiger1103.gfast.v3.api.v1.plugins.sms.PluginsSmsConfigChangeStatusReq|body|true|github.com.tiger1103.gfast.v3.api.v1.plugins.sms.PluginsSmsConfigChangeStatusReq|github.com.tiger1103.gfast.v3.api.v1.plugins.sms.PluginsSmsConfigChangeStatusReq|
|&emsp;&emsp;id|配置ID||true|integer(uint64)||
|&emsp;&emsp;status|状态||true|integer(int)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.plugins.sms.PluginsSmsConfigChangeStatusRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 删除短信配置
**接口地址**:`/api/v1/plugins/sms/pluginSmsConfig/config/delete`
**请求方式**:`DELETE`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|ids|配置ID列表|query|true|array|integer|
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.plugins.sms.PluginSmsConfigDeleteRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 修改短信配置
**接口地址**:`/api/v1/plugins/sms/pluginSmsConfig/config/edit`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "id": 0,
  "name": "",
  "driver": "",
  "config": "",
  "status": 0
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.plugins.sms.PluginSmsConfigEditReq|github.com.tiger1103.gfast.v3.api.v1.plugins.sms.PluginSmsConfigEditReq|body|true|github.com.tiger1103.gfast.v3.api.v1.plugins.sms.PluginSmsConfigEditReq|github.com.tiger1103.gfast.v3.api.v1.plugins.sms.PluginSmsConfigEditReq|
|&emsp;&emsp;id|配置ID||true|integer(uint64)||
|&emsp;&emsp;name|配置名称||true|string(string)||
|&emsp;&emsp;driver|短信驱动||true|string(string)||
|&emsp;&emsp;config|配置信息||false|string(string)||
|&emsp;&emsp;status|状态||false|integer(int)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.plugins.sms.PluginSmsConfigEditRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 获取短信配置
**接口地址**:`/api/v1/plugins/sms/pluginSmsConfig/config/get`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id|配置ID|query|true|integer(uint64)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.plugins.sms.PluginSmsConfigGetRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|data||interface|interface|
**响应示例**:
```javascript
{
	"data": {}
}
```
## 短信配置列表
**接口地址**:`/api/v1/plugins/sms/pluginSmsConfig/config/list`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|page|页码|query|false|integer(int)||
|pageSize|每页数量|query|false|integer(int)||
|name|配置名称|query|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.plugins.sms.PluginSmsConfigSearchRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|data||interface|interface|
**响应示例**:
```javascript
{
	"data": {}
}
```
## 添加短信日志
**接口地址**:`/api/v1/plugins/sms/pluginSmsLog/log/add`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "mobile": "",
  "content": "",
  "status": 0
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.plugins.sms.PluginSmsLogAddReq|github.com.tiger1103.gfast.v3.api.v1.plugins.sms.PluginSmsLogAddReq|body|true|github.com.tiger1103.gfast.v3.api.v1.plugins.sms.PluginSmsLogAddReq|github.com.tiger1103.gfast.v3.api.v1.plugins.sms.PluginSmsLogAddReq|
|&emsp;&emsp;mobile|手机号||true|string(string)||
|&emsp;&emsp;content|短信内容||false|string(string)||
|&emsp;&emsp;status|发送状态||false|integer(int)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.plugins.sms.PluginSmsLogAddRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 删除短信日志
**接口地址**:`/api/v1/plugins/sms/pluginSmsLog/log/delete`
**请求方式**:`DELETE`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|ids|日志ID列表|query|true|array|integer|
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.plugins.sms.PluginSmsLogDeleteRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 修改短信日志
**接口地址**:`/api/v1/plugins/sms/pluginSmsLog/log/edit`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "id": 0,
  "mobile": "",
  "content": "",
  "status": 0
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.plugins.sms.PluginSmsLogEditReq|github.com.tiger1103.gfast.v3.api.v1.plugins.sms.PluginSmsLogEditReq|body|true|github.com.tiger1103.gfast.v3.api.v1.plugins.sms.PluginSmsLogEditReq|github.com.tiger1103.gfast.v3.api.v1.plugins.sms.PluginSmsLogEditReq|
|&emsp;&emsp;id|日志ID||true|integer(uint64)||
|&emsp;&emsp;mobile|手机号||true|string(string)||
|&emsp;&emsp;content|短信内容||false|string(string)||
|&emsp;&emsp;status|发送状态||false|integer(int)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.plugins.sms.PluginSmsLogEditRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 获取短信日志
**接口地址**:`/api/v1/plugins/sms/pluginSmsLog/log/get`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id|日志ID|query|true|integer(uint64)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.plugins.sms.PluginSmsLogGetRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|data||interface|interface|
**响应示例**:
```javascript
{
	"data": {}
}
```
## 短信日志列表
**接口地址**:`/api/v1/plugins/sms/pluginSmsLog/log/list`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|page|页码|query|false|integer(int)||
|pageSize|每页数量|query|false|integer(int)||
|mobile|手机号|query|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.plugins.sms.PluginSmsLogSearchRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|data||interface|interface|
**响应示例**:
```javascript
{
	"data": {}
}
```
# 通用接口-验证码
## 获取验证码
**接口地址**:`/api/v1/pub/captcha/get`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
暂无
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.common.CaptchaRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|key||string(string)|string(string)|
|img||string(string)|string(string)|
|verifyStatus||integer(int)|integer(int)|
**响应示例**:
```javascript
{
	"key": "",
	"img": "",
	"verifyStatus": 0
}
```
## 获取v2验证码
**接口地址**:`/api/v1/pub/captcha/v2`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
暂无
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.common.CaptchaV2Res|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|key||string(string)|string(string)|
|img||string(string)|string(string)|
|thumb||string(string)|string(string)|
**响应示例**:
```javascript
{
	"key": "",
	"img": "",
	"thumb": ""
}
```
## 检查v2验证码
**接口地址**:`/api/v1/pub/captcha/v2Check`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "key": "",
  "dots": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.common.CheckCaptchaV2Req|github.com.tiger1103.gfast.v3.api.v1.common.CheckCaptchaV2Req|body|true|github.com.tiger1103.gfast.v3.api.v1.common.CheckCaptchaV2Req|github.com.tiger1103.gfast.v3.api.v1.common.CheckCaptchaV2Req|
|&emsp;&emsp;key|||false|string(string)||
|&emsp;&emsp;dots|||false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.common.CheckCaptchaV2Res|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
# 公开接口
## 获取公开分类列表
**接口地址**:`/api/v1/public/category/public/category/list`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|type|分类类型（story/factory/people/general）|query|false|string(string)||
|is_hot|是否热门（0否 1是）|query|false|integer(*int)||
|is_recommend|是否推荐（0否 1是）|query|false|integer(*int)||
|page_num|页码|query|false|integer(int)||
|page_size|每页数量|query|false|integer(int)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 获取公开标签列表
**接口地址**:`/api/v1/public/tag/public/tag/list`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|type|标签类型（story/factory/people/general）|query|false|string(string)||
|is_hot|是否热门（0否 1是）|query|false|integer(*int)||
|page_num|页码|query|false|integer(int)||
|page_size|每页数量|query|false|integer(int)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
# 工厂人员
## 工厂人员添加
**接口地址**:`/api/v1/sanxianren/factoryPeople/add`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|name||query|true|string||
|personUuid||query|true|string||
|Authorization|Bearer {{token}}|header|false|string(string)||
|laterDevelopment||query|false|string||
|relatedPeople||query|false|string||
|notes||query|false|string||
|wikiPageTitle||query|false|string||
|importantEvents||query|false|string||
|submissionId||query|false|integer||
|personalExperience||query|false|string||
|contributions||query|false|string||
|relatedPhotos||query|false|string||
|relatedOralRecords||query|false|string||
|status||query|false|string||
|isFeatured||query|false|integer||
|gender||query|false|string||
|birthPlace||query|false|string||
|workLocation||query|false|string||
|relatedDocuments||query|false|string||
|deathDate||query|false|string||
|birthDate||query|false|string||
|employer||query|false|string||
|familySituation||query|false|string||
|jobTitle||query|false|string||
|participationTime||query|false|string||
|skills||query|false|string||
|references||query|false|string||
|achievements||query|false|string||
|wikiPageId||query|false|integer||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.FactoryPeopleAddRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 删除工厂人员
**接口地址**:`/api/v1/sanxianren/factoryPeople/delete`
**请求方式**:`DELETE`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|ids||query|true|array|integer|
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.FactoryPeopleDeleteRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 工厂人员修改
**接口地址**:`/api/v1/sanxianren/factoryPeople/edit`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id||query|true|integer||
|personUuid||query|true|string||
|name||query|true|string||
|Authorization|Bearer {{token}}|header|false|string(string)||
|workLocation||query|false|string||
|personalExperience||query|false|string||
|contributions||query|false|string||
|achievements||query|false|string||
|submissionId||query|false|integer||
|familySituation||query|false|string||
|laterDevelopment||query|false|string||
|wikiPageTitle||query|false|string||
|birthDate||query|false|string||
|birthPlace||query|false|string||
|employer||query|false|string||
|participationTime||query|false|string||
|importantEvents||query|false|string||
|relatedOralRecords||query|false|string||
|relatedPeople||query|false|string||
|notes||query|false|string||
|status||query|false|string||
|gender||query|false|string||
|skills||query|false|string||
|relatedDocuments||query|false|string||
|wikiPageId||query|false|integer||
|jobTitle||query|false|string||
|relatedPhotos||query|false|string||
|references||query|false|string||
|deathDate||query|false|string||
|isFeatured||query|false|integer||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.FactoryPeopleEditRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 获取工厂人员信息
**接口地址**:`/api/v1/sanxianren/factoryPeople/get`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id||query|true|integer(int64)||
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.FactoryPeopleGetRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|id||integer(int64)|integer(int64)|
|personUuid||string(string)|string(string)|
|submissionId||integer(*int64)|integer(*int64)|
|name||string(string)|string(string)|
|gender||string(string)|string(string)|
|birthDate||string(*gtime.Time)|string(*gtime.Time)|
|birthPlace||string(*string)|string(*string)|
|deathDate||string(*gtime.Time)|string(*gtime.Time)|
|jobTitle||string(*string)|string(*string)|
|employer||string(*string)|string(*string)|
|participationTime||string(*string)|string(*string)|
|workLocation||string(*string)|string(*string)|
|personalExperience||string(*string)|string(*string)|
|contributions||string(*string)|string(*string)|
|achievements||string(*string)|string(*string)|
|skills||string(*string)|string(*string)|
|importantEvents||string(*string)|string(*string)|
|familySituation||string(*string)|string(*string)|
|laterDevelopment||string(*string)|string(*string)|
|relatedPhotos||string(*string)|string(*string)|
|relatedDocuments||string(*string)|string(*string)|
|relatedOralRecords||string(*string)|string(*string)|
|relatedPeople||string(*string)|string(*string)|
|references||string(*string)|string(*string)|
|notes||string(*string)|string(*string)|
|wikiPageId||integer(*int)|integer(*int)|
|wikiPageTitle||string(*string)|string(*string)|
|status||string(string)|string(string)|
|isFeatured||integer(int)|integer(int)|
|viewCount||integer(int)|integer(int)|
|createdAt||string(*gtime.Time)|string(*gtime.Time)|
|updatedAt||string(*gtime.Time)|string(*gtime.Time)|
**响应示例**:
```javascript
{
	"id": 0,
	"personUuid": "",
	"submissionId": 0,
	"name": "",
	"gender": "",
	"birthDate": "",
	"birthPlace": "",
	"deathDate": "",
	"jobTitle": "",
	"employer": "",
	"participationTime": "",
	"workLocation": "",
	"personalExperience": "",
	"contributions": "",
	"achievements": "",
	"skills": "",
	"importantEvents": "",
	"familySituation": "",
	"laterDevelopment": "",
	"relatedPhotos": "",
	"relatedDocuments": "",
	"relatedOralRecords": "",
	"relatedPeople": "",
	"references": "",
	"notes": "",
	"wikiPageId": 0,
	"wikiPageTitle": "",
	"status": "",
	"isFeatured": 0,
	"viewCount": 0,
	"createdAt": "",
	"updatedAt": ""
}
```
## 工厂人员列表
**接口地址**:`/api/v1/sanxianren/factoryPeople/list`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|Authorization|Bearer {{token}}|header|false|string(string)||
|dateRange||query|false|array|string|
|pageNum||query|false|integer(int)||
|pageSize||query|false|integer(int)||
|orderBy||query|false|string(string)||
|id||query|false|string(string)||
|name||query|false|string(string)||
|gender||query|false|string(string)||
|status||query|false|string(string)||
|createdAt||query|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.FactoryPeopleSearchRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|currentPage||integer(int)|integer(int)|
|total||interface|interface|
|list||github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.FactoryPeopleListRes|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.FactoryPeopleListRes|
|&emsp;&emsp;id||integer(int64)||
|&emsp;&emsp;name||string(string)||
|&emsp;&emsp;gender||string(string)||
|&emsp;&emsp;birthDate||string(*gtime.Time)||
|&emsp;&emsp;jobTitle||string(*string)||
|&emsp;&emsp;employer||string(*string)||
|&emsp;&emsp;status||string(string)||
|&emsp;&emsp;createdAt||string(*gtime.Time)||
**响应示例**:
```javascript
{
	"currentPage": 0,
	"total": {},
	"list": [
		{
			"id": 0,
			"name": "",
			"gender": "",
			"birthDate": "",
			"jobTitle": "",
			"employer": "",
			"status": "",
			"createdAt": ""
		}
	]
}
```
# OAuth认证
## 发起OAuth授权
**接口地址**:`/api/v1/sanxianren/oauth/authorize`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "return_url": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.OAuthAuthorizeReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.OAuthAuthorizeReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.OAuthAuthorizeReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.OAuthAuthorizeReq|
|&emsp;&emsp;return_url|授权成功后的返回URL||false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 处理OAuth回调
**接口地址**:`/api/v1/sanxianren/oauth/callback`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|code|授权码|query|false|string(string)||
|state|状态参数|query|false|string(string)||
|error|错误代码|query|false|string(string)||
|error_description|错误描述|query|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 处理OAuth回调
**接口地址**:`/api/v1/sanxianren/oauth/callback`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "code": "",
  "state": "",
  "error": "",
  "error_description": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.OAuthCallbackReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.OAuthCallbackReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.OAuthCallbackReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.OAuthCallbackReq|
|&emsp;&emsp;code|授权码||false|string(string)||
|&emsp;&emsp;state|状态参数||false|string(string)||
|&emsp;&emsp;error|错误代码||false|string(string)||
|&emsp;&emsp;error_description|错误描述||false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 刷新访问令牌
**接口地址**:`/api/v1/sanxianren/oauth/refresh`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "refresh_token": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.OAuthRefreshTokenReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.OAuthRefreshTokenReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.OAuthRefreshTokenReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.OAuthRefreshTokenReq|
|&emsp;&emsp;refresh_token|刷新令牌||true|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 撤销访问令牌
**接口地址**:`/api/v1/sanxianren/oauth/revoke`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "access_token": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.OAuthRevokeTokenReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.OAuthRevokeTokenReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.OAuthRevokeTokenReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.OAuthRevokeTokenReq|
|&emsp;&emsp;access_token|访问令牌||true|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 获取OAuth会话信息
**接口地址**:`/api/v1/sanxianren/oauth/session`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|session_id|会话ID|query|true|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 删除OAuth会话
**接口地址**:`/api/v1/sanxianren/oauth/session`
**请求方式**:`DELETE`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|session_id|会话ID|query|true|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 获取OAuth认证状态
**接口地址**:`/api/v1/sanxianren/oauth/status`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
暂无
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 获取MediaWiki用户信息
**接口地址**:`/api/v1/sanxianren/oauth/userinfo`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|access_token|访问令牌|query|true|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 验证访问令牌
**接口地址**:`/api/v1/sanxianren/oauth/validate`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "access_token": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.OAuthValidateTokenReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.OAuthValidateTokenReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.OAuthValidateTokenReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.OAuthValidateTokenReq|
|&emsp;&emsp;access_token|访问令牌||true|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
# default
## e2644e055aa27683598b84351ef39006
**接口地址**:`/api/v1/sanxianren/story/ai-clean`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "submissionUUID": "",
  "cleanOptions": {}
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.AICleanStoryReq|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.AICleanStoryReq|body|true|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.AICleanStoryReq|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.AICleanStoryReq|
|&emsp;&emsp;submissionUUID|||true|string(string)||
|&emsp;&emsp;cleanOptions|||false|object||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## fdc4ad1577321d1d5dbd6e50d3969f89
**接口地址**:`/api/v1/sanxianren/story/attachments`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "submissionUUID": "",
  "attachmentType": "",
  "isPublic": 0,
  "orderBy": "",
  "pageNum": 0,
  "pageSize": 0
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.AttachmentsListReq|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.AttachmentsListReq|body|true|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.AttachmentsListReq|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.AttachmentsListReq|
|&emsp;&emsp;submissionUUID|||true|string(string)||
|&emsp;&emsp;attachmentType|||false|string(string)||
|&emsp;&emsp;isPublic|||false|integer(*uint)||
|&emsp;&emsp;orderBy|||false|string(string)||
|&emsp;&emsp;pageNum|||false|integer(int)||
|&emsp;&emsp;pageSize|||false|integer(int)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## fdddf6aad49c6098486eaaf881b4fb87
**接口地址**:`/api/v1/sanxianren/story/counters`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "submissionUUID": "",
  "deltaView": 0,
  "deltaLike": 0,
  "deltaComment": 0
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.UpdateCountersReq|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.UpdateCountersReq|body|true|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.UpdateCountersReq|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.UpdateCountersReq|
|&emsp;&emsp;submissionUUID|||true|string(string)||
|&emsp;&emsp;deltaView|||false|integer(int)||
|&emsp;&emsp;deltaLike|||false|integer(int)||
|&emsp;&emsp;deltaComment|||false|integer(int)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## b4fe030b19c6fb4e7c1105cdd163e423
**接口地址**:`/api/v1/sanxianren/story/detail`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "submissionUUID": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.StoryDetailReq|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.StoryDetailReq|body|true|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.StoryDetailReq|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.StoryDetailReq|
|&emsp;&emsp;submissionUUID|||true|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 914be81f2de08997012c6571ea67718b
**接口地址**:`/api/v1/sanxianren/story/list`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "pageNum": 0,
  "pageSize": 0,
  "orderBy": "",
  "dateRange": [],
  "status": "",
  "title": "",
  "author": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.StoryListReq|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.StoryListReq|body|true|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.StoryListReq|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.StoryListReq|
|&emsp;&emsp;pageNum|||false|integer(int)||
|&emsp;&emsp;pageSize|||false|integer(int)||
|&emsp;&emsp;orderBy|||false|string(string)||
|&emsp;&emsp;dateRange|||false|array([]string)|string|
|&emsp;&emsp;status|||false|string(string)||
|&emsp;&emsp;title|||false|string(string)||
|&emsp;&emsp;author|||false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## dbcea3c5979434368a94c71e6d897b15
**接口地址**:`/api/v1/sanxianren/story/publish`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "mediaType": "",
  "formData": {
    "title": "",
    "author": "",
    "factoryCode": "",
    "factoryName": "",
    "description": "",
    "coverImage": "",
    "images": [],
    "mediaFiles": [],
    "contact": "",
    "relationship": ""
  }
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.PublishStoryReq|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.PublishStoryReq|body|true|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.PublishStoryReq|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.PublishStoryReq|
|&emsp;&emsp;mediaType|||false|string(string)||
|&emsp;&emsp;formData|||false|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.PublishStoryFormData|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.PublishStoryFormData|
|&emsp;&emsp;&emsp;&emsp;title|||false|string||
|&emsp;&emsp;&emsp;&emsp;author|||false|string||
|&emsp;&emsp;&emsp;&emsp;factoryCode|||false|string||
|&emsp;&emsp;&emsp;&emsp;factoryName|||false|string||
|&emsp;&emsp;&emsp;&emsp;description|||false|string||
|&emsp;&emsp;&emsp;&emsp;coverImage|||false|string||
|&emsp;&emsp;&emsp;&emsp;images|||false|array|string|
|&emsp;&emsp;&emsp;&emsp;mediaFiles|||false|array|string|
|&emsp;&emsp;&emsp;&emsp;contact|||false|string||
|&emsp;&emsp;&emsp;&emsp;relationship|||false|string||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 75ba950c7c7ca43d85c7780323313a1d
**接口地址**:`/api/v1/sanxianren/story/review`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "submissionUUID": "",
  "reviewerId": 0,
  "reviewerName": "",
  "reviewAction": "",
  "reviewFeedback": "",
  "changesRequested": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.ReviewStoryReq|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.ReviewStoryReq|body|true|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.ReviewStoryReq|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.ReviewStoryReq|
|&emsp;&emsp;submissionUUID|||true|string(string)||
|&emsp;&emsp;reviewerId|||false|integer(int64)||
|&emsp;&emsp;reviewerName|||false|string(string)||
|&emsp;&emsp;reviewAction|可用值:approve,reject,request_changes||false|string(string)||
|&emsp;&emsp;reviewFeedback|||false|string(string)||
|&emsp;&emsp;changesRequested|||false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 6030a1f37b8ddd7fa8aabcf577bc39bb
**接口地址**:`/api/v1/sanxianren/story/reviews`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "submissionUUID": "",
  "pageNum": 0,
  "pageSize": 0
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.ReviewsListReq|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.ReviewsListReq|body|true|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.ReviewsListReq|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.ReviewsListReq|
|&emsp;&emsp;submissionUUID|||true|string(string)||
|&emsp;&emsp;pageNum|||false|integer(int)||
|&emsp;&emsp;pageSize|||false|integer(int)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 0c70973557930bf75a555cd94dcf8cb2
**接口地址**:`/api/v1/sanxianren/story/stats`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "dateRange": [],
  "submissionType": "",
  "status": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.StatsReq|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.StatsReq|body|true|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.StatsReq|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.StatsReq|
|&emsp;&emsp;dateRange|||false|array([]string)|string|
|&emsp;&emsp;submissionType|||false|string(string)||
|&emsp;&emsp;status|||false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 2ba731c39120e8cb458c1ea3b2ed66bb
**接口地址**:`/api/v1/sanxianren/story/status`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "submissionUUID": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.GetStoryStatusReq|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.GetStoryStatusReq|body|true|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.GetStoryStatusReq|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.GetStoryStatusReq|
|&emsp;&emsp;submissionUUID|||true|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 2b94c35681bac3d30a0f578a6f3a8c7d
**接口地址**:`/api/v1/template-api/active-prompts`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|category|提示词分类|query|false|string(string)||
|template_id|关联模板ID|query|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.GetActivePromptsRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|list|提示词列表|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.PromptApiItem|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.PromptApiItem|
|&emsp;&emsp;id|提示词ID|string(string)||
|&emsp;&emsp;name|提示词名称|string(string)||
|&emsp;&emsp;category|提示词分类|string(string)||
|&emsp;&emsp;description|提示词描述|string(string)||
|&emsp;&emsp;template_id|关联模板ID|string(string)||
|&emsp;&emsp;template_name|关联模板名称|string(string)||
|&emsp;&emsp;version|版本号|string(string)||
|&emsp;&emsp;content|提示词内容|string(string)||
|&emsp;&emsp;parameters|参数配置|interface|interface|
|&emsp;&emsp;status|状态：0-禁用,1-启用|integer(int)||
|&emsp;&emsp;created_at|创建时间|string(*gtime.Time)||
|&emsp;&emsp;updated_at|更新时间|string(*gtime.Time)||
|total|总数|integer(int)|integer(int)|
**响应示例**:
```javascript
{
	"list": [
		{
			"id": "",
			"name": "",
			"category": "",
			"description": "",
			"template_id": "",
			"template_name": "",
			"version": "",
			"content": "",
			"parameters": {
				"additionalProperties1": {}
			},
			"status": 0,
			"created_at": "",
			"updated_at": ""
		}
	],
	"total": 0
}
```
## e2bf9a30bd82a8302c424e12e6d287de
**接口地址**:`/api/v1/template-api/active-templates`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|category|模板分类|query|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.GetActiveTemplatesRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|list|模板列表|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.TemplateApiItem|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.TemplateApiItem|
|&emsp;&emsp;id|模板ID|string(string)||
|&emsp;&emsp;name|模板名称|string(string)||
|&emsp;&emsp;category|模板分类|string(string)||
|&emsp;&emsp;description|模板描述|string(string)||
|&emsp;&emsp;version|版本号|string(string)||
|&emsp;&emsp;fields|字段定义|interface|interface|
|&emsp;&emsp;rules|验证规则|interface|interface|
|&emsp;&emsp;status|状态：0-禁用,1-启用|integer(int)||
|&emsp;&emsp;created_at|创建时间|string(*gtime.Time)||
|&emsp;&emsp;updated_at|更新时间|string(*gtime.Time)||
|total|总数|integer(int)|integer(int)|
**响应示例**:
```javascript
{
	"list": [
		{
			"id": "",
			"name": "",
			"category": "",
			"description": "",
			"version": "",
			"fields": {
				"additionalProperties1": {}
			},
			"rules": {},
			"status": 0,
			"created_at": "",
			"updated_at": ""
		}
	],
	"total": 0
}
```
## 650d8c64cb9305cdcf940df371d4336d
**接口地址**:`/api/v1/template-api/log`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "template_id": "",
  "prompt_id": "",
  "original_data": "",
  "cleaned_data": {
    "additionalProperties1": {}
  },
  "processing_log": "",
  "status": "",
  "process_time": 0,
  "error_message": "",
  "related_id": "",
  "related_type": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.LogCleaningProcessReq|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.LogCleaningProcessReq|body|true|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.LogCleaningProcessReq|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.LogCleaningProcessReq|
|&emsp;&emsp;template_id|模板ID||true|string(string)||
|&emsp;&emsp;prompt_id|提示词ID||false|string(string)||
|&emsp;&emsp;original_data|原始数据||true|string(string)||
|&emsp;&emsp;cleaned_data|清洗后数据||false|interface|interface|
|&emsp;&emsp;processing_log|处理日志||false|string(string)||
|&emsp;&emsp;status|处理状态||true|string(string)||
|&emsp;&emsp;process_time|处理耗时(毫秒)||false|integer(int64)||
|&emsp;&emsp;error_message|错误信息||false|string(string)||
|&emsp;&emsp;related_id|关联业务ID||false|string(string)||
|&emsp;&emsp;related_type|关联业务类型||false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.LogCleaningProcessRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|log_id|日志ID|string(string)|string(string)|
|message|记录结果消息|string(string)|string(string)|
**响应示例**:
```javascript
{
	"log_id": "",
	"message": ""
}
```
## 97ec5f331ead6546062adfe7287e404c
**接口地址**:`/api/v1/template-api/process`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "template_id": "",
  "prompt_id": "",
  "raw_data": "",
  "parameters": {
    "additionalProperties1": {}
  }
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.ProcessWithTemplateReq|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.ProcessWithTemplateReq|body|true|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.ProcessWithTemplateReq|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.ProcessWithTemplateReq|
|&emsp;&emsp;template_id|模板ID||true|string(string)||
|&emsp;&emsp;prompt_id|提示词ID||true|string(string)||
|&emsp;&emsp;raw_data|原始数据||true|string(string)||
|&emsp;&emsp;parameters|处理参数||false|interface|interface|
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.ProcessWithTemplateRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|processed|是否处理成功|boolean(bool)|boolean(bool)|
|cleaned_data|清洗后的数据|interface|interface|
|original_data|原始数据|string(string)|string(string)|
|processing_log|处理日志|string(string)|string(string)|
|message|处理结果消息|string(string)|string(string)|
|process_time|处理耗时(毫秒)|integer(int64)|integer(int64)|
**响应示例**:
```javascript
{
	"processed": true,
	"cleaned_data": {
		"additionalProperties1": {}
	},
	"original_data": "",
	"processing_log": "",
	"message": "",
	"process_time": 0
}
```
## 3194d9e076779f2717301d986b4b186b
**接口地址**:`/api/v1/template-api/prompt/preview`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "id": "",
  "test_data": {
    "additionalProperties1": {}
  }
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.PromptPreviewReq|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.PromptPreviewReq|body|true|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.PromptPreviewReq|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.PromptPreviewReq|
|&emsp;&emsp;id|提示词ID||false|string(string)||
|&emsp;&emsp;test_data|测试数据（用于占位符替换）||false|interface|interface|
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.PromptPreviewRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|original_content|原始提示词内容|string(string)|string(string)|
|processed_content|渲染后的提示词内容|string(string)|string(string)|
|statistics|统计信息|interface|interface|
|message|结果消息|string(string)|string(string)|
**响应示例**:
```javascript
{
	"original_content": "",
	"processed_content": "",
	"statistics": {
		"additionalProperties1": {}
	},
	"message": ""
}
```
## 4e1ea578bb485ad0f27749b577f0fc7f
**接口地址**:`/api/v1/template-api/prompt/{category}`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|category|提示词分类|path|true|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.GetPromptByCategoryRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|prompt|提示词详情|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.PromptApiItem|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.PromptApiItem|
|&emsp;&emsp;id|提示词ID|string(string)||
|&emsp;&emsp;name|提示词名称|string(string)||
|&emsp;&emsp;category|提示词分类|string(string)||
|&emsp;&emsp;description|提示词描述|string(string)||
|&emsp;&emsp;template_id|关联模板ID|string(string)||
|&emsp;&emsp;template_name|关联模板名称|string(string)||
|&emsp;&emsp;version|版本号|string(string)||
|&emsp;&emsp;content|提示词内容|string(string)||
|&emsp;&emsp;parameters|参数配置|interface|interface|
|&emsp;&emsp;status|状态：0-禁用,1-启用|integer(int)||
|&emsp;&emsp;created_at|创建时间|string(*gtime.Time)||
|&emsp;&emsp;updated_at|更新时间|string(*gtime.Time)||
**响应示例**:
```javascript
{
	"prompt": {
		"id": "",
		"name": "",
		"category": "",
		"description": "",
		"template_id": "",
		"template_name": "",
		"version": "",
		"content": "",
		"parameters": {
			"additionalProperties1": {}
		},
		"status": 0,
		"created_at": "",
		"updated_at": ""
	}
}
```
## 6997041a8b56d0dc9467be20be1605b4
**接口地址**:`/api/v1/template-api/template/{category}`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|category|模板分类|path|true|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.GetTemplateByCategoryRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|template|模板详情|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.TemplateApiItem|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.TemplateApiItem|
|&emsp;&emsp;id|模板ID|string(string)||
|&emsp;&emsp;name|模板名称|string(string)||
|&emsp;&emsp;category|模板分类|string(string)||
|&emsp;&emsp;description|模板描述|string(string)||
|&emsp;&emsp;version|版本号|string(string)||
|&emsp;&emsp;fields|字段定义|interface|interface|
|&emsp;&emsp;rules|验证规则|interface|interface|
|&emsp;&emsp;status|状态：0-禁用,1-启用|integer(int)||
|&emsp;&emsp;created_at|创建时间|string(*gtime.Time)||
|&emsp;&emsp;updated_at|更新时间|string(*gtime.Time)||
**响应示例**:
```javascript
{
	"template": {
		"id": "",
		"name": "",
		"category": "",
		"description": "",
		"version": "",
		"fields": {
			"additionalProperties1": {}
		},
		"rules": {},
		"status": 0,
		"created_at": "",
		"updated_at": ""
	}
}
```
## f6152fb91de933280c1a6ef9c2fb3cfc
**接口地址**:`/api/v1/template-api/validate`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "template_id": "",
  "data": {
    "additionalProperties1": {}
  }
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.ValidateDataReq|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.ValidateDataReq|body|true|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.ValidateDataReq|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.ValidateDataReq|
|&emsp;&emsp;template_id|模板ID||true|string(string)||
|&emsp;&emsp;data|待验证数据||true|interface|interface|
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.ValidateDataRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|valid|是否验证通过|boolean(bool)|boolean(bool)|
|errors|验证错误列表|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.ValidationError|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.ValidationError|
|&emsp;&emsp;field|字段名|string(string)||
|&emsp;&emsp;message|错误信息|string(string)||
|&emsp;&emsp;code|错误代码|string(string)||
|data|标准化后的数据|interface|interface|
|message|验证结果消息|string(string)|string(string)|
**响应示例**:
```javascript
{
	"valid": true,
	"errors": [
		{
			"field": "",
			"message": "",
			"code": ""
		}
	],
	"data": {
		"additionalProperties1": {}
	},
	"message": ""
}
```
# 用户? method:
## c709359c8f5e76f73c26660c6eb15dd5
**接口地址**:`/api/v1/sanxianren/sxrUsers/add`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|username|用户名|query|true|string(string)||
|passwordHash|密码哈希|query|true|string(string)||
|nickname|昵称|query|true|string(string)||
|status|状态：0-禁用，1-正常|query|true|integer(int)||
|Authorization|Bearer {{token}}|header|false|string(string)||
|phone|手机号|query|false|string(string)||
|email|邮箱|query|false|string(string)||
|avatarUrl|头像URL|query|false|string(string)||
|gender|性别：0-未知，1-男，2-女|query|false|integer(int)||
|birthDate|出生日期|query|false|string(*gtime.Time)||
|location|所在地|query|false|integer(int)||
|bio|个人简介|query|false|string(string)||
|lastLoginAt|最后登录时间|query|false|string(*gtime.Time)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.SxrUsersAddRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## d4457b8da2aa9a4e1a29c057f8e5f7ca
**接口地址**:`/api/v1/sanxianren/sxrUsers/add`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|username|用户名|query|true|string||
|nickname|昵称|query|true|string||
|status|状态：0-禁用，1-正常|query|true|integer||
|passwordHash|密码哈希|query|true|string||
|Authorization|Bearer {{token}}|header|false|string(string)||
|phone|手机号|query|false|string||
|avatarUrl|头像URL|query|false|string||
|bio|个人简介|query|false|string||
|lastLoginAt|最后登录时间|query|false|string||
|email|邮箱|query|false|string||
|gender|性别：0-未知，1-男，2-女|query|false|integer||
|birthDate|出生日期|query|false|string||
|location|所在地|query|false|integer||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.SxrUsersAddRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 18a7e10c719447c46b06523c4f5c0dd8
**接口地址**:`/api/v1/sanxianren/sxrUsers/add`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|passwordHash|密码哈希|query|true|string||
|status|状态：0-禁用，1-正常|query|true|integer||
|nickname|昵称|query|true|string||
|username|用户名|query|true|string||
|Authorization|Bearer {{token}}|header|false|string(string)||
|avatarUrl|头像URL|query|false|string||
|birthDate|出生日期|query|false|string||
|location|所在地|query|false|integer||
|bio|个人简介|query|false|string||
|phone|手机号|query|false|string||
|gender|性别：0-未知，1-男，2-女|query|false|integer||
|lastLoginAt|最后登录时间|query|false|string||
|email|邮箱|query|false|string||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.SxrUsersAddRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 19ce1d59ab6777303d6123cad5576328
**接口地址**:`/api/v1/sanxianren/sxrUsers/add`
**请求方式**:`DELETE`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|username|用户名|query|true|string(string)||
|passwordHash|密码哈希|query|true|string(string)||
|nickname|昵称|query|true|string(string)||
|status|状态：0-禁用，1-正常|query|true|integer(int)||
|Authorization|Bearer {{token}}|header|false|string(string)||
|phone|手机号|query|false|string(string)||
|email|邮箱|query|false|string(string)||
|avatarUrl|头像URL|query|false|string(string)||
|gender|性别：0-未知，1-男，2-女|query|false|integer(int)||
|birthDate|出生日期|query|false|string(*gtime.Time)||
|location|所在地|query|false|integer(int)||
|bio|个人简介|query|false|string(string)||
|lastLoginAt|最后登录时间|query|false|string(*gtime.Time)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.SxrUsersAddRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## de369cab3c37f104229d35f266ca3805
**接口地址**:`/api/v1/sanxianren/sxrUsers/add`
**请求方式**:`PATCH`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|username|用户名|query|true|string||
|nickname|昵称|query|true|string||
|status|状态：0-禁用，1-正常|query|true|integer||
|passwordHash|密码哈希|query|true|string||
|Authorization|Bearer {{token}}|header|false|string(string)||
|location|所在地|query|false|integer||
|bio|个人简介|query|false|string||
|phone|手机号|query|false|string||
|email|邮箱|query|false|string||
|birthDate|出生日期|query|false|string||
|lastLoginAt|最后登录时间|query|false|string||
|avatarUrl|头像URL|query|false|string||
|gender|性别：0-未知，1-男，2-女|query|false|integer||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.SxrUsersAddRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## aeca60d3cca5200aefbad712a1614cd2
**接口地址**:`/api/v1/sanxianren/sxrUsers/add`
**请求方式**:`OPTIONS`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|status|状态：0-禁用，1-正常|query|true|integer||
|username|用户名|query|true|string||
|passwordHash|密码哈希|query|true|string||
|nickname|昵称|query|true|string||
|Authorization|Bearer {{token}}|header|false|string(string)||
|phone|手机号|query|false|string||
|avatarUrl|头像URL|query|false|string||
|birthDate|出生日期|query|false|string||
|location|所在地|query|false|integer||
|lastLoginAt|最后登录时间|query|false|string||
|email|邮箱|query|false|string||
|gender|性别：0-未知，1-男，2-女|query|false|integer||
|bio|个人简介|query|false|string||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.SxrUsersAddRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 844fa9d38c56cb0d2278aa4b07886f24
**接口地址**:`/api/v1/sanxianren/sxrUsers/add`
**请求方式**:`TRACE`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|passwordHash|密码哈希|query|true|string||
|nickname|昵称|query|true|string||
|status|状态：0-禁用，1-正常|query|true|integer||
|username|用户名|query|true|string||
|Authorization|Bearer {{token}}|header|false|string(string)||
|gender|性别：0-未知，1-男，2-女|query|false|integer||
|birthDate|出生日期|query|false|string||
|location|所在地|query|false|integer||
|lastLoginAt|最后登录时间|query|false|string||
|avatarUrl|头像URL|query|false|string||
|bio|个人简介|query|false|string||
|phone|手机号|query|false|string||
|email|邮箱|query|false|string||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.SxrUsersAddRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 11f6225ce094182332ece0bcdefabff3
**接口地址**:`/api/v1/sanxianren/sxrUsers/add`
**请求方式**:`HEAD`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|nickname|昵称|query|true|string||
|status|状态：0-禁用，1-正常|query|true|integer||
|username|用户名|query|true|string||
|passwordHash|密码哈希|query|true|string||
|Authorization|Bearer {{token}}|header|false|string(string)||
|location|所在地|query|false|integer||
|email|邮箱|query|false|string||
|bio|个人简介|query|false|string||
|lastLoginAt|最后登录时间|query|false|string||
|phone|手机号|query|false|string||
|avatarUrl|头像URL|query|false|string||
|gender|性别：0-未知，1-男，2-女|query|false|integer||
|birthDate|出生日期|query|false|string||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.SxrUsersAddRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 64433fa0832c3620f2fc0734f1e3ee0e
**接口地址**:`/api/v1/sanxianren/sxrUsers/delete`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|ids||query|true|array|integer|
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.SxrUsersDeleteRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 03a5e4c080b684b16654b9ee9c67b8ef
**接口地址**:`/api/v1/sanxianren/sxrUsers/delete`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|ids||query|true|array|integer|
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.SxrUsersDeleteRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 25116d98a906045eef04eb26c6fb5c8f
**接口地址**:`/api/v1/sanxianren/sxrUsers/delete`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|ids||query|true|array|integer|
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.SxrUsersDeleteRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 5ce56dec667db64af9d00cf59cc59950
**接口地址**:`/api/v1/sanxianren/sxrUsers/delete`
**请求方式**:`DELETE`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|ids||query|true|array|integer|
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.SxrUsersDeleteRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## ac408293085d693d5249e18c64103835
**接口地址**:`/api/v1/sanxianren/sxrUsers/delete`
**请求方式**:`PATCH`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|ids||query|true|array|integer|
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.SxrUsersDeleteRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 19200dcdf8f09a986fe54fd7b34f6f01
**接口地址**:`/api/v1/sanxianren/sxrUsers/delete`
**请求方式**:`OPTIONS`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|ids||query|true|array|integer|
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.SxrUsersDeleteRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## ec4151aa678093c295e900f4936c662a
**接口地址**:`/api/v1/sanxianren/sxrUsers/delete`
**请求方式**:`TRACE`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|ids||query|true|array|integer|
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.SxrUsersDeleteRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 086de0c6f853c8e512322a29a099d8e0
**接口地址**:`/api/v1/sanxianren/sxrUsers/delete`
**请求方式**:`HEAD`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|ids||query|true|array|integer|
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.SxrUsersDeleteRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 2664413026d89bf65e089bc29d477e3f
**接口地址**:`/api/v1/sanxianren/sxrUsers/edit`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id|用户ID|query|true|integer(int64)||
|username|用户名|query|true|string(string)||
|passwordHash|密码哈希|query|true|string(string)||
|nickname|昵称|query|true|string(string)||
|status|状态：0-禁用，1-正常|query|true|integer(int)||
|Authorization|Bearer {{token}}|header|false|string(string)||
|phone|手机号|query|false|string(string)||
|email|邮箱|query|false|string(string)||
|avatarUrl|头像URL|query|false|string(string)||
|gender|性别：0-未知，1-男，2-女|query|false|integer(int)||
|birthDate|出生日期|query|false|string(*gtime.Time)||
|location|所在地|query|false|integer(int)||
|bio|个人简介|query|false|string(string)||
|lastLoginAt|最后登录时间|query|false|string(*gtime.Time)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.SxrUsersEditRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 7157da3e3ab3272270086300ed690956
**接口地址**:`/api/v1/sanxianren/sxrUsers/edit`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|status|状态：0-禁用，1-正常|query|true|integer||
|id|用户ID|query|true|integer||
|username|用户名|query|true|string||
|passwordHash|密码哈希|query|true|string||
|nickname|昵称|query|true|string||
|Authorization|Bearer {{token}}|header|false|string(string)||
|avatarUrl|头像URL|query|false|string||
|bio|个人简介|query|false|string||
|phone|手机号|query|false|string||
|gender|性别：0-未知，1-男，2-女|query|false|integer||
|birthDate|出生日期|query|false|string||
|location|所在地|query|false|integer||
|lastLoginAt|最后登录时间|query|false|string||
|email|邮箱|query|false|string||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.SxrUsersEditRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## c7664375758d1ca0f91070e819383eee
**接口地址**:`/api/v1/sanxianren/sxrUsers/edit`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id|用户ID|query|true|integer||
|passwordHash|密码哈希|query|true|string||
|nickname|昵称|query|true|string||
|status|状态：0-禁用，1-正常|query|true|integer||
|username|用户名|query|true|string||
|Authorization|Bearer {{token}}|header|false|string(string)||
|email|邮箱|query|false|string||
|gender|性别：0-未知，1-男，2-女|query|false|integer||
|lastLoginAt|最后登录时间|query|false|string||
|phone|手机号|query|false|string||
|avatarUrl|头像URL|query|false|string||
|birthDate|出生日期|query|false|string||
|location|所在地|query|false|integer||
|bio|个人简介|query|false|string||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.SxrUsersEditRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 0fc6bca0283f9abd2368f474cc3e1a2b
**接口地址**:`/api/v1/sanxianren/sxrUsers/edit`
**请求方式**:`DELETE`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id|用户ID|query|true|integer(int64)||
|username|用户名|query|true|string(string)||
|passwordHash|密码哈希|query|true|string(string)||
|nickname|昵称|query|true|string(string)||
|status|状态：0-禁用，1-正常|query|true|integer(int)||
|Authorization|Bearer {{token}}|header|false|string(string)||
|phone|手机号|query|false|string(string)||
|email|邮箱|query|false|string(string)||
|avatarUrl|头像URL|query|false|string(string)||
|gender|性别：0-未知，1-男，2-女|query|false|integer(int)||
|birthDate|出生日期|query|false|string(*gtime.Time)||
|location|所在地|query|false|integer(int)||
|bio|个人简介|query|false|string(string)||
|lastLoginAt|最后登录时间|query|false|string(*gtime.Time)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.SxrUsersEditRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## eebd3d7bcd3cbe716428e46eb535833e
**接口地址**:`/api/v1/sanxianren/sxrUsers/edit`
**请求方式**:`PATCH`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|passwordHash|密码哈希|query|true|string||
|status|状态：0-禁用，1-正常|query|true|integer||
|id|用户ID|query|true|integer||
|username|用户名|query|true|string||
|nickname|昵称|query|true|string||
|Authorization|Bearer {{token}}|header|false|string(string)||
|email|邮箱|query|false|string||
|avatarUrl|头像URL|query|false|string||
|bio|个人简介|query|false|string||
|lastLoginAt|最后登录时间|query|false|string||
|phone|手机号|query|false|string||
|gender|性别：0-未知，1-男，2-女|query|false|integer||
|birthDate|出生日期|query|false|string||
|location|所在地|query|false|integer||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.SxrUsersEditRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## c55c11a301e7a880b08c1285011374e0
**接口地址**:`/api/v1/sanxianren/sxrUsers/edit`
**请求方式**:`OPTIONS`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|passwordHash|密码哈希|query|true|string||
|nickname|昵称|query|true|string||
|status|状态：0-禁用，1-正常|query|true|integer||
|id|用户ID|query|true|integer||
|username|用户名|query|true|string||
|Authorization|Bearer {{token}}|header|false|string(string)||
|email|邮箱|query|false|string||
|avatarUrl|头像URL|query|false|string||
|birthDate|出生日期|query|false|string||
|location|所在地|query|false|integer||
|bio|个人简介|query|false|string||
|phone|手机号|query|false|string||
|gender|性别：0-未知，1-男，2-女|query|false|integer||
|lastLoginAt|最后登录时间|query|false|string||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.SxrUsersEditRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 7c6da37f004c6dcfcc41c317d33f231b
**接口地址**:`/api/v1/sanxianren/sxrUsers/edit`
**请求方式**:`TRACE`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|status|状态：0-禁用，1-正常|query|true|integer||
|passwordHash|密码哈希|query|true|string||
|nickname|昵称|query|true|string||
|id|用户ID|query|true|integer||
|username|用户名|query|true|string||
|Authorization|Bearer {{token}}|header|false|string(string)||
|email|邮箱|query|false|string||
|bio|个人简介|query|false|string||
|lastLoginAt|最后登录时间|query|false|string||
|phone|手机号|query|false|string||
|avatarUrl|头像URL|query|false|string||
|gender|性别：0-未知，1-男，2-女|query|false|integer||
|birthDate|出生日期|query|false|string||
|location|所在地|query|false|integer||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.SxrUsersEditRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 8bb3b4f1e889b71c38c98dce269b0021
**接口地址**:`/api/v1/sanxianren/sxrUsers/edit`
**请求方式**:`HEAD`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|passwordHash|密码哈希|query|true|string||
|id|用户ID|query|true|integer||
|username|用户名|query|true|string||
|nickname|昵称|query|true|string||
|status|状态：0-禁用，1-正常|query|true|integer||
|Authorization|Bearer {{token}}|header|false|string(string)||
|email|邮箱|query|false|string||
|gender|性别：0-未知，1-男，2-女|query|false|integer||
|birthDate|出生日期|query|false|string||
|bio|个人简介|query|false|string||
|lastLoginAt|最后登录时间|query|false|string||
|avatarUrl|头像URL|query|false|string||
|location|所在地|query|false|integer||
|phone|手机号|query|false|string||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.SxrUsersEditRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## e4742d2d09f9739900bf84294cd3f64a
**接口地址**:`/api/v1/sanxianren/sxrUsers/list`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|Authorization|Bearer {{token}}|header|false|string(string)||
|dateRange||query|false|array|string|
|pageNum||query|false|integer(int)||
|pageSize||query|false|integer(int)||
|orderBy||query|false|string(string)||
|id|用户ID|query|false|string(string)||
|username|用户名|query|false|string(string)||
|phone|手机号|query|false|string(string)||
|email|邮箱|query|false|string(string)||
|passwordHash|密码哈希|query|false|string(string)||
|nickname|昵称|query|false|string(string)||
|avatarUrl|头像URL|query|false|string(string)||
|gender|性别：0-未知，1-男，2-女|query|false|string(string)||
|birthDate|出生日期|query|false|string(string)||
|location|所在地|query|false|string(string)||
|bio|个人简介|query|false|string(string)||
|status|状态：0-禁用，1-正常|query|false|string(string)||
|createdAt|创建时间|query|false|string(string)||
|lastLoginAt|最后登录时间|query|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.SxrUsersSearchRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|currentPage||integer(int)|integer(int)|
|total||interface|interface|
|list||github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.SxrUsersListRes|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.SxrUsersListRes|
|&emsp;&emsp;id|用户ID|integer(int64)||
|&emsp;&emsp;username|用户名|string(string)||
|&emsp;&emsp;phone|手机号|string(string)||
|&emsp;&emsp;email|邮箱|string(string)||
|&emsp;&emsp;passwordHash|密码哈希|string(string)||
|&emsp;&emsp;nickname|昵称|string(string)||
|&emsp;&emsp;avatarUrl|头像URL|string(string)||
|&emsp;&emsp;gender|性别：0-未知，1-男，2-女|integer(int)||
|&emsp;&emsp;birthDate|出生日期|string(*gtime.Time)||
|&emsp;&emsp;location|所在地|integer(int)||
|&emsp;&emsp;bio|个人简介|string(string)||
|&emsp;&emsp;status|状态：0-禁用，1-正常|integer(int)||
|&emsp;&emsp;createdAt|创建时间|string(*gtime.Time)||
|&emsp;&emsp;lastLoginAt|最后登录时间|string(*gtime.Time)||
**响应示例**:
```javascript
{
	"currentPage": 0,
	"total": {},
	"list": [
		{
			"id": 0,
			"username": "",
			"phone": "",
			"email": "",
			"passwordHash": "",
			"nickname": "",
			"avatarUrl": "",
			"gender": 0,
			"birthDate": "",
			"location": 0,
			"bio": "",
			"status": 0,
			"createdAt": "",
			"lastLoginAt": ""
		}
	]
}
```
## 7d0735a086db60ab4c805464ba770985
**接口地址**:`/api/v1/sanxianren/sxrUsers/list`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|Authorization|Bearer {{token}}|header|false|string(string)||
|birthDate|出生日期|query|false|string||
|status|状态：0-禁用，1-正常|query|false|string||
|pageNum||query|false|integer||
|pageSize||query|false|integer||
|orderBy||query|false|string||
|phone|手机号|query|false|string||
|bio|个人简介|query|false|string||
|lastLoginAt|最后登录时间|query|false|string||
|passwordHash|密码哈希|query|false|string||
|nickname|昵称|query|false|string||
|createdAt|创建时间|query|false|string||
|email|邮箱|query|false|string||
|location|所在地|query|false|string||
|dateRange||query|false|array|string|
|id|用户ID|query|false|string||
|username|用户名|query|false|string||
|avatarUrl|头像URL|query|false|string||
|gender|性别：0-未知，1-男，2-女|query|false|string||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.SxrUsersSearchRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|currentPage||integer(int)|integer(int)|
|total||interface|interface|
|list||github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.SxrUsersListRes|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.SxrUsersListRes|
|&emsp;&emsp;id|用户ID|integer(int64)||
|&emsp;&emsp;username|用户名|string(string)||
|&emsp;&emsp;phone|手机号|string(string)||
|&emsp;&emsp;email|邮箱|string(string)||
|&emsp;&emsp;passwordHash|密码哈希|string(string)||
|&emsp;&emsp;nickname|昵称|string(string)||
|&emsp;&emsp;avatarUrl|头像URL|string(string)||
|&emsp;&emsp;gender|性别：0-未知，1-男，2-女|integer(int)||
|&emsp;&emsp;birthDate|出生日期|string(*gtime.Time)||
|&emsp;&emsp;location|所在地|integer(int)||
|&emsp;&emsp;bio|个人简介|string(string)||
|&emsp;&emsp;status|状态：0-禁用，1-正常|integer(int)||
|&emsp;&emsp;createdAt|创建时间|string(*gtime.Time)||
|&emsp;&emsp;lastLoginAt|最后登录时间|string(*gtime.Time)||
**响应示例**:
```javascript
{
	"currentPage": 0,
	"total": {},
	"list": [
		{
			"id": 0,
			"username": "",
			"phone": "",
			"email": "",
			"passwordHash": "",
			"nickname": "",
			"avatarUrl": "",
			"gender": 0,
			"birthDate": "",
			"location": 0,
			"bio": "",
			"status": 0,
			"createdAt": "",
			"lastLoginAt": ""
		}
	]
}
```
## c4c5c65393e81b01e8c8bc1f07dfcc47
**接口地址**:`/api/v1/sanxianren/sxrUsers/list`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|Authorization|Bearer {{token}}|header|false|string(string)||
|id|用户ID|query|false|string||
|avatarUrl|头像URL|query|false|string||
|gender|性别：0-未知，1-男，2-女|query|false|string||
|birthDate|出生日期|query|false|string||
|location|所在地|query|false|string||
|orderBy||query|false|string||
|passwordHash|密码哈希|query|false|string||
|status|状态：0-禁用，1-正常|query|false|string||
|dateRange||query|false|array|string|
|username|用户名|query|false|string||
|phone|手机号|query|false|string||
|email|邮箱|query|false|string||
|nickname|昵称|query|false|string||
|bio|个人简介|query|false|string||
|createdAt|创建时间|query|false|string||
|lastLoginAt|最后登录时间|query|false|string||
|pageNum||query|false|integer||
|pageSize||query|false|integer||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.SxrUsersSearchRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|currentPage||integer(int)|integer(int)|
|total||interface|interface|
|list||github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.SxrUsersListRes|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.SxrUsersListRes|
|&emsp;&emsp;id|用户ID|integer(int64)||
|&emsp;&emsp;username|用户名|string(string)||
|&emsp;&emsp;phone|手机号|string(string)||
|&emsp;&emsp;email|邮箱|string(string)||
|&emsp;&emsp;passwordHash|密码哈希|string(string)||
|&emsp;&emsp;nickname|昵称|string(string)||
|&emsp;&emsp;avatarUrl|头像URL|string(string)||
|&emsp;&emsp;gender|性别：0-未知，1-男，2-女|integer(int)||
|&emsp;&emsp;birthDate|出生日期|string(*gtime.Time)||
|&emsp;&emsp;location|所在地|integer(int)||
|&emsp;&emsp;bio|个人简介|string(string)||
|&emsp;&emsp;status|状态：0-禁用，1-正常|integer(int)||
|&emsp;&emsp;createdAt|创建时间|string(*gtime.Time)||
|&emsp;&emsp;lastLoginAt|最后登录时间|string(*gtime.Time)||
**响应示例**:
```javascript
{
	"currentPage": 0,
	"total": {},
	"list": [
		{
			"id": 0,
			"username": "",
			"phone": "",
			"email": "",
			"passwordHash": "",
			"nickname": "",
			"avatarUrl": "",
			"gender": 0,
			"birthDate": "",
			"location": 0,
			"bio": "",
			"status": 0,
			"createdAt": "",
			"lastLoginAt": ""
		}
	]
}
```
## e0168c495768172a774d0015cf56ec6c
**接口地址**:`/api/v1/sanxianren/sxrUsers/list`
**请求方式**:`DELETE`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|Authorization|Bearer {{token}}|header|false|string(string)||
|dateRange||query|false|array|string|
|pageNum||query|false|integer(int)||
|pageSize||query|false|integer(int)||
|orderBy||query|false|string(string)||
|id|用户ID|query|false|string(string)||
|username|用户名|query|false|string(string)||
|phone|手机号|query|false|string(string)||
|email|邮箱|query|false|string(string)||
|passwordHash|密码哈希|query|false|string(string)||
|nickname|昵称|query|false|string(string)||
|avatarUrl|头像URL|query|false|string(string)||
|gender|性别：0-未知，1-男，2-女|query|false|string(string)||
|birthDate|出生日期|query|false|string(string)||
|location|所在地|query|false|string(string)||
|bio|个人简介|query|false|string(string)||
|status|状态：0-禁用，1-正常|query|false|string(string)||
|createdAt|创建时间|query|false|string(string)||
|lastLoginAt|最后登录时间|query|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.SxrUsersSearchRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|currentPage||integer(int)|integer(int)|
|total||interface|interface|
|list||github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.SxrUsersListRes|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.SxrUsersListRes|
|&emsp;&emsp;id|用户ID|integer(int64)||
|&emsp;&emsp;username|用户名|string(string)||
|&emsp;&emsp;phone|手机号|string(string)||
|&emsp;&emsp;email|邮箱|string(string)||
|&emsp;&emsp;passwordHash|密码哈希|string(string)||
|&emsp;&emsp;nickname|昵称|string(string)||
|&emsp;&emsp;avatarUrl|头像URL|string(string)||
|&emsp;&emsp;gender|性别：0-未知，1-男，2-女|integer(int)||
|&emsp;&emsp;birthDate|出生日期|string(*gtime.Time)||
|&emsp;&emsp;location|所在地|integer(int)||
|&emsp;&emsp;bio|个人简介|string(string)||
|&emsp;&emsp;status|状态：0-禁用，1-正常|integer(int)||
|&emsp;&emsp;createdAt|创建时间|string(*gtime.Time)||
|&emsp;&emsp;lastLoginAt|最后登录时间|string(*gtime.Time)||
**响应示例**:
```javascript
{
	"currentPage": 0,
	"total": {},
	"list": [
		{
			"id": 0,
			"username": "",
			"phone": "",
			"email": "",
			"passwordHash": "",
			"nickname": "",
			"avatarUrl": "",
			"gender": 0,
			"birthDate": "",
			"location": 0,
			"bio": "",
			"status": 0,
			"createdAt": "",
			"lastLoginAt": ""
		}
	]
}
```
## 7d15c113d7ffabd356972822ed060e03
**接口地址**:`/api/v1/sanxianren/sxrUsers/list`
**请求方式**:`PATCH`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|Authorization|Bearer {{token}}|header|false|string(string)||
|dateRange||query|false|array|string|
|id|用户ID|query|false|string||
|phone|手机号|query|false|string||
|avatarUrl|头像URL|query|false|string||
|birthDate|出生日期|query|false|string||
|location|所在地|query|false|string||
|lastLoginAt|最后登录时间|query|false|string||
|pageSize||query|false|integer||
|username|用户名|query|false|string||
|nickname|昵称|query|false|string||
|gender|性别：0-未知，1-男，2-女|query|false|string||
|bio|个人简介|query|false|string||
|status|状态：0-禁用，1-正常|query|false|string||
|pageNum||query|false|integer||
|createdAt|创建时间|query|false|string||
|orderBy||query|false|string||
|email|邮箱|query|false|string||
|passwordHash|密码哈希|query|false|string||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.SxrUsersSearchRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|currentPage||integer(int)|integer(int)|
|total||interface|interface|
|list||github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.SxrUsersListRes|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.SxrUsersListRes|
|&emsp;&emsp;id|用户ID|integer(int64)||
|&emsp;&emsp;username|用户名|string(string)||
|&emsp;&emsp;phone|手机号|string(string)||
|&emsp;&emsp;email|邮箱|string(string)||
|&emsp;&emsp;passwordHash|密码哈希|string(string)||
|&emsp;&emsp;nickname|昵称|string(string)||
|&emsp;&emsp;avatarUrl|头像URL|string(string)||
|&emsp;&emsp;gender|性别：0-未知，1-男，2-女|integer(int)||
|&emsp;&emsp;birthDate|出生日期|string(*gtime.Time)||
|&emsp;&emsp;location|所在地|integer(int)||
|&emsp;&emsp;bio|个人简介|string(string)||
|&emsp;&emsp;status|状态：0-禁用，1-正常|integer(int)||
|&emsp;&emsp;createdAt|创建时间|string(*gtime.Time)||
|&emsp;&emsp;lastLoginAt|最后登录时间|string(*gtime.Time)||
**响应示例**:
```javascript
{
	"currentPage": 0,
	"total": {},
	"list": [
		{
			"id": 0,
			"username": "",
			"phone": "",
			"email": "",
			"passwordHash": "",
			"nickname": "",
			"avatarUrl": "",
			"gender": 0,
			"birthDate": "",
			"location": 0,
			"bio": "",
			"status": 0,
			"createdAt": "",
			"lastLoginAt": ""
		}
	]
}
```
## ce9fbc6927f652b8b96684cd6eebd494
**接口地址**:`/api/v1/sanxianren/sxrUsers/list`
**请求方式**:`OPTIONS`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|Authorization|Bearer {{token}}|header|false|string(string)||
|pageNum||query|false|integer||
|email|邮箱|query|false|string||
|orderBy||query|false|string||
|passwordHash|密码哈希|query|false|string||
|nickname|昵称|query|false|string||
|gender|性别：0-未知，1-男，2-女|query|false|string||
|birthDate|出生日期|query|false|string||
|dateRange||query|false|array|string|
|id|用户ID|query|false|string||
|username|用户名|query|false|string||
|phone|手机号|query|false|string||
|avatarUrl|头像URL|query|false|string||
|bio|个人简介|query|false|string||
|status|状态：0-禁用，1-正常|query|false|string||
|pageSize||query|false|integer||
|location|所在地|query|false|string||
|createdAt|创建时间|query|false|string||
|lastLoginAt|最后登录时间|query|false|string||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.SxrUsersSearchRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|currentPage||integer(int)|integer(int)|
|total||interface|interface|
|list||github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.SxrUsersListRes|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.SxrUsersListRes|
|&emsp;&emsp;id|用户ID|integer(int64)||
|&emsp;&emsp;username|用户名|string(string)||
|&emsp;&emsp;phone|手机号|string(string)||
|&emsp;&emsp;email|邮箱|string(string)||
|&emsp;&emsp;passwordHash|密码哈希|string(string)||
|&emsp;&emsp;nickname|昵称|string(string)||
|&emsp;&emsp;avatarUrl|头像URL|string(string)||
|&emsp;&emsp;gender|性别：0-未知，1-男，2-女|integer(int)||
|&emsp;&emsp;birthDate|出生日期|string(*gtime.Time)||
|&emsp;&emsp;location|所在地|integer(int)||
|&emsp;&emsp;bio|个人简介|string(string)||
|&emsp;&emsp;status|状态：0-禁用，1-正常|integer(int)||
|&emsp;&emsp;createdAt|创建时间|string(*gtime.Time)||
|&emsp;&emsp;lastLoginAt|最后登录时间|string(*gtime.Time)||
**响应示例**:
```javascript
{
	"currentPage": 0,
	"total": {},
	"list": [
		{
			"id": 0,
			"username": "",
			"phone": "",
			"email": "",
			"passwordHash": "",
			"nickname": "",
			"avatarUrl": "",
			"gender": 0,
			"birthDate": "",
			"location": 0,
			"bio": "",
			"status": 0,
			"createdAt": "",
			"lastLoginAt": ""
		}
	]
}
```
## f8c060be234283c4d9ba4c7d10568d35
**接口地址**:`/api/v1/sanxianren/sxrUsers/list`
**请求方式**:`TRACE`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|Authorization|Bearer {{token}}|header|false|string(string)||
|passwordHash|密码哈希|query|false|string||
|location|所在地|query|false|string||
|dateRange||query|false|array|string|
|phone|手机号|query|false|string||
|avatarUrl|头像URL|query|false|string||
|gender|性别：0-未知，1-男，2-女|query|false|string||
|pageNum||query|false|integer||
|pageSize||query|false|integer||
|id|用户ID|query|false|string||
|email|邮箱|query|false|string||
|nickname|昵称|query|false|string||
|status|状态：0-禁用，1-正常|query|false|string||
|orderBy||query|false|string||
|username|用户名|query|false|string||
|birthDate|出生日期|query|false|string||
|bio|个人简介|query|false|string||
|createdAt|创建时间|query|false|string||
|lastLoginAt|最后登录时间|query|false|string||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.SxrUsersSearchRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|currentPage||integer(int)|integer(int)|
|total||interface|interface|
|list||github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.SxrUsersListRes|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.SxrUsersListRes|
|&emsp;&emsp;id|用户ID|integer(int64)||
|&emsp;&emsp;username|用户名|string(string)||
|&emsp;&emsp;phone|手机号|string(string)||
|&emsp;&emsp;email|邮箱|string(string)||
|&emsp;&emsp;passwordHash|密码哈希|string(string)||
|&emsp;&emsp;nickname|昵称|string(string)||
|&emsp;&emsp;avatarUrl|头像URL|string(string)||
|&emsp;&emsp;gender|性别：0-未知，1-男，2-女|integer(int)||
|&emsp;&emsp;birthDate|出生日期|string(*gtime.Time)||
|&emsp;&emsp;location|所在地|integer(int)||
|&emsp;&emsp;bio|个人简介|string(string)||
|&emsp;&emsp;status|状态：0-禁用，1-正常|integer(int)||
|&emsp;&emsp;createdAt|创建时间|string(*gtime.Time)||
|&emsp;&emsp;lastLoginAt|最后登录时间|string(*gtime.Time)||
**响应示例**:
```javascript
{
	"currentPage": 0,
	"total": {},
	"list": [
		{
			"id": 0,
			"username": "",
			"phone": "",
			"email": "",
			"passwordHash": "",
			"nickname": "",
			"avatarUrl": "",
			"gender": 0,
			"birthDate": "",
			"location": 0,
			"bio": "",
			"status": 0,
			"createdAt": "",
			"lastLoginAt": ""
		}
	]
}
```
## e97605679fdbdf36e4e044591e2be13b
**接口地址**:`/api/v1/sanxianren/sxrUsers/list`
**请求方式**:`HEAD`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|Authorization|Bearer {{token}}|header|false|string(string)||
|avatarUrl|头像URL|query|false|string||
|gender|性别：0-未知，1-男，2-女|query|false|string||
|createdAt|创建时间|query|false|string||
|dateRange||query|false|array|string|
|birthDate|出生日期|query|false|string||
|status|状态：0-禁用，1-正常|query|false|string||
|orderBy||query|false|string||
|id|用户ID|query|false|string||
|pageNum||query|false|integer||
|pageSize||query|false|integer||
|email|邮箱|query|false|string||
|passwordHash|密码哈希|query|false|string||
|nickname|昵称|query|false|string||
|location|所在地|query|false|string||
|bio|个人简介|query|false|string||
|lastLoginAt|最后登录时间|query|false|string||
|username|用户名|query|false|string||
|phone|手机号|query|false|string||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.SxrUsersSearchRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|currentPage||integer(int)|integer(int)|
|total||interface|interface|
|list||github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.SxrUsersListRes|github.com.tiger1103.gfast.v3.internal.app.sanxianren.model.SxrUsersListRes|
|&emsp;&emsp;id|用户ID|integer(int64)||
|&emsp;&emsp;username|用户名|string(string)||
|&emsp;&emsp;phone|手机号|string(string)||
|&emsp;&emsp;email|邮箱|string(string)||
|&emsp;&emsp;passwordHash|密码哈希|string(string)||
|&emsp;&emsp;nickname|昵称|string(string)||
|&emsp;&emsp;avatarUrl|头像URL|string(string)||
|&emsp;&emsp;gender|性别：0-未知，1-男，2-女|integer(int)||
|&emsp;&emsp;birthDate|出生日期|string(*gtime.Time)||
|&emsp;&emsp;location|所在地|integer(int)||
|&emsp;&emsp;bio|个人简介|string(string)||
|&emsp;&emsp;status|状态：0-禁用，1-正常|integer(int)||
|&emsp;&emsp;createdAt|创建时间|string(*gtime.Time)||
|&emsp;&emsp;lastLoginAt|最后登录时间|string(*gtime.Time)||
**响应示例**:
```javascript
{
	"currentPage": 0,
	"total": {},
	"list": [
		{
			"id": 0,
			"username": "",
			"phone": "",
			"email": "",
			"passwordHash": "",
			"nickname": "",
			"avatarUrl": "",
			"gender": 0,
			"birthDate": "",
			"location": 0,
			"bio": "",
			"status": 0,
			"createdAt": "",
			"lastLoginAt": ""
		}
	]
}
```
# 用户
## 获取用户表信息
**接口地址**:`/api/v1/sanxianren/sxrUsers/get`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id||query|true|integer(int64)||
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.SxrUsersGetRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|id|用户ID|integer(int64)|integer(int64)|
|username|用户名|string(string)|string(string)|
|phone|手机号|string(string)|string(string)|
|email|邮箱|string(string)|string(string)|
|passwordHash|密码哈希|string(string)|string(string)|
|nickname|昵称|string(string)|string(string)|
|avatarUrl|头像URL|string(string)|string(string)|
|gender|性别：0-未知，1-男，2-女|integer(int)|integer(int)|
|birthDate|出生日期|string(*gtime.Time)|string(*gtime.Time)|
|location|所在地|integer(int)|integer(int)|
|bio|个人简介|string(string)|string(string)|
|status|状态：0-禁用，1-正常|integer(int)|integer(int)|
|createdAt|创建时间|string(*gtime.Time)|string(*gtime.Time)|
|updatedAt|更新时间|string(*gtime.Time)|string(*gtime.Time)|
|lastLoginAt|最后登录时间|string(*gtime.Time)|string(*gtime.Time)|
**响应示例**:
```javascript
{
	"id": 0,
	"username": "",
	"phone": "",
	"email": "",
	"passwordHash": "",
	"nickname": "",
	"avatarUrl": "",
	"gender": 0,
	"birthDate": "",
	"location": 0,
	"bio": "",
	"status": 0,
	"createdAt": "",
	"updatedAt": "",
	"lastLoginAt": ""
}
```
# 三线人-测试
## 获取当前用户信息
**接口地址**:`/api/v1/sanxianren/test/current`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 获取测试token（仅开发环境）
**接口地址**:`/api/v1/sanxianren/test/token`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|userId|测试用户ID|query|false|integer(uint64)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 验证token状态
**接口地址**:`/api/v1/sanxianren/test/validate`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "token": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ValidateTokenReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ValidateTokenReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ValidateTokenReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.ValidateTokenReq|
|&emsp;&emsp;token|要验证的token||false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
# 三线人-用户个人中心
## 获取用户徽章列表
**接口地址**:`/api/v1/sanxianren/user/badges`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.UserBadgesRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|list|徽章列表|github.com.tiger1103.gfast.v3.api.v1.sanxianren.UserBadgeInfo|github.com.tiger1103.gfast.v3.api.v1.sanxianren.UserBadgeInfo|
|&emsp;&emsp;type|徽章类型|string(string)||
|&emsp;&emsp;name|徽章名称|string(string)||
|&emsp;&emsp;icon|徽章图标|string(string)||
|&emsp;&emsp;description|徽章描述|string(string)||
**响应示例**:
```javascript
{
	"list": [
		{
			"type": "",
			"name": "",
			"icon": "",
			"description": ""
		}
	]
}
```
## 获取用户个人资料
**接口地址**:`/api/v1/sanxianren/user/profile`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.UserProfileRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|id|用户ID|integer(uint64)|integer(uint64)|
|name|用户姓名|string(string)|string(string)|
|avatar|头像|string(string)|string(string)|
|gender|性别|string(string)|string(string)|
|birthYear|出生年份|integer(int)|integer(int)|
|phone|手机号|string(string)|string(string)|
|factoryCode|工厂代码|string(string)|string(string)|
|factory|工厂名称|string(string)|string(string)|
|position|职位|string(string)|string(string)|
|workYears|工作年限|integer(int)|integer(int)|
|bio|个人简介|string(string)|string(string)|
|isVip|是否VIP|boolean(bool)|boolean(bool)|
|vipExpireDate|VIP到期时间|string(string)|string(string)|
|isAuthenticated|是否已认证|boolean(bool)|boolean(bool)|
|authCount|认证数量|integer(int)|integer(int)|
|submissionCount|投稿数量|integer(int)|integer(int)|
|collectionCount|收藏数量|integer(int)|integer(int)|
|followCount|关注数量|integer(int)|integer(int)|
|donateAmount|捐赠金额|number(float64)|number(float64)|
|badges|用户徽章|github.com.tiger1103.gfast.v3.api.v1.sanxianren.UserBadgeInfo|github.com.tiger1103.gfast.v3.api.v1.sanxianren.UserBadgeInfo|
|&emsp;&emsp;type|徽章类型|string(string)||
|&emsp;&emsp;name|徽章名称|string(string)||
|&emsp;&emsp;icon|徽章图标|string(string)||
|&emsp;&emsp;description|徽章描述|string(string)||
|factories|关联工厂|github.com.tiger1103.gfast.v3.api.v1.sanxianren.UserFactoryInfo|github.com.tiger1103.gfast.v3.api.v1.sanxianren.UserFactoryInfo|
|&emsp;&emsp;code|工厂代码|string(string)||
|&emsp;&emsp;name|工厂名称|string(string)||
|&emsp;&emsp;position|职位|string(string)||
**响应示例**:
```javascript
{
	"id": 0,
	"name": "",
	"avatar": "",
	"gender": "",
	"birthYear": 0,
	"phone": "",
	"factoryCode": "",
	"factory": "",
	"position": "",
	"workYears": 0,
	"bio": "",
	"isVip": true,
	"vipExpireDate": "",
	"isAuthenticated": true,
	"authCount": 0,
	"submissionCount": 0,
	"collectionCount": 0,
	"followCount": 0,
	"donateAmount": 0,
	"badges": [
		{
			"type": "",
			"name": "",
			"icon": "",
			"description": ""
		}
	],
	"factories": [
		{
			"code": "",
			"name": "",
			"position": ""
		}
	]
}
```
## 编辑用户个人资料
**接口地址**:`/api/v1/sanxianren/user/profile/edit`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|name|姓名|query|true|string||
|Authorization|Bearer {{token}}|header|false|string(string)||
|gender|性别|query|false|string||
|birthYear|出生年份|query|false|integer||
|phone|手机号|query|false|string||
|bio|个人简介|query|false|string||
|avatar|头像|query|false|string||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.EditUserProfileRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 获取用户投稿列表
**接口地址**:`/api/v1/sanxianren/user/submissions`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|dateRange||query|false|array|string|
|pageNum||query|false|integer(int)||
|pageSize||query|false|integer(int)||
|orderBy||query|false|string(string)||
|status|投稿状态：pending-待审核，reviewing-审核中，approved-已通过，rejected-已拒绝，published-已发布|query|false|string(string)||
|type|投稿类型：story-故事，factory-工厂，people-人物|query|false|string(string)||
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.UserSubmissionsRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|currentPage||integer(int)|integer(int)|
|total||interface|interface|
|list|投稿列表|github.com.tiger1103.gfast.v3.api.v1.sanxianren.UserSubmissionInfo|github.com.tiger1103.gfast.v3.api.v1.sanxianren.UserSubmissionInfo|
|&emsp;&emsp;id|投稿ID|integer(uint64)||
|&emsp;&emsp;submissionType|投稿类型|string(string)||
|&emsp;&emsp;title|投稿标题|string(string)||
|&emsp;&emsp;status|投稿状态|integer(int)||
|&emsp;&emsp;statusText|状态文本|string(string)||
|&emsp;&emsp;adminComment|审核意见|string(string)||
|&emsp;&emsp;createdAt|创建时间|string(string)||
|&emsp;&emsp;reviewedAt|审核时间|string(string)||
|&emsp;&emsp;publishedAt|发布时间|string(string)||
**响应示例**:
```javascript
{
	"currentPage": 0,
	"total": {},
	"list": [
		{
			"id": 0,
			"submissionType": "",
			"title": "",
			"status": 0,
			"statusText": "",
			"adminComment": "",
			"createdAt": "",
			"reviewedAt": "",
			"publishedAt": ""
		}
	]
}
```
## 获取用户投稿统计
**接口地址**:`/api/v1/sanxianren/user/submissions/stats`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.UserSubmissionStatsRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|total|总数|integer(int)|integer(int)|
|pending|待审核|integer(int)|integer(int)|
|reviewing|审核中|integer(int)|integer(int)|
|approved|已通过|integer(int)|integer(int)|
|rejected|已拒绝|integer(int)|integer(int)|
|published|已发布|integer(int)|integer(int)|
**响应示例**:
```javascript
{
	"total": 0,
	"pending": 0,
	"reviewing": 0,
	"approved": 0,
	"rejected": 0,
	"published": 0
}
```
## 获取用户VIP信息
**接口地址**:`/api/v1/sanxianren/user/vip`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.UserVipInfoRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|isVip|是否VIP|boolean(bool)|boolean(bool)|
|vipType|VIP类型|string(string)|string(string)|
|startDate|开始时间|string(string)|string(string)|
|endDate|结束时间|string(string)|string(string)|
|paymentAmount|支付金额|number(float64)|number(float64)|
|orderNo|订单号|string(string)|string(string)|
**响应示例**:
```javascript
{
	"isVip": true,
	"vipType": "",
	"startDate": "",
	"endDate": "",
	"paymentAmount": 0,
	"orderNo": ""
}
```
# 三线人-用户收藏
## 获取收藏列表
**接口地址**:`/api/v1/sanxianren/user/collections`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|contentType|内容类型：story-故事，factory-工厂，people-人物，all-全部|query|false|string(string)||
|dateRange||query|false|array|string|
|pageNum||query|false|integer(int)||
|pageSize||query|false|integer(int)||
|orderBy||query|false|string(string)||
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.GetCollectionsRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|currentPage||integer(int)|integer(int)|
|total||interface|interface|
|list|收藏列表|github.com.tiger1103.gfast.v3.api.v1.sanxianren.CollectionInfo|github.com.tiger1103.gfast.v3.api.v1.sanxianren.CollectionInfo|
|&emsp;&emsp;id|收藏ID|integer(uint64)||
|&emsp;&emsp;contentType|内容类型|string(string)||
|&emsp;&emsp;contentId|内容ID|integer(uint64)||
|&emsp;&emsp;title|内容标题|string(string)||
|&emsp;&emsp;summary|内容摘要|string(string)||
|&emsp;&emsp;coverImage|封面图片|string(string)||
|&emsp;&emsp;createdAt|收藏时间|string(string)||
**响应示例**:
```javascript
{
	"currentPage": 0,
	"total": {},
	"list": [
		{
			"id": 0,
			"contentType": "",
			"contentId": 0,
			"title": "",
			"summary": "",
			"coverImage": "",
			"createdAt": ""
		}
	]
}
```
## 添加收藏
**接口地址**:`/api/v1/sanxianren/user/collections/add`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|contentType|内容类型：story-故事，factory-工厂，people-人物|query|true|string||
|contentId|内容ID|query|true|integer||
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.AddCollectionRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 检查收藏状态
**接口地址**:`/api/v1/sanxianren/user/collections/check`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|contentType|内容类型：story-故事，factory-工厂，people-人物|query|true|string(string)||
|contentId|内容ID|query|true|integer(uint64)||
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.CheckCollectionRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|isCollected|是否已收藏|boolean(bool)|boolean(bool)|
**响应示例**:
```javascript
{
	"isCollected": true
}
```
## 取消收藏
**接口地址**:`/api/v1/sanxianren/user/collections/remove`
**请求方式**:`DELETE`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|contentType|内容类型：story-故事，factory-工厂，people-人物|query|true|string(string)||
|contentId|内容ID|query|true|integer(uint64)||
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.RemoveCollectionRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
# 三线人-用户浏览历史
## 获取浏览历史
**接口地址**:`/api/v1/sanxianren/user/history`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|contentType|内容类型：story-故事，factory-工厂，people-人物，all-全部|query|false|string(string)||
|dateRange||query|false|array|string|
|pageNum||query|false|integer(int)||
|pageSize||query|false|integer(int)||
|orderBy||query|false|string(string)||
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.GetHistoryRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|currentPage||integer(int)|integer(int)|
|total||interface|interface|
|list|浏览历史列表|github.com.tiger1103.gfast.v3.api.v1.sanxianren.HistoryInfo|github.com.tiger1103.gfast.v3.api.v1.sanxianren.HistoryInfo|
|&emsp;&emsp;id|历史记录ID|integer(uint64)||
|&emsp;&emsp;contentType|内容类型|string(string)||
|&emsp;&emsp;contentId|内容ID|integer(uint64)||
|&emsp;&emsp;title|内容标题|string(string)||
|&emsp;&emsp;summary|内容摘要|string(string)||
|&emsp;&emsp;coverImage|封面图片|string(string)||
|&emsp;&emsp;viewedAt|浏览时间|string(string)||
**响应示例**:
```javascript
{
	"currentPage": 0,
	"total": {},
	"list": [
		{
			"id": 0,
			"contentType": "",
			"contentId": 0,
			"title": "",
			"summary": "",
			"coverImage": "",
			"viewedAt": ""
		}
	]
}
```
## 添加浏览历史
**接口地址**:`/api/v1/sanxianren/user/history/add`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|contentId|内容ID|query|true|integer||
|contentType|内容类型：story-故事，factory-工厂，people-人物|query|true|string||
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.AddHistoryRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 清空浏览历史
**接口地址**:`/api/v1/sanxianren/user/history/clear`
**请求方式**:`DELETE`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|contentType|内容类型：story-故事，factory-工厂，people-人物，all-全部|query|false|string(string)||
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.ClearHistoryRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
# 用户认证
## 用户登录
**接口地址**:`/api/v1/sanxianren/user/login`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "phone": "",
  "password": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.UserLoginReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.UserLoginReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.UserLoginReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.UserLoginReq|
|&emsp;&emsp;phone|手机号||true|string(string)||
|&emsp;&emsp;password|密码||true|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 用户注册
**接口地址**:`/api/v1/sanxianren/user/register`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "phone": "",
  "password": "",
  "nickname": "",
  "sms_code": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.UserRegisterReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.UserRegisterReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.UserRegisterReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.UserRegisterReq|
|&emsp;&emsp;phone|手机号||true|string(string)||
|&emsp;&emsp;password|密码||true|string(string)||
|&emsp;&emsp;nickname|昵称||true|string(string)||
|&emsp;&emsp;sms_code|短信验证码||true|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 发送短信验证码
**接口地址**:`/api/v1/sanxianren/user/send-sms-code`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "phone": "",
  "type": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.SendSmsCodeReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.SendSmsCodeReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.SendSmsCodeReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.SendSmsCodeReq|
|&emsp;&emsp;phone|手机号||true|string(string)||
|&emsp;&emsp;type|验证码类型：register-注册，login-登录，reset-重置密码,可用值:register,login,reset||true|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 短信验证码登录
**接口地址**:`/api/v1/sanxianren/user/sms-login`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "phone": "",
  "sms_code": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.SmsLoginReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.SmsLoginReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.SmsLoginReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.SmsLoginReq|
|&emsp;&emsp;phone|手机号||true|string(string)||
|&emsp;&emsp;sms_code|短信验证码||true|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
# 三线人-用户通知
## 获取通知列表
**接口地址**:`/api/v1/sanxianren/user/notifications`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|notificationType|通知类型：system-系统通知，submission-投稿通知，interaction-互动通知，all-全部|query|false|string(string)||
|isRead|是否已读：1-已读，0-未读，null-全部|query|false|integer(*int)||
|dateRange||query|false|array|string|
|pageNum||query|false|integer(int)||
|pageSize||query|false|integer(int)||
|orderBy||query|false|string(string)||
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.GetNotificationsRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|currentPage||integer(int)|integer(int)|
|total||interface|interface|
|list|通知列表|github.com.tiger1103.gfast.v3.api.v1.sanxianren.NotificationInfo|github.com.tiger1103.gfast.v3.api.v1.sanxianren.NotificationInfo|
|&emsp;&emsp;id|通知ID|integer(uint64)||
|&emsp;&emsp;notificationType|通知类型|string(string)||
|&emsp;&emsp;title|通知标题|string(string)||
|&emsp;&emsp;content|通知内容|string(string)||
|&emsp;&emsp;isRead|是否已读|integer(int)||
|&emsp;&emsp;relatedType|关联类型|string(string)||
|&emsp;&emsp;relatedId|关联ID|integer(uint64)||
|&emsp;&emsp;createdAt|创建时间|string(string)||
|unreadCount|未读数量|integer(int)|integer(int)|
**响应示例**:
```javascript
{
	"currentPage": 0,
	"total": {},
	"list": [
		{
			"id": 0,
			"notificationType": "",
			"title": "",
			"content": "",
			"isRead": 0,
			"relatedType": "",
			"relatedId": 0,
			"createdAt": ""
		}
	],
	"unreadCount": 0
}
```
## 标记通知已读
**接口地址**:`/api/v1/sanxianren/user/notifications/read`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|Authorization|Bearer {{token}}|header|false|string(string)||
|notificationId|通知ID，0表示全部标记为已读|query|false|integer||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.MarkNotificationReadRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 获取通知统计
**接口地址**:`/api/v1/sanxianren/user/notifications/stats`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.sanxianren.GetNotificationStatsRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|total|总数|integer(int)|integer(int)|
|unread|未读数|integer(int)|integer(int)|
|system|系统通知数|integer(int)|integer(int)|
|submission|投稿通知数|integer(int)|integer(int)|
|interaction|互动通知数|integer(int)|integer(int)|
**响应示例**:
```javascript
{
	"total": 0,
	"unread": 0,
	"system": 0,
	"submission": 0,
	"interaction": 0
}
```
# Wikipedia集成
## 登录Wikipedia
**接口地址**:`/api/v1/sanxianren/wikipedia/wikipedia/auth/login`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "username": "",
  "password": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaLoginReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaLoginReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaLoginReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaLoginReq|
|&emsp;&emsp;username|用户名||true|string(string)||
|&emsp;&emsp;password|密码||true|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 验证Wikipedia内容格式
**接口地址**:`/api/v1/sanxianren/wikipedia/wikipedia/content/validate`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "content": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaValidateContentReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaValidateContentReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaValidateContentReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaValidateContentReq|
|&emsp;&emsp;content|要验证的内容||true|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 提交工厂数据到Wikipedia
**接口地址**:`/api/v1/sanxianren/wikipedia/wikipedia/factory/submit`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "submission_id": 0,
  "data_type": "",
  "title": "",
  "content": "",
  "summary": "",
  "metadata": {}
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaSubmitFactoryDataReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaSubmitFactoryDataReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaSubmitFactoryDataReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaSubmitFactoryDataReq|
|&emsp;&emsp;submission_id|提交记录ID||true|integer(int64)||
|&emsp;&emsp;data_type|数据类型（factory/people/story）,可用值:factory,people,story||true|string(string)||
|&emsp;&emsp;title|页面标题||true|string(string)||
|&emsp;&emsp;content|页面内容||true|string(string)||
|&emsp;&emsp;summary|编辑摘要||false|string(string)||
|&emsp;&emsp;metadata|元数据||false|object(map[string]string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 上传文件到Wikipedia
**接口地址**:`/api/v1/sanxianren/wikipedia/wikipedia/file/upload`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "file_name": "",
  "file_content": "",
  "description": "",
  "license": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaUploadFileReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaUploadFileReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaUploadFileReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaUploadFileReq|
|&emsp;&emsp;file_name|文件名||true|string(string)||
|&emsp;&emsp;file_content|文件内容（base64编码）||true|string(binary)||
|&emsp;&emsp;description|文件描述||false|string(string)||
|&emsp;&emsp;license|许可证信息||false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 创建Wikipedia页面
**接口地址**:`/api/v1/sanxianren/wikipedia/wikipedia/page/create`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "title": "",
  "content": "",
  "summary": "",
  "categories": [],
  "tags": [],
  "metadata": {}
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaCreatePageReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaCreatePageReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaCreatePageReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaCreatePageReq|
|&emsp;&emsp;title|页面标题||true|string(string)||
|&emsp;&emsp;content|页面内容（wikitext格式）||true|string(string)||
|&emsp;&emsp;summary|编辑摘要||false|string(string)||
|&emsp;&emsp;categories|页面分类||false|array([]string)|string|
|&emsp;&emsp;tags|页面标签||false|array([]string)|string|
|&emsp;&emsp;metadata|页面元数据||false|object(map[string]string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 获取Wikipedia页面
**接口地址**:`/api/v1/sanxianren/wikipedia/wikipedia/page/get`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|page_id|页面ID（与title二选一）|query|false|integer(int64)||
|title|页面标题（与page_id二选一）|query|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 搜索Wikipedia页面
**接口地址**:`/api/v1/sanxianren/wikipedia/wikipedia/page/search`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|query|搜索关键词|query|true|string(string)||
|limit|返回结果数量限制（默认10，最大50）|query|false|integer(int)||
|namespace|命名空间|query|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 更新Wikipedia页面
**接口地址**:`/api/v1/sanxianren/wikipedia/wikipedia/page/update`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "page_id": 0,
  "title": "",
  "content": "",
  "summary": "",
  "minor_edit": true
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaUpdatePageReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaUpdatePageReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaUpdatePageReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaUpdatePageReq|
|&emsp;&emsp;page_id|页面ID（与title二选一）||false|integer(int64)||
|&emsp;&emsp;title|页面标题（与page_id二选一）||false|string(string)||
|&emsp;&emsp;content|页面内容（wikitext格式）||true|string(string)||
|&emsp;&emsp;summary|编辑摘要||false|string(string)||
|&emsp;&emsp;minor_edit|是否为小编辑||false|boolean(bool)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 获取Wikipedia编辑令牌
**接口地址**:`/api/v1/sanxianren/wikipedia/wikipedia/token/edit`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
暂无
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 登录Wikipedia
**接口地址**:`/api/v1/wikipedia/wikipedia/auth/login`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "username": "",
  "password": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaLoginReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaLoginReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaLoginReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaLoginReq|
|&emsp;&emsp;username|用户名||true|string(string)||
|&emsp;&emsp;password|密码||true|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 验证Wikipedia内容格式
**接口地址**:`/api/v1/wikipedia/wikipedia/content/validate`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "content": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaValidateContentReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaValidateContentReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaValidateContentReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaValidateContentReq|
|&emsp;&emsp;content|要验证的内容||true|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 提交工厂数据到Wikipedia
**接口地址**:`/api/v1/wikipedia/wikipedia/factory/submit`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "submission_id": 0,
  "data_type": "",
  "title": "",
  "content": "",
  "summary": "",
  "metadata": {}
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaSubmitFactoryDataReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaSubmitFactoryDataReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaSubmitFactoryDataReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaSubmitFactoryDataReq|
|&emsp;&emsp;submission_id|提交记录ID||true|integer(int64)||
|&emsp;&emsp;data_type|数据类型（factory/people/story）,可用值:factory,people,story||true|string(string)||
|&emsp;&emsp;title|页面标题||true|string(string)||
|&emsp;&emsp;content|页面内容||true|string(string)||
|&emsp;&emsp;summary|编辑摘要||false|string(string)||
|&emsp;&emsp;metadata|元数据||false|object(map[string]string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 上传文件到Wikipedia
**接口地址**:`/api/v1/wikipedia/wikipedia/file/upload`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "file_name": "",
  "file_content": "",
  "description": "",
  "license": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaUploadFileReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaUploadFileReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaUploadFileReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaUploadFileReq|
|&emsp;&emsp;file_name|文件名||true|string(string)||
|&emsp;&emsp;file_content|文件内容（base64编码）||true|string(binary)||
|&emsp;&emsp;description|文件描述||false|string(string)||
|&emsp;&emsp;license|许可证信息||false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 创建Wikipedia页面
**接口地址**:`/api/v1/wikipedia/wikipedia/page/create`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "title": "",
  "content": "",
  "summary": "",
  "categories": [],
  "tags": [],
  "metadata": {}
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaCreatePageReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaCreatePageReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaCreatePageReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaCreatePageReq|
|&emsp;&emsp;title|页面标题||true|string(string)||
|&emsp;&emsp;content|页面内容（wikitext格式）||true|string(string)||
|&emsp;&emsp;summary|编辑摘要||false|string(string)||
|&emsp;&emsp;categories|页面分类||false|array([]string)|string|
|&emsp;&emsp;tags|页面标签||false|array([]string)|string|
|&emsp;&emsp;metadata|页面元数据||false|object(map[string]string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 获取Wikipedia页面
**接口地址**:`/api/v1/wikipedia/wikipedia/page/get`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|page_id|页面ID（与title二选一）|query|false|integer(int64)||
|title|页面标题（与page_id二选一）|query|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 搜索Wikipedia页面
**接口地址**:`/api/v1/wikipedia/wikipedia/page/search`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|query|搜索关键词|query|true|string(string)||
|limit|返回结果数量限制（默认10，最大50）|query|false|integer(int)||
|namespace|命名空间|query|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 更新Wikipedia页面
**接口地址**:`/api/v1/wikipedia/wikipedia/page/update`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "page_id": 0,
  "title": "",
  "content": "",
  "summary": "",
  "minor_edit": true
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaUpdatePageReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaUpdatePageReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaUpdatePageReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.WikipediaUpdatePageReq|
|&emsp;&emsp;page_id|页面ID（与title二选一）||false|integer(int64)||
|&emsp;&emsp;title|页面标题（与page_id二选一）||false|string(string)||
|&emsp;&emsp;content|页面内容（wikitext格式）||true|string(string)||
|&emsp;&emsp;summary|编辑摘要||false|string(string)||
|&emsp;&emsp;minor_edit|是否为小编辑||false|boolean(bool)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 获取Wikipedia编辑令牌
**接口地址**:`/api/v1/wikipedia/wikipedia/token/edit`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
暂无
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
# 统计分析
## 统计概览
**接口地址**:`/api/v1/statistics/dashboard/overview`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
暂无
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 内容统计
**接口地址**:`/api/v1/statistics/statistics/content`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|content_type|内容类型（story/factory/people）|query|false|string(string)||
|start_date|开始日期（YYYY-MM-DD）|query|false|string(string)||
|end_date|结束日期（YYYY-MM-DD）|query|false|string(string)||
|group_by|分组方式（day/week/month）|query|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 工厂统计
**接口地址**:`/api/v1/statistics/statistics/factory`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|factory_type|工厂类型|query|false|string(string)||
|start_date|开始日期（YYYY-MM-DD）|query|false|string(string)||
|end_date|结束日期（YYYY-MM-DD）|query|false|string(string)||
|group_by|分组方式（day/week/month）|query|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 热门内容
**接口地址**:`/api/v1/statistics/statistics/hot-content`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|content_type|内容类型（story/factory/people）|query|false|string(string)||
|stat_date|统计日期（YYYY-MM-DD）|query|false|string(string)||
|limit|限制数量|query|false|integer(int)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 实时统计
**接口地址**:`/api/v1/statistics/statistics/realtime`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
暂无
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 地域统计
**接口地址**:`/api/v1/statistics/statistics/region`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|province|省份|query|false|string(string)||
|city|城市|query|false|string(string)||
|start_date|开始日期（YYYY-MM-DD）|query|false|string(string)||
|end_date|结束日期（YYYY-MM-DD）|query|false|string(string)||
|page_num|页码|query|false|integer(int)||
|page_size|每页数量|query|false|integer(int)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 趋势分析
**接口地址**:`/api/v1/statistics/statistics/trend`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|metric_type|指标类型（user/content/engagement）|query|false|string(string)||
|metric_name|指标名称|query|false|string(string)||
|start_date|开始日期（YYYY-MM-DD）|query|false|string(string)||
|end_date|结束日期（YYYY-MM-DD）|query|false|string(string)||
|group_by|分组方式（hour/day/week/month）|query|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 用户统计
**接口地址**:`/api/v1/statistics/statistics/users`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|start_date|开始日期（YYYY-MM-DD）|query|false|string(string)||
|end_date|结束日期（YYYY-MM-DD）|query|false|string(string)||
|group_by|分组方式（day/week/month）|query|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
# 系统后台-缓存管理
## 清除缓存
**接口地址**:`/api/v1/system/cache/remove`
**请求方式**:`DELETE`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.CacheRemoveRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
# 系统后台-系统参数管理
## 添加系统参数
**接口地址**:`/api/v1/system/config/add`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "configName": "",
  "configKey": "",
  "configValue": "",
  "configType": 0,
  "remark": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.system.ConfigAddReq|github.com.tiger1103.gfast.v3.api.v1.system.ConfigAddReq|body|true|github.com.tiger1103.gfast.v3.api.v1.system.ConfigAddReq|github.com.tiger1103.gfast.v3.api.v1.system.ConfigAddReq|
|&emsp;&emsp;configName|||true|string(string)||
|&emsp;&emsp;configKey|||true|string(string)||
|&emsp;&emsp;configValue|||true|string(string)||
|&emsp;&emsp;configType|可用值:0,1||true|integer(int)||
|&emsp;&emsp;remark|||false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 删除系统参数
**接口地址**:`/api/v1/system/config/delete`
**请求方式**:`DELETE`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|ids||query|false|array|integer|
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 修改系统参数
**接口地址**:`/api/v1/system/config/edit`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "configId": 0,
  "configName": "",
  "configKey": "",
  "configValue": "",
  "configType": 0,
  "remark": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.system.ConfigEditReq|github.com.tiger1103.gfast.v3.api.v1.system.ConfigEditReq|body|true|github.com.tiger1103.gfast.v3.api.v1.system.ConfigEditReq|github.com.tiger1103.gfast.v3.api.v1.system.ConfigEditReq|
|&emsp;&emsp;configId|||true|integer(int64)||
|&emsp;&emsp;configName|||true|string(string)||
|&emsp;&emsp;configKey|||true|string(string)||
|&emsp;&emsp;configValue|||true|string(string)||
|&emsp;&emsp;configType|可用值:0,1||true|integer(int)||
|&emsp;&emsp;remark|||false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 获取系统参数
**接口地址**:`/api/v1/system/config/get`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id||query|false|integer(int)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.ConfigGetRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|data||github.com.tiger1103.gfast.v3.internal.app.common.model.entity.SysConfig|github.com.tiger1103.gfast.v3.internal.app.common.model.entity.SysConfig|
|&emsp;&emsp;configId|参数主键|integer(uint)||
|&emsp;&emsp;configName|参数名称|string(string)||
|&emsp;&emsp;configKey|参数键名|string(string)||
|&emsp;&emsp;configValue|参数键值|string(string)||
|&emsp;&emsp;configType|系统内置（Y是 N否）|integer(int)||
|&emsp;&emsp;createBy|创建者|integer(uint)||
|&emsp;&emsp;updateBy|更新者|integer(uint)||
|&emsp;&emsp;remark|备注|string(string)||
|&emsp;&emsp;createdAt|创建时间|string(*gtime.Time)||
|&emsp;&emsp;updatedAt|修改时间|string(*gtime.Time)||
**响应示例**:
```javascript
{
	"data": {
		"configId": 0,
		"configName": "",
		"configKey": "",
		"configValue": "",
		"configType": 0,
		"createBy": 0,
		"updateBy": 0,
		"remark": "",
		"createdAt": "",
		"updatedAt": ""
	}
}
```
## 系统参数列表
**接口地址**:`/api/v1/system/config/list`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|configName||query|false|string(string)||
|configKey||query|false|string(string)||
|configType||query|false|string(string)||
|dateRange||query|false|array|string|
|pageNum||query|false|integer(int)||
|pageSize||query|false|integer(int)||
|orderBy||query|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.ConfigSearchRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|list||github.com.tiger1103.gfast.v3.internal.app.common.model.entity.SysConfig|github.com.tiger1103.gfast.v3.internal.app.common.model.entity.SysConfig|
|&emsp;&emsp;configId|参数主键|integer(uint)||
|&emsp;&emsp;configName|参数名称|string(string)||
|&emsp;&emsp;configKey|参数键名|string(string)||
|&emsp;&emsp;configValue|参数键值|string(string)||
|&emsp;&emsp;configType|系统内置（Y是 N否）|integer(int)||
|&emsp;&emsp;createBy|创建者|integer(uint)||
|&emsp;&emsp;updateBy|更新者|integer(uint)||
|&emsp;&emsp;remark|备注|string(string)||
|&emsp;&emsp;createdAt|创建时间|string(*gtime.Time)||
|&emsp;&emsp;updatedAt|修改时间|string(*gtime.Time)||
|currentPage||integer(int)|integer(int)|
|total||interface|interface|
**响应示例**:
```javascript
{
	"list": [
		{
			"configId": 0,
			"configName": "",
			"configKey": "",
			"configValue": "",
			"configType": 0,
			"createBy": 0,
			"updateBy": 0,
			"remark": "",
			"createdAt": "",
			"updatedAt": ""
		}
	],
	"currentPage": 0,
	"total": {}
}
```
# 系统后台-部门管理
## 添加部门
**接口地址**:`/api/v1/system/dept/add`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "parentId": 0,
  "deptName": "",
  "orderNum": 0,
  "leader": "",
  "phone": "",
  "email": "",
  "status": 0
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.system.DeptAddReq|github.com.tiger1103.gfast.v3.api.v1.system.DeptAddReq|body|true|github.com.tiger1103.gfast.v3.api.v1.system.DeptAddReq|github.com.tiger1103.gfast.v3.api.v1.system.DeptAddReq|
|&emsp;&emsp;parentId|||true|integer(int)||
|&emsp;&emsp;deptName|||true|string(string)||
|&emsp;&emsp;orderNum|||true|integer(int)||
|&emsp;&emsp;leader|||false|string(string)||
|&emsp;&emsp;phone|||false|string(string)||
|&emsp;&emsp;email|||false|string(string)||
|&emsp;&emsp;status|||true|integer(uint)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 删除部门
**接口地址**:`/api/v1/system/dept/delete`
**请求方式**:`DELETE`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id||query|true|integer(uint64)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 修改部门
**接口地址**:`/api/v1/system/dept/edit`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "deptId": 0,
  "parentId": 0,
  "deptName": "",
  "orderNum": 0,
  "leader": "",
  "phone": "",
  "email": "",
  "status": 0
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.system.DeptEditReq|github.com.tiger1103.gfast.v3.api.v1.system.DeptEditReq|body|true|github.com.tiger1103.gfast.v3.api.v1.system.DeptEditReq|github.com.tiger1103.gfast.v3.api.v1.system.DeptEditReq|
|&emsp;&emsp;deptId|||true|integer(int)||
|&emsp;&emsp;parentId|||true|integer(int)||
|&emsp;&emsp;deptName|||true|string(string)||
|&emsp;&emsp;orderNum|||true|integer(int)||
|&emsp;&emsp;leader|||false|string(string)||
|&emsp;&emsp;phone|||false|string(string)||
|&emsp;&emsp;email|||false|string(string)||
|&emsp;&emsp;status|||true|integer(uint)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 部门列表
**接口地址**:`/api/v1/system/dept/list`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|deptName||query|false|string(string)||
|status||query|false|string(string)||
|ShowAll||query|false|boolean(bool)||
|UserId||query|false|integer(uint64)||
|UserDeptId||query|false|integer(uint64)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.DeptSearchRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|deptList||github.com.tiger1103.gfast.v3.internal.app.system.model.entity.SysDept|github.com.tiger1103.gfast.v3.internal.app.system.model.entity.SysDept|
|&emsp;&emsp;deptId|部门id|integer(uint64)||
|&emsp;&emsp;parentId|父部门id|integer(uint64)||
|&emsp;&emsp;ancestors|祖级列表|string(string)||
|&emsp;&emsp;deptName|部门名称|string(string)||
|&emsp;&emsp;orderNum|显示顺序|integer(int)||
|&emsp;&emsp;leader|负责人|array([]int)|integer|
|&emsp;&emsp;phone|联系电话|string(string)||
|&emsp;&emsp;email|邮箱|string(string)||
|&emsp;&emsp;status|部门状态（0正常 1停用）|integer(uint)||
|&emsp;&emsp;createdBy|创建人|integer(uint64)||
|&emsp;&emsp;updatedBy|修改人|integer(int64)||
|&emsp;&emsp;createdAt|创建时间|string(*gtime.Time)||
|&emsp;&emsp;updatedAt|修改时间|string(*gtime.Time)||
|&emsp;&emsp;deletedAt|删除时间|string(*gtime.Time)||
**响应示例**:
```javascript
{
	"deptList": [
		{
			"deptId": 0,
			"parentId": 0,
			"ancestors": "",
			"deptName": "",
			"orderNum": 0,
			"leader": [],
			"phone": "",
			"email": "",
			"status": 0,
			"createdBy": 0,
			"updatedBy": 0,
			"createdAt": "",
			"updatedAt": "",
			"deletedAt": ""
		}
	]
}
```
## 获取部门树形菜单
**接口地址**:`/api/v1/system/dept/treeSelect`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|showOwner||query|false|boolean(bool)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.DeptTreeSelectRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|deps||github.com.tiger1103.gfast.v3.internal.app.system.model.SysDeptTreeRes|github.com.tiger1103.gfast.v3.internal.app.system.model.SysDeptTreeRes|
|&emsp;&emsp;deptId|部门id|integer(uint64)||
|&emsp;&emsp;parentId|父部门id|integer(uint64)||
|&emsp;&emsp;ancestors|祖级列表|string(string)||
|&emsp;&emsp;deptName|部门名称|string(string)||
|&emsp;&emsp;orderNum|显示顺序|integer(int)||
|&emsp;&emsp;leader|负责人|array([]int)|integer|
|&emsp;&emsp;phone|联系电话|string(string)||
|&emsp;&emsp;email|邮箱|string(string)||
|&emsp;&emsp;status|部门状态（0正常 1停用）|integer(uint)||
|&emsp;&emsp;createdBy|创建人|integer(uint64)||
|&emsp;&emsp;updatedBy|修改人|integer(int64)||
|&emsp;&emsp;createdAt|创建时间|string(*gtime.Time)||
|&emsp;&emsp;updatedAt|修改时间|string(*gtime.Time)||
|&emsp;&emsp;deletedAt|删除时间|string(*gtime.Time)||
|&emsp;&emsp;children||array([]*model.SysDeptTreeRes)|github.com.tiger1103.gfast.v3.internal.app.system.model.SysDeptTreeRes|
**响应示例**:
```javascript
{
	"deps": [
		{
			"deptId": 0,
			"parentId": 0,
			"ancestors": "",
			"deptName": "",
			"orderNum": 0,
			"leader": [],
			"phone": "",
			"email": "",
			"status": 0,
			"createdBy": 0,
			"updatedBy": 0,
			"createdAt": "",
			"updatedAt": "",
			"deletedAt": "",
			"children": [
				{
					"deptId": 0,
					"parentId": 0,
					"ancestors": "",
					"deptName": "",
					"orderNum": 0,
					"leader": [],
					"phone": "",
					"email": "",
					"status": 0,
					"createdBy": 0,
					"updatedBy": 0,
					"createdAt": "",
					"updatedAt": "",
					"deletedAt": "",
					"children": [
						{}
					]
				}
			]
		}
	]
}
```
# 系统后台-字典管理
## 添加字典数据
**接口地址**:`/api/v1/system/dict/data/add`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "dictLabel": "",
  "dictValue": "",
  "dictType": "",
  "dictSort": 0,
  "cssClass": "",
  "listClass": "",
  "isDefault": 0,
  "status": 0,
  "remark": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.system.DictDataAddReq|github.com.tiger1103.gfast.v3.api.v1.system.DictDataAddReq|body|true|github.com.tiger1103.gfast.v3.api.v1.system.DictDataAddReq|github.com.tiger1103.gfast.v3.api.v1.system.DictDataAddReq|
|&emsp;&emsp;dictLabel|||true|string(string)||
|&emsp;&emsp;dictValue|||true|string(string)||
|&emsp;&emsp;dictType|||true|string(string)||
|&emsp;&emsp;dictSort|||false|integer(int)||
|&emsp;&emsp;cssClass|||false|string(string)||
|&emsp;&emsp;listClass|||false|string(string)||
|&emsp;&emsp;isDefault|可用值:0,1||true|integer(int)||
|&emsp;&emsp;status|可用值:0,1||true|integer(int)||
|&emsp;&emsp;remark|||false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 删除字典数据
**接口地址**:`/api/v1/system/dict/data/delete`
**请求方式**:`DELETE`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|ids||query|false|array|integer|
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 修改字典数据
**接口地址**:`/api/v1/system/dict/data/edit`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "dictCode": 0,
  "dictLabel": "",
  "dictValue": "",
  "dictType": "",
  "dictSort": 0,
  "cssClass": "",
  "listClass": "",
  "isDefault": 0,
  "status": 0,
  "remark": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.system.DictDataEditReq|github.com.tiger1103.gfast.v3.api.v1.system.DictDataEditReq|body|true|github.com.tiger1103.gfast.v3.api.v1.system.DictDataEditReq|github.com.tiger1103.gfast.v3.api.v1.system.DictDataEditReq|
|&emsp;&emsp;dictCode|||true|integer(int)||
|&emsp;&emsp;dictLabel|||true|string(string)||
|&emsp;&emsp;dictValue|||true|string(string)||
|&emsp;&emsp;dictType|||true|string(string)||
|&emsp;&emsp;dictSort|||false|integer(int)||
|&emsp;&emsp;cssClass|||false|string(string)||
|&emsp;&emsp;listClass|||false|string(string)||
|&emsp;&emsp;isDefault|可用值:0,1||true|integer(int)||
|&emsp;&emsp;status|可用值:0,1||true|integer(int)||
|&emsp;&emsp;remark|||false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 获取字典数据
**接口地址**:`/api/v1/system/dict/data/get`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|dictCode||query|false|integer(uint)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.DictDataGetRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|dict||github.com.tiger1103.gfast.v3.internal.app.common.model.entity.SysDictData|github.com.tiger1103.gfast.v3.internal.app.common.model.entity.SysDictData|
|&emsp;&emsp;dictCode|字典编码|integer(int64)||
|&emsp;&emsp;dictSort|字典排序|integer(int)||
|&emsp;&emsp;dictLabel|字典标签|string(string)||
|&emsp;&emsp;dictValue|字典键值|string(string)||
|&emsp;&emsp;dictType|字典类型|string(string)||
|&emsp;&emsp;cssClass|样式属性（其他样式扩展）|string(string)||
|&emsp;&emsp;listClass|表格回显样式|string(string)||
|&emsp;&emsp;isDefault|是否默认（1是 0否）|integer(int)||
|&emsp;&emsp;status|状态（0正常 1停用）|integer(int)||
|&emsp;&emsp;createBy|创建者|integer(uint64)||
|&emsp;&emsp;updateBy|更新者|integer(uint64)||
|&emsp;&emsp;remark|备注|string(string)||
|&emsp;&emsp;createdAt|创建时间|string(*gtime.Time)||
|&emsp;&emsp;updatedAt|修改时间|string(*gtime.Time)||
**响应示例**:
```javascript
{
	"dict": {
		"dictCode": 0,
		"dictSort": 0,
		"dictLabel": "",
		"dictValue": "",
		"dictType": "",
		"cssClass": "",
		"listClass": "",
		"isDefault": 0,
		"status": 0,
		"createBy": 0,
		"updateBy": 0,
		"remark": "",
		"createdAt": "",
		"updatedAt": ""
	}
}
```
## 获取字典数据公共方法
**接口地址**:`/api/v1/system/dict/data/getDictData`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|dictType||query|true|string(string)||
|Authorization|Bearer {{token}}|header|false|string(string)||
|defaultValue||query|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.GetDictRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|info||github.com.tiger1103.gfast.v3.internal.app.common.model.DictTypeRes|github.com.tiger1103.gfast.v3.internal.app.common.model.DictTypeRes|
|&emsp;&emsp;name||string(string)||
|&emsp;&emsp;remark||string(string)||
|values||github.com.tiger1103.gfast.v3.internal.app.common.model.DictDataRes|github.com.tiger1103.gfast.v3.internal.app.common.model.DictDataRes|
|&emsp;&emsp;key||string(string)||
|&emsp;&emsp;value||string(string)||
|&emsp;&emsp;isDefault||integer(int)||
|&emsp;&emsp;remark||string(string)||
**响应示例**:
```javascript
{
	"info": {
		"name": "",
		"remark": ""
	},
	"values": [
		{
			"key": "",
			"value": "",
			"isDefault": 0,
			"remark": ""
		}
	]
}
```
## 字典数据列表
**接口地址**:`/api/v1/system/dict/data/list`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|dictType||query|false|string(string)||
|dictLabel||query|false|string(string)||
|status||query|false|string(string)||
|typeId||query|false|integer(uint64)||
|dateRange||query|false|array|string|
|pageNum||query|false|integer(int)||
|pageSize||query|false|integer(int)||
|orderBy||query|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.DictDataSearchRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|list||github.com.tiger1103.gfast.v3.internal.app.common.model.entity.SysDictData|github.com.tiger1103.gfast.v3.internal.app.common.model.entity.SysDictData|
|&emsp;&emsp;dictCode|字典编码|integer(int64)||
|&emsp;&emsp;dictSort|字典排序|integer(int)||
|&emsp;&emsp;dictLabel|字典标签|string(string)||
|&emsp;&emsp;dictValue|字典键值|string(string)||
|&emsp;&emsp;dictType|字典类型|string(string)||
|&emsp;&emsp;cssClass|样式属性（其他样式扩展）|string(string)||
|&emsp;&emsp;listClass|表格回显样式|string(string)||
|&emsp;&emsp;isDefault|是否默认（1是 0否）|integer(int)||
|&emsp;&emsp;status|状态（0正常 1停用）|integer(int)||
|&emsp;&emsp;createBy|创建者|integer(uint64)||
|&emsp;&emsp;updateBy|更新者|integer(uint64)||
|&emsp;&emsp;remark|备注|string(string)||
|&emsp;&emsp;createdAt|创建时间|string(*gtime.Time)||
|&emsp;&emsp;updatedAt|修改时间|string(*gtime.Time)||
|currentPage||integer(int)|integer(int)|
|total||interface|interface|
**响应示例**:
```javascript
{
	"list": [
		{
			"dictCode": 0,
			"dictSort": 0,
			"dictLabel": "",
			"dictValue": "",
			"dictType": "",
			"cssClass": "",
			"listClass": "",
			"isDefault": 0,
			"status": 0,
			"createBy": 0,
			"updateBy": 0,
			"remark": "",
			"createdAt": "",
			"updatedAt": ""
		}
	],
	"currentPage": 0,
	"total": {}
}
```
## 添加字典类型
**接口地址**:`/api/v1/system/dict/type/add`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "pid": 0,
  "dictName": "",
  "dictType": "",
  "status": 0,
  "remark": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.system.DictTypeAddReq|github.com.tiger1103.gfast.v3.api.v1.system.DictTypeAddReq|body|true|github.com.tiger1103.gfast.v3.api.v1.system.DictTypeAddReq|github.com.tiger1103.gfast.v3.api.v1.system.DictTypeAddReq|
|&emsp;&emsp;pid|||false|integer(uint64)||
|&emsp;&emsp;dictName|||true|string(string)||
|&emsp;&emsp;dictType|||true|string(string)||
|&emsp;&emsp;status|可用值:0,1||true|integer(uint)||
|&emsp;&emsp;remark|||false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 删除字典类型
**接口地址**:`/api/v1/system/dict/type/delete`
**请求方式**:`DELETE`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|dictIds||query|true|array|integer|
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 修改字典类型
**接口地址**:`/api/v1/system/dict/type/edit`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "dictId": 0,
  "pid": 0,
  "dictName": "",
  "dictType": "",
  "status": 0,
  "remark": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.system.DictTypeEditReq|github.com.tiger1103.gfast.v3.api.v1.system.DictTypeEditReq|body|true|github.com.tiger1103.gfast.v3.api.v1.system.DictTypeEditReq|github.com.tiger1103.gfast.v3.api.v1.system.DictTypeEditReq|
|&emsp;&emsp;dictId|||true|integer(int64)||
|&emsp;&emsp;pid|||false|integer(uint64)||
|&emsp;&emsp;dictName|||true|string(string)||
|&emsp;&emsp;dictType|||true|string(string)||
|&emsp;&emsp;status|可用值:0,1||true|integer(uint)||
|&emsp;&emsp;remark|||false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 获取字典类型
**接口地址**:`/api/v1/system/dict/type/get`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|dictId||query|true|integer(uint)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.DictTypeGetRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|dictType||github.com.tiger1103.gfast.v3.internal.app.common.model.entity.SysDictType|github.com.tiger1103.gfast.v3.internal.app.common.model.entity.SysDictType|
|&emsp;&emsp;dictId|字典主键|integer(uint64)||
|&emsp;&emsp;pid|上级ID|integer(uint64)||
|&emsp;&emsp;dictName|字典名称|string(string)||
|&emsp;&emsp;dictType|字典类型|string(string)||
|&emsp;&emsp;status|状态（0正常 1停用）|integer(uint)||
|&emsp;&emsp;createBy|创建者|integer(uint)||
|&emsp;&emsp;updateBy|更新者|integer(uint)||
|&emsp;&emsp;remark|备注|string(string)||
|&emsp;&emsp;createdAt|创建日期|string(*gtime.Time)||
|&emsp;&emsp;updatedAt|修改日期|string(*gtime.Time)||
**响应示例**:
```javascript
{
	"dictType": {
		"dictId": 0,
		"pid": 0,
		"dictName": "",
		"dictType": "",
		"status": 0,
		"createBy": 0,
		"updateBy": 0,
		"remark": "",
		"createdAt": "",
		"updatedAt": ""
	}
}
```
## 字典类型列表
**接口地址**:`/api/v1/system/dict/type/list`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|dictName||query|false|string(string)||
|dictType||query|false|string(string)||
|status||query|false|string(string)||
|dateRange||query|false|array|string|
|pageNum||query|false|integer(int)||
|pageSize||query|false|integer(int)||
|orderBy||query|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.DictTypeSearchRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|dictTypeList||github.com.tiger1103.gfast.v3.internal.app.common.model.SysDictTypeInfoRes|github.com.tiger1103.gfast.v3.internal.app.common.model.SysDictTypeInfoRes|
|&emsp;&emsp;dictId||integer(uint64)||
|&emsp;&emsp;pid||integer(uint64)||
|&emsp;&emsp;dictName||string(string)||
|&emsp;&emsp;dictType||string(string)||
|&emsp;&emsp;status||integer(uint)||
|&emsp;&emsp;remark||string(string)||
|&emsp;&emsp;createdAt||string(*gtime.Time)||
|currentPage||integer(int)|integer(int)|
|total||interface|interface|
**响应示例**:
```javascript
{
	"dictTypeList": [
		{
			"dictId": 0,
			"pid": 0,
			"dictName": "",
			"dictType": "",
			"status": 0,
			"remark": "",
			"createdAt": ""
		}
	],
	"currentPage": 0,
	"total": {}
}
```
## 获取字典选择框列表
**接口地址**:`/api/v1/system/dict/type/optionSelect`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|all||query|false|boolean(bool)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.DictTYpeAllRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|dictType||github.com.tiger1103.gfast.v3.internal.app.common.model.entity.SysDictType|github.com.tiger1103.gfast.v3.internal.app.common.model.entity.SysDictType|
|&emsp;&emsp;dictId|字典主键|integer(uint64)||
|&emsp;&emsp;pid|上级ID|integer(uint64)||
|&emsp;&emsp;dictName|字典名称|string(string)||
|&emsp;&emsp;dictType|字典类型|string(string)||
|&emsp;&emsp;status|状态（0正常 1停用）|integer(uint)||
|&emsp;&emsp;createBy|创建者|integer(uint)||
|&emsp;&emsp;updateBy|更新者|integer(uint)||
|&emsp;&emsp;remark|备注|string(string)||
|&emsp;&emsp;createdAt|创建日期|string(*gtime.Time)||
|&emsp;&emsp;updatedAt|修改日期|string(*gtime.Time)||
**响应示例**:
```javascript
{
	"dictType": [
		{
			"dictId": 0,
			"pid": 0,
			"dictName": "",
			"dictType": "",
			"status": 0,
			"createBy": 0,
			"updateBy": 0,
			"remark": "",
			"createdAt": "",
			"updatedAt": ""
		}
	]
}
```
# 系统后台-登录
## 用户登录
**接口地址**:`/api/v1/system/login`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "username": "",
  "password": "",
  "verifyCode": "",
  "verifyKey": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.system.UserLoginReq|github.com.tiger1103.gfast.v3.api.v1.system.UserLoginReq|body|true|github.com.tiger1103.gfast.v3.api.v1.system.UserLoginReq|github.com.tiger1103.gfast.v3.api.v1.system.UserLoginReq|
|&emsp;&emsp;username|||true|string(string)||
|&emsp;&emsp;password|||true|string(string)||
|&emsp;&emsp;verifyCode|||false|string(string)||
|&emsp;&emsp;verifyKey|||false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.UserLoginRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|userInfo||github.com.tiger1103.gfast.v3.internal.app.system.model.LoginUserRes|github.com.tiger1103.gfast.v3.internal.app.system.model.LoginUserRes|
|&emsp;&emsp;id||integer(uint64)||
|&emsp;&emsp;userName||string(string)||
|&emsp;&emsp;mobile||string(string)||
|&emsp;&emsp;userNickname||string(string)||
|&emsp;&emsp;userStatus||integer(uint)||
|&emsp;&emsp;isAdmin||integer(int)||
|&emsp;&emsp;avatar||string(string)||
|&emsp;&emsp;deptId||integer(uint64)||
|token||string(string)|string(string)|
|menuList||github.com.tiger1103.gfast.v3.internal.app.system.model.UserMenus|github.com.tiger1103.gfast.v3.internal.app.system.model.UserMenus|
|&emsp;&emsp;id||integer(uint)||
|&emsp;&emsp;pid||integer(uint)||
|&emsp;&emsp;name||string(string)||
|&emsp;&emsp;component||string(string)||
|&emsp;&emsp;path||string(string)||
|&emsp;&emsp;icon||string(string)||
|&emsp;&emsp;title||string(string)||
|&emsp;&emsp;isLink||string(string)||
|&emsp;&emsp;isHide||boolean(bool)||
|&emsp;&emsp;isKeepAlive||boolean(bool)||
|&emsp;&emsp;isAffix||boolean(bool)||
|&emsp;&emsp;isIframe||boolean(bool)||
|&emsp;&emsp;children||array([]*model.UserMenus)|github.com.tiger1103.gfast.v3.internal.app.system.model.UserMenus|
|permissions||array([]string)||
**响应示例**:
```javascript
{
	"userInfo": {
		"id": 0,
		"userName": "",
		"mobile": "",
		"userNickname": "",
		"userStatus": 0,
		"isAdmin": 0,
		"avatar": "",
		"deptId": 0
	},
	"token": "",
	"menuList": [
		{
			"id": 0,
			"pid": 0,
			"name": "",
			"component": "",
			"path": "",
			"icon": "",
			"title": "",
			"isLink": "",
			"isHide": true,
			"isKeepAlive": true,
			"isAffix": true,
			"isIframe": true,
			"children": [
				{
					"id": 0,
					"pid": 0,
					"name": "",
					"component": "",
					"path": "",
					"icon": "",
					"title": "",
					"isLink": "",
					"isHide": true,
					"isKeepAlive": true,
					"isAffix": true,
					"isIframe": true,
					"children": [
						{}
					]
				}
			]
		}
	],
	"permissions": []
}
```
## 退出登录
**接口地址**:`/api/v1/system/logout`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
# 系统后台-登录日志管理
## 清除日志
**接口地址**:`/api/v1/system/loginLog/clear`
**请求方式**:`DELETE`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
暂无
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 删除日志
**接口地址**:`/api/v1/system/loginLog/delete`
**请求方式**:`DELETE`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|ids||query|true|array|integer|
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 日志列表
**接口地址**:`/api/v1/system/loginLog/list`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|userName||query|false|string(string)||
|status||query|false|string(string)||
|ipaddr||query|false|string(string)||
|orderByColumn||query|false|string(string)||
|isAsc||query|false|string(string)||
|loginLocation||query|false|string(string)||
|dateRange||query|false|array|string|
|pageNum||query|false|integer(int)||
|pageSize||query|false|integer(int)||
|orderBy||query|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.LoginLogSearchRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|currentPage||integer(int)|integer(int)|
|total||interface|interface|
|list||github.com.tiger1103.gfast.v3.internal.app.system.model.entity.SysLoginLog|github.com.tiger1103.gfast.v3.internal.app.system.model.entity.SysLoginLog|
|&emsp;&emsp;infoId|访问ID|integer(int64)||
|&emsp;&emsp;loginName|登录账号|string(string)||
|&emsp;&emsp;ipaddr|登录IP地址|string(string)||
|&emsp;&emsp;loginLocation|登录地点|string(string)||
|&emsp;&emsp;browser|浏览器类型|string(string)||
|&emsp;&emsp;os|操作系统|string(string)||
|&emsp;&emsp;status|登录状态（0成功 1失败）|integer(int)||
|&emsp;&emsp;msg|提示消息|string(string)||
|&emsp;&emsp;loginTime|登录时间|string(*gtime.Time)||
|&emsp;&emsp;module|登录模块|string(string)||
**响应示例**:
```javascript
{
	"currentPage": 0,
	"total": {},
	"list": [
		{
			"infoId": 0,
			"loginName": "",
			"ipaddr": "",
			"loginLocation": "",
			"browser": "",
			"os": "",
			"status": 0,
			"msg": "",
			"loginTime": "",
			"module": ""
		}
	]
}
```
# 系统后台-菜单管理
## 添加菜单
**接口地址**:`/api/v1/system/menu/add`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|name||query|true|string||
|menuName||query|true|string||
|Authorization|Bearer {{token}}|header|false|string(string)||
|condition||query|false|string||
|remark||query|false|string||
|isHide||query|false|integer||
|roles||query|false|array|integer|
|isLink||query|false|integer||
|isKeepAlive||query|false|integer||
|pid||query|false|integer||
|icon||query|false|string||
|isAffix||query|false|integer||
|menuSort||query|false|integer||
|path||query|false|string||
|component||query|false|string||
|menuType||query|false|integer||
|redirect||query|false|string||
|isIframe||query|false|integer||
|linkUrl||query|false|string||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 删除菜单
**接口地址**:`/api/v1/system/menu/delete`
**请求方式**:`DELETE`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|ids||query|true|array|integer|
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 获取菜单信息
**接口地址**:`/api/v1/system/menu/get`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id||query|true|integer(uint)||
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.RuleInfoRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|rule||github.com.tiger1103.gfast.v3.internal.app.system.model.entity.SysAuthRule|github.com.tiger1103.gfast.v3.internal.app.system.model.entity.SysAuthRule|
|&emsp;&emsp;id||integer(uint)||
|&emsp;&emsp;pid|父ID|integer(uint)||
|&emsp;&emsp;name|规则名称|string(string)||
|&emsp;&emsp;title|规则名称|string(string)||
|&emsp;&emsp;icon|图标|string(string)||
|&emsp;&emsp;condition|条件|string(string)||
|&emsp;&emsp;remark|备注|string(string)||
|&emsp;&emsp;menuType|类型 0目录 1菜单 2按钮|integer(uint)||
|&emsp;&emsp;weigh|权重|integer(int)||
|&emsp;&emsp;isHide|显示状态|integer(uint)||
|&emsp;&emsp;path|路由地址|string(string)||
|&emsp;&emsp;component|组件路径|string(string)||
|&emsp;&emsp;isLink|是否外链 1是 0否|integer(uint)||
|&emsp;&emsp;moduleType|所属模块|string(string)||
|&emsp;&emsp;modelId|模型ID|integer(uint)||
|&emsp;&emsp;isIframe|是否内嵌iframe|integer(uint)||
|&emsp;&emsp;isCached|是否缓存|integer(uint)||
|&emsp;&emsp;redirect|路由重定向地址|string(string)||
|&emsp;&emsp;isAffix|是否固定|integer(uint)||
|&emsp;&emsp;linkUrl|链接地址|string(string)||
|&emsp;&emsp;createdAt|创建日期|string(*gtime.Time)||
|&emsp;&emsp;updatedAt|修改日期|string(*gtime.Time)||
|roleIds||array([]uint)||
**响应示例**:
```javascript
{
	"rule": {
		"id": 0,
		"pid": 0,
		"name": "",
		"title": "",
		"icon": "",
		"condition": "",
		"remark": "",
		"menuType": 0,
		"weigh": 0,
		"isHide": 0,
		"path": "",
		"component": "",
		"isLink": 0,
		"moduleType": "",
		"modelId": 0,
		"isIframe": 0,
		"isCached": 0,
		"redirect": "",
		"isAffix": 0,
		"linkUrl": "",
		"createdAt": "",
		"updatedAt": ""
	},
	"roleIds": []
}
```
## 获取添加、编辑菜单相关参数
**接口地址**:`/api/v1/system/menu/getParams`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.RuleGetParamsRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|roles||github.com.tiger1103.gfast.v3.internal.app.system.model.entity.SysRole|github.com.tiger1103.gfast.v3.internal.app.system.model.entity.SysRole|
|&emsp;&emsp;id||integer(uint)||
|&emsp;&emsp;pid||integer(uint)||
|&emsp;&emsp;status|状态;0:禁用;1:正常|integer(uint)||
|&emsp;&emsp;listOrder|排序|integer(uint)||
|&emsp;&emsp;name|角色名称|string(string)||
|&emsp;&emsp;remark|备注|string(string)||
|&emsp;&emsp;dataScope|数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）|integer(uint)||
|&emsp;&emsp;createdAt|创建时间|string(*gtime.Time)||
|&emsp;&emsp;updatedAt|更新时间|string(*gtime.Time)||
|&emsp;&emsp;userCnt|用户数量|integer(uint)||
|&emsp;&emsp;createdBy|创建人|integer(uint64)||
|&emsp;&emsp;effectiveTime|角色有效日期|string(string)||
|menus||github.com.tiger1103.gfast.v3.internal.app.system.model.SysAuthRuleInfoRes|github.com.tiger1103.gfast.v3.internal.app.system.model.SysAuthRuleInfoRes|
|&emsp;&emsp;id||integer(uint)||
|&emsp;&emsp;pid||integer(uint)||
|&emsp;&emsp;name||string(string)||
|&emsp;&emsp;title||string(string)||
|&emsp;&emsp;icon||string(string)||
|&emsp;&emsp;condition||string(string)||
|&emsp;&emsp;remark||string(string)||
|&emsp;&emsp;menuType||integer(uint)||
|&emsp;&emsp;weigh||integer(int)||
|&emsp;&emsp;isHide||integer(uint)||
|&emsp;&emsp;isCached||integer(uint)||
|&emsp;&emsp;isAffix||integer(uint)||
|&emsp;&emsp;path||string(string)||
|&emsp;&emsp;redirect||string(string)||
|&emsp;&emsp;component||string(string)||
|&emsp;&emsp;isIframe||integer(uint)||
|&emsp;&emsp;isLink||integer(uint)||
|&emsp;&emsp;linkUrl||string(string)||
**响应示例**:
```javascript
{
	"roles": [
		{
			"id": 0,
			"pid": 0,
			"status": 0,
			"listOrder": 0,
			"name": "",
			"remark": "",
			"dataScope": 0,
			"createdAt": "",
			"updatedAt": "",
			"userCnt": 0,
			"createdBy": 0,
			"effectiveTime": ""
		}
	],
	"menus": [
		{
			"id": 0,
			"pid": 0,
			"name": "",
			"title": "",
			"icon": "",
			"condition": "",
			"remark": "",
			"menuType": 0,
			"weigh": 0,
			"isHide": 0,
			"isCached": 0,
			"isAffix": 0,
			"path": "",
			"redirect": "",
			"component": "",
			"isIframe": 0,
			"isLink": 0,
			"linkUrl": ""
		}
	]
}
```
## 菜单列表
**接口地址**:`/api/v1/system/menu/list`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|Authorization|Bearer {{token}}|header|false|string(string)||
|menuName||query|false|string(string)||
|component||query|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.RuleListRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|rules||github.com.tiger1103.gfast.v3.internal.app.system.model.SysAuthRuleTreeRes|github.com.tiger1103.gfast.v3.internal.app.system.model.SysAuthRuleTreeRes|
|&emsp;&emsp;id||integer(uint)||
|&emsp;&emsp;pid||integer(uint)||
|&emsp;&emsp;name||string(string)||
|&emsp;&emsp;title||string(string)||
|&emsp;&emsp;icon||string(string)||
|&emsp;&emsp;condition||string(string)||
|&emsp;&emsp;remark||string(string)||
|&emsp;&emsp;menuType||integer(uint)||
|&emsp;&emsp;weigh||integer(int)||
|&emsp;&emsp;isHide||integer(uint)||
|&emsp;&emsp;isCached||integer(uint)||
|&emsp;&emsp;isAffix||integer(uint)||
|&emsp;&emsp;path||string(string)||
|&emsp;&emsp;redirect||string(string)||
|&emsp;&emsp;component||string(string)||
|&emsp;&emsp;isIframe||integer(uint)||
|&emsp;&emsp;isLink||integer(uint)||
|&emsp;&emsp;linkUrl||string(string)||
|&emsp;&emsp;children||array([]*model.SysAuthRuleTreeRes)|github.com.tiger1103.gfast.v3.internal.app.system.model.SysAuthRuleTreeRes|
**响应示例**:
```javascript
{
	"rules": [
		{
			"id": 0,
			"pid": 0,
			"name": "",
			"title": "",
			"icon": "",
			"condition": "",
			"remark": "",
			"menuType": 0,
			"weigh": 0,
			"isHide": 0,
			"isCached": 0,
			"isAffix": 0,
			"path": "",
			"redirect": "",
			"component": "",
			"isIframe": 0,
			"isLink": 0,
			"linkUrl": "",
			"children": [
				{
					"id": 0,
					"pid": 0,
					"name": "",
					"title": "",
					"icon": "",
					"condition": "",
					"remark": "",
					"menuType": 0,
					"weigh": 0,
					"isHide": 0,
					"isCached": 0,
					"isAffix": 0,
					"path": "",
					"redirect": "",
					"component": "",
					"isIframe": 0,
					"isLink": 0,
					"linkUrl": "",
					"children": [
						{}
					]
				}
			]
		}
	]
}
```
## 修改菜单
**接口地址**:`/api/v1/system/menu/update`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|name||query|true|string||
|id||query|true|integer||
|menuName||query|true|string||
|Authorization|Bearer {{token}}|header|false|string(string)||
|redirect||query|false|string||
|component||query|false|string||
|menuType||query|false|integer||
|menuSort||query|false|integer||
|remark||query|false|string||
|isLink||query|false|integer||
|isIframe||query|false|integer||
|roles||query|false|array|integer|
|isKeepAlive||query|false|integer||
|isAffix||query|false|integer||
|pid||query|false|integer||
|icon||query|false|string||
|condition||query|false|string||
|isHide||query|false|integer||
|linkUrl||query|false|string||
|path||query|false|string||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
# 系统后台-服务监控
## 服务监控
**接口地址**:`/api/v1/system/monitor/server`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
暂无
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
# 系统后台-在线用户管理
## 强制用户退出登录
**接口地址**:`/api/v1/system/online/forceLogout`
**请求方式**:`DELETE`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|ids||query|true|array|integer|
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.SysUserOnlineForceLogoutRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 列表
**接口地址**:`/api/v1/system/online/list`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|userName||query|false|string(string)||
|ipaddr||query|false|string(string)||
|dateRange||query|false|array|string|
|pageNum||query|false|integer(int)||
|pageSize||query|false|integer(int)||
|orderBy||query|false|string(string)||
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.SysUserOnlineSearchRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|currentPage||integer(int)|integer(int)|
|total||interface|interface|
|list||github.com.tiger1103.gfast.v3.internal.app.system.model.entity.SysUserOnline|github.com.tiger1103.gfast.v3.internal.app.system.model.entity.SysUserOnline|
|&emsp;&emsp;id||integer(uint64)||
|&emsp;&emsp;uuid|用户标识|string(string)||
|&emsp;&emsp;token|用户token|string(string)||
|&emsp;&emsp;createTime|登录时间|string(*gtime.Time)||
|&emsp;&emsp;userName|用户名|string(string)||
|&emsp;&emsp;ip|登录ip|string(string)||
|&emsp;&emsp;explorer|浏览器|string(string)||
|&emsp;&emsp;os|操作系统|string(string)||
**响应示例**:
```javascript
{
	"currentPage": 0,
	"total": {},
	"list": [
		{
			"id": 0,
			"uuid": "",
			"token": "",
			"createTime": "",
			"userName": "",
			"ip": "",
			"explorer": "",
			"os": ""
		}
	]
}
```
# 系统后台-操作日志
## 清除日志
**接口地址**:`/api/v1/system/operLog/clear`
**请求方式**:`DELETE`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.SysOperLogClearRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 删除操作日志
**接口地址**:`/api/v1/system/operLog/delete`
**请求方式**:`DELETE`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|operIds||query|true|array|integer|
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.SysOperLogDeleteRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 获取操作日志信息
**接口地址**:`/api/v1/system/operLog/get`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|operId||query|true|integer(uint64)||
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.SysOperLogGetRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|operId||integer(uint64)|integer(uint64)|
|title||string(string)|string(string)|
|businessType||integer(int)|integer(int)|
|method||string(string)|string(string)|
|requestMethod||string(string)|string(string)|
|operatorType||integer(int)|integer(int)|
|operName||string(string)|string(string)|
|deptName||string(string)|string(string)|
|linkedDeptName||github.com.tiger1103.gfast.v3.internal.app.system.model.LinkedSysOperLogSysDept|github.com.tiger1103.gfast.v3.internal.app.system.model.LinkedSysOperLogSysDept|
|&emsp;&emsp;deptId||integer(int64)||
|&emsp;&emsp;deptName||string(string)||
|operUrl||string(string)|string(string)|
|operIp||string(string)|string(string)|
|operLocation||string(string)|string(string)|
|operParam||string(string)|string(string)|
|errorMsg||string(string)|string(string)|
|operTime||string(*gtime.Time)|string(*gtime.Time)|
**响应示例**:
```javascript
{
	"operId": 0,
	"title": "",
	"businessType": 0,
	"method": "",
	"requestMethod": "",
	"operatorType": 0,
	"operName": "",
	"deptName": "",
	"linkedDeptName": {
		"deptId": 0,
		"deptName": ""
	},
	"operUrl": "",
	"operIp": "",
	"operLocation": "",
	"operParam": "",
	"errorMsg": "",
	"operTime": ""
}
```
## 操作日志列表
**接口地址**:`/api/v1/system/operLog/list`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|title||query|false|string(string)||
|requestMethod||query|false|string(string)||
|operName||query|false|string(string)||
|dateRange||query|false|array|string|
|pageNum||query|false|integer(int)||
|pageSize||query|false|integer(int)||
|orderBy||query|false|string(string)||
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.SysOperLogSearchRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|currentPage||integer(int)|integer(int)|
|total||interface|interface|
|list||github.com.tiger1103.gfast.v3.internal.app.system.model.SysOperLogListRes|github.com.tiger1103.gfast.v3.internal.app.system.model.SysOperLogListRes|
|&emsp;&emsp;operId||integer(uint64)||
|&emsp;&emsp;title||string(string)||
|&emsp;&emsp;requestMethod||string(string)||
|&emsp;&emsp;operName||string(string)||
|&emsp;&emsp;deptName||string(string)||
|&emsp;&emsp;linkedDeptName||github.com.tiger1103.gfast.v3.internal.app.system.model.LinkedSysOperLogSysDept|github.com.tiger1103.gfast.v3.internal.app.system.model.LinkedSysOperLogSysDept|
|&emsp;&emsp;&emsp;&emsp;deptId||integer||
|&emsp;&emsp;&emsp;&emsp;deptName||string||
|&emsp;&emsp;operUrl||string(string)||
|&emsp;&emsp;operIp||string(string)||
|&emsp;&emsp;operLocation||string(string)||
|&emsp;&emsp;operParam||string(string)||
|&emsp;&emsp;operTime||string(*gtime.Time)||
**响应示例**:
```javascript
{
	"currentPage": 0,
	"total": {},
	"list": [
		{
			"operId": 0,
			"title": "",
			"requestMethod": "",
			"operName": "",
			"deptName": "",
			"linkedDeptName": {
				"deptId": 0,
				"deptName": ""
			},
			"operUrl": "",
			"operIp": "",
			"operLocation": "",
			"operParam": "",
			"operTime": ""
		}
	]
}
```
# 系统后台-用户管理
## 修改个人资料
**接口地址**:`/api/v1/system/personal/edit`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|userEmail||query|true|string||
|nickname||query|true|string||
|mobile||query|true|string||
|Authorization|Bearer {{token}}|header|false|string(string)||
|remark||query|false|string||
|sex||query|false|integer||
|describe||query|false|string||
|avatar||query|false|string||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.PersonalEditRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|userInfo||github.com.tiger1103.gfast.v3.internal.app.system.model.LoginUserRes|github.com.tiger1103.gfast.v3.internal.app.system.model.LoginUserRes|
|&emsp;&emsp;id||integer(uint64)||
|&emsp;&emsp;userName||string(string)||
|&emsp;&emsp;mobile||string(string)||
|&emsp;&emsp;userNickname||string(string)||
|&emsp;&emsp;userStatus||integer(uint)||
|&emsp;&emsp;isAdmin||integer(int)||
|&emsp;&emsp;avatar||string(string)||
|&emsp;&emsp;deptId||integer(uint64)||
|token||string(string)|string(string)|
**响应示例**:
```javascript
{
	"userInfo": {
		"id": 0,
		"userName": "",
		"mobile": "",
		"userNickname": "",
		"userStatus": 0,
		"isAdmin": 0,
		"avatar": "",
		"deptId": 0
	},
	"token": ""
}
```
## 登录用户信息
**接口地址**:`/api/v1/system/personal/getPersonalInfo`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.PersonalInfoRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|user||github.com.tiger1103.gfast.v3.internal.app.system.model.entity.SysUser|github.com.tiger1103.gfast.v3.internal.app.system.model.entity.SysUser|
|&emsp;&emsp;id||integer(uint64)||
|&emsp;&emsp;userName|用户名|string(string)||
|&emsp;&emsp;mobile|中国手机不带国家代码，国际手机号格式为：国家代码-手机号|string(string)||
|&emsp;&emsp;userNickname|用户昵称|string(string)||
|&emsp;&emsp;birthday|生日|integer(int)||
|&emsp;&emsp;userPassword|登录密码;cmf_password加密|string(string)||
|&emsp;&emsp;userSalt|加密盐|string(string)||
|&emsp;&emsp;userStatus|用户状态;0:禁用,1:正常,2:未验证|integer(uint)||
|&emsp;&emsp;userEmail|用户登录邮箱|string(string)||
|&emsp;&emsp;sex|性别;0:保密,1:男,2:女|integer(int)||
|&emsp;&emsp;avatar|用户头像|string(string)||
|&emsp;&emsp;deptId|部门id|integer(uint64)||
|&emsp;&emsp;remark|备注|string(string)||
|&emsp;&emsp;isAdmin|是否后台管理员 1 是  0   否|integer(int)||
|&emsp;&emsp;address|联系地址|string(string)||
|&emsp;&emsp;describe|描述信息|string(string)||
|&emsp;&emsp;lastLoginIp|最后登录ip|string(string)||
|&emsp;&emsp;lastLoginTime|最后登录时间|string(*gtime.Time)||
|&emsp;&emsp;createdAt|创建时间|string(*gtime.Time)||
|&emsp;&emsp;updatedAt|更新时间|string(*gtime.Time)||
|&emsp;&emsp;deletedAt|删除时间|string(*gtime.Time)||
|&emsp;&emsp;openId|微信小程序open id|string(string)||
|roles||array([]string)||
|deptName||string(string)|string(string)|
**响应示例**:
```javascript
{
	"user": {
		"id": 0,
		"userName": "",
		"mobile": "",
		"userNickname": "",
		"birthday": 0,
		"userPassword": "",
		"userSalt": "",
		"userStatus": 0,
		"userEmail": "",
		"sex": 0,
		"avatar": "",
		"deptId": 0,
		"remark": "",
		"isAdmin": 0,
		"address": "",
		"describe": "",
		"lastLoginIp": "",
		"lastLoginTime": "",
		"createdAt": "",
		"updatedAt": "",
		"deletedAt": "",
		"openId": ""
	},
	"roles": [],
	"deptName": ""
}
```
## 刷新token
**接口地址**:`/api/v1/system/personal/refreshToken`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.RefreshTokenRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|token||string(string)|string(string)|
|userInfo||github.com.tiger1103.gfast.v3.internal.app.system.model.LoginUserRes|github.com.tiger1103.gfast.v3.internal.app.system.model.LoginUserRes|
|&emsp;&emsp;id||integer(uint64)||
|&emsp;&emsp;userName||string(string)||
|&emsp;&emsp;mobile||string(string)||
|&emsp;&emsp;userNickname||string(string)||
|&emsp;&emsp;userStatus||integer(uint)||
|&emsp;&emsp;isAdmin||integer(int)||
|&emsp;&emsp;avatar||string(string)||
|&emsp;&emsp;deptId||integer(uint64)||
**响应示例**:
```javascript
{
	"token": "",
	"userInfo": {
		"id": 0,
		"userName": "",
		"mobile": "",
		"userNickname": "",
		"userStatus": 0,
		"isAdmin": 0,
		"avatar": "",
		"deptId": 0
	}
}
```
## 重置个人密码
**接口地址**:`/api/v1/system/personal/resetPwd`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|password||query|true|string||
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 添加用户
**接口地址**:`/api/v1/system/user/add`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "deptId": 0,
  "email": "",
  "nickName": "",
  "mobile": "",
  "postIds": [],
  "remark": "",
  "roleIds": [],
  "sex": 0,
  "status": 0,
  "isAdmin": 0,
  "userName": "",
  "password": "",
  "UserSalt": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.system.UserAddReq|github.com.tiger1103.gfast.v3.api.v1.system.UserAddReq|body|true|github.com.tiger1103.gfast.v3.api.v1.system.UserAddReq|github.com.tiger1103.gfast.v3.api.v1.system.UserAddReq|
|&emsp;&emsp;deptId|||true|integer(uint64)||
|&emsp;&emsp;email|||false|string(string)||
|&emsp;&emsp;nickName|||true|string(string)||
|&emsp;&emsp;mobile|||true|string(string)||
|&emsp;&emsp;postIds|||false|array([]int64)|integer|
|&emsp;&emsp;remark|||false|string(string)||
|&emsp;&emsp;roleIds|||false|array([]uint)|integer|
|&emsp;&emsp;sex|||false|integer(int)||
|&emsp;&emsp;status|||false|integer(uint)||
|&emsp;&emsp;isAdmin|||false|integer(int)||
|&emsp;&emsp;userName|||true|string(string)||
|&emsp;&emsp;password|||true|string(string)||
|&emsp;&emsp;UserSalt|||false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 删除用户
**接口地址**:`/api/v1/system/user/delete`
**请求方式**:`DELETE`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|ids||query|true|array|integer|
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 修改用户
**接口地址**:`/api/v1/system/user/edit`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "deptId": 0,
  "email": "",
  "nickName": "",
  "mobile": "",
  "postIds": [],
  "remark": "",
  "roleIds": [],
  "sex": 0,
  "status": 0,
  "isAdmin": 0,
  "userId": 0
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.system.UserEditReq|github.com.tiger1103.gfast.v3.api.v1.system.UserEditReq|body|true|github.com.tiger1103.gfast.v3.api.v1.system.UserEditReq|github.com.tiger1103.gfast.v3.api.v1.system.UserEditReq|
|&emsp;&emsp;deptId|||true|integer(uint64)||
|&emsp;&emsp;email|||false|string(string)||
|&emsp;&emsp;nickName|||true|string(string)||
|&emsp;&emsp;mobile|||true|string(string)||
|&emsp;&emsp;postIds|||false|array([]int64)|integer|
|&emsp;&emsp;remark|||false|string(string)||
|&emsp;&emsp;roleIds|||false|array([]uint)|integer|
|&emsp;&emsp;sex|||false|integer(int)||
|&emsp;&emsp;status|||false|integer(uint)||
|&emsp;&emsp;isAdmin|||false|integer(int)||
|&emsp;&emsp;userId|||true|integer(int64)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 获取角色对应用户
**接口地址**:`/api/v1/system/user/getByRoleId`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|roleId||query|false|integer(uint)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.UsersRoleIdRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|userList||github.com.tiger1103.gfast.v3.internal.app.system.model.SysUserRoleDeptRes|github.com.tiger1103.gfast.v3.internal.app.system.model.SysUserRoleDeptRes|
|&emsp;&emsp;id||integer(uint64)||
|&emsp;&emsp;userName|用户名|string(string)||
|&emsp;&emsp;mobile|中国手机不带国家代码，国际手机号格式为：国家代码-手机号|string(string)||
|&emsp;&emsp;userNickname|用户昵称|string(string)||
|&emsp;&emsp;birthday|生日|integer(int)||
|&emsp;&emsp;userPassword|登录密码;cmf_password加密|string(string)||
|&emsp;&emsp;userSalt|加密盐|string(string)||
|&emsp;&emsp;userStatus|用户状态;0:禁用,1:正常,2:未验证|integer(uint)||
|&emsp;&emsp;userEmail|用户登录邮箱|string(string)||
|&emsp;&emsp;sex|性别;0:保密,1:男,2:女|integer(int)||
|&emsp;&emsp;avatar|用户头像|string(string)||
|&emsp;&emsp;deptId|部门id|integer(uint64)||
|&emsp;&emsp;remark|备注|string(string)||
|&emsp;&emsp;isAdmin|是否后台管理员 1 是  0   否|integer(int)||
|&emsp;&emsp;address|联系地址|string(string)||
|&emsp;&emsp;describe|描述信息|string(string)||
|&emsp;&emsp;lastLoginIp|最后登录ip|string(string)||
|&emsp;&emsp;lastLoginTime|最后登录时间|string(*gtime.Time)||
|&emsp;&emsp;createdAt|创建时间|string(*gtime.Time)||
|&emsp;&emsp;updatedAt|更新时间|string(*gtime.Time)||
|&emsp;&emsp;deletedAt|删除时间|string(*gtime.Time)||
|&emsp;&emsp;openId|微信小程序open id|string(string)||
|&emsp;&emsp;dept||github.com.tiger1103.gfast.v3.internal.app.system.model.entity.SysDept|github.com.tiger1103.gfast.v3.internal.app.system.model.entity.SysDept|
|&emsp;&emsp;&emsp;&emsp;deptId|部门id|integer||
|&emsp;&emsp;&emsp;&emsp;parentId|父部门id|integer||
|&emsp;&emsp;&emsp;&emsp;ancestors|祖级列表|string||
|&emsp;&emsp;&emsp;&emsp;deptName|部门名称|string||
|&emsp;&emsp;&emsp;&emsp;orderNum|显示顺序|integer||
|&emsp;&emsp;&emsp;&emsp;leader|负责人|array|integer|
|&emsp;&emsp;&emsp;&emsp;phone|联系电话|string||
|&emsp;&emsp;&emsp;&emsp;email|邮箱|string||
|&emsp;&emsp;&emsp;&emsp;status|部门状态（0正常 1停用）|integer||
|&emsp;&emsp;&emsp;&emsp;createdBy|创建人|integer||
|&emsp;&emsp;&emsp;&emsp;updatedBy|修改人|integer||
|&emsp;&emsp;&emsp;&emsp;createdAt|创建时间|string||
|&emsp;&emsp;&emsp;&emsp;updatedAt|修改时间|string||
|&emsp;&emsp;&emsp;&emsp;deletedAt|删除时间|string||
|&emsp;&emsp;roleInfo||array([]*model.SysUserRoleInfoRes)|github.com.tiger1103.gfast.v3.internal.app.system.model.SysUserRoleInfoRes|
|&emsp;&emsp;&emsp;&emsp;roleId||integer||
|&emsp;&emsp;&emsp;&emsp;name||string||
|&emsp;&emsp;post||array([]*model.SysUserPostInfoRes)|github.com.tiger1103.gfast.v3.internal.app.system.model.SysUserPostInfoRes|
|&emsp;&emsp;&emsp;&emsp;postId||integer||
|&emsp;&emsp;&emsp;&emsp;postName||string||
**响应示例**:
```javascript
{
	"userList": [
		{
			"id": 0,
			"userName": "",
			"mobile": "",
			"userNickname": "",
			"birthday": 0,
			"userPassword": "",
			"userSalt": "",
			"userStatus": 0,
			"userEmail": "",
			"sex": 0,
			"avatar": "",
			"deptId": 0,
			"remark": "",
			"isAdmin": 0,
			"address": "",
			"describe": "",
			"lastLoginIp": "",
			"lastLoginTime": "",
			"createdAt": "",
			"updatedAt": "",
			"deletedAt": "",
			"openId": "",
			"dept": {
				"deptId": 0,
				"parentId": 0,
				"ancestors": "",
				"deptName": "",
				"orderNum": 0,
				"leader": [],
				"phone": "",
				"email": "",
				"status": 0,
				"createdBy": 0,
				"updatedBy": 0,
				"createdAt": "",
				"updatedAt": "",
				"deletedAt": ""
			},
			"roleInfo": [
				{
					"roleId": 0,
					"name": ""
				}
			],
			"post": [
				{
					"postId": 0,
					"postName": ""
				}
			]
		}
	]
}
```
## 获取用户信息
**接口地址**:`/api/v1/system/user/getEdit`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id||query|false|integer(uint64)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.UserGetEditRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|user||github.com.tiger1103.gfast.v3.internal.app.system.model.entity.SysUser|github.com.tiger1103.gfast.v3.internal.app.system.model.entity.SysUser|
|&emsp;&emsp;id||integer(uint64)||
|&emsp;&emsp;userName|用户名|string(string)||
|&emsp;&emsp;mobile|中国手机不带国家代码，国际手机号格式为：国家代码-手机号|string(string)||
|&emsp;&emsp;userNickname|用户昵称|string(string)||
|&emsp;&emsp;birthday|生日|integer(int)||
|&emsp;&emsp;userPassword|登录密码;cmf_password加密|string(string)||
|&emsp;&emsp;userSalt|加密盐|string(string)||
|&emsp;&emsp;userStatus|用户状态;0:禁用,1:正常,2:未验证|integer(uint)||
|&emsp;&emsp;userEmail|用户登录邮箱|string(string)||
|&emsp;&emsp;sex|性别;0:保密,1:男,2:女|integer(int)||
|&emsp;&emsp;avatar|用户头像|string(string)||
|&emsp;&emsp;deptId|部门id|integer(uint64)||
|&emsp;&emsp;remark|备注|string(string)||
|&emsp;&emsp;isAdmin|是否后台管理员 1 是  0   否|integer(int)||
|&emsp;&emsp;address|联系地址|string(string)||
|&emsp;&emsp;describe|描述信息|string(string)||
|&emsp;&emsp;lastLoginIp|最后登录ip|string(string)||
|&emsp;&emsp;lastLoginTime|最后登录时间|string(*gtime.Time)||
|&emsp;&emsp;createdAt|创建时间|string(*gtime.Time)||
|&emsp;&emsp;updatedAt|更新时间|string(*gtime.Time)||
|&emsp;&emsp;deletedAt|删除时间|string(*gtime.Time)||
|&emsp;&emsp;openId|微信小程序open id|string(string)||
|checkedRoleIds||array([]uint)||
|checkedPosts||array([]int64)||
**响应示例**:
```javascript
{
	"user": {
		"id": 0,
		"userName": "",
		"mobile": "",
		"userNickname": "",
		"birthday": 0,
		"userPassword": "",
		"userSalt": "",
		"userStatus": 0,
		"userEmail": "",
		"sex": 0,
		"avatar": "",
		"deptId": 0,
		"remark": "",
		"isAdmin": 0,
		"address": "",
		"describe": "",
		"lastLoginIp": "",
		"lastLoginTime": "",
		"createdAt": "",
		"updatedAt": "",
		"deletedAt": "",
		"openId": ""
	},
	"checkedRoleIds": [],
	"checkedPosts": []
}
```
## 根据ids获取用户
**接口地址**:`/api/v1/system/user/getUserByIds`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|ids||query|true|array|integer|
|dateRange||query|false|array|string|
|pageNum||query|false|integer(int)||
|pageSize||query|false|integer(int)||
|orderBy||query|false|string(string)||
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.UserByIdsRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|userList||github.com.tiger1103.gfast.v3.internal.app.system.model.SysUserSimpleRes|github.com.tiger1103.gfast.v3.internal.app.system.model.SysUserSimpleRes|
|&emsp;&emsp;id||integer(uint64)||
|&emsp;&emsp;avatar||string(string)||
|&emsp;&emsp;sex||integer(int)||
|&emsp;&emsp;userNickname||string(string)||
**响应示例**:
```javascript
{
	"userList": [
		{
			"id": 0,
			"avatar": "",
			"sex": 0,
			"userNickname": ""
		}
	]
}
```
## 获取用户菜单
**接口地址**:`/api/v1/system/user/getUserMenus`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.UserMenusRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|menuList||github.com.tiger1103.gfast.v3.internal.app.system.model.UserMenus|github.com.tiger1103.gfast.v3.internal.app.system.model.UserMenus|
|&emsp;&emsp;id||integer(uint)||
|&emsp;&emsp;pid||integer(uint)||
|&emsp;&emsp;name||string(string)||
|&emsp;&emsp;component||string(string)||
|&emsp;&emsp;path||string(string)||
|&emsp;&emsp;icon||string(string)||
|&emsp;&emsp;title||string(string)||
|&emsp;&emsp;isLink||string(string)||
|&emsp;&emsp;isHide||boolean(bool)||
|&emsp;&emsp;isKeepAlive||boolean(bool)||
|&emsp;&emsp;isAffix||boolean(bool)||
|&emsp;&emsp;isIframe||boolean(bool)||
|&emsp;&emsp;children||array([]*model.UserMenus)|github.com.tiger1103.gfast.v3.internal.app.system.model.UserMenus|
|permissions||array([]string)||
|userInfo||github.com.tiger1103.gfast.v3.internal.app.system.model.LoginUserRes|github.com.tiger1103.gfast.v3.internal.app.system.model.LoginUserRes|
|&emsp;&emsp;id||integer(uint64)||
|&emsp;&emsp;userName||string(string)||
|&emsp;&emsp;mobile||string(string)||
|&emsp;&emsp;userNickname||string(string)||
|&emsp;&emsp;userStatus||integer(uint)||
|&emsp;&emsp;isAdmin||integer(int)||
|&emsp;&emsp;avatar||string(string)||
|&emsp;&emsp;deptId||integer(uint64)||
**响应示例**:
```javascript
{
	"menuList": [
		{
			"id": 0,
			"pid": 0,
			"name": "",
			"component": "",
			"path": "",
			"icon": "",
			"title": "",
			"isLink": "",
			"isHide": true,
			"isKeepAlive": true,
			"isAffix": true,
			"isIframe": true,
			"children": [
				{
					"id": 0,
					"pid": 0,
					"name": "",
					"component": "",
					"path": "",
					"icon": "",
					"title": "",
					"isLink": "",
					"isHide": true,
					"isKeepAlive": true,
					"isAffix": true,
					"isIframe": true,
					"children": [
						{}
					]
				}
			]
		}
	],
	"permissions": [],
	"userInfo": {
		"id": 0,
		"userName": "",
		"mobile": "",
		"userNickname": "",
		"userStatus": 0,
		"isAdmin": 0,
		"avatar": "",
		"deptId": 0
	}
}
```
## 用户列表
**接口地址**:`/api/v1/system/user/list`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|deptId||query|false|string(string)||
|roleId||query|false|integer(uint)||
|mobile||query|false|string(string)||
|status||query|false|string(string)||
|keyWords||query|false|string(string)||
|UserInfo||query|false|github.com.tiger1103.gfast.v3.internal.app.system.model.ContextUser|github.com.tiger1103.gfast.v3.internal.app.system.model.ContextUser|
|&emsp;&emsp;id|||false|integer(uint64)||
|&emsp;&emsp;userName|||false|string(string)||
|&emsp;&emsp;mobile|||false|string(string)||
|&emsp;&emsp;userNickname|||false|string(string)||
|&emsp;&emsp;userStatus|||false|integer(uint)||
|&emsp;&emsp;isAdmin|||false|integer(int)||
|&emsp;&emsp;avatar|||false|string(string)||
|&emsp;&emsp;deptId|||false|integer(uint64)||
|dateRange||query|false|array|string|
|pageNum||query|false|integer(int)||
|pageSize||query|false|integer(int)||
|orderBy||query|false|string(string)||
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.UserSearchRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|userList||github.com.tiger1103.gfast.v3.internal.app.system.model.SysUserRoleDeptRes|github.com.tiger1103.gfast.v3.internal.app.system.model.SysUserRoleDeptRes|
|&emsp;&emsp;id||integer(uint64)||
|&emsp;&emsp;userName|用户名|string(string)||
|&emsp;&emsp;mobile|中国手机不带国家代码，国际手机号格式为：国家代码-手机号|string(string)||
|&emsp;&emsp;userNickname|用户昵称|string(string)||
|&emsp;&emsp;birthday|生日|integer(int)||
|&emsp;&emsp;userPassword|登录密码;cmf_password加密|string(string)||
|&emsp;&emsp;userSalt|加密盐|string(string)||
|&emsp;&emsp;userStatus|用户状态;0:禁用,1:正常,2:未验证|integer(uint)||
|&emsp;&emsp;userEmail|用户登录邮箱|string(string)||
|&emsp;&emsp;sex|性别;0:保密,1:男,2:女|integer(int)||
|&emsp;&emsp;avatar|用户头像|string(string)||
|&emsp;&emsp;deptId|部门id|integer(uint64)||
|&emsp;&emsp;remark|备注|string(string)||
|&emsp;&emsp;isAdmin|是否后台管理员 1 是  0   否|integer(int)||
|&emsp;&emsp;address|联系地址|string(string)||
|&emsp;&emsp;describe|描述信息|string(string)||
|&emsp;&emsp;lastLoginIp|最后登录ip|string(string)||
|&emsp;&emsp;lastLoginTime|最后登录时间|string(*gtime.Time)||
|&emsp;&emsp;createdAt|创建时间|string(*gtime.Time)||
|&emsp;&emsp;updatedAt|更新时间|string(*gtime.Time)||
|&emsp;&emsp;deletedAt|删除时间|string(*gtime.Time)||
|&emsp;&emsp;openId|微信小程序open id|string(string)||
|&emsp;&emsp;dept||github.com.tiger1103.gfast.v3.internal.app.system.model.entity.SysDept|github.com.tiger1103.gfast.v3.internal.app.system.model.entity.SysDept|
|&emsp;&emsp;&emsp;&emsp;deptId|部门id|integer||
|&emsp;&emsp;&emsp;&emsp;parentId|父部门id|integer||
|&emsp;&emsp;&emsp;&emsp;ancestors|祖级列表|string||
|&emsp;&emsp;&emsp;&emsp;deptName|部门名称|string||
|&emsp;&emsp;&emsp;&emsp;orderNum|显示顺序|integer||
|&emsp;&emsp;&emsp;&emsp;leader|负责人|array|integer|
|&emsp;&emsp;&emsp;&emsp;phone|联系电话|string||
|&emsp;&emsp;&emsp;&emsp;email|邮箱|string||
|&emsp;&emsp;&emsp;&emsp;status|部门状态（0正常 1停用）|integer||
|&emsp;&emsp;&emsp;&emsp;createdBy|创建人|integer||
|&emsp;&emsp;&emsp;&emsp;updatedBy|修改人|integer||
|&emsp;&emsp;&emsp;&emsp;createdAt|创建时间|string||
|&emsp;&emsp;&emsp;&emsp;updatedAt|修改时间|string||
|&emsp;&emsp;&emsp;&emsp;deletedAt|删除时间|string||
|&emsp;&emsp;roleInfo||array([]*model.SysUserRoleInfoRes)|github.com.tiger1103.gfast.v3.internal.app.system.model.SysUserRoleInfoRes|
|&emsp;&emsp;&emsp;&emsp;roleId||integer||
|&emsp;&emsp;&emsp;&emsp;name||string||
|&emsp;&emsp;post||array([]*model.SysUserPostInfoRes)|github.com.tiger1103.gfast.v3.internal.app.system.model.SysUserPostInfoRes|
|&emsp;&emsp;&emsp;&emsp;postId||integer||
|&emsp;&emsp;&emsp;&emsp;postName||string||
|currentPage||integer(int)|integer(int)|
|total||interface|interface|
**响应示例**:
```javascript
{
	"userList": [
		{
			"id": 0,
			"userName": "",
			"mobile": "",
			"userNickname": "",
			"birthday": 0,
			"userPassword": "",
			"userSalt": "",
			"userStatus": 0,
			"userEmail": "",
			"sex": 0,
			"avatar": "",
			"deptId": 0,
			"remark": "",
			"isAdmin": 0,
			"address": "",
			"describe": "",
			"lastLoginIp": "",
			"lastLoginTime": "",
			"createdAt": "",
			"updatedAt": "",
			"deletedAt": "",
			"openId": "",
			"dept": {
				"deptId": 0,
				"parentId": 0,
				"ancestors": "",
				"deptName": "",
				"orderNum": 0,
				"leader": [],
				"phone": "",
				"email": "",
				"status": 0,
				"createdBy": 0,
				"updatedBy": 0,
				"createdAt": "",
				"updatedAt": "",
				"deletedAt": ""
			},
			"roleInfo": [
				{
					"roleId": 0,
					"name": ""
				}
			],
			"post": [
				{
					"postId": 0,
					"postName": ""
				}
			]
		}
	],
	"currentPage": 0,
	"total": {}
}
```
## 用户维护参数获取
**接口地址**:`/api/v1/system/user/params`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
暂无
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.UserGetParamsRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|roleList||github.com.tiger1103.gfast.v3.internal.app.system.model.entity.SysRole|github.com.tiger1103.gfast.v3.internal.app.system.model.entity.SysRole|
|&emsp;&emsp;id||integer(uint)||
|&emsp;&emsp;pid||integer(uint)||
|&emsp;&emsp;status|状态;0:禁用;1:正常|integer(uint)||
|&emsp;&emsp;listOrder|排序|integer(uint)||
|&emsp;&emsp;name|角色名称|string(string)||
|&emsp;&emsp;remark|备注|string(string)||
|&emsp;&emsp;dataScope|数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）|integer(uint)||
|&emsp;&emsp;createdAt|创建时间|string(*gtime.Time)||
|&emsp;&emsp;updatedAt|更新时间|string(*gtime.Time)||
|&emsp;&emsp;userCnt|用户数量|integer(uint)||
|&emsp;&emsp;createdBy|创建人|integer(uint64)||
|&emsp;&emsp;effectiveTime|角色有效日期|string(string)||
|posts||github.com.tiger1103.gfast.v3.internal.app.system.model.entity.SysPost|github.com.tiger1103.gfast.v3.internal.app.system.model.entity.SysPost|
|&emsp;&emsp;postId|岗位ID|integer(uint64)||
|&emsp;&emsp;postCode|岗位编码|string(string)||
|&emsp;&emsp;postName|岗位名称|string(string)||
|&emsp;&emsp;postSort|显示顺序|integer(int)||
|&emsp;&emsp;status|状态（0正常 1停用）|integer(uint)||
|&emsp;&emsp;remark|备注|string(string)||
|&emsp;&emsp;createdBy|创建人|integer(uint64)||
|&emsp;&emsp;updatedBy|修改人|integer(uint64)||
|&emsp;&emsp;createdAt|创建时间|string(*gtime.Time)||
|&emsp;&emsp;updatedAt|修改时间|string(*gtime.Time)||
|&emsp;&emsp;deletedAt|删除时间|string(*gtime.Time)||
|roleAccess||array([]uint)||
**响应示例**:
```javascript
{
	"roleList": [
		{
			"id": 0,
			"pid": 0,
			"status": 0,
			"listOrder": 0,
			"name": "",
			"remark": "",
			"dataScope": 0,
			"createdAt": "",
			"updatedAt": "",
			"userCnt": 0,
			"createdBy": 0,
			"effectiveTime": ""
		}
	],
	"posts": [
		{
			"postId": 0,
			"postCode": "",
			"postName": "",
			"postSort": 0,
			"status": 0,
			"remark": "",
			"createdBy": 0,
			"updatedBy": 0,
			"createdAt": "",
			"updatedAt": "",
			"deletedAt": ""
		}
	],
	"roleAccess": []
}
```
## 重置用户密码
**接口地址**:`/api/v1/system/user/resetPwd`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "userId": 0,
  "password": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.system.UserResetPwdReq|github.com.tiger1103.gfast.v3.api.v1.system.UserResetPwdReq|body|true|github.com.tiger1103.gfast.v3.api.v1.system.UserResetPwdReq|github.com.tiger1103.gfast.v3.api.v1.system.UserResetPwdReq|
|&emsp;&emsp;userId|||true|integer(uint64)||
|&emsp;&emsp;password|||true|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 获取用户选择器
**接口地址**:`/api/v1/system/user/selector`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|deptId||query|false|string(string)||
|roleId||query|false|integer(uint)||
|mobile||query|false|string(string)||
|status||query|false|string(string)||
|keyWords||query|false|string(string)||
|dateRange||query|false|array|string|
|pageNum||query|false|integer(int)||
|pageSize||query|false|integer(int)||
|orderBy||query|false|string(string)||
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.UserSelectorRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|currentPage||integer(int)|integer(int)|
|total||interface|interface|
|userList||github.com.tiger1103.gfast.v3.internal.app.system.model.SysUserSimpleRes|github.com.tiger1103.gfast.v3.internal.app.system.model.SysUserSimpleRes|
|&emsp;&emsp;id||integer(uint64)||
|&emsp;&emsp;avatar||string(string)||
|&emsp;&emsp;sex||integer(int)||
|&emsp;&emsp;userNickname||string(string)||
**响应示例**:
```javascript
{
	"currentPage": 0,
	"total": {},
	"userList": [
		{
			"id": 0,
			"avatar": "",
			"sex": 0,
			"userNickname": ""
		}
	]
}
```
## 设置用户状态
**接口地址**:`/api/v1/system/user/setStatus`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "userId": 0,
  "status": 0
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.system.UserStatusReq|github.com.tiger1103.gfast.v3.api.v1.system.UserStatusReq|body|true|github.com.tiger1103.gfast.v3.api.v1.system.UserStatusReq|github.com.tiger1103.gfast.v3.api.v1.system.UserStatusReq|
|&emsp;&emsp;userId|||true|integer(uint64)||
|&emsp;&emsp;status|||true|integer(uint)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
# 系统后台-岗位管理
## 添加岗位
**接口地址**:`/api/v1/system/post/add`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "postCode": "",
  "postName": "",
  "postSort": 0,
  "status": 0,
  "remark": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.system.PostAddReq|github.com.tiger1103.gfast.v3.api.v1.system.PostAddReq|body|true|github.com.tiger1103.gfast.v3.api.v1.system.PostAddReq|github.com.tiger1103.gfast.v3.api.v1.system.PostAddReq|
|&emsp;&emsp;postCode|||true|string(string)||
|&emsp;&emsp;postName|||true|string(string)||
|&emsp;&emsp;postSort|||true|integer(int)||
|&emsp;&emsp;status|||true|integer(uint)||
|&emsp;&emsp;remark|||false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 删除岗位
**接口地址**:`/api/v1/system/post/delete`
**请求方式**:`DELETE`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|ids||query|false|array|integer|
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 修改岗位
**接口地址**:`/api/v1/system/post/edit`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "postId": 0,
  "postCode": "",
  "postName": "",
  "postSort": 0,
  "status": 0,
  "remark": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.system.PostEditReq|github.com.tiger1103.gfast.v3.api.v1.system.PostEditReq|body|true|github.com.tiger1103.gfast.v3.api.v1.system.PostEditReq|github.com.tiger1103.gfast.v3.api.v1.system.PostEditReq|
|&emsp;&emsp;postId|||true|integer(int64)||
|&emsp;&emsp;postCode|||true|string(string)||
|&emsp;&emsp;postName|||true|string(string)||
|&emsp;&emsp;postSort|||true|integer(int)||
|&emsp;&emsp;status|||true|integer(uint)||
|&emsp;&emsp;remark|||false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 岗位列表
**接口地址**:`/api/v1/system/post/list`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|postCode||query|false|string(string)||
|postName||query|false|string(string)||
|status||query|false|string(string)||
|dateRange||query|false|array|string|
|pageNum||query|false|integer(int)||
|pageSize||query|false|integer(int)||
|orderBy||query|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.PostSearchRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|currentPage||integer(int)|integer(int)|
|total||interface|interface|
|postList||github.com.tiger1103.gfast.v3.internal.app.system.model.entity.SysPost|github.com.tiger1103.gfast.v3.internal.app.system.model.entity.SysPost|
|&emsp;&emsp;postId|岗位ID|integer(uint64)||
|&emsp;&emsp;postCode|岗位编码|string(string)||
|&emsp;&emsp;postName|岗位名称|string(string)||
|&emsp;&emsp;postSort|显示顺序|integer(int)||
|&emsp;&emsp;status|状态（0正常 1停用）|integer(uint)||
|&emsp;&emsp;remark|备注|string(string)||
|&emsp;&emsp;createdBy|创建人|integer(uint64)||
|&emsp;&emsp;updatedBy|修改人|integer(uint64)||
|&emsp;&emsp;createdAt|创建时间|string(*gtime.Time)||
|&emsp;&emsp;updatedAt|修改时间|string(*gtime.Time)||
|&emsp;&emsp;deletedAt|删除时间|string(*gtime.Time)||
**响应示例**:
```javascript
{
	"currentPage": 0,
	"total": {},
	"postList": [
		{
			"postId": 0,
			"postCode": "",
			"postName": "",
			"postSort": 0,
			"status": 0,
			"remark": "",
			"createdBy": 0,
			"updatedBy": 0,
			"createdAt": "",
			"updatedAt": "",
			"deletedAt": ""
		}
	]
}
```
# 系统后台-角色管理
## 添加角色
**接口地址**:`/api/v1/system/role/add`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "pid": 0,
  "name": "",
  "status": 0,
  "listOrder": 0,
  "remark": "",
  "menuIds": [],
  "CreatedBy": 0,
  "effectiveType": 0,
  "weekDay": [],
  "dayRange": [],
  "dateRange": []
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.system.RoleAddReq|github.com.tiger1103.gfast.v3.api.v1.system.RoleAddReq|body|true|github.com.tiger1103.gfast.v3.api.v1.system.RoleAddReq|github.com.tiger1103.gfast.v3.api.v1.system.RoleAddReq|
|&emsp;&emsp;pid|||false|integer(uint)||
|&emsp;&emsp;name|||true|string(string)||
|&emsp;&emsp;status|||false|integer(uint)||
|&emsp;&emsp;listOrder|||false|integer(uint)||
|&emsp;&emsp;remark|||false|string(string)||
|&emsp;&emsp;menuIds|||false|array([]uint)|integer|
|&emsp;&emsp;CreatedBy|||false|integer(uint64)||
|&emsp;&emsp;effectiveType|||false|integer(int)||
|&emsp;&emsp;weekDay|||false|array([]int)|integer|
|&emsp;&emsp;dayRange|||false|array([]*gtime.Time)|string|
|&emsp;&emsp;dateRange|||false|array([]*gtime.Time)|string|
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 角色数据授权
**接口地址**:`/api/v1/system/role/dataScope`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "roleId": 0,
  "authData": [
    {
      "menuId": 0,
      "scope": 0,
      "deptIds": []
    }
  ]
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.system.DataScopeReq|github.com.tiger1103.gfast.v3.api.v1.system.DataScopeReq|body|true|github.com.tiger1103.gfast.v3.api.v1.system.DataScopeReq|github.com.tiger1103.gfast.v3.api.v1.system.DataScopeReq|
|&emsp;&emsp;roleId|||true|integer(uint)||
|&emsp;&emsp;authData|||false|array([]*model.ScopeAuthDataReq)|github.com.tiger1103.gfast.v3.internal.app.system.model.ScopeAuthDataReq|
|&emsp;&emsp;&emsp;&emsp;menuId|||false|integer||
|&emsp;&emsp;&emsp;&emsp;scope|||false|integer||
|&emsp;&emsp;&emsp;&emsp;deptIds|||false|array|integer|
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.DataScopeRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 删除角色
**接口地址**:`/api/v1/system/role/delete`
**请求方式**:`DELETE`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|ids||query|true|array|integer|
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.RoleDeleteRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 获取部门树
**接口地址**:`/api/v1/system/role/deptTreeSelect`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.RoleDeptTreeSelectRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|depts||github.com.tiger1103.gfast.v3.internal.app.system.model.SysDeptTreeRes|github.com.tiger1103.gfast.v3.internal.app.system.model.SysDeptTreeRes|
|&emsp;&emsp;deptId|部门id|integer(uint64)||
|&emsp;&emsp;parentId|父部门id|integer(uint64)||
|&emsp;&emsp;ancestors|祖级列表|string(string)||
|&emsp;&emsp;deptName|部门名称|string(string)||
|&emsp;&emsp;orderNum|显示顺序|integer(int)||
|&emsp;&emsp;leader|负责人|array([]int)|integer|
|&emsp;&emsp;phone|联系电话|string(string)||
|&emsp;&emsp;email|邮箱|string(string)||
|&emsp;&emsp;status|部门状态（0正常 1停用）|integer(uint)||
|&emsp;&emsp;createdBy|创建人|integer(uint64)||
|&emsp;&emsp;updatedBy|修改人|integer(int64)||
|&emsp;&emsp;createdAt|创建时间|string(*gtime.Time)||
|&emsp;&emsp;updatedAt|修改时间|string(*gtime.Time)||
|&emsp;&emsp;deletedAt|删除时间|string(*gtime.Time)||
|&emsp;&emsp;children||array([]*model.SysDeptTreeRes)|github.com.tiger1103.gfast.v3.internal.app.system.model.SysDeptTreeRes|
**响应示例**:
```javascript
{
	"depts": [
		{
			"deptId": 0,
			"parentId": 0,
			"ancestors": "",
			"deptName": "",
			"orderNum": 0,
			"leader": [],
			"phone": "",
			"email": "",
			"status": 0,
			"createdBy": 0,
			"updatedBy": 0,
			"createdAt": "",
			"updatedAt": "",
			"deletedAt": "",
			"children": [
				{
					"deptId": 0,
					"parentId": 0,
					"ancestors": "",
					"deptName": "",
					"orderNum": 0,
					"leader": [],
					"phone": "",
					"email": "",
					"status": 0,
					"createdBy": 0,
					"updatedBy": 0,
					"createdAt": "",
					"updatedAt": "",
					"deletedAt": "",
					"children": [
						{}
					]
				}
			]
		}
	]
}
```
## 修改角色
**接口地址**:`/api/v1/system/role/edit`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id||query|true|integer||
|name||query|true|string||
|Authorization|Bearer {{token}}|header|false|string(string)||
|dateRange||query|false|array|string|
|pid||query|false|integer||
|listOrder||query|false|integer||
|effectiveType||query|false|integer||
|status||query|false|integer||
|remark||query|false|string||
|menuIds||query|false|array|integer|
|weekDay||query|false|array|integer|
|dayRange||query|false|array|string|
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 获取角色信息
**接口地址**:`/api/v1/system/role/get`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id||query|true|integer(uint)||
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.RoleGetRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|role||github.com.tiger1103.gfast.v3.internal.app.system.model.RoleInfoRes|github.com.tiger1103.gfast.v3.internal.app.system.model.RoleInfoRes|
|&emsp;&emsp;id||integer(uint)||
|&emsp;&emsp;pid||integer(uint)||
|&emsp;&emsp;status|状态;0:禁用;1:正常|integer(uint)||
|&emsp;&emsp;listOrder|排序|integer(uint)||
|&emsp;&emsp;name|角色名称|string(string)||
|&emsp;&emsp;remark|备注|string(string)||
|&emsp;&emsp;dataScope|数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）|integer(uint)||
|&emsp;&emsp;createdAt|创建时间|string(*gtime.Time)||
|&emsp;&emsp;updatedAt|更新时间|string(*gtime.Time)||
|&emsp;&emsp;userCnt|用户数量|integer(uint)||
|&emsp;&emsp;createdBy|创建人|integer(uint64)||
|&emsp;&emsp;effectiveTime|角色有效日期|string(string)||
|&emsp;&emsp;effectiveType||integer(int)||
|&emsp;&emsp;weekDay||array([]int)|integer|
|&emsp;&emsp;dayRange||array([]*gtime.Time)|string|
|&emsp;&emsp;dateRange||array([]*gtime.Time)|string|
|menuIds||array([]int)||
**响应示例**:
```javascript
{
	"role": {
		"id": 0,
		"pid": 0,
		"status": 0,
		"listOrder": 0,
		"name": "",
		"remark": "",
		"dataScope": 0,
		"createdAt": "",
		"updatedAt": "",
		"userCnt": 0,
		"createdBy": 0,
		"effectiveTime": "",
		"effectiveType": 0,
		"weekDay": [],
		"dayRange": [],
		"dateRange": []
	},
	"menuIds": []
}
```
## 角色编辑参数
**接口地址**:`/api/v1/system/role/getParams`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
暂无
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.RoleGetParamsRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|menu||github.com.tiger1103.gfast.v3.internal.app.system.model.SysAuthRuleInfoRes|github.com.tiger1103.gfast.v3.internal.app.system.model.SysAuthRuleInfoRes|
|&emsp;&emsp;id||integer(uint)||
|&emsp;&emsp;pid||integer(uint)||
|&emsp;&emsp;name||string(string)||
|&emsp;&emsp;title||string(string)||
|&emsp;&emsp;icon||string(string)||
|&emsp;&emsp;condition||string(string)||
|&emsp;&emsp;remark||string(string)||
|&emsp;&emsp;menuType||integer(uint)||
|&emsp;&emsp;weigh||integer(int)||
|&emsp;&emsp;isHide||integer(uint)||
|&emsp;&emsp;isCached||integer(uint)||
|&emsp;&emsp;isAffix||integer(uint)||
|&emsp;&emsp;path||string(string)||
|&emsp;&emsp;redirect||string(string)||
|&emsp;&emsp;component||string(string)||
|&emsp;&emsp;isIframe||integer(uint)||
|&emsp;&emsp;isLink||integer(uint)||
|&emsp;&emsp;linkUrl||string(string)||
|accessMenus||github.com.gogf.gf.v2.container.garray.Array|github.com.gogf.gf.v2.container.garray.Array|
**响应示例**:
```javascript
{
	"menu": [
		{
			"id": 0,
			"pid": 0,
			"name": "",
			"title": "",
			"icon": "",
			"condition": "",
			"remark": "",
			"menuType": 0,
			"weigh": 0,
			"isHide": 0,
			"isCached": 0,
			"isAffix": 0,
			"path": "",
			"redirect": "",
			"component": "",
			"isIframe": 0,
			"isLink": 0,
			"linkUrl": ""
		}
	],
	"accessMenus": {}
}
```
## 角色列表
**接口地址**:`/api/v1/system/role/list`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|roleName||query|false|string(string)||
|roleStatus||query|false|string(string)||
|dateRange||query|false|array|string|
|pageNum||query|false|integer(int)||
|pageSize||query|false|integer(int)||
|orderBy||query|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.RoleListRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|currentPage||integer(int)|integer(int)|
|total||interface|interface|
|list||github.com.tiger1103.gfast.v3.internal.app.system.model.entity.SysRole|github.com.tiger1103.gfast.v3.internal.app.system.model.entity.SysRole|
|&emsp;&emsp;id||integer(uint)||
|&emsp;&emsp;pid||integer(uint)||
|&emsp;&emsp;status|状态;0:禁用;1:正常|integer(uint)||
|&emsp;&emsp;listOrder|排序|integer(uint)||
|&emsp;&emsp;name|角色名称|string(string)||
|&emsp;&emsp;remark|备注|string(string)||
|&emsp;&emsp;dataScope|数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）|integer(uint)||
|&emsp;&emsp;createdAt|创建时间|string(*gtime.Time)||
|&emsp;&emsp;updatedAt|更新时间|string(*gtime.Time)||
|&emsp;&emsp;userCnt|用户数量|integer(uint)||
|&emsp;&emsp;createdBy|创建人|integer(uint64)||
|&emsp;&emsp;effectiveTime|角色有效日期|string(string)||
**响应示例**:
```javascript
{
	"currentPage": 0,
	"total": {},
	"list": [
		{
			"id": 0,
			"pid": 0,
			"status": 0,
			"listOrder": 0,
			"name": "",
			"remark": "",
			"dataScope": 0,
			"createdAt": "",
			"updatedAt": "",
			"userCnt": 0,
			"createdBy": 0,
			"effectiveTime": ""
		}
	]
}
```
## 获取菜单树
**接口地址**:`/api/v1/system/role/menuTreeSelect`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|roleId||query|true|integer(uint)||
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.RoleMenuTreeSelectRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|rules||github.com.tiger1103.gfast.v3.internal.app.system.model.SysAuthRuleTreeRes|github.com.tiger1103.gfast.v3.internal.app.system.model.SysAuthRuleTreeRes|
|&emsp;&emsp;id||integer(uint)||
|&emsp;&emsp;pid||integer(uint)||
|&emsp;&emsp;name||string(string)||
|&emsp;&emsp;title||string(string)||
|&emsp;&emsp;icon||string(string)||
|&emsp;&emsp;condition||string(string)||
|&emsp;&emsp;remark||string(string)||
|&emsp;&emsp;menuType||integer(uint)||
|&emsp;&emsp;weigh||integer(int)||
|&emsp;&emsp;isHide||integer(uint)||
|&emsp;&emsp;isCached||integer(uint)||
|&emsp;&emsp;isAffix||integer(uint)||
|&emsp;&emsp;path||string(string)||
|&emsp;&emsp;redirect||string(string)||
|&emsp;&emsp;component||string(string)||
|&emsp;&emsp;isIframe||integer(uint)||
|&emsp;&emsp;isLink||integer(uint)||
|&emsp;&emsp;linkUrl||string(string)||
|&emsp;&emsp;children||array([]*model.SysAuthRuleTreeRes)|github.com.tiger1103.gfast.v3.internal.app.system.model.SysAuthRuleTreeRes|
|dataScope||github.com.tiger1103.gfast.v3.internal.app.system.model.ScopeAuthData|github.com.tiger1103.gfast.v3.internal.app.system.model.ScopeAuthData|
|&emsp;&emsp;id||integer(uint64)||
|&emsp;&emsp;roleId||integer(uint)||
|&emsp;&emsp;menuId||integer(uint)||
|&emsp;&emsp;dataScope||integer(int)||
|&emsp;&emsp;deptIds||array([]int64)|integer|
**响应示例**:
```javascript
{
	"rules": [
		{
			"id": 0,
			"pid": 0,
			"name": "",
			"title": "",
			"icon": "",
			"condition": "",
			"remark": "",
			"menuType": 0,
			"weigh": 0,
			"isHide": 0,
			"isCached": 0,
			"isAffix": 0,
			"path": "",
			"redirect": "",
			"component": "",
			"isIframe": 0,
			"isLink": 0,
			"linkUrl": "",
			"children": [
				{
					"id": 0,
					"pid": 0,
					"name": "",
					"title": "",
					"icon": "",
					"condition": "",
					"remark": "",
					"menuType": 0,
					"weigh": 0,
					"isHide": 0,
					"isCached": 0,
					"isAffix": 0,
					"path": "",
					"redirect": "",
					"component": "",
					"isIframe": 0,
					"isLink": 0,
					"linkUrl": "",
					"children": [
						{}
					]
				}
			]
		}
	],
	"dataScope": [
		{
			"id": 0,
			"roleId": 0,
			"menuId": 0,
			"dataScope": 0,
			"deptIds": []
		}
	]
}
```
## 角色用户授权
**接口地址**:`/api/v1/system/role/setRoleUser`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "roleId": 0,
  "userIds": []
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.system.SetRoleUserReq|github.com.tiger1103.gfast.v3.api.v1.system.SetRoleUserReq|body|true|github.com.tiger1103.gfast.v3.api.v1.system.SetRoleUserReq|github.com.tiger1103.gfast.v3.api.v1.system.SetRoleUserReq|
|&emsp;&emsp;roleId|||true|integer(uint)||
|&emsp;&emsp;userIds|||false|array([]uint64)|integer|
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.SetRoleUserRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
# 附件管理
## 附件管理添加
**接口地址**:`/api/v1/system/sysAttachment/add`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|appId|应用ID|query|true|string||
|name|文件原始名|query|true|string||
|status|状态|query|true|boolean||
|Authorization|Bearer {{token}}|header|false|string(string)||
|drive|上传驱动|query|false|integer||
|kind|上传类型|query|false|string||
|md5|md5校验码|query|false|string||
|CreatedBy||query|false|integer||
|mimeType|扩展类型|query|false|string||
|path|本地路径|query|false|string||
|size|文件大小|query|false|integer||
|ext|扩展名|query|false|string||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.SysAttachmentAddRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 修改状态
**接口地址**:`/api/v1/system/sysAttachment/changeStatus`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id||query|true|integer||
|status||query|true|boolean||
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.SysAttachmentStatusSwitchRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 删除附件管理
**接口地址**:`/api/v1/system/sysAttachment/delete`
**请求方式**:`DELETE`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|ids||query|true|array|integer|
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.SysAttachmentDeleteRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 附件管理修改
**接口地址**:`/api/v1/system/sysAttachment/edit`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id|文件ID|query|true|integer||
|appId|应用ID|query|true|integer||
|name|文件原始名|query|true|string||
|status|状态|query|true|boolean||
|Authorization|Bearer {{token}}|header|false|string(string)||
|drive|上传驱动|query|false|string||
|kind|上传类型|query|false|string||
|mimeType|扩展类型|query|false|string||
|ext|扩展名|query|false|string||
|md5|md5校验码|query|false|string||
|path|本地路径|query|false|string||
|size|文件大小|query|false|integer||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.SysAttachmentEditRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 获取附件管理信息
**接口地址**:`/api/v1/system/sysAttachment/get`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id||query|true|integer(int64)||
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.SysAttachmentGetRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|id|文件ID|integer(int64)|integer(int64)|
|appId|应用ID|string(string)|string(string)|
|drive|上传驱动|integer(uint)|integer(uint)|
|name|文件原始名|string(string)|string(string)|
|kind|上传类型|string(string)|string(string)|
|mimeType|扩展类型|string(string)|string(string)|
|path|本地路径|string(string)|string(string)|
|size|文件大小|integer(int64)|integer(int64)|
|ext|扩展名|string(string)|string(string)|
|md5|md5校验码|string(string)|string(string)|
|createdBy|上传人ID|integer(int64)|integer(int64)|
|createdUser||github.com.tiger1103.gfast.v3.internal.app.common.model.LinkUserRes|github.com.tiger1103.gfast.v3.internal.app.common.model.LinkUserRes|
|&emsp;&emsp;id||integer(uint64)||
|&emsp;&emsp;userNickname||string(string)||
|status|状态|boolean(bool)|boolean(bool)|
|createdAt|创建时间|string(*gtime.Time)|string(*gtime.Time)|
|updatedAt|修改时间|string(*gtime.Time)|string(*gtime.Time)|
**响应示例**:
```javascript
{
	"id": 0,
	"appId": "",
	"drive": 0,
	"name": "",
	"kind": "",
	"mimeType": "",
	"path": "",
	"size": 0,
	"ext": "",
	"md5": "",
	"createdBy": 0,
	"createdUser": {
		"id": 0,
		"userNickname": ""
	},
	"status": true,
	"createdAt": "",
	"updatedAt": ""
}
```
## 附件管理列表
**接口地址**:`/api/v1/system/sysAttachment/list`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|Authorization|Bearer {{token}}|header|false|string(string)||
|dateRange||query|false|array|string|
|pageNum||query|false|integer(int)||
|pageSize||query|false|integer(int)||
|orderBy||query|false|string(string)||
|appId|应用ID|query|false|string(string)||
|drive|上传驱动|query|false|string(string)||
|name|文件原始名|query|false|string(string)||
|kind|上传类型|query|false|string(string)||
|mimeType|扩展类型|query|false|string(string)||
|status|状态|query|false|string(string)||
|createdAt|创建时间|query|false|array|string|
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.SysAttachmentSearchRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|currentPage||integer(int)|integer(int)|
|total||interface|interface|
|list||github.com.tiger1103.gfast.v3.internal.app.common.model.SysAttachmentListRes|github.com.tiger1103.gfast.v3.internal.app.common.model.SysAttachmentListRes|
|&emsp;&emsp;id|文件ID|integer(int64)||
|&emsp;&emsp;appId|应用ID|string(string)||
|&emsp;&emsp;drive|上传驱动|integer(uint)||
|&emsp;&emsp;name|文件原始名|string(string)||
|&emsp;&emsp;kind|上传类型|string(string)||
|&emsp;&emsp;path|本地路径|string(string)||
|&emsp;&emsp;size|文件大小|integer(int64)||
|&emsp;&emsp;ext|扩展名|string(string)||
|&emsp;&emsp;status|状态|boolean(bool)||
|&emsp;&emsp;createdAt|创建时间|string(*gtime.Time)||
|&emsp;&emsp;updatedAt|修改时间|string(*gtime.Time)||
**响应示例**:
```javascript
{
	"currentPage": 0,
	"total": {},
	"list": [
		{
			"id": 0,
			"appId": "",
			"drive": 0,
			"name": "",
			"kind": "",
			"path": "",
			"size": 0,
			"ext": "",
			"status": true,
			"createdAt": "",
			"updatedAt": ""
		}
	]
}
```
# 系统后台-定时任务
## 定时任务添加
**接口地址**:`/api/v1/system/sysJob/add`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|invokeTarget||query|true|string||
|cronExpression||query|true|string||
|status||query|true|integer||
|jobName||query|true|string||
|Authorization|Bearer {{token}}|header|false|string(string)||
|misfirePolicy||query|false|integer||
|remark||query|false|string||
|CreatedBy||query|false|integer||
|jobGroup||query|false|string||
|jobParams||query|false|string||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.SysJobAddRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 删除定时任务
**接口地址**:`/api/v1/system/sysJob/delete`
**请求方式**:`DELETE`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|jobIds||query|true|array|integer|
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.SysJobDeleteRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 删除执行日志
**接口地址**:`/api/v1/system/sysJob/deleteLogs`
**请求方式**:`DELETE`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|targetName||query|true|string(string)||
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.SysJobLogDeleteRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 定时任务修改
**接口地址**:`/api/v1/system/sysJob/edit`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|jobId||query|true|integer||
|jobName||query|true|string||
|invokeTarget||query|true|string||
|cronExpression||query|true|string||
|status||query|true|integer||
|Authorization|Bearer {{token}}|header|false|string(string)||
|jobParams||query|false|string||
|jobGroup||query|false|string||
|remark||query|false|string||
|UpdatedBy||query|false|integer||
|misfirePolicy||query|false|integer||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.SysJobEditRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 获取定时任务信息
**接口地址**:`/api/v1/system/sysJob/get`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|jobId||query|true|integer(uint64)||
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.SysJobGetRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|jobId||integer(uint64)|integer(uint64)|
|jobName||string(string)|string(string)|
|jobParams||string(string)|string(string)|
|jobGroup||string(string)|string(string)|
|invokeTarget||string(string)|string(string)|
|cronExpression||string(string)|string(string)|
|misfirePolicy||integer(int)|integer(int)|
|concurrent||integer(int)|integer(int)|
|status||integer(int)|integer(int)|
|createdBy||integer(uint64)|integer(uint64)|
|updatedBy||integer(uint64)|integer(uint64)|
|remark||string(string)|string(string)|
|createdAt||string(*gtime.Time)|string(*gtime.Time)|
|updatedAt||string(*gtime.Time)|string(*gtime.Time)|
|createdUser||github.com.tiger1103.gfast.v3.internal.app.system.model.LinkUserRes|github.com.tiger1103.gfast.v3.internal.app.system.model.LinkUserRes|
|&emsp;&emsp;id||integer(uint64)||
|&emsp;&emsp;userNickname||string(string)||
|updatedUser||github.com.tiger1103.gfast.v3.internal.app.system.model.LinkUserRes|github.com.tiger1103.gfast.v3.internal.app.system.model.LinkUserRes|
|&emsp;&emsp;id||integer(uint64)||
|&emsp;&emsp;userNickname||string(string)||
**响应示例**:
```javascript
{
	"jobId": 0,
	"jobName": "",
	"jobParams": "",
	"jobGroup": "",
	"invokeTarget": "",
	"cronExpression": "",
	"misfirePolicy": 0,
	"concurrent": 0,
	"status": 0,
	"createdBy": 0,
	"updatedBy": 0,
	"remark": "",
	"createdAt": "",
	"updatedAt": "",
	"createdUser": {
		"id": 0,
		"userNickname": ""
	},
	"updatedUser": {
		"id": 0,
		"userNickname": ""
	}
}
```
## 定时任务列表
**接口地址**:`/api/v1/system/sysJob/list`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|jobName||query|false|string(string)||
|jobGroup||query|false|string(string)||
|status||query|false|string(string)||
|dateRange||query|false|array|string|
|pageNum||query|false|integer(int)||
|pageSize||query|false|integer(int)||
|orderBy||query|false|string(string)||
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.SysJobSearchRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|currentPage||integer(int)|integer(int)|
|total||interface|interface|
|list||github.com.tiger1103.gfast.v3.internal.app.system.model.SysJobListRes|github.com.tiger1103.gfast.v3.internal.app.system.model.SysJobListRes|
|&emsp;&emsp;jobId||integer(uint64)||
|&emsp;&emsp;jobName||string(string)||
|&emsp;&emsp;jobGroup||string(string)||
|&emsp;&emsp;invokeTarget||string(string)||
|&emsp;&emsp;cronExpression||string(string)||
|&emsp;&emsp;misfirePolicy||integer(int)||
|&emsp;&emsp;status||integer(int)||
**响应示例**:
```javascript
{
	"currentPage": 0,
	"total": {},
	"list": [
		{
			"jobId": 0,
			"jobName": "",
			"jobGroup": "",
			"invokeTarget": "",
			"cronExpression": "",
			"misfirePolicy": 0,
			"status": 0
		}
	]
}
```
## 执行日志
**接口地址**:`/api/v1/system/sysJob/logs`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|targetName||query|true|string(string)||
|Authorization|Bearer {{token}}|header|false|string(string)||
|dateRange||query|false|array|string|
|pageNum||query|false|integer(int)||
|pageSize||query|false|integer(int)||
|orderBy||query|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.SysJobLogListRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|currentPage||integer(int)|integer(int)|
|total||interface|interface|
|list||github.com.tiger1103.gfast.v3.internal.app.system.model.entity.SysJobLog|github.com.tiger1103.gfast.v3.internal.app.system.model.entity.SysJobLog|
|&emsp;&emsp;id|主键|integer(uint64)||
|&emsp;&emsp;targetName|方法名|string(string)||
|&emsp;&emsp;createdAt|执行日期|string(*gtime.Time)||
|&emsp;&emsp;result|执行结果|string(string)||
**响应示例**:
```javascript
{
	"currentPage": 0,
	"total": {},
	"list": [
		{
			"id": 0,
			"targetName": "",
			"createdAt": "",
			"result": ""
		}
	]
}
```
## 运行任务
**接口地址**:`/api/v1/system/sysJob/run`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|jobId||query|true|integer||
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.SysJobRunRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 启动任务
**接口地址**:`/api/v1/system/sysJob/start`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|jobId||query|true|integer||
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.SysJobStartRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 停止任务
**接口地址**:`/api/v1/system/sysJob/stop`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|jobId||query|true|integer||
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.SysJobStopRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
# 系统后台-通知私信
## 通知公告添加
**接口地址**:`/api/v1/system/sysNotice/add`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|type|类型|query|true|integer||
|content|内容|query|true|string||
|status|状态|query|true|integer||
|title|标题|query|true|string||
|Authorization|Bearer {{token}}|header|false|string(string)||
|tag|标签|query|false|integer||
|receiver||query|false|array|integer|
|CreatedBy||query|false|integer||
|remark|备注|query|false|string||
|sort|排序|query|false|integer||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.SysNoticeAddRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 删除通知公告
**接口地址**:`/api/v1/system/sysNotice/delete`
**请求方式**:`DELETE`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|ids||query|true|array|integer|
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.SysNoticeDeleteRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 通知公告修改
**接口地址**:`/api/v1/system/sysNotice/edit`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id|ID|query|true|integer||
|type|类型|query|true|integer||
|title|标题|query|true|string||
|content|内容|query|true|string||
|status|状态|query|true|integer||
|Authorization|Bearer {{token}}|header|false|string(string)||
|UpdatedBy||query|false|integer||
|remark|备注|query|false|string||
|tag|标签|query|false|integer||
|sort|排序|query|false|integer||
|receiver||query|false|array|integer|
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.SysNoticeEditRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 获取通知公告信息
**接口地址**:`/api/v1/system/sysNotice/get`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id||query|true|integer(int64)||
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.SysNoticeGetRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|id|ID|integer(int64)|integer(int64)|
|title|标题|string(string)|string(string)|
|type|类型|integer(int64)|integer(int64)|
|tag|标签|integer(int)|integer(int)|
|content|内容|string(string)|string(string)|
|remark|备注|string(string)|string(string)|
|sort|排序|integer(int)|integer(int)|
|status|状态|integer(int)|integer(int)|
|createdBy|发送人|integer(int64)|integer(int64)|
|createdUser||github.com.tiger1103.gfast.v3.internal.app.system.model.LinkUserRes|github.com.tiger1103.gfast.v3.internal.app.system.model.LinkUserRes|
|&emsp;&emsp;id||integer(uint64)||
|&emsp;&emsp;userNickname||string(string)||
|updatedBy|修改人|integer(int64)|integer(int64)|
|updatedUser||github.com.tiger1103.gfast.v3.internal.app.system.model.LinkUserRes|github.com.tiger1103.gfast.v3.internal.app.system.model.LinkUserRes|
|&emsp;&emsp;id||integer(uint64)||
|&emsp;&emsp;userNickname||string(string)||
|createdAt|创建时间|string(*gtime.Time)|string(*gtime.Time)|
|updatedAt|更新时间|string(*gtime.Time)|string(*gtime.Time)|
|deletedAt|删除时间|string(*gtime.Time)|string(*gtime.Time)|
|receiver||array([]uint64)||
|receiverUser||github.com.tiger1103.gfast.v3.internal.app.system.model.LinkUserRes|github.com.tiger1103.gfast.v3.internal.app.system.model.LinkUserRes|
|&emsp;&emsp;id||integer(uint64)||
|&emsp;&emsp;userNickname||string(string)||
**响应示例**:
```javascript
{
	"id": 0,
	"title": "",
	"type": 0,
	"tag": 0,
	"content": "",
	"remark": "",
	"sort": 0,
	"status": 0,
	"createdBy": 0,
	"createdUser": {
		"id": 0,
		"userNickname": ""
	},
	"updatedBy": 0,
	"updatedUser": {
		"id": 0,
		"userNickname": ""
	},
	"createdAt": "",
	"updatedAt": "",
	"deletedAt": "",
	"receiver": [],
	"receiverUser": [
		{
			"id": 0,
			"userNickname": ""
		}
	]
}
```
## 获取首页通知预览数据
**接口地址**:`/api/v1/system/sysNotice/getIndexData`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.SysNoticeIndexDataRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|type1Total||integer(int)|integer(int)|
|type2Total||integer(int)|integer(int)|
|type1List||github.com.tiger1103.gfast.v3.internal.app.system.model.SysNoticeListRes|github.com.tiger1103.gfast.v3.internal.app.system.model.SysNoticeListRes|
|&emsp;&emsp;id|ID|integer(int64)||
|&emsp;&emsp;title|标题|string(string)||
|&emsp;&emsp;type|类型|integer(int64)||
|&emsp;&emsp;tag|标签|integer(int)||
|&emsp;&emsp;content|内容|string(string)||
|&emsp;&emsp;remark|备注|string(string)||
|&emsp;&emsp;sort|排序|integer(int)||
|&emsp;&emsp;status|状态|integer(int)||
|&emsp;&emsp;isRead|当前用户是否已读|boolean(bool)||
|&emsp;&emsp;clickNumber||integer(int)||
|&emsp;&emsp;createdUser||github.com.tiger1103.gfast.v3.internal.app.system.model.LinkUserRes|github.com.tiger1103.gfast.v3.internal.app.system.model.LinkUserRes|
|&emsp;&emsp;&emsp;&emsp;id||integer||
|&emsp;&emsp;&emsp;&emsp;userNickname||string||
|&emsp;&emsp;createdBy|发送人|integer(int64)||
|&emsp;&emsp;createdAt|创建时间|string(*gtime.Time)||
|type2List||github.com.tiger1103.gfast.v3.internal.app.system.model.SysNoticeListRes|github.com.tiger1103.gfast.v3.internal.app.system.model.SysNoticeListRes|
|&emsp;&emsp;id|ID|integer(int64)||
|&emsp;&emsp;title|标题|string(string)||
|&emsp;&emsp;type|类型|integer(int64)||
|&emsp;&emsp;tag|标签|integer(int)||
|&emsp;&emsp;content|内容|string(string)||
|&emsp;&emsp;remark|备注|string(string)||
|&emsp;&emsp;sort|排序|integer(int)||
|&emsp;&emsp;status|状态|integer(int)||
|&emsp;&emsp;isRead|当前用户是否已读|boolean(bool)||
|&emsp;&emsp;clickNumber||integer(int)||
|&emsp;&emsp;createdUser||github.com.tiger1103.gfast.v3.internal.app.system.model.LinkUserRes|github.com.tiger1103.gfast.v3.internal.app.system.model.LinkUserRes|
|&emsp;&emsp;&emsp;&emsp;id||integer||
|&emsp;&emsp;&emsp;&emsp;userNickname||string||
|&emsp;&emsp;createdBy|发送人|integer(int64)||
|&emsp;&emsp;createdAt|创建时间|string(*gtime.Time)||
**响应示例**:
```javascript
{
	"type1Total": 0,
	"type2Total": 0,
	"type1List": {
		"id": 0,
		"title": "",
		"type": 0,
		"tag": 0,
		"content": "",
		"remark": "",
		"sort": 0,
		"status": 0,
		"isRead": true,
		"clickNumber": 0,
		"createdUser": {
			"id": 0,
			"userNickname": ""
		},
		"createdBy": 0,
		"createdAt": ""
	},
	"type2List": {
		"id": 0,
		"title": "",
		"type": 0,
		"tag": 0,
		"content": "",
		"remark": "",
		"sort": 0,
		"status": 0,
		"isRead": true,
		"clickNumber": 0,
		"createdUser": {
			"id": 0,
			"userNickname": ""
		},
		"createdBy": 0,
		"createdAt": ""
	}
}
```
## 通知公告列表(管理)
**接口地址**:`/api/v1/system/sysNotice/list`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|Authorization|Bearer {{token}}|header|false|string(string)||
|dateRange||query|false|array|string|
|pageNum||query|false|integer(int)||
|pageSize||query|false|integer(int)||
|orderBy||query|false|string(string)||
|id|ID|query|false|string(string)||
|title|标题|query|false|string(string)||
|type|类型|query|false|string(string)||
|tag|标签|query|false|string(string)||
|status|状态|query|false|string(string)||
|createdAt|创建时间|query|false|string(string)||
|isTrim||query|false|boolean(bool)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.SysNoticeSearchRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|currentPage||integer(int)|integer(int)|
|total||interface|interface|
|list||github.com.tiger1103.gfast.v3.internal.app.system.model.SysNoticeListRes|github.com.tiger1103.gfast.v3.internal.app.system.model.SysNoticeListRes|
|&emsp;&emsp;id|ID|integer(int64)||
|&emsp;&emsp;title|标题|string(string)||
|&emsp;&emsp;type|类型|integer(int64)||
|&emsp;&emsp;tag|标签|integer(int)||
|&emsp;&emsp;content|内容|string(string)||
|&emsp;&emsp;remark|备注|string(string)||
|&emsp;&emsp;sort|排序|integer(int)||
|&emsp;&emsp;status|状态|integer(int)||
|&emsp;&emsp;isRead|当前用户是否已读|boolean(bool)||
|&emsp;&emsp;clickNumber||integer(int)||
|&emsp;&emsp;createdUser||github.com.tiger1103.gfast.v3.internal.app.system.model.LinkUserRes|github.com.tiger1103.gfast.v3.internal.app.system.model.LinkUserRes|
|&emsp;&emsp;&emsp;&emsp;id||integer||
|&emsp;&emsp;&emsp;&emsp;userNickname||string||
|&emsp;&emsp;createdBy|发送人|integer(int64)||
|&emsp;&emsp;createdAt|创建时间|string(*gtime.Time)||
**响应示例**:
```javascript
{
	"currentPage": 0,
	"total": {},
	"list": [
		{
			"id": 0,
			"title": "",
			"type": 0,
			"tag": 0,
			"content": "",
			"remark": "",
			"sort": 0,
			"status": 0,
			"isRead": true,
			"clickNumber": 0,
			"createdUser": {
				"id": 0,
				"userNickname": ""
			},
			"createdBy": 0,
			"createdAt": ""
		}
	]
}
```
## 通知公告列表(浏览)
**接口地址**:`/api/v1/system/sysNotice/listShow`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|Authorization|Bearer {{token}}|header|false|string(string)||
|dateRange||query|false|array|string|
|pageNum||query|false|integer(int)||
|pageSize||query|false|integer(int)||
|orderBy||query|false|string(string)||
|id|ID|query|false|string(string)||
|title|标题|query|false|string(string)||
|type|类型|query|false|string(string)||
|tag|标签|query|false|string(string)||
|status|状态|query|false|string(string)||
|createdAt|创建时间|query|false|string(string)||
|isTrim||query|false|boolean(bool)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.SysNoticeShowSearchRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|currentPage||integer(int)|integer(int)|
|total||interface|interface|
|list||github.com.tiger1103.gfast.v3.internal.app.system.model.SysNoticeListRes|github.com.tiger1103.gfast.v3.internal.app.system.model.SysNoticeListRes|
|&emsp;&emsp;id|ID|integer(int64)||
|&emsp;&emsp;title|标题|string(string)||
|&emsp;&emsp;type|类型|integer(int64)||
|&emsp;&emsp;tag|标签|integer(int)||
|&emsp;&emsp;content|内容|string(string)||
|&emsp;&emsp;remark|备注|string(string)||
|&emsp;&emsp;sort|排序|integer(int)||
|&emsp;&emsp;status|状态|integer(int)||
|&emsp;&emsp;isRead|当前用户是否已读|boolean(bool)||
|&emsp;&emsp;clickNumber||integer(int)||
|&emsp;&emsp;createdUser||github.com.tiger1103.gfast.v3.internal.app.system.model.LinkUserRes|github.com.tiger1103.gfast.v3.internal.app.system.model.LinkUserRes|
|&emsp;&emsp;&emsp;&emsp;id||integer||
|&emsp;&emsp;&emsp;&emsp;userNickname||string||
|&emsp;&emsp;createdBy|发送人|integer(int64)||
|&emsp;&emsp;createdAt|创建时间|string(*gtime.Time)||
**响应示例**:
```javascript
{
	"currentPage": 0,
	"total": {},
	"list": [
		{
			"id": 0,
			"title": "",
			"type": 0,
			"tag": 0,
			"content": "",
			"remark": "",
			"sort": 0,
			"status": 0,
			"isRead": true,
			"clickNumber": 0,
			"createdUser": {
				"id": 0,
				"userNickname": ""
			},
			"createdBy": 0,
			"createdAt": ""
		}
	]
}
```
## 获取未读数量
**接口地址**:`/api/v1/system/sysNotice/unReadCount`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.SysNoticeUnReadCountRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|notifyCount||integer(int)|integer(int)|
|noticeCount||integer(int)|integer(int)|
**响应示例**:
```javascript
{
	"notifyCount": 0,
	"noticeCount": 0
}
```
## 获取待指定的用户
**接口地址**:`/api/v1/system/sysNotice/userList`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|Authorization|Bearer {{token}}|header|false|string(string)||
|userNickname||query|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.SysNoticeUserSearchRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|userList||github.com.tiger1103.gfast.v3.internal.app.system.model.SysNoticeUserNickname|github.com.tiger1103.gfast.v3.internal.app.system.model.SysNoticeUserNickname|
|&emsp;&emsp;id||integer(int64)||
|&emsp;&emsp;userNickname||string(string)||
**响应示例**:
```javascript
{
	"userList": [
		{
			"id": 0,
			"userNickname": ""
		}
	]
}
```
# 系统后台-已读记录
## 已读记录添加
**接口地址**:`/api/v1/system/sysNoticeRead/add`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|Authorization|Bearer {{token}}|header|false|string(string)||
|noticeId|信息id|query|false|integer||
|userId|用户id|query|false|integer||
|createdAt|阅读时间|query|false|string||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.SysNoticeReadAddRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 删除已读记录
**接口地址**:`/api/v1/system/sysNoticeRead/delete`
**请求方式**:`DELETE`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|ids||query|true|array|integer|
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.SysNoticeReadDeleteRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 已读记录修改
**接口地址**:`/api/v1/system/sysNoticeRead/edit`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id|id|query|true|integer||
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.SysNoticeReadEditRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 获取已读记录信息
**接口地址**:`/api/v1/system/sysNoticeRead/get`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id||query|true|integer(int64)||
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.SysNoticeReadGetRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|id|id|integer(int64)|integer(int64)|
|noticeId|信息id|integer(int64)|integer(int64)|
|userId|用户id|integer(int64)|integer(int64)|
|updatedAt|更新时间|string(*gtime.Time)|string(*gtime.Time)|
|createdAt|阅读时间|string(*gtime.Time)|string(*gtime.Time)|
**响应示例**:
```javascript
{
	"id": 0,
	"noticeId": 0,
	"userId": 0,
	"updatedAt": "",
	"createdAt": ""
}
```
## 已读记录列表
**接口地址**:`/api/v1/system/sysNoticeRead/list`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|Authorization|Bearer {{token}}|header|false|string(string)||
|dateRange||query|false|array|string|
|pageNum||query|false|integer(int)||
|pageSize||query|false|integer(int)||
|orderBy||query|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.SysNoticeReadSearchRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|currentPage||integer(int)|integer(int)|
|total||interface|interface|
|list||github.com.tiger1103.gfast.v3.internal.app.system.model.SysNoticeReadListRes|github.com.tiger1103.gfast.v3.internal.app.system.model.SysNoticeReadListRes|
|&emsp;&emsp;id|id|integer(int64)||
|&emsp;&emsp;noticeId|信息id|integer(int64)||
|&emsp;&emsp;userId|用户id|integer(int64)||
|&emsp;&emsp;createdAt|阅读时间|string(*gtime.Time)||
**响应示例**:
```javascript
{
	"currentPage": 0,
	"total": {},
	"list": [
		{
			"id": 0,
			"noticeId": 0,
			"userId": 0,
			"createdAt": ""
		}
	]
}
```
## 已读记录
**接口地址**:`/api/v1/system/sysNoticeRead/readNotice`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|Authorization|Bearer {{token}}|header|false|string(string)||
|noticeId|信息id|query|false|integer||
|UserId||query|false|integer||
|CreatedAt||query|false|string||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.SysNoticeReadNoticeAddRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
# 系统后台-代码生成
## 代码生成
**接口地址**:`/api/v1/system/tools/gen/batchGenCode`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "ids": []
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.system.ToolsGenTableBatchGenCodeReq|github.com.tiger1103.gfast.v3.api.v1.system.ToolsGenTableBatchGenCodeReq|body|true|github.com.tiger1103.gfast.v3.api.v1.system.ToolsGenTableBatchGenCodeReq|github.com.tiger1103.gfast.v3.api.v1.system.ToolsGenTableBatchGenCodeReq|
|&emsp;&emsp;ids|||true|array([]int)|integer|
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.ToolsGenTableBatchGenCodeRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 生成数据编辑
**接口地址**:`/api/v1/system/tools/gen/columnList`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|tableId||query|true|integer(int64)||
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.ToolsGenTableEditRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|list||github.com.tiger1103.gfast.v3.internal.app.system.model.entity.ToolsGenTableColumn|github.com.tiger1103.gfast.v3.internal.app.system.model.entity.ToolsGenTableColumn|
|&emsp;&emsp;columnId|编号|integer(int64)||
|&emsp;&emsp;tableId|归属表编号|integer(int64)||
|&emsp;&emsp;columnName|列名称|string(string)||
|&emsp;&emsp;columnComment|列描述|string(string)||
|&emsp;&emsp;columnType|列类型|string(string)||
|&emsp;&emsp;goType|Go类型|string(string)||
|&emsp;&emsp;tsType|TS类型|string(string)||
|&emsp;&emsp;goField|Go字段名|string(string)||
|&emsp;&emsp;htmlField|html字段名|string(string)||
|&emsp;&emsp;isPk|是否主键（1是）|boolean(bool)||
|&emsp;&emsp;isIncrement|是否自增（1是）|boolean(bool)||
|&emsp;&emsp;isRequired|是否必填（1是）|boolean(bool)||
|&emsp;&emsp;isEdit|是否编辑字段（1是）|boolean(bool)||
|&emsp;&emsp;isList|是否列表字段（1是）|boolean(bool)||
|&emsp;&emsp;isDetail|是否详情字段|boolean(bool)||
|&emsp;&emsp;isQuery|是否查询字段（1是）|boolean(bool)||
|&emsp;&emsp;sortOrderEdit|插入编辑显示顺序|integer(int)||
|&emsp;&emsp;sortOrderList|列表显示顺序|integer(int)||
|&emsp;&emsp;sortOrderDetail|详情显示顺序|integer(int)||
|&emsp;&emsp;sortOrderQuery|查询显示顺序|integer(int)||
|&emsp;&emsp;queryType|查询方式（等于、不等于、大于、小于、范围）|string(string)||
|&emsp;&emsp;htmlType|显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）|string(string)||
|&emsp;&emsp;dictType|字典类型|string(string)||
|&emsp;&emsp;linkTableName|关联表名|string(string)||
|&emsp;&emsp;linkTableClass|关联表类名|string(string)||
|&emsp;&emsp;linkTableModuleName|关联表模块名|string(string)||
|&emsp;&emsp;linkTableBusinessName|关联表业务名|string(string)||
|&emsp;&emsp;linkTablePackage|关联表包名|string(string)||
|&emsp;&emsp;linkLabelId|关联表键名|string(string)||
|&emsp;&emsp;linkLabelName|关联表字段值|string(string)||
|&emsp;&emsp;colSpan|详情页占列数|integer(int)||
|&emsp;&emsp;rowSpan|详情页占行数|integer(int)||
|&emsp;&emsp;isRowStart|详情页为行首|boolean(bool)||
|&emsp;&emsp;minWidth|表格最小宽度|integer(int)||
|&emsp;&emsp;isFixed|是否表格列左固定|boolean(bool)||
|&emsp;&emsp;isOverflowTooltip|是否过长自动隐藏|boolean(bool)||
|&emsp;&emsp;isCascade|是否级联查询|boolean(bool)||
|&emsp;&emsp;parentColumnName|上级字段名|string(string)||
|&emsp;&emsp;cascadeColumnName|级联查询字段|string(string)||
|info||github.com.tiger1103.gfast.v3.internal.app.system.model.ToolsGenTableEditData|github.com.tiger1103.gfast.v3.internal.app.system.model.ToolsGenTableEditData|
|&emsp;&emsp;tableId|编号|integer(int64)||
|&emsp;&emsp;tableName|表名称|string(string)||
|&emsp;&emsp;tableComment|表描述|string(string)||
|&emsp;&emsp;className|实体类名称|string(string)||
|&emsp;&emsp;tplCategory|使用的模板（crud单表操作 tree树表操作）|string(string)||
|&emsp;&emsp;packageName|生成包路径|string(string)||
|&emsp;&emsp;moduleName|生成模块名|string(string)||
|&emsp;&emsp;businessName|生成业务名|string(string)||
|&emsp;&emsp;functionName|生成功能名|string(string)||
|&emsp;&emsp;functionAuthor|生成功能作者|string(string)||
|&emsp;&emsp;options|其它生成选项|string(string)||
|&emsp;&emsp;createTime|创建时间|string(*gtime.Time)||
|&emsp;&emsp;updateTime|更新时间|string(*gtime.Time)||
|&emsp;&emsp;remark|备注|string(string)||
|&emsp;&emsp;overwrite|是否覆盖原有文件|boolean(bool)||
|&emsp;&emsp;sortColumn|排序字段名|string(string)||
|&emsp;&emsp;sortType|排序方式 (asc顺序 desc倒序)|string(string)||
|&emsp;&emsp;showDetail|是否有查看详情功能|boolean(bool)||
|&emsp;&emsp;excelPort|是否有excel导出功能|boolean(bool)||
|&emsp;&emsp;excelImp|是否有excel导入功能|boolean(bool)||
|&emsp;&emsp;useSnowId|主键是否使用雪花ID|boolean(bool)||
|&emsp;&emsp;useVirtual|树表是否使用虚拟表|boolean(bool)||
|&emsp;&emsp;overwriteInfo|覆盖文件信息|array([]*entity.OverwriteInfo)|github.com.tiger1103.gfast.v3.internal.app.system.model.entity.OverwriteInfo|
|&emsp;&emsp;&emsp;&emsp;key||string||
|&emsp;&emsp;&emsp;&emsp;value||boolean||
|&emsp;&emsp;menuPid|父级菜单ID|integer(uint)||
|&emsp;&emsp;treeCode||interface|interface|
|&emsp;&emsp;treeParentCode||interface|interface|
|&emsp;&emsp;treeName||interface|interface|
**响应示例**:
```javascript
{
	"list": [
		{
			"columnId": 0,
			"tableId": 0,
			"columnName": "",
			"columnComment": "",
			"columnType": "",
			"goType": "",
			"tsType": "",
			"goField": "",
			"htmlField": "",
			"isPk": true,
			"isIncrement": true,
			"isRequired": true,
			"isEdit": true,
			"isList": true,
			"isDetail": true,
			"isQuery": true,
			"sortOrderEdit": 0,
			"sortOrderList": 0,
			"sortOrderDetail": 0,
			"sortOrderQuery": 0,
			"queryType": "",
			"htmlType": "",
			"dictType": "",
			"linkTableName": "",
			"linkTableClass": "",
			"linkTableModuleName": "",
			"linkTableBusinessName": "",
			"linkTablePackage": "",
			"linkLabelId": "",
			"linkLabelName": "",
			"colSpan": 0,
			"rowSpan": 0,
			"isRowStart": true,
			"minWidth": 0,
			"isFixed": true,
			"isOverflowTooltip": true,
			"isCascade": true,
			"parentColumnName": "",
			"cascadeColumnName": ""
		}
	],
	"info": {
		"tableId": 0,
		"tableName": "",
		"tableComment": "",
		"className": "",
		"tplCategory": "",
		"packageName": "",
		"moduleName": "",
		"businessName": "",
		"functionName": "",
		"functionAuthor": "",
		"options": "",
		"createTime": "",
		"updateTime": "",
		"remark": "",
		"overwrite": true,
		"sortColumn": "",
		"sortType": "",
		"showDetail": true,
		"excelPort": true,
		"excelImp": true,
		"useSnowId": true,
		"useVirtual": true,
		"overwriteInfo": [
			{
				"key": "",
				"value": true
			}
		],
		"menuPid": 0,
		"treeCode": {},
		"treeParentCode": {},
		"treeName": {}
	}
}
```
## 获取需要导入的数据表
**接口地址**:`/api/v1/system/tools/gen/dataList`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|tableName||query|false|string(string)||
|tableComment||query|false|string(string)||
|dateRange||query|false|array|string|
|pageNum||query|false|integer(int)||
|pageSize||query|false|integer(int)||
|orderBy||query|false|string(string)||
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.ToolsGenTableSearchRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|currentPage||integer(int)|integer(int)|
|total||interface|interface|
|list||github.com.tiger1103.gfast.v3.internal.app.system.model.entity.ToolsGenTable|github.com.tiger1103.gfast.v3.internal.app.system.model.entity.ToolsGenTable|
|&emsp;&emsp;tableId|编号|integer(int64)||
|&emsp;&emsp;tableName|表名称|string(string)||
|&emsp;&emsp;tableComment|表描述|string(string)||
|&emsp;&emsp;className|实体类名称|string(string)||
|&emsp;&emsp;tplCategory|使用的模板（crud单表操作 tree树表操作）|string(string)||
|&emsp;&emsp;packageName|生成包路径|string(string)||
|&emsp;&emsp;moduleName|生成模块名|string(string)||
|&emsp;&emsp;businessName|生成业务名|string(string)||
|&emsp;&emsp;functionName|生成功能名|string(string)||
|&emsp;&emsp;functionAuthor|生成功能作者|string(string)||
|&emsp;&emsp;options|其它生成选项|string(string)||
|&emsp;&emsp;createTime|创建时间|string(*gtime.Time)||
|&emsp;&emsp;updateTime|更新时间|string(*gtime.Time)||
|&emsp;&emsp;remark|备注|string(string)||
|&emsp;&emsp;overwrite|是否覆盖原有文件|boolean(bool)||
|&emsp;&emsp;sortColumn|排序字段名|string(string)||
|&emsp;&emsp;sortType|排序方式 (asc顺序 desc倒序)|string(string)||
|&emsp;&emsp;showDetail|是否有查看详情功能|boolean(bool)||
|&emsp;&emsp;excelPort|是否有excel导出功能|boolean(bool)||
|&emsp;&emsp;excelImp|是否有excel导入功能|boolean(bool)||
|&emsp;&emsp;useSnowId|主键是否使用雪花ID|boolean(bool)||
|&emsp;&emsp;useVirtual|树表是否使用虚拟表|boolean(bool)||
|&emsp;&emsp;overwriteInfo|覆盖文件信息|array([]*entity.OverwriteInfo)|github.com.tiger1103.gfast.v3.internal.app.system.model.entity.OverwriteInfo|
|&emsp;&emsp;&emsp;&emsp;key||string||
|&emsp;&emsp;&emsp;&emsp;value||boolean||
|&emsp;&emsp;menuPid|父级菜单ID|integer(uint)||
**响应示例**:
```javascript
{
	"currentPage": 0,
	"total": {},
	"list": [
		{
			"tableId": 0,
			"tableName": "",
			"tableComment": "",
			"className": "",
			"tplCategory": "",
			"packageName": "",
			"moduleName": "",
			"businessName": "",
			"functionName": "",
			"functionAuthor": "",
			"options": "",
			"createTime": "",
			"updateTime": "",
			"remark": "",
			"overwrite": true,
			"sortColumn": "",
			"sortType": "",
			"showDetail": true,
			"excelPort": true,
			"excelImp": true,
			"useSnowId": true,
			"useVirtual": true,
			"overwriteInfo": [
				{
					"key": "",
					"value": true
				}
			],
			"menuPid": 0
		}
	]
}
```
## 生成信息修改保存
**接口地址**:`/api/v1/system/tools/gen/editSave`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|packageName||query|true|string||
|sort_column||query|true|string||
|businessName||query|true|string||
|tableComment||query|true|string||
|className||query|true|string||
|overwrite||query|true|string||
|functionName||query|true|string||
|sort_type||query|true|string||
|show_detail||query|true|string||
|tableId||query|true|integer||
|tableName||query|true|string||
|functionAuthor||query|true|string||
|Authorization|Bearer {{token}}|header|false|string(string)||
|tree_code||query|false|string||
|excelPort||query|false|string||
|tplCategory||query|false|string||
|params||query|false|string||
|tree_parent_code||query|false|string||
|tree_name||query|false|string||
|useVirtual||query|false|string||
|overwriteInfo||query|false|array|github.com.tiger1103.gfast.v3.internal.app.system.model.entity.OverwriteInfo|
|&emsp;&emsp;key|||false|string(string)||
|&emsp;&emsp;value|||false|boolean(bool)||
|menuPid||query|false|integer||
|moduleName||query|false|string||
|excelImp||query|false|string||
|useSnowId||query|false|string||
|remark||query|false|string||
|columns||query|false|array|github.com.tiger1103.gfast.v3.internal.app.system.model.entity.ToolsGenTableColumn|
|&emsp;&emsp;columnId|编号||false|integer(int64)||
|&emsp;&emsp;tableId|归属表编号||false|integer(int64)||
|&emsp;&emsp;columnName|列名称||false|string(string)||
|&emsp;&emsp;columnComment|列描述||false|string(string)||
|&emsp;&emsp;columnType|列类型||false|string(string)||
|&emsp;&emsp;goType|Go类型||false|string(string)||
|&emsp;&emsp;tsType|TS类型||false|string(string)||
|&emsp;&emsp;goField|Go字段名||false|string(string)||
|&emsp;&emsp;htmlField|html字段名||false|string(string)||
|&emsp;&emsp;isPk|是否主键（1是）||false|boolean(bool)||
|&emsp;&emsp;isIncrement|是否自增（1是）||false|boolean(bool)||
|&emsp;&emsp;isRequired|是否必填（1是）||false|boolean(bool)||
|&emsp;&emsp;isEdit|是否编辑字段（1是）||false|boolean(bool)||
|&emsp;&emsp;isList|是否列表字段（1是）||false|boolean(bool)||
|&emsp;&emsp;isDetail|是否详情字段||false|boolean(bool)||
|&emsp;&emsp;isQuery|是否查询字段（1是）||false|boolean(bool)||
|&emsp;&emsp;sortOrderEdit|插入编辑显示顺序||false|integer(int)||
|&emsp;&emsp;sortOrderList|列表显示顺序||false|integer(int)||
|&emsp;&emsp;sortOrderDetail|详情显示顺序||false|integer(int)||
|&emsp;&emsp;sortOrderQuery|查询显示顺序||false|integer(int)||
|&emsp;&emsp;queryType|查询方式（等于、不等于、大于、小于、范围）||false|string(string)||
|&emsp;&emsp;htmlType|显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）||false|string(string)||
|&emsp;&emsp;dictType|字典类型||false|string(string)||
|&emsp;&emsp;linkTableName|关联表名||false|string(string)||
|&emsp;&emsp;linkTableClass|关联表类名||false|string(string)||
|&emsp;&emsp;linkTableModuleName|关联表模块名||false|string(string)||
|&emsp;&emsp;linkTableBusinessName|关联表业务名||false|string(string)||
|&emsp;&emsp;linkTablePackage|关联表包名||false|string(string)||
|&emsp;&emsp;linkLabelId|关联表键名||false|string(string)||
|&emsp;&emsp;linkLabelName|关联表字段值||false|string(string)||
|&emsp;&emsp;colSpan|详情页占列数||false|integer(int)||
|&emsp;&emsp;rowSpan|详情页占行数||false|integer(int)||
|&emsp;&emsp;isRowStart|详情页为行首||false|boolean(bool)||
|&emsp;&emsp;minWidth|表格最小宽度||false|integer(int)||
|&emsp;&emsp;isFixed|是否表格列左固定||false|boolean(bool)||
|&emsp;&emsp;isOverflowTooltip|是否过长自动隐藏||false|boolean(bool)||
|&emsp;&emsp;isCascade|是否级联查询||false|boolean(bool)||
|&emsp;&emsp;parentColumnName|上级字段名||false|string(string)||
|&emsp;&emsp;cascadeColumnName|级联查询字段||false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.ToolsGenTableColumnsEditRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 导入表结构操作
**接口地址**:`/api/v1/system/tools/gen/importTableSave`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|tables||query|true|array|string|
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.common.EmptyRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 代码预览
**接口地址**:`/api/v1/system/tools/gen/preview`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|tableId||query|true|integer(int64)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.ToolsGenTablePreviewRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|data||object(map[string]string)|object(map[string]string)|
**响应示例**:
```javascript
{
	"data": {}
}
```
## 获取关联表数据
**接口地址**:`/api/v1/system/tools/gen/relationTable`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|tableName||query|false|string(string)||
|tableComment||query|false|string(string)||
|dateRange||query|false|array|string|
|pageNum||query|false|integer(int)||
|pageSize||query|false|integer(int)||
|orderBy||query|false|string(string)||
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.ToolsGenRelationTableRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|currentPage||integer(int)|integer(int)|
|total||interface|interface|
|data||github.com.tiger1103.gfast.v3.internal.app.system.model.ToolsGenTableColumnsData|github.com.tiger1103.gfast.v3.internal.app.system.model.ToolsGenTableColumnsData|
|&emsp;&emsp;tableId|编号|integer(int64)||
|&emsp;&emsp;tableName|表名称|string(string)||
|&emsp;&emsp;tableComment|表描述|string(string)||
|&emsp;&emsp;className|实体类名称|string(string)||
|&emsp;&emsp;tplCategory|使用的模板（crud单表操作 tree树表操作）|string(string)||
|&emsp;&emsp;packageName|生成包路径|string(string)||
|&emsp;&emsp;moduleName|生成模块名|string(string)||
|&emsp;&emsp;businessName|生成业务名|string(string)||
|&emsp;&emsp;functionName|生成功能名|string(string)||
|&emsp;&emsp;functionAuthor|生成功能作者|string(string)||
|&emsp;&emsp;options|其它生成选项|string(string)||
|&emsp;&emsp;createTime|创建时间|string(*gtime.Time)||
|&emsp;&emsp;updateTime|更新时间|string(*gtime.Time)||
|&emsp;&emsp;remark|备注|string(string)||
|&emsp;&emsp;overwrite|是否覆盖原有文件|boolean(bool)||
|&emsp;&emsp;sortColumn|排序字段名|string(string)||
|&emsp;&emsp;sortType|排序方式 (asc顺序 desc倒序)|string(string)||
|&emsp;&emsp;showDetail|是否有查看详情功能|boolean(bool)||
|&emsp;&emsp;excelPort|是否有excel导出功能|boolean(bool)||
|&emsp;&emsp;excelImp|是否有excel导入功能|boolean(bool)||
|&emsp;&emsp;useSnowId|主键是否使用雪花ID|boolean(bool)||
|&emsp;&emsp;useVirtual|树表是否使用虚拟表|boolean(bool)||
|&emsp;&emsp;overwriteInfo|覆盖文件信息|array([]*entity.OverwriteInfo)|github.com.tiger1103.gfast.v3.internal.app.system.model.entity.OverwriteInfo|
|&emsp;&emsp;&emsp;&emsp;key||string||
|&emsp;&emsp;&emsp;&emsp;value||boolean||
|&emsp;&emsp;menuPid|父级菜单ID|integer(uint)||
|&emsp;&emsp;columns||array([]*entity.ToolsGenTableColumn)|github.com.tiger1103.gfast.v3.internal.app.system.model.entity.ToolsGenTableColumn|
|&emsp;&emsp;&emsp;&emsp;columnId|编号|integer||
|&emsp;&emsp;&emsp;&emsp;tableId|归属表编号|integer||
|&emsp;&emsp;&emsp;&emsp;columnName|列名称|string||
|&emsp;&emsp;&emsp;&emsp;columnComment|列描述|string||
|&emsp;&emsp;&emsp;&emsp;columnType|列类型|string||
|&emsp;&emsp;&emsp;&emsp;goType|Go类型|string||
|&emsp;&emsp;&emsp;&emsp;tsType|TS类型|string||
|&emsp;&emsp;&emsp;&emsp;goField|Go字段名|string||
|&emsp;&emsp;&emsp;&emsp;htmlField|html字段名|string||
|&emsp;&emsp;&emsp;&emsp;isPk|是否主键（1是）|boolean||
|&emsp;&emsp;&emsp;&emsp;isIncrement|是否自增（1是）|boolean||
|&emsp;&emsp;&emsp;&emsp;isRequired|是否必填（1是）|boolean||
|&emsp;&emsp;&emsp;&emsp;isEdit|是否编辑字段（1是）|boolean||
|&emsp;&emsp;&emsp;&emsp;isList|是否列表字段（1是）|boolean||
|&emsp;&emsp;&emsp;&emsp;isDetail|是否详情字段|boolean||
|&emsp;&emsp;&emsp;&emsp;isQuery|是否查询字段（1是）|boolean||
|&emsp;&emsp;&emsp;&emsp;sortOrderEdit|插入编辑显示顺序|integer||
|&emsp;&emsp;&emsp;&emsp;sortOrderList|列表显示顺序|integer||
|&emsp;&emsp;&emsp;&emsp;sortOrderDetail|详情显示顺序|integer||
|&emsp;&emsp;&emsp;&emsp;sortOrderQuery|查询显示顺序|integer||
|&emsp;&emsp;&emsp;&emsp;queryType|查询方式（等于、不等于、大于、小于、范围）|string||
|&emsp;&emsp;&emsp;&emsp;htmlType|显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）|string||
|&emsp;&emsp;&emsp;&emsp;dictType|字典类型|string||
|&emsp;&emsp;&emsp;&emsp;linkTableName|关联表名|string||
|&emsp;&emsp;&emsp;&emsp;linkTableClass|关联表类名|string||
|&emsp;&emsp;&emsp;&emsp;linkTableModuleName|关联表模块名|string||
|&emsp;&emsp;&emsp;&emsp;linkTableBusinessName|关联表业务名|string||
|&emsp;&emsp;&emsp;&emsp;linkTablePackage|关联表包名|string||
|&emsp;&emsp;&emsp;&emsp;linkLabelId|关联表键名|string||
|&emsp;&emsp;&emsp;&emsp;linkLabelName|关联表字段值|string||
|&emsp;&emsp;&emsp;&emsp;colSpan|详情页占列数|integer||
|&emsp;&emsp;&emsp;&emsp;rowSpan|详情页占行数|integer||
|&emsp;&emsp;&emsp;&emsp;isRowStart|详情页为行首|boolean||
|&emsp;&emsp;&emsp;&emsp;minWidth|表格最小宽度|integer||
|&emsp;&emsp;&emsp;&emsp;isFixed|是否表格列左固定|boolean||
|&emsp;&emsp;&emsp;&emsp;isOverflowTooltip|是否过长自动隐藏|boolean||
|&emsp;&emsp;&emsp;&emsp;isCascade|是否级联查询|boolean||
|&emsp;&emsp;&emsp;&emsp;parentColumnName|上级字段名|string||
|&emsp;&emsp;&emsp;&emsp;cascadeColumnName|级联查询字段|string||
**响应示例**:
```javascript
{
	"currentPage": 0,
	"total": {},
	"data": [
		{
			"tableId": 0,
			"tableName": "",
			"tableComment": "",
			"className": "",
			"tplCategory": "",
			"packageName": "",
			"moduleName": "",
			"businessName": "",
			"functionName": "",
			"functionAuthor": "",
			"options": "",
			"createTime": "",
			"updateTime": "",
			"remark": "",
			"overwrite": true,
			"sortColumn": "",
			"sortType": "",
			"showDetail": true,
			"excelPort": true,
			"excelImp": true,
			"useSnowId": true,
			"useVirtual": true,
			"overwriteInfo": [
				{
					"key": "",
					"value": true
				}
			],
			"menuPid": 0,
			"columns": [
				{
					"columnId": 0,
					"tableId": 0,
					"columnName": "",
					"columnComment": "",
					"columnType": "",
					"goType": "",
					"tsType": "",
					"goField": "",
					"htmlField": "",
					"isPk": true,
					"isIncrement": true,
					"isRequired": true,
					"isEdit": true,
					"isList": true,
					"isDetail": true,
					"isQuery": true,
					"sortOrderEdit": 0,
					"sortOrderList": 0,
					"sortOrderDetail": 0,
					"sortOrderQuery": 0,
					"queryType": "",
					"htmlType": "",
					"dictType": "",
					"linkTableName": "",
					"linkTableClass": "",
					"linkTableModuleName": "",
					"linkTableBusinessName": "",
					"linkTablePackage": "",
					"linkLabelId": "",
					"linkLabelName": "",
					"colSpan": 0,
					"rowSpan": 0,
					"isRowStart": true,
					"minWidth": 0,
					"isFixed": true,
					"isOverflowTooltip": true,
					"isCascade": true,
					"parentColumnName": "",
					"cascadeColumnName": ""
				}
			]
		}
	]
}
```
## 同步表结构
**接口地址**:`/api/v1/system/tools/gen/syncTable`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|tableId||query|true|integer||
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.ToolsGenTableSyncTableRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 删除已导入的表
**接口地址**:`/api/v1/system/tools/gen/tableDelete`
**请求方式**:`DELETE`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|ids||query|true|array|integer|
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.common.EmptyRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## 获取数据表
**接口地址**:`/api/v1/system/tools/gen/tableList`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|tableName||query|false|string(string)||
|tableComment||query|false|string(string)||
|dateRange||query|false|array|string|
|pageNum||query|false|integer(int)||
|pageSize||query|false|integer(int)||
|orderBy||query|false|string(string)||
|Authorization|Bearer {{token}}|header|false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.ToolsGenTableSearchRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|currentPage||integer(int)|integer(int)|
|total||interface|interface|
|list||github.com.tiger1103.gfast.v3.internal.app.system.model.entity.ToolsGenTable|github.com.tiger1103.gfast.v3.internal.app.system.model.entity.ToolsGenTable|
|&emsp;&emsp;tableId|编号|integer(int64)||
|&emsp;&emsp;tableName|表名称|string(string)||
|&emsp;&emsp;tableComment|表描述|string(string)||
|&emsp;&emsp;className|实体类名称|string(string)||
|&emsp;&emsp;tplCategory|使用的模板（crud单表操作 tree树表操作）|string(string)||
|&emsp;&emsp;packageName|生成包路径|string(string)||
|&emsp;&emsp;moduleName|生成模块名|string(string)||
|&emsp;&emsp;businessName|生成业务名|string(string)||
|&emsp;&emsp;functionName|生成功能名|string(string)||
|&emsp;&emsp;functionAuthor|生成功能作者|string(string)||
|&emsp;&emsp;options|其它生成选项|string(string)||
|&emsp;&emsp;createTime|创建时间|string(*gtime.Time)||
|&emsp;&emsp;updateTime|更新时间|string(*gtime.Time)||
|&emsp;&emsp;remark|备注|string(string)||
|&emsp;&emsp;overwrite|是否覆盖原有文件|boolean(bool)||
|&emsp;&emsp;sortColumn|排序字段名|string(string)||
|&emsp;&emsp;sortType|排序方式 (asc顺序 desc倒序)|string(string)||
|&emsp;&emsp;showDetail|是否有查看详情功能|boolean(bool)||
|&emsp;&emsp;excelPort|是否有excel导出功能|boolean(bool)||
|&emsp;&emsp;excelImp|是否有excel导入功能|boolean(bool)||
|&emsp;&emsp;useSnowId|主键是否使用雪花ID|boolean(bool)||
|&emsp;&emsp;useVirtual|树表是否使用虚拟表|boolean(bool)||
|&emsp;&emsp;overwriteInfo|覆盖文件信息|array([]*entity.OverwriteInfo)|github.com.tiger1103.gfast.v3.internal.app.system.model.entity.OverwriteInfo|
|&emsp;&emsp;&emsp;&emsp;key||string||
|&emsp;&emsp;&emsp;&emsp;value||boolean||
|&emsp;&emsp;menuPid|父级菜单ID|integer(uint)||
**响应示例**:
```javascript
{
	"currentPage": 0,
	"total": {},
	"list": [
		{
			"tableId": 0,
			"tableName": "",
			"tableComment": "",
			"className": "",
			"tplCategory": "",
			"packageName": "",
			"moduleName": "",
			"businessName": "",
			"functionName": "",
			"functionAuthor": "",
			"options": "",
			"createTime": "",
			"updateTime": "",
			"remark": "",
			"overwrite": true,
			"sortColumn": "",
			"sortType": "",
			"showDetail": true,
			"excelPort": true,
			"excelImp": true,
			"useSnowId": true,
			"useVirtual": true,
			"overwriteInfo": [
				{
					"key": "",
					"value": true
				}
			],
			"menuPid": 0
		}
	]
}
```
# 系统后台-UEditor
## 获取UEditor配置
**接口地址**:`/api/v1/system/uEditor/action`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|action||query|false|string(string)||
|callback||query|false|string(string)||
|upfile||query|false|file||
|start||query|false|integer(int)||
|size||query|false|integer(int)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.UEditorRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
## UEditor上传
**接口地址**:`/api/v1/system/uEditor/action`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "action": "",
  "callback": "",
  "upfile": "",
  "start": 0,
  "size": 0
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.system.UEditorUpFileReq|github.com.tiger1103.gfast.v3.api.v1.system.UEditorUpFileReq|body|true|github.com.tiger1103.gfast.v3.api.v1.system.UEditorUpFileReq|github.com.tiger1103.gfast.v3.api.v1.system.UEditorUpFileReq|
|&emsp;&emsp;action|||false|string(string)||
|&emsp;&emsp;callback|||false|string(string)||
|&emsp;&emsp;upfile|||false|file(*ghttp.UploadFile)||
|&emsp;&emsp;start|||false|integer(int)||
|&emsp;&emsp;size|||false|integer(int)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.UEditorRes|
**响应参数**:
暂无
**响应示例**:
```javascript
{}
```
# 系统后台-后台文件上传
## 检查分片
**接口地址**:`/api/v1/system/upload/checkMultipart`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|Authorization|Bearer {{token}}|header|false|string(string)||
|fileName|文件名称|query|false|string||
|size|文件大小|query|false|integer||
|md5|文件md5值|query|false|string||
|shardsCount|分片数量|query|false|integer||
|AppId||query|false|string||
|uploadType|文件类型|query|false|string||
|DriverType||query|false|integer||
|UserId||query|false|integer||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.CheckMultipartRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|attachment|附件|github.com.tiger1103.gfast.v3.internal.app.common.model.UploadResponse|github.com.tiger1103.gfast.v3.internal.app.common.model.UploadResponse|
|&emsp;&emsp;size|文件大小|integer(int64)||
|&emsp;&emsp;path|文件相对路径|string(string)||
|&emsp;&emsp;fullPath|文件绝对路径|string(string)||
|&emsp;&emsp;name|文件名称|string(string)||
|&emsp;&emsp;type|文件类型|string(string)||
|waitUploadIndex|等待上传的分片索引|array([]int)||
|progress|上传进度|number(float64)|number(float64)|
**响应示例**:
```javascript
{
	"attachment": {
		"size": 0,
		"path": "",
		"fullPath": "",
		"name": "",
		"type": ""
	},
	"waitUploadIndex": [],
	"progress": 0
}
```
## 上传多文件
**接口地址**:`/api/v1/system/upload/multipleFile`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "file": []
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.system.UploadMultipleFileReq|github.com.tiger1103.gfast.v3.api.v1.system.UploadMultipleFileReq|body|true|github.com.tiger1103.gfast.v3.api.v1.system.UploadMultipleFileReq|github.com.tiger1103.gfast.v3.api.v1.system.UploadMultipleFileReq|
|&emsp;&emsp;file|选择上传文件||true|array(ghttp.UploadFiles)|file|
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|array|array|
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": ""
}
```
## 上传多图片
**接口地址**:`/api/v1/system/upload/multipleImg`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "file": []
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.system.UploadMultipleImgReq|github.com.tiger1103.gfast.v3.api.v1.system.UploadMultipleImgReq|body|true|github.com.tiger1103.gfast.v3.api.v1.system.UploadMultipleImgReq|github.com.tiger1103.gfast.v3.api.v1.system.UploadMultipleImgReq|
|&emsp;&emsp;file|选择上传文件||true|array(ghttp.UploadFiles)|file|
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|array|array|
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": ""
}
```
## 上传文件
**接口地址**:`/api/v1/system/upload/singleFile`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "file": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.system.UploadSingleFileReq|github.com.tiger1103.gfast.v3.api.v1.system.UploadSingleFileReq|body|true|github.com.tiger1103.gfast.v3.api.v1.system.UploadSingleFileReq|github.com.tiger1103.gfast.v3.api.v1.system.UploadSingleFileReq|
|&emsp;&emsp;file|选择上传文件||true|file(*ghttp.UploadFile)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.UploadSingleRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|size|文件大小|integer(int64)|integer(int64)|
|path|文件相对路径|string(string)|string(string)|
|fullPath|文件绝对路径|string(string)|string(string)|
|name|文件名称|string(string)|string(string)|
|type|文件类型|string(string)|string(string)|
**响应示例**:
```javascript
{
	"size": 0,
	"path": "",
	"fullPath": "",
	"name": "",
	"type": ""
}
```
## 上传图片
**接口地址**:`/api/v1/system/upload/singleImg`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "file": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.system.UploadSingleImgReq|github.com.tiger1103.gfast.v3.api.v1.system.UploadSingleImgReq|body|true|github.com.tiger1103.gfast.v3.api.v1.system.UploadSingleImgReq|github.com.tiger1103.gfast.v3.api.v1.system.UploadSingleImgReq|
|&emsp;&emsp;file|选择上传文件||true|file(*ghttp.UploadFile)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.UploadSingleRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|size|文件大小|integer(int64)|integer(int64)|
|path|文件相对路径|string(string)|string(string)|
|fullPath|文件绝对路径|string(string)|string(string)|
|name|文件名称|string(string)|string(string)|
|type|文件类型|string(string)|string(string)|
**响应示例**:
```javascript
{
	"size": 0,
	"path": "",
	"fullPath": "",
	"name": "",
	"type": ""
}
```
## 分片上传
**接口地址**:`/api/v1/system/upload/uploadPart`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|Authorization|Bearer {{token}}|header|false|string(string)||
|fileName|文件名称|query|false|string||
|size|文件大小|query|false|integer||
|AppId||query|false|string||
|uploadType|文件类型|query|false|string||
|md5|文件md5值|query|false|string||
|shardsCount|分片数量|query|false|integer||
|DriverType||query|false|integer||
|UserId||query|false|integer||
|index|分片索引|query|false|integer||
|file|分片文件|query|false|file||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.system.UploadPartRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|attachment|附件|github.com.tiger1103.gfast.v3.internal.app.common.model.UploadResponse|github.com.tiger1103.gfast.v3.internal.app.common.model.UploadResponse|
|&emsp;&emsp;size|文件大小|integer(int64)||
|&emsp;&emsp;path|文件相对路径|string(string)||
|&emsp;&emsp;fullPath|文件绝对路径|string(string)||
|&emsp;&emsp;name|文件名称|string(string)||
|&emsp;&emsp;type|文件类型|string(string)||
|finish|是否完成|boolean(bool)|boolean(bool)|
**响应示例**:
```javascript
{
	"attachment": {
		"size": 0,
		"path": "",
		"fullPath": "",
		"name": "",
		"type": ""
	},
	"finish": true
}
```
# 标签管理
## 添加标签
**接口地址**:`/api/v1/tag/tag/add`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "name": "",
  "slug": "",
  "type": "",
  "color": "",
  "description": "",
  "is_hot": 0,
  "status": 1
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.TagAddReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.TagAddReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.TagAddReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.TagAddReq|
|&emsp;&emsp;name|标签名称||true|string(string)||
|&emsp;&emsp;slug|标签标识（英文）||true|string(string)||
|&emsp;&emsp;type|标签类型,可用值:story,factory,people,general||true|string(string)||
|&emsp;&emsp;color|标签颜色||false|string(string)||
|&emsp;&emsp;description|标签描述||false|string(string)||
|&emsp;&emsp;is_hot|是否热门,可用值:0,1||false|integer(int)||
|&emsp;&emsp;status|状态,可用值:0,1||true|integer(int)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 删除标签
**接口地址**:`/api/v1/tag/tag/delete`
**请求方式**:`DELETE`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|ids|标签ID数组|query|true|array|integer|
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 编辑标签
**接口地址**:`/api/v1/tag/tag/edit`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "id": 0,
  "name": "",
  "slug": "",
  "type": "",
  "color": "",
  "description": "",
  "is_hot": 0,
  "status": 1
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.TagEditReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.TagEditReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.TagEditReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.TagEditReq|
|&emsp;&emsp;id|标签ID||true|integer(uint64)||
|&emsp;&emsp;name|标签名称||true|string(string)||
|&emsp;&emsp;slug|标签标识（英文）||true|string(string)||
|&emsp;&emsp;type|标签类型,可用值:story,factory,people,general||true|string(string)||
|&emsp;&emsp;color|标签颜色||false|string(string)||
|&emsp;&emsp;description|标签描述||false|string(string)||
|&emsp;&emsp;is_hot|是否热门,可用值:0,1||false|integer(int)||
|&emsp;&emsp;status|状态,可用值:0,1||true|integer(int)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 获取标签详情
**接口地址**:`/api/v1/tag/tag/get`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|id|标签ID|query|true|integer(uint64)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 标签列表
**接口地址**:`/api/v1/tag/tag/list`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|name|标签名称|query|false|string(string)||
|type|标签类型（story/factory/people/general）|query|false|string(string)||
|is_hot|是否热门（0否 1是）|query|false|integer(*int)||
|status|状态（0禁用 1启用）|query|false|integer(*int)||
|page_num|页码|query|false|integer(int)||
|page_size|每页数量|query|false|integer(int)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
## 更新标签状态
**接口地址**:`/api/v1/tag/tag/status`
**请求方式**:`PUT`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "ids": [],
  "status": 0
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.sanxianren.TagStatusReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.TagStatusReq|body|true|github.com.tiger1103.gfast.v3.api.v1.sanxianren.TagStatusReq|github.com.tiger1103.gfast.v3.api.v1.sanxianren.TagStatusReq|
|&emsp;&emsp;ids|标签ID数组||true|array([]uint64)|integer|
|&emsp;&emsp;status|状态（0禁用 1启用）,可用值:0,1||true|integer(int)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||object|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|Error code|integer(int)|integer(int)|
|message|Error message|string(string)|string(string)|
|data|Result data for certain request according API definition|object||
**响应示例**:
```javascript
{
	"code": 0,
	"message": "",
	"data": {}
}
```
# 微信接口-小程序登陆
## 微信登录
**接口地址**:`/api/v1/wechat/wxLogin`
**请求方式**:`POST`
**请求数据类型**:`application/x-www-form-urlencoded,application/json`
**响应数据类型**:`application/json`
**接口描述**:
**请求示例**:
```javascript
{
  "code": "",
  "phoneCode": ""
}
```
**请求参数**:
| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|github.com.tiger1103.gfast.v3.api.v1.wechat.WxLoginReq|github.com.tiger1103.gfast.v3.api.v1.wechat.WxLoginReq|body|true|github.com.tiger1103.gfast.v3.api.v1.wechat.WxLoginReq|github.com.tiger1103.gfast.v3.api.v1.wechat.WxLoginReq|
|&emsp;&emsp;code|||true|string(string)||
|&emsp;&emsp;phoneCode|||false|string(string)||
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.wechat.WxLoginRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|userInfo||github.com.tiger1103.gfast.v3.internal.app.system.model.LoginUserRes|github.com.tiger1103.gfast.v3.internal.app.system.model.LoginUserRes|
|&emsp;&emsp;id||integer(uint64)||
|&emsp;&emsp;userName||string(string)||
|&emsp;&emsp;mobile||string(string)||
|&emsp;&emsp;userNickname||string(string)||
|&emsp;&emsp;userStatus||integer(uint)||
|&emsp;&emsp;isAdmin||integer(int)||
|&emsp;&emsp;avatar||string(string)||
|&emsp;&emsp;deptId||integer(uint64)||
|token||string(string)|string(string)|
**响应示例**:
```javascript
{
	"userInfo": {
		"id": 0,
		"userName": "",
		"mobile": "",
		"userNickname": "",
		"userStatus": 0,
		"isAdmin": 0,
		"avatar": "",
		"deptId": 0
	},
	"token": ""
}
```
## 获取微信小程序url
**接口地址**:`/api/v1/wechat/wxUrl`
**请求方式**:`GET`
**请求数据类型**:`application/x-www-form-urlencoded`
**响应数据类型**:`application/json`
**接口描述**:
**请求参数**:
暂无
**响应状态**:
| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200||github.com.tiger1103.gfast.v3.api.v1.wechat.WxUrlRes|
**响应参数**:
| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|url||string(string)|string(string)|
**响应示例**:
```javascript
{
	"url": ""
}
```