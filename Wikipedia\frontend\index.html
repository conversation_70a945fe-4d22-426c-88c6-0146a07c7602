<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>天南地北三线人 - 三线建设历史百科</title>
    <meta name="description" content="记录三线建设历史，传承三线精神。探索三线人故事、三线厂历史、口述历史和历史遗址。">
    <meta name="keywords" content="三线建设,三线人,三线厂,口述历史,历史遗址,故事馆">
    
    <!-- Favicon -->
    <link rel="icon" href="../favicon.ico" type="image/x-icon">
    
    <!-- CSS -->
    <link rel="stylesheet" href="./assets/css/main.css">
    <link rel="stylesheet" href="./assets/css/components.css">
    <link rel="stylesheet" href="./assets/css/responsive.css">
    
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus@2.4.4/dist/index.css">
    
    <!-- Font Icons -->
    <link rel="stylesheet" href="https://unpkg.com/@element-plus/icons-vue@2.3.1/dist/index.css">
    
    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3.4.15/dist/vue.global.js"></script>
    
    <!-- Element Plus -->
    <script src="https://unpkg.com/element-plus@2.4.4/dist/index.full.js"></script>
    
    <!-- Element Plus Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue@2.3.1/dist/index.iife.js"></script>
</head>
<body>
    <div id="app">
        <!-- 顶部导航栏 -->
        <nav class="navbar">
            <div class="navbar-container">
                <!-- Logo和标题 -->
                <div class="navbar-brand" @click="goHome">
                    <div class="logo">
                        <i class="el-icon-reading"></i>
                        <span class="brand-text">天南地北三线人</span>
                    </div>
                    <div class="subtitle">记录三线建设历史，传承三线精神</div>
                </div>

                <!-- 主导航菜单 -->
                <div class="navbar-nav">
                    <div 
                        v-for="nav in navigationItems"
                        :key="nav.key"
                        class="nav-item"
                        :class="{ active: currentNav === nav.key }"
                        @click="handleNavClick(nav)"
                    >
                        <i :class="nav.icon"></i>
                        <span>{{ nav.label }}</span>
                    </div>
                </div>

                <!-- 搜索栏 -->
                <div class="navbar-search">
                    <el-input
                        v-model="searchQuery"
                        placeholder="搜索三线人、三线厂、故事..."
                        class="search-input"
                        size="large"
                        clearable
                        @keyup.enter="handleSearch"
                    >
                        <template #prefix>
                            <i class="el-icon-search"></i>
                        </template>
                        <template #suffix>
                            <el-button 
                                type="primary" 
                                @click="handleSearch"
                                class="search-button"
                            >
                                搜索
                            </el-button>
                        </template>
                    </el-input>
                </div>

                <!-- 移动端菜单按钮 -->
                <el-button 
                    text 
                    @click="toggleMobileMenu"
                    class="mobile-menu-btn"
                >
                    <i class="el-icon-menu"></i>
                </el-button>
            </div>
        </nav>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 搜索结果提示 -->
            <div v-if="searchQuery" class="search-results-header">
                <h2>搜索结果: "{{ searchQuery }}"</h2>
                <p>找到 {{ totalCount }} 个相关词条</p>
            </div>

            <!-- 分类筛选器 -->
            <div class="category-filters">
                <div class="filter-tags">
                    <el-tag
                        v-for="category in categories"
                        :key="category.id"
                        :type="selectedCategory === category.id ? 'primary' : ''"
                        :effect="selectedCategory === category.id ? 'dark' : 'plain'"
                        class="category-tag"
                        @click="selectCategory(category.id)"
                    >
                        <i :class="category.icon"></i>
                        {{ category.name }}
                        <span class="count">({{ category.count }})</span>
                    </el-tag>
                </div>
            </div>

            <!-- 瀑布流内容区域 -->
            <div 
                ref="waterfallContainer" 
                class="waterfall-container"
                v-loading="initialLoading"
                element-loading-text="正在加载精彩内容..."
            >
                <!-- 瀑布流列 -->
                <div 
                    v-for="(column, index) in waterfallColumns" 
                    :key="index"
                    class="waterfall-column"
                    :style="{ width: columnWidth }"
                >
                    <div
                        v-for="article in column"
                        :key="article.id"
                        class="article-card"
                        @click="openArticle(article)"
                    >
                        <!-- 特色标识 -->
                        <div v-if="article.featured" class="featured-badge">
                            <i class="el-icon-star-filled"></i>
                            <span>精选</span>
                        </div>

                        <!-- 图片区域 -->
                        <div v-if="article.image" class="card-image">
                            <img :src="article.image" :alt="article.title" loading="lazy">
                            <div class="image-overlay">
                                <div class="overlay-actions">
                                    <el-button 
                                        circle 
                                        size="small"
                                        @click.stop="toggleFavorite(article)"
                                        :type="article.isFavorite ? 'danger' : 'default'"
                                    >
                                        <i :class="article.isFavorite ? 'el-icon-star-filled' : 'el-icon-star-off'"></i>
                                    </el-button>
                                    <el-button 
                                        circle 
                                        size="small"
                                        @click.stop="shareArticle(article)"
                                    >
                                        <i class="el-icon-share"></i>
                                    </el-button>
                                </div>
                            </div>
                        </div>

                        <!-- 内容区域 -->
                        <div class="card-content">
                            <!-- 分类标签 -->
                            <div class="card-category">
                                <el-tag 
                                    :type="getCategoryType(article.category)"
                                    size="small"
                                    effect="light"
                                >
                                    <i :class="getCategoryIcon(article.category)"></i>
                                    {{ getCategoryName(article.category) }}
                                </el-tag>
                            </div>

                            <!-- 标题 -->
                            <h3 class="card-title">{{ article.title }}</h3>

                            <!-- 摘要 -->
                            <p class="card-summary">{{ article.summary }}</p>

                            <!-- 标签 -->
                            <div v-if="article.tags && article.tags.length" class="card-tags">
                                <el-tag
                                    v-for="tag in article.tags.slice(0, 3)"
                                    :key="tag"
                                    size="small"
                                    type="info"
                                    effect="plain"
                                    class="tag-item"
                                >
                                    {{ tag }}
                                </el-tag>
                                <span v-if="article.tags.length > 3" class="more-tags">
                                    +{{ article.tags.length - 3 }}
                                </span>
                            </div>

                            <!-- 统计信息 -->
                            <div class="card-stats">
                                <div class="stat-item">
                                    <i class="el-icon-view"></i>
                                    <span>{{ formatNumber(article.views) }}</span>
                                </div>
                                <div class="stat-item">
                                    <i class="el-icon-edit"></i>
                                    <span>{{ formatNumber(article.edits) }}</span>
                                </div>
                                <div class="stat-item">
                                    <i class="el-icon-star-off"></i>
                                    <span>{{ formatNumber(article.favorites) }}</span>
                                </div>
                            </div>

                            <!-- 作者和时间 -->
                            <div class="card-meta">
                                <div class="author-info">
                                    <el-avatar :size="20" :src="article.author?.avatar">
                                        <i class="el-icon-user"></i>
                                    </el-avatar>
                                    <span class="author-name">{{ article.author?.name || '匿名' }}</span>
                                </div>
                                <div class="time-info">
                                    <i class="el-icon-time"></i>
                                    <span>{{ formatTime(article.updatedAt) }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 加载更多指示器 -->
            <div v-if="loadingMore" class="loading-more">
                <i class="el-icon-loading loading-icon"></i>
                <span>正在加载更多内容...</span>
            </div>

            <!-- 无更多内容提示 -->
            <div v-if="noMoreData && displayArticles.length > 0" class="no-more-data">
                <i class="el-icon-check"></i>
                <span>已加载全部内容</span>
            </div>

            <!-- 空状态 -->
            <div v-if="!initialLoading && displayArticles.length === 0" class="empty-state">
                <i class="el-icon-document-remove empty-icon"></i>
                <h3>暂无内容</h3>
                <p>{{ searchQuery ? '没有找到相关词条，请尝试其他关键词' : '暂时没有可显示的词条' }}</p>
                <el-button type="primary" @click="refreshData">
                    <i class="el-icon-refresh"></i>
                    刷新页面
                </el-button>
            </div>
        </main>

        <!-- 返回顶部按钮 -->
        <el-backtop :right="40" :bottom="40" :visibility-height="300">
            <div class="backtop-button">
                <i class="el-icon-top"></i>
            </div>
        </el-backtop>

        <!-- 移动端抽屉菜单 -->
        <el-drawer
            v-model="mobileMenuVisible"
            direction="rtl"
            size="280px"
            :show-close="false"
            class="mobile-menu-drawer"
        >
            <template #header>
                <div class="mobile-menu-header">
                    <div class="logo">
                        <i class="el-icon-reading"></i>
                        <span>天南地北三线人</span>
                    </div>
                </div>
            </template>

            <div class="mobile-menu-content">
                <!-- 主导航菜单 -->
                <div class="mobile-nav-section">
                    <div class="section-title">主要栏目</div>
                    <div class="mobile-nav-items">
                        <div 
                            v-for="nav in navigationItems"
                            :key="nav.key"
                            class="nav-item"
                            :class="{ active: currentNav === nav.key }"
                            @click="handleNavClick(nav)"
                        >
                            <i :class="nav.icon"></i>
                            <div class="nav-content">
                                <span class="nav-title">{{ nav.label }}</span>
                                <span class="nav-desc">{{ nav.description }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </el-drawer>
    </div>

    <!-- JavaScript -->
    <script src="./assets/js/data.js"></script>
    <script src="./assets/js/utils.js"></script>
    <script src="./assets/js/main.js"></script>
</body>
</html>
