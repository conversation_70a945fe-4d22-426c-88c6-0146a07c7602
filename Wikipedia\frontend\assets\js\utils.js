// 工具函数文件

// 格式化数字
function formatNumber(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
}

// 格式化时间
function formatTime(time) {
    const date = new Date(time);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (minutes < 60) {
        return `${minutes}分钟前`;
    } else if (hours < 24) {
        return `${hours}小时前`;
    } else if (days < 30) {
        return `${days}天前`;
    } else {
        return date.toLocaleDateString('zh-CN');
    }
}

// 获取分类类型
function getCategoryType(category) {
    const typeMap = {
        sanxianren: 'success',
        sanxianchang: 'primary',
        storyhall: 'danger',
        oralhistory: 'info',
        heritage: 'warning',
    };
    return typeMap[category] || 'primary';
}

// 获取分类图标
function getCategoryIcon(category) {
    const iconMap = {
        sanxianren: 'el-icon-user',
        sanxianchang: 'el-icon-office-building',
        storyhall: 'el-icon-reading',
        oralhistory: 'el-icon-microphone',
        heritage: 'el-icon-place',
    };
    return iconMap[category] || 'el-icon-document';
}

// 获取分类名称
function getCategoryName(category) {
    const nameMap = {
        sanxianren: '三线人',
        sanxianchang: '三线厂',
        storyhall: '故事馆',
        oralhistory: '口述历史',
        heritage: '遗址馆',
    };
    return nameMap[category] || '其他';
}

// 获取质量评级文本
function getQualityText(quality) {
    const textMap = {
        stub: '小作品',
        start: '初级',
        c: '良好',
        b: '优秀',
        ga: '优良',
        fa: '特色',
    };
    return textMap[quality] || '一般';
}

// 获取质量评级星数
function getQualityStars(quality) {
    const qualityMap = {
        stub: 1,
        start: 2,
        c: 3,
        b: 4,
        ga: 4,
        fa: 5,
    };
    return qualityMap[quality] || 3;
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// 简单的瀑布流分布算法
function distributeToColumns(items, columnCount) {
    const columns = Array.from({ length: columnCount }, () => []);
    
    items.forEach((item, index) => {
        const columnIndex = index % columnCount;
        columns[columnIndex].push(item);
    });
    
    return columns;
}

// 获取最短列的索引
function getShortestColumnIndex(columns) {
    if (columns.length === 0) return 0;
    
    let shortestIndex = 0;
    let shortestLength = columns[0].length;
    
    for (let i = 1; i < columns.length; i++) {
        if (columns[i].length < shortestLength) {
            shortestLength = columns[i].length;
            shortestIndex = i;
        }
    }
    
    return shortestIndex;
}

// 根据屏幕宽度计算列数
function calculateColumnCount(width) {
    if (width >= 1400) return 5;
    if (width >= 1200) return 4;
    if (width >= 992) return 3;
    if (width >= 768) return 2;
    return 1;
}

// 本地存储工具
const storage = {
    set(key, value) {
        try {
            localStorage.setItem(key, JSON.stringify(value));
        } catch (error) {
            console.error('存储数据失败:', error);
        }
    },
    
    get(key, defaultValue = null) {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : defaultValue;
        } catch (error) {
            console.error('读取数据失败:', error);
            return defaultValue;
        }
    },
    
    remove(key) {
        try {
            localStorage.removeItem(key);
        } catch (error) {
            console.error('删除数据失败:', error);
        }
    },
    
    clear() {
        try {
            localStorage.clear();
        } catch (error) {
            console.error('清空数据失败:', error);
        }
    }
};

// 图片懒加载
function lazyLoadImages() {
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
                observer.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
}

// 复制到剪贴板
async function copyToClipboard(text) {
    try {
        if (navigator.clipboard) {
            await navigator.clipboard.writeText(text);
            return true;
        } else {
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            return true;
        }
    } catch (error) {
        console.error('复制失败:', error);
        return false;
    }
}

// 分享功能
async function shareContent(title, text, url) {
    if (navigator.share) {
        try {
            await navigator.share({ title, text, url });
            return true;
        } catch (error) {
            console.error('分享失败:', error);
            return false;
        }
    } else {
        // 降级到复制链接
        return await copyToClipboard(url);
    }
}

// 滚动到顶部
function scrollToTop(smooth = true) {
    window.scrollTo({
        top: 0,
        behavior: smooth ? 'smooth' : 'auto'
    });
}

// 滚动到元素
function scrollToElement(element, offset = 0) {
    if (typeof element === 'string') {
        element = document.querySelector(element);
    }
    
    if (element) {
        const elementPosition = element.offsetTop - offset;
        window.scrollTo({
            top: elementPosition,
            behavior: 'smooth'
        });
    }
}

// 检测是否到达页面底部
function isAtBottom(threshold = 200) {
    const { scrollTop, scrollHeight, clientHeight } = document.documentElement;
    return scrollTop + clientHeight >= scrollHeight - threshold;
}

// 生成唯一ID
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// 深拷贝
function deepClone(obj) {
    if (obj === null || typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj);
    if (obj instanceof Array) return obj.map(item => deepClone(item));
    if (typeof obj === 'object') {
        const clonedObj = {};
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                clonedObj[key] = deepClone(obj[key]);
            }
        }
        return clonedObj;
    }
}

// 过滤HTML标签
function stripHtml(html) {
    const tmp = document.createElement('div');
    tmp.innerHTML = html;
    return tmp.textContent || tmp.innerText || '';
}

// 截断文本
function truncateText(text, length, suffix = '...') {
    if (text.length <= length) return text;
    return text.substring(0, length) + suffix;
}

// 高亮搜索关键词
function highlightKeywords(text, keywords) {
    if (!keywords || keywords.length === 0) return text;
    
    const regex = new RegExp(`(${keywords.join('|')})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
}

// 验证邮箱
function isValidEmail(email) {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return regex.test(email);
}

// 验证URL
function isValidUrl(url) {
    try {
        new URL(url);
        return true;
    } catch {
        return false;
    }
}

// 获取URL参数
function getUrlParams() {
    const params = {};
    const urlSearchParams = new URLSearchParams(window.location.search);
    for (const [key, value] of urlSearchParams) {
        params[key] = value;
    }
    return params;
}

// 设置URL参数
function setUrlParams(params) {
    const url = new URL(window.location);
    Object.keys(params).forEach(key => {
        if (params[key] !== null && params[key] !== undefined) {
            url.searchParams.set(key, params[key]);
        } else {
            url.searchParams.delete(key);
        }
    });
    window.history.replaceState({}, '', url);
}

// 移动端检测
function isMobile() {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

// 触摸设备检测
function isTouchDevice() {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
}
