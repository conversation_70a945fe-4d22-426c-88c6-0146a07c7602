<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三线人中台管理系统 - 登录</title>
    <link href="https://cdn.jsdelivr.net/npm/element-plus@2.4.4/dist/index.css" rel="stylesheet">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/element-plus@2.4.4/dist/index.full.js"></script>
    <style>
        body {
            margin: 0;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 900px;
            height: 500px;
            display: flex;
        }
        .login-left {
            flex: 1;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
        }
        .login-left::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }
        .login-left h1 {
            font-size: 2.5em;
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }
        .login-left p {
            font-size: 1.1em;
            line-height: 1.6;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }
        .login-right {
            flex: 1;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        .login-form {
            max-width: 300px;
            margin: 0 auto;
            width: 100%;
        }
        .login-title {
            text-align: center;
            margin-bottom: 40px;
        }
        .login-title h2 {
            color: #333;
            margin-bottom: 10px;
        }
        .login-title p {
            color: #666;
            font-size: 14px;
        }
        .form-item {
            margin-bottom: 20px;
        }
        .login-button {
            width: 100%;
            height: 45px;
            font-size: 16px;
        }
        .quick-login {
            margin-top: 30px;
            text-align: center;
        }
        .quick-login h4 {
            color: #666;
            margin-bottom: 15px;
            font-size: 14px;
        }
        .quick-buttons {
            display: flex;
            gap: 10px;
        }
        .quick-btn {
            flex: 1;
            height: 35px;
            font-size: 12px;
        }
        .features {
            margin-top: 30px;
            position: relative;
            z-index: 1;
        }
        .features h3 {
            margin-bottom: 15px;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            margin-bottom: 10px;
            padding-left: 20px;
            position: relative;
        }
        .feature-list li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #4CAF50;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="login-container">
            <!-- 左侧介绍 -->
            <div class="login-left">
                <h1>🏭 三线人中台管理系统</h1>
                <p>传承三线精神，记录历史足迹。为三线建设的历史文化保护和传承提供强大的管理平台。</p>
                
                <div class="features">
                    <h3>核心功能</h3>
                    <ul class="feature-list">
                        <li>用户管理和权限控制</li>
                        <li>内容审核和发布管理</li>
                        <li>安全监控和日志分析</li>
                        <li>数据统计和可视化</li>
                        <li>投稿审核和内容管理</li>
                        <li>系统配置和维护</li>
                    </ul>
                </div>
            </div>

            <!-- 右侧登录表单 -->
            <div class="login-right">
                <div class="login-form">
                    <div class="login-title">
                        <h2>管理员登录</h2>
                        <p>请输入您的管理员账号和密码</p>
                    </div>

                    <el-form :model="loginForm" :rules="loginRules" ref="loginFormRef" @submit.prevent="handleLogin">
                        <div class="form-item">
                            <el-input
                                v-model="loginForm.username"
                                placeholder="请输入用户名"
                                size="large"
                                prefix-icon="User">
                            </el-input>
                        </div>
                        
                        <div class="form-item">
                            <el-input
                                v-model="loginForm.password"
                                type="password"
                                placeholder="请输入密码"
                                size="large"
                                prefix-icon="Lock"
                                show-password
                                @keyup.enter="handleLogin">
                            </el-input>
                        </div>

                        <div class="form-item">
                            <el-checkbox v-model="loginForm.remember">记住密码</el-checkbox>
                        </div>

                        <el-button
                            type="primary"
                            class="login-button"
                            :loading="loginLoading"
                            @click="handleLogin">
                            {{ loginLoading ? '登录中...' : '登录' }}
                        </el-button>
                    </el-form>

                    <!-- 快速登录 -->
                    <div class="quick-login">
                        <h4>快速体验</h4>
                        <div class="quick-buttons">
                            <el-button class="quick-btn" @click="quickLogin('admin')">管理员</el-button>
                            <el-button class="quick-btn" @click="quickLogin('editor')">编辑员</el-button>
                            <el-button class="quick-btn" @click="quickLogin('viewer')">查看员</el-button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp } = Vue;
        const { ElMessage } = ElementPlus;

        createApp({
            data() {
                return {
                    loginForm: {
                        username: '',
                        password: '',
                        remember: false
                    },
                    loginRules: {
                        username: [
                            { required: true, message: '请输入用户名', trigger: 'blur' }
                        ],
                        password: [
                            { required: true, message: '请输入密码', trigger: 'blur' },
                            { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
                        ]
                    },
                    loginLoading: false
                }
            },
            methods: {
                async handleLogin() {
                    try {
                        await this.$refs.loginFormRef.validate();
                        this.loginLoading = true;

                        // 模拟登录请求
                        await new Promise(resolve => setTimeout(resolve, 1500));

                        // 模拟登录成功
                        if (this.loginForm.username === 'admin' && this.loginForm.password === '123456') {
                            ElMessage.success('登录成功！');
                            
                            // 保存登录状态
                            localStorage.setItem('admin_token', 'mock_admin_token');
                            localStorage.setItem('admin_user', JSON.stringify({
                                username: this.loginForm.username,
                                role: 'admin',
                                loginTime: new Date().toISOString()
                            }));

                            // 跳转到管理页面
                            setTimeout(() => {
                                window.location.href = 'admin_dashboard.html';
                            }, 1000);
                        } else {
                            ElMessage.error('用户名或密码错误');
                        }
                    } catch (error) {
                        console.log('表单验证失败:', error);
                    } finally {
                        this.loginLoading = false;
                    }
                },

                quickLogin(role) {
                    const accounts = {
                        admin: { username: 'admin', password: '123456', name: '系统管理员' },
                        editor: { username: 'editor', password: '123456', name: '内容编辑员' },
                        viewer: { username: 'viewer', password: '123456', name: '数据查看员' }
                    };

                    const account = accounts[role];
                    if (account) {
                        this.loginForm.username = account.username;
                        this.loginForm.password = account.password;
                        ElMessage.info(`已填入${account.name}账号信息`);
                    }
                }
            },
            mounted() {
                // 检查是否已经登录
                const token = localStorage.getItem('admin_token');
                if (token) {
                    ElMessage.info('检测到已登录状态，正在跳转...');
                    setTimeout(() => {
                        window.location.href = 'admin_dashboard.html';
                    }, 1000);
                }
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
