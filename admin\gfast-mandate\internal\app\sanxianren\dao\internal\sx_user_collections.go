// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SxUserCollectionsDao is the data access object for table sx_user_collections.
type SxUserCollectionsDao struct {
	table   string                   // table is the underlying table name of the DAO.
	group   string                   // group is the database configuration group name of current DAO.
	columns SxUserCollectionsColumns // columns contains all the column names of Table for convenient usage.
}

// SxUserCollectionsColumns defines and stores column names for table sx_user_collections.
type SxUserCollectionsColumns struct {
	Id          string // 收藏ID
	UserId      string // 用户ID
	ContentType string // 内容类型
	ContentId   string // 内容ID
	Title       string // 内容标题
	Summary     string // 内容摘要
	CoverImage  string // 封面图片
	CreatedAt   string // 创建时间
}

// sxUserCollectionsColumns holds the columns for table sx_user_collections.
var sxUserCollectionsColumns = SxUserCollectionsColumns{
	Id:          "id",
	UserId:      "user_id",
	ContentType: "content_type",
	ContentId:   "content_id",
	Title:       "title",
	Summary:     "summary",
	CoverImage:  "cover_image",
	CreatedAt:   "created_at",
}

// NewSxUserCollectionsDao creates and returns a new DAO object for table data access.
func NewSxUserCollectionsDao() *SxUserCollectionsDao {
	return &SxUserCollectionsDao{
		group:   "default",
		table:   "sx_user_collections",
		columns: sxUserCollectionsColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *SxUserCollectionsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *SxUserCollectionsDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *SxUserCollectionsDao) Columns() SxUserCollectionsColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *SxUserCollectionsDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *SxUserCollectionsDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}
