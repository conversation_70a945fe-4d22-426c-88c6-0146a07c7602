<template>
  <div class="wikipedia-home">
    <!-- 顶部导航栏 -->
    <WikipediaNavbar 
      @search="handleSearch"
      @category-change="handleCategoryChange"
      @language-change="handleLanguageChange"
    />

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 搜索结果提示 -->
      <div v-if="searchQuery" class="search-results-header">
        <h2>搜索结果: "{{ searchQuery }}"</h2>
        <p>找到 {{ totalCount }} 个相关词条</p>
      </div>

      <!-- 分类筛选器 -->
      <div class="category-filters">
        <el-scrollbar>
          <div class="filter-tags">
            <el-tag
              v-for="category in categories"
              :key="category.id"
              :type="selectedCategory === category.id ? 'primary' : ''"
              :effect="selectedCategory === category.id ? 'dark' : 'plain'"
              class="category-tag"
              @click="selectCategory(category.id)"
            >
              <SvgIcon :name="category.icon" :size="14" class="mr5" />
              {{ category.name }}
              <span class="count">({{ category.count }})</span>
            </el-tag>
          </div>
        </el-scrollbar>
      </div>

      <!-- 瀑布流内容区域 -->
      <div 
        ref="waterfallContainer" 
        class="waterfall-container"
        v-loading="initialLoading"
        element-loading-text="正在加载精彩内容..."
        element-loading-background="rgba(255, 255, 255, 0.8)"
      >
        <!-- 瀑布流列 -->
        <div
          v-for="(column, index) in waterfallColumns"
          :key="index"
          class="waterfall-column"
          :style="{ width: columnWidth }"
        >
          <WikipediaCard
            v-for="article in column"
            :key="article.id"
            :article="article"
            @click="openArticle"
            @favorite="toggleFavorite"
            @share="shareArticle"
          />
        </div>
      </div>

      <!-- 加载更多指示器 -->
      <div v-if="loadingMore" class="loading-more">
        <el-icon class="loading-icon">
          <Loading />
        </el-icon>
        <span>正在加载更多内容...</span>
      </div>

      <!-- 无更多内容提示 -->
      <div v-if="noMoreData && displayArticles.length > 0" class="no-more-data">
        <SvgIcon name="ele-Check" :size="20" />
        <span>已加载全部内容</span>
      </div>

      <!-- 空状态 -->
      <div v-if="!initialLoading && displayArticles.length === 0" class="empty-state">
        <SvgIcon name="ele-DocumentRemove" :size="64" class="empty-icon" />
        <h3>暂无内容</h3>
        <p>{{ searchQuery ? '没有找到相关词条，请尝试其他关键词' : '暂时没有可显示的词条' }}</p>
        <el-button type="primary" @click="filterArticles">
          <SvgIcon name="ele-Refresh" />
          刷新页面
        </el-button>
      </div>
    </div>

    <!-- 返回顶部按钮 -->
    <el-backtop 
      :right="40" 
      :bottom="40"
      :visibility-height="300"
    >
      <div class="backtop-button">
        <SvgIcon name="ele-Top" :size="20" />
      </div>
    </el-backtop>

    <!-- 文章详情抽屉 -->
    <el-drawer
      v-model="articleDrawerVisible"
      :title="selectedArticle?.title"
      direction="rtl"
      size="60%"
      :before-close="closeArticleDrawer"
    >
      <WikipediaArticleDetail
        v-if="selectedArticle"
        :article="selectedArticle"
        @close="closeArticleDrawer"
        @edit="editArticle"
        @favorite="toggleFavorite"
        @share="shareArticle"
      />
    </el-drawer>
  </div>
</template>

<script setup lang="ts" name="WikipediaHome">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { Loading } from '@element-plus/icons-vue';
import { useRouter } from 'vue-router';
import WikipediaNavbar from './components/WikipediaNavbar.vue';
import WikipediaCard from './components/WikipediaCard.vue';
import WikipediaArticleDetail from './components/WikipediaArticleDetail.vue';
import SvgIcon from '/@/components/svgIcon/index.vue';
import { mockArticles, generateMoreArticles } from './mock/articleData';
import WikipediaApi from '/@/api/sanxianren/wikipediaApi';

// Router
const router = useRouter();

// 响应式数据
const waterfallContainer = ref<HTMLElement>();
const articleDrawerVisible = ref(false);
const selectedArticle = ref(null);

const searchQuery = ref('');
const selectedCategory = ref('all');
const currentLanguage = ref('zh');

const initialLoading = ref(true);
const loadingMore = ref(false);
const noMoreData = ref(false);

// 文章数据
const allArticles = ref([...mockArticles]);
const displayArticles = ref([...mockArticles]);
const totalCount = ref(mockArticles.length);

// 瀑布流列数据
const waterfallColumns = ref<any[][]>([[], [], [], []]);
const columnNum = ref(4);

// 分类数据 - 对应导航栏的主要栏目
const categories = ref([
  { id: 'all', name: '全部', icon: 'ele-Grid', count: 0 },
  { id: 'sanxianren', name: '三线人', icon: 'ele-User', count: 0 },
  { id: 'sanxianchang', name: '三线厂', icon: 'ele-OfficeBuilding', count: 0 },
  { id: 'storyhall', name: '故事馆', icon: 'ele-Reading', count: 0 },
  { id: 'oralhistory', name: '口述历史', icon: 'ele-Microphone', count: 0 },
  { id: 'heritage', name: '遗址馆', icon: 'ele-Monument', count: 0 },
]);

// 计算属性
const columnWidth = computed(() => `${100 / columnNum.value}%`);

// 简单的瀑布流分布算法
const distributeArticles = () => {
  // 清空列
  waterfallColumns.value = Array.from({ length: columnNum.value }, () => []);

  // 分配文章到列
  displayArticles.value.forEach((article, index) => {
    const columnIndex = index % columnNum.value;
    waterfallColumns.value[columnIndex].push(article);
  });
};

// 方法
const handleSearch = (query: string) => {
  searchQuery.value = query;
  filterArticles();
};

const handleCategoryChange = (category: string) => {
  selectedCategory.value = category;
  filterArticles();
};

const handleLanguageChange = (language: string) => {
  currentLanguage.value = language;
  filterArticles();
};

const selectCategory = (categoryId: string) => {
  selectedCategory.value = categoryId;
  filterArticles();
};

const filterArticles = async () => {
  try {
    initialLoading.value = true;

    // 构建API参数
    const params = {
      query: searchQuery.value,
      category: selectedCategory.value !== 'all' ? selectedCategory.value : undefined,
      language: currentLanguage.value,
      page: 1,
      limit: 20,
    };

    // 调用对应的API
    let apiResponse;
    switch (selectedCategory.value) {
      case 'sanxianren':
        apiResponse = await WikipediaApi.getPeople(params);
        break;
      case 'sanxianchang':
        apiResponse = await WikipediaApi.getFactories(params);
        break;
      case 'storyhall':
        apiResponse = await WikipediaApi.getStories(params);
        break;
      case 'oralhistory':
        apiResponse = await WikipediaApi.getOralHistory(params);
        break;
      case 'heritage':
        apiResponse = await WikipediaApi.getHeritage(params);
        break;
      default:
        if (searchQuery.value) {
          apiResponse = await WikipediaApi.searchArticles(params);
        } else {
          apiResponse = await WikipediaApi.getArticles(params);
        }
    }

    // 更新数据
    displayArticles.value = apiResponse.list || [];
    totalCount.value = apiResponse.total || 0;

    // 重新分布到瀑布流
    distributeArticles();

    // 更新分类计数
    await updateCategoryCounts();

  } catch (error) {
    console.error('获取数据失败，使用模拟数据:', error);

    // API调用失败时使用模拟数据
    let filtered = [...allArticles.value];

    // 按搜索关键词过滤
    if (searchQuery.value) {
      filtered = filtered.filter(article =>
        article.title.includes(searchQuery.value) ||
        article.summary.includes(searchQuery.value) ||
        article.tags.some(tag => tag.includes(searchQuery.value))
      );
    }

    // 按分类过滤
    if (selectedCategory.value && selectedCategory.value !== 'all') {
      filtered = filtered.filter(article => article.category === selectedCategory.value);
    }

    displayArticles.value = filtered;
    totalCount.value = filtered.length;

    // 重新分布到瀑布流
    distributeArticles();

    // 更新分类计数
    updateCategoryCounts();

    ElMessage.warning('网络连接异常，显示本地数据');
  } finally {
    initialLoading.value = false;
  }
};

const loadMoreArticles = async () => {
  if (loadingMore.value || noMoreData.value) return;

  loadingMore.value = true;

  try {
    // 模拟加载更多数据
    await new Promise(resolve => setTimeout(resolve, 1000));

    const newArticles = generateMoreArticles(10);
    allArticles.value.push(...newArticles);

    // 重新过滤和分布
    filterArticles();

    ElMessage.success('加载了更多内容');
  } catch (error) {
    console.error('加载更多文章失败:', error);
    ElMessage.error('加载更多内容失败');
  } finally {
    loadingMore.value = false;
  }
};

const updateCategoryCounts = async () => {
  try {
    // 尝试从API获取分类统计
    const stats = await WikipediaApi.getCategoryStats();
    categories.value.forEach(category => {
      category.count = stats[category.id] || 0;
    });
  } catch (error) {
    // API失败时使用本地数据计算
    categories.value.forEach(category => {
      if (category.id === 'all') {
        category.count = allArticles.value.length;
      } else {
        category.count = allArticles.value.filter(article =>
          article.category === category.id
        ).length;
      }
    });
  }
};

const openArticle = (article: any) => {
  selectedArticle.value = article;
  articleDrawerVisible.value = true;
};

const closeArticleDrawer = () => {
  articleDrawerVisible.value = false;
  selectedArticle.value = null;
};

const editArticle = (article: any) => {
  router.push(`/sanxianren/wikipedia/edit/${article.id}`);
};

const toggleFavorite = async (article: any) => {
  try {
    // 模拟切换收藏状态
    article.isFavorite = !article.isFavorite;
    article.favorites += article.isFavorite ? 1 : -1;
    ElMessage.success(article.isFavorite ? '已添加到收藏' : '已取消收藏');
  } catch (error) {
    ElMessage.error('操作失败，请稍后重试');
  }
};

const shareArticle = (article: any) => {
  // 分享功能
  if (navigator.share) {
    navigator.share({
      title: article.title,
      text: article.summary,
      url: window.location.origin + `/sanxianren/wikipedia/${article.id}`,
    });
  } else {
    // 复制链接到剪贴板
    const url = window.location.origin + `/sanxianren/wikipedia/${article.id}`;
    navigator.clipboard.writeText(url).then(() => {
      ElMessage.success('链接已复制到剪贴板');
    });
  }
};

// 窗口大小变化时重新计算布局
const handleResize = () => {
  nextTick(() => {
    // 根据窗口大小调整列数
    const width = window.innerWidth;
    if (width >= 1400) {
      columnNum.value = 5;
    } else if (width >= 1200) {
      columnNum.value = 4;
    } else if (width >= 992) {
      columnNum.value = 3;
    } else if (width >= 768) {
      columnNum.value = 2;
    } else {
      columnNum.value = 1;
    }
    distributeArticles();
  });
};

// 滚动加载更多
const handleScroll = () => {
  const { scrollTop, scrollHeight, clientHeight } = document.documentElement;
  if (scrollTop + clientHeight >= scrollHeight - 200 && !loadingMore.value && !noMoreData.value) {
    loadMoreArticles();
  }
};

// 生命周期
onMounted(async () => {
  initialLoading.value = true;

  // 初始化数据
  await new Promise(resolve => setTimeout(resolve, 500)); // 模拟加载
  filterArticles();

  initialLoading.value = false;

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize);

  // 监听滚动事件
  window.addEventListener('scroll', handleScroll);

  // 初始化列数
  handleResize();
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  window.removeEventListener('scroll', handleScroll);
});
</script>

<style scoped lang="scss">
.wikipedia-home {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.main-content {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.search-results-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

  h2 {
    margin: 0 0 8px 0;
    color: var(--el-text-color-primary);
    font-size: 24px;
    font-weight: 600;
  }

  p {
    margin: 0;
    color: var(--el-text-color-regular);
    font-size: 14px;
  }
}

.category-filters {
  margin-bottom: 30px;
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

  .filter-tags {
    display: flex;
    gap: 12px;
    padding-bottom: 4px;

    .category-tag {
      cursor: pointer;
      transition: all 0.3s ease;
      white-space: nowrap;
      font-size: 14px;
      padding: 8px 16px;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      .count {
        margin-left: 4px;
        opacity: 0.7;
        font-size: 12px;
      }
    }
  }
}

.waterfall-container {
  display: flex;
  gap: 20px;
  align-items: flex-start;
  min-height: 400px;

  .waterfall-column {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: var(--el-text-color-regular);
  font-size: 14px;

  .loading-icon {
    margin-right: 8px;
    animation: spin 1s linear infinite;
  }
}

.no-more-data {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: var(--el-text-color-secondary);
  font-size: 14px;

  svg {
    margin-right: 8px;
    color: var(--el-color-success);
  }
}

.empty-state {
  text-align: center;
  padding: 80px 20px;
  color: var(--el-text-color-regular);

  .empty-icon {
    color: var(--el-text-color-placeholder);
    margin-bottom: 20px;
  }

  h3 {
    margin: 0 0 12px 0;
    font-size: 20px;
    font-weight: 500;
  }

  p {
    margin: 0 0 24px 0;
    font-size: 14px;
    line-height: 1.5;
  }
}

.backtop-button {
  width: 40px;
  height: 40px;
  background: var(--el-color-primary);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;

  &:hover {
    background: var(--el-color-primary-dark-2);
    transform: translateY(-2px);
  }
}

.mr5 {
  margin-right: 5px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 响应式设计
@media (max-width: 1200px) {
  .waterfall-container {
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: 16px;
  }

  .category-filters {
    padding: 16px;
  }

  .waterfall-container {
    gap: 12px;
  }
}
</style>
