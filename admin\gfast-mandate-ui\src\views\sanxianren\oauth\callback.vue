<template>
  <div class="oauth-callback">
    <div class="callback-container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-section">
        <el-card shadow="hover" class="callback-card">
          <div class="loading-content">
            <el-icon class="loading-icon" :size="48">
              <Loading />
            </el-icon>
            <h3>正在处理OAuth认证...</h3>
            <p class="loading-text">{{ loadingText }}</p>
            <el-progress 
              :percentage="progress" 
              :show-text="false"
              class="progress-bar"
            />
          </div>
        </el-card>
      </div>

      <!-- 成功状态 -->
      <div v-else-if="success" class="success-section">
        <el-card shadow="hover" class="callback-card success-card">
          <div class="result-content">
            <el-icon class="result-icon success-icon" :size="64">
              <SuccessFilled />
            </el-icon>
            <h2>OAuth认证成功！</h2>
            <p class="result-text">
              您的账户已成功与MediaWiki用户 
              <strong>{{ userInfo?.name }}</strong> 关联
            </p>
            
            <!-- 用户信息预览 -->
            <div v-if="userInfo" class="user-preview">
              <el-descriptions :column="2" border size="small">
                <el-descriptions-item label="用户名">
                  {{ userInfo.name }}
                </el-descriptions-item>
                <el-descriptions-item label="邮箱">
                  {{ userInfo.email || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="编辑次数">
                  {{ userInfo.edit_count }}
                </el-descriptions-item>
                <el-descriptions-item label="用户组">
                  <el-tag
                    v-for="group in userInfo.groups"
                    :key="group"
                    size="small"
                    class="mr5"
                  >
                    {{ group }}
                  </el-tag>
                </el-descriptions-item>
              </el-descriptions>
            </div>

            <div class="action-buttons">
              <el-button type="primary" @click="goToOAuthManagement">
                <SvgIcon name="ele-Setting" />
                管理OAuth设置
              </el-button>
              <el-button @click="goToHome">
                <SvgIcon name="ele-HomeFilled" />
                返回首页
              </el-button>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 错误状态 -->
      <div v-else class="error-section">
        <el-card shadow="hover" class="callback-card error-card">
          <div class="result-content">
            <el-icon class="result-icon error-icon" :size="64">
              <CircleCloseFilled />
            </el-icon>
            <h2>OAuth认证失败</h2>
            <p class="result-text error-text">{{ errorMessage }}</p>
            
            <!-- 错误详情 -->
            <div v-if="errorDetails" class="error-details">
              <el-collapse>
                <el-collapse-item title="错误详情" name="details">
                  <pre>{{ errorDetails }}</pre>
                </el-collapse-item>
              </el-collapse>
            </div>

            <div class="action-buttons">
              <el-button type="primary" @click="retryAuthentication">
                <SvgIcon name="ele-Refresh" />
                重新认证
              </el-button>
              <el-button @click="goToOAuthManagement">
                <SvgIcon name="ele-Setting" />
                OAuth设置
              </el-button>
              <el-button @click="goToHome">
                <SvgIcon name="ele-HomeFilled" />
                返回首页
              </el-button>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="OAuthCallback">
import { ref, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ElMessage } from 'element-plus';
import { Loading, SuccessFilled, CircleCloseFilled } from '@element-plus/icons-vue';
import { useOAuthStore } from '/@/stores/oauthStore';
import { OAuthUtils, type MediaWikiUserInfo } from '/@/api/sanxianren/oauth';
import SvgIcon from '/@/components/svgIcon/index.vue';
import { Session } from '/@/utils/storage';

// Router
const router = useRouter();
const route = useRoute();

// Store
const oauthStore = useOAuthStore();

// 响应式数据
const loading = ref(true);
const success = ref(false);
const loadingText = ref('正在验证授权码...');
const progress = ref(0);
const errorMessage = ref('');
const errorDetails = ref('');
const userInfo = ref<MediaWikiUserInfo | null>(null);

// 进度更新函数
const updateProgress = (percent: number, text: string) => {
  progress.value = percent;
  loadingText.value = text;
};

// 处理OAuth回调
const handleOAuthCallback = async () => {
  try {
    // 步骤1: 获取URL参数
    updateProgress(20, '正在获取认证参数...');
    
    const callbackParams = OAuthUtils.getCallbackParams();
    if (!callbackParams) {
      throw new Error('缺少必要的OAuth回调参数');
    }

    // 检查是否有错误参数
    if (callbackParams.error) {
      throw new Error(`OAuth认证失败: ${callbackParams.error_description || callbackParams.error}`);
    }

    // 步骤2: 处理回调
    updateProgress(50, '正在处理OAuth回调...');
    
    const success = await oauthStore.handleCallback(
      callbackParams.code,
      callbackParams.state,
      callbackParams.error,
      callbackParams.error_description
    );

    if (!success) {
      throw new Error(oauthStore.error || 'OAuth回调处理失败');
    }

    // 步骤3: 获取用户信息
    updateProgress(80, '正在获取用户信息...');
    
    await oauthStore.getStatus();
    userInfo.value = oauthStore.userInfo;

    // 步骤4: 完成
    updateProgress(100, '认证完成！');
    
    setTimeout(() => {
      loading.value = false;
      success.value = true;
      
      // 清理URL参数
      OAuthUtils.cleanCallbackParams();
      
      ElMessage.success('OAuth认证成功！');
    }, 500);

  } catch (error: any) {
    console.error('OAuth回调处理失败:', error);
    
    loading.value = false;
    success.value = false;
    errorMessage.value = error.message || 'OAuth认证失败';
    errorDetails.value = error.stack || JSON.stringify(error, null, 2);
    
    // 清理URL参数
    OAuthUtils.cleanCallbackParams();
    
    ElMessage.error(errorMessage.value);
  }
};

// 重新认证
const retryAuthentication = async () => {
  const returnUrl = Session.get('oauth_return_url') || '/sanxianren/oauth';
  await oauthStore.startAuthentication(returnUrl);
};

// 跳转到OAuth管理页面
const goToOAuthManagement = () => {
  router.push('/sanxianren/oauth');
};

// 跳转到首页
const goToHome = () => {
  router.push('/home');
};

// 自动重定向
const autoRedirect = () => {
  const returnUrl = Session.get('oauth_return_url');
  if (returnUrl && success.value) {
    setTimeout(() => {
      Session.remove('oauth_return_url');
      router.push(returnUrl);
    }, 3000);
  }
};

// 生命周期
onMounted(async () => {
  // 检查是否有回调参数
  const hasCallbackParams = route.query.code || route.query.error;
  
  if (hasCallbackParams) {
    // 处理OAuth回调
    await handleOAuthCallback();
    
    // 如果成功，自动重定向
    autoRedirect();
  } else {
    // 没有回调参数，可能是直接访问
    loading.value = false;
    success.value = false;
    errorMessage.value = '无效的OAuth回调请求';
  }
});
</script>

<style scoped lang="scss">
.oauth-callback {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;

  .callback-container {
    width: 100%;
    max-width: 600px;
  }

  .callback-card {
    border-radius: 12px;
    overflow: hidden;

    &.success-card {
      border-top: 4px solid var(--el-color-success);
    }

    &.error-card {
      border-top: 4px solid var(--el-color-danger);
    }
  }

  .loading-content,
  .result-content {
    text-align: center;
    padding: 40px 20px;
  }

  .loading-icon {
    color: var(--el-color-primary);
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
  }

  .result-icon {
    margin-bottom: 20px;

    &.success-icon {
      color: var(--el-color-success);
    }

    &.error-icon {
      color: var(--el-color-danger);
    }
  }

  h2, h3 {
    margin: 0 0 16px 0;
    color: var(--el-text-color-primary);
  }

  .loading-text,
  .result-text {
    margin: 0 0 20px 0;
    color: var(--el-text-color-regular);
    font-size: 16px;
    line-height: 1.5;

    &.error-text {
      color: var(--el-color-danger);
    }
  }

  .progress-bar {
    margin: 20px 0;
  }

  .user-preview {
    margin: 24px 0;
    text-align: left;
  }

  .error-details {
    margin: 20px 0;
    text-align: left;

    pre {
      background: var(--el-fill-color-light);
      padding: 12px;
      border-radius: 4px;
      font-size: 12px;
      max-height: 200px;
      overflow-y: auto;
    }
  }

  .action-buttons {
    margin-top: 30px;

    .el-button {
      margin: 0 8px;
    }
  }

  .mr5 {
    margin-right: 5px;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .oauth-callback {
    padding: 10px;

    .callback-card {
      .loading-content,
      .result-content {
        padding: 30px 15px;
      }

      .action-buttons {
        .el-button {
          margin: 5px;
          width: 100%;
        }
      }
    }
  }
}
</style>
