// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/sanxianren/dao/internal"
)

// internalSxUserHistoryDao is internal type for wrapping internal DAO implements.
type internalSxUserHistoryDao = *internal.SxUserHistoryDao

// sxUserHistoryDao is the data access object for table sx_user_history.
// You can define custom methods on it to extend its functionality as you wish.
type sxUserHistoryDao struct {
	internalSxUserHistoryDao
}

var (
	// SxUserHistory is globally public accessible object for table sx_user_history operations.
	SxUserHistory = sxUserHistoryDao{
		internal.NewSxUserHistoryDao(),
	}
)

// Fill with you ideas below.
