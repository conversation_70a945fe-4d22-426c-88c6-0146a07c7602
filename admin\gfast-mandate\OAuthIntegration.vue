<template>
  <div class="oauth-integration">
    <!-- OAuth状态显示 -->
    <div class="oauth-status">
      <h3>MediaWiki OAuth认证状态</h3>
      <div v-if="loading" class="loading">
        <i class="el-icon-loading"></i> 检查认证状态...
      </div>
      <div v-else-if="oauthStatus.is_authenticated" class="authenticated">
        <el-alert
          title="已认证"
          type="success"
          :description="`已与MediaWiki账户 ${oauthStatus.user_info?.name} 关联`"
          show-icon
          :closable="false"
        />
        <div class="user-info">
          <h4>用户信息</h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="用户名">
              {{ oauthStatus.user_info?.name }}
            </el-descriptions-item>
            <el-descriptions-item label="真实姓名">
              {{ oauthStatus.user_info?.real_name }}
            </el-descriptions-item>
            <el-descriptions-item label="邮箱">
              {{ oauthStatus.user_info?.email }}
            </el-descriptions-item>
            <el-descriptions-item label="编辑次数">
              {{ oauthStatus.user_info?.edit_count }}
            </el-descriptions-item>
            <el-descriptions-item label="用户组">
              <el-tag
                v-for="group in oauthStatus.user_info?.groups"
                :key="group"
                size="small"
                style="margin-right: 5px;"
              >
                {{ group }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="会话过期时间">
              {{ formatDate(oauthStatus.expires_at) }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <div class="actions">
          <el-button type="warning" @click="revokeOAuth" :loading="revoking">
            撤销认证
          </el-button>
          <el-button type="primary" @click="refreshStatus">
            刷新状态
          </el-button>
        </div>
      </div>
      <div v-else class="not-authenticated">
        <el-alert
          title="未认证"
          type="warning"
          description="您还没有与MediaWiki账户关联，请点击下方按钮进行OAuth认证"
          show-icon
          :closable="false"
        />
        <div class="actions">
          <el-button type="primary" @click="startOAuth" :loading="authenticating">
            <i class="el-icon-link"></i> 连接MediaWiki账户
          </el-button>
        </div>
      </div>
    </div>

    <!-- OAuth配置信息 -->
    <div class="oauth-config" v-if="showConfig">
      <h3>OAuth配置信息</h3>
      <el-descriptions :column="1" border>
        <el-descriptions-item label="客户端ID">
          {{ maskString(config.client_id) }}
        </el-descriptions-item>
        <el-descriptions-item label="授权URL">
          {{ config.authorization_url }}
        </el-descriptions-item>
        <el-descriptions-item label="回调URL">
          {{ config.redirect_uri }}
        </el-descriptions-item>
        <el-descriptions-item label="权限范围">
          <el-tag
            v-for="scope in config.scopes"
            :key="scope"
            size="small"
            style="margin-right: 5px;"
          >
            {{ scope }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <!-- 操作日志 -->
    <div class="oauth-logs" v-if="showLogs">
      <h3>操作日志</h3>
      <el-table :data="logs" style="width: 100%">
        <el-table-column prop="action" label="操作" width="120" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag
              :type="scope.row.status === 'success' ? 'success' : 'danger'"
              size="small"
            >
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="message" label="消息" />
        <el-table-column prop="created_at" label="时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

export default {
  name: 'OAuthIntegration',
  props: {
    showConfig: {
      type: Boolean,
      default: false
    },
    showLogs: {
      type: Boolean,
      default: false
    }
  },
  setup() {
    const loading = ref(false)
    const authenticating = ref(false)
    const revoking = ref(false)
    
    const oauthStatus = reactive({
      is_authenticated: false,
      user_info: null,
      session_id: null,
      expires_at: null
    })
    
    const config = reactive({
      client_id: '',
      authorization_url: '',
      redirect_uri: '',
      scopes: []
    })
    
    const logs = ref([])
    
    // 获取OAuth状态
    const getOAuthStatus = async () => {
      loading.value = true
      try {
        const response = await fetch('/api/v1/sanxianren/oauth/status', {
          headers: {
            'Authorization': `Bearer ${getToken()}`
          }
        })
        const data = await response.json()
        
        Object.assign(oauthStatus, data)
        
        if (data.is_authenticated) {
          addLog('status_check', 'success', '已认证状态')
        } else {
          addLog('status_check', 'success', '未认证状态')
        }
      } catch (error) {
        console.error('获取OAuth状态失败:', error)
        ElMessage.error('获取OAuth状态失败')
        addLog('status_check', 'failed', '获取状态失败: ' + error.message)
      } finally {
        loading.value = false
      }
    }
    
    // 获取OAuth配置
    const getOAuthConfig = async () => {
      try {
        const response = await fetch('/api/v1/sanxianren/oauth/config')
        const data = await response.json()
        Object.assign(config, data)
      } catch (error) {
        console.error('获取OAuth配置失败:', error)
      }
    }
    
    // 发起OAuth认证
    const startOAuth = async () => {
      authenticating.value = true
      try {
        const returnUrl = window.location.origin + '/admin/oauth/success'
        
        const response = await fetch('/api/v1/sanxianren/oauth/authorize', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${getToken()}`
          },
          body: JSON.stringify({ return_url: returnUrl })
        })
        
        const data = await response.json()
        
        if (data.auth_url) {
          addLog('authorize', 'success', '重定向到MediaWiki授权页面')
          // 重定向到MediaWiki授权页面
          window.location.href = data.auth_url
        } else {
          ElMessage.error('获取授权URL失败')
          addLog('authorize', 'failed', '获取授权URL失败')
        }
      } catch (error) {
        console.error('发起OAuth认证失败:', error)
        ElMessage.error('发起OAuth认证失败')
        addLog('authorize', 'failed', '发起认证失败: ' + error.message)
      } finally {
        authenticating.value = false
      }
    }
    
    // 撤销OAuth认证
    const revokeOAuth = async () => {
      try {
        await ElMessageBox.confirm(
          '确定要撤销MediaWiki OAuth认证吗？撤销后将无法访问MediaWiki相关功能。',
          '确认撤销',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        
        revoking.value = true
        
        // 这里需要获取当前的access token
        const response = await fetch('/api/v1/sanxianren/oauth/revoke', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ 
            access_token: 'current_access_token' // 实际应用中需要从状态中获取
          })
        })
        
        const data = await response.json()
        
        if (data.success) {
          ElMessage.success('OAuth认证已撤销')
          addLog('revoke', 'success', 'OAuth认证已撤销')
          // 刷新状态
          await getOAuthStatus()
        } else {
          ElMessage.error('撤销OAuth认证失败')
          addLog('revoke', 'failed', '撤销认证失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('撤销OAuth认证失败:', error)
          ElMessage.error('撤销OAuth认证失败')
          addLog('revoke', 'failed', '撤销认证失败: ' + error.message)
        }
      } finally {
        revoking.value = false
      }
    }
    
    // 刷新状态
    const refreshStatus = () => {
      getOAuthStatus()
    }
    
    // 添加日志
    const addLog = (action, status, message) => {
      logs.value.unshift({
        action,
        status,
        message,
        created_at: new Date().toISOString()
      })
      
      // 只保留最近50条日志
      if (logs.value.length > 50) {
        logs.value = logs.value.slice(0, 50)
      }
    }
    
    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return '-'
      return new Date(dateString).toLocaleString('zh-CN')
    }
    
    // 遮蔽字符串
    const maskString = (str) => {
      if (!str || str.length <= 8) return str
      return str.substring(0, 4) + '****' + str.substring(str.length - 4)
    }
    
    // 获取token
    const getToken = () => {
      return localStorage.getItem('gfast_token') || ''
    }
    
    // 组件挂载时获取状态和配置
    onMounted(() => {
      getOAuthStatus()
      getOAuthConfig()
    })
    
    return {
      loading,
      authenticating,
      revoking,
      oauthStatus,
      config,
      logs,
      startOAuth,
      revokeOAuth,
      refreshStatus,
      formatDate,
      maskString
    }
  }
}
</script>

<style scoped>
.oauth-integration {
  padding: 20px;
}

.oauth-status,
.oauth-config,
.oauth-logs {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.oauth-status h3,
.oauth-config h3,
.oauth-logs h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #303133;
}

.loading {
  text-align: center;
  padding: 20px;
  color: #909399;
}

.user-info {
  margin: 20px 0;
}

.user-info h4 {
  margin-bottom: 15px;
  color: #606266;
}

.actions {
  margin-top: 20px;
  text-align: center;
}

.actions .el-button {
  margin: 0 10px;
}

.authenticated .el-alert {
  margin-bottom: 20px;
}

.not-authenticated .el-alert {
  margin-bottom: 20px;
}
</style>
