# gfast中台 OAuth认证集成完成指南

## 🎉 集成完成概览

OAuth认证功能已成功集成到gfast中台管理后台中，现在您可以在管理界面中直接使用MediaWiki OAuth认证功能。

## 📁 已创建的文件

### 1. API接口层
- `src/api/sanxianren/oauth.ts` - OAuth API接口定义和调用方法

### 2. 状态管理层
- `src/stores/oauthStore.ts` - OAuth状态管理（Pinia Store）

### 3. 页面组件
- `src/views/sanxianren/oauth/index.vue` - OAuth管理主页面
- `src/views/sanxianren/oauth/callback.vue` - OAuth回调处理页面
- `src/views/sanxianren/oauth/success.vue` - OAuth认证成功页面
- `src/views/sanxianren/oauth/test.vue` - OAuth功能测试页面

### 4. 通用组件
- `src/components/oauth/OAuthStatus.vue` - OAuth状态显示组件

### 5. 插件和工具
- `src/plugins/oauth.ts` - OAuth插件和工具函数

### 6. 路由配置
- 已在 `src/router/route.ts` 中添加OAuth相关路由

### 7. 国际化配置
- 已在 `src/i18n/lang/zh-cn.ts` 中添加OAuth相关翻译

## 🚀 功能特性

### 1. OAuth认证管理
- ✅ 发起OAuth认证
- ✅ 处理OAuth回调
- ✅ 撤销OAuth认证
- ✅ 刷新访问令牌
- ✅ 验证令牌有效性

### 2. 用户信息同步
- ✅ 获取MediaWiki用户信息
- ✅ 显示用户权限和组织
- ✅ 实时状态更新

### 3. 状态管理
- ✅ Pinia Store集成
- ✅ 响应式状态更新
- ✅ 本地存储持久化

### 4. UI组件
- ✅ 多种显示模式（简洁、卡片、详细、内联）
- ✅ 响应式设计
- ✅ Element Plus UI集成

### 5. 开发工具
- ✅ 功能测试页面
- ✅ 错误处理和调试
- ✅ 自定义指令支持

## 🎯 使用方法

### 1. 访问OAuth管理页面

在管理后台中访问：
```
/sanxianren/oauth
```

或通过菜单导航：
```
三线人管理 → MediaWiki OAuth认证
```

### 2. 在其他页面中使用OAuth状态组件

```vue
<template>
  <!-- 简洁模式 -->
  <OAuthStatus mode="simple" />
  
  <!-- 卡片模式 -->
  <OAuthStatus mode="card" />
  
  <!-- 详细模式 -->
  <OAuthStatus mode="detailed" />
  
  <!-- 内联模式 -->
  <OAuthStatus mode="inline" />
</template>

<script setup>
import OAuthStatus from '/@/components/oauth/OAuthStatus.vue';
</script>
```

### 3. 在组件中使用OAuth Store

```vue
<script setup>
import { useOAuthStore } from '/@/stores/oauthStore';

const oauthStore = useOAuthStore();

// 检查认证状态
console.log('是否已认证:', oauthStore.isAuthenticated);
console.log('用户名:', oauthStore.username);
console.log('用户权限:', oauthStore.rights);

// 发起认证
const authenticate = async () => {
  await oauthStore.startAuthentication();
};

// 撤销认证
const revoke = async () => {
  await oauthStore.revokeAuthentication();
};
</script>
```

### 4. 使用OAuth组合式API

```vue
<script setup>
import { useOAuth } from '/@/plugins/oauth';

const {
  isAuthenticated,
  userInfo,
  username,
  canEdit,
  authenticate,
  revoke,
  hasPermission,
} = useOAuth();

// 检查权限
if (hasPermission('editpage')) {
  console.log('用户有编辑权限');
}
</script>
```

### 5. 使用OAuth指令

```vue
<template>
  <!-- 需要OAuth认证才显示 -->
  <div v-oauth-required>
    这个内容只有在OAuth认证后才显示
  </div>
  
  <!-- 需要特定权限才显示 -->
  <button v-oauth-permission="'editpage'">
    编辑页面
  </button>
  
  <!-- 需要特定用户组才显示 -->
  <div v-oauth-group="'admin'">
    管理员功能
  </div>
</template>
```

## 🔧 配置说明

### 1. OAuth插件配置

在 `src/main.ts` 中的配置：

```typescript
app.use(oauthPlugin, {
  autoInit: true,              // 自动初始化
  handleCallback: true,        // 处理回调
  callbackRouteName: 'sanxianrenOAuthCallback',
  checkInRouteGuard: false,    // 路由守卫检查
  protectedRoutes: [],         // 受保护的路由
});
```

### 2. 后端API配置

确保后端OAuth API正常运行：
- OAuth认证接口：`/api/v1/sanxianren/oauth/*`
- MediaWiki API配置正确
- 数据库表已创建

## 🧪 测试功能

### 1. 访问测试页面

```
/sanxianren/oauth/test
```

### 2. 测试项目

- ✅ OAuth配置测试
- ✅ OAuth状态测试  
- ✅ Store状态测试
- ✅ API接口测试
- ✅ 令牌验证测试

### 3. 运行测试

1. 访问测试页面
2. 选择要运行的测试项目
3. 点击"运行所有测试"
4. 查看测试结果

## 🎨 UI界面预览

### 1. OAuth管理页面
- 认证状态显示
- 用户信息展示
- 权限信息显示
- 操作按钮（连接/撤销）

### 2. OAuth回调页面
- 加载进度显示
- 成功/失败状态
- 错误信息展示
- 自动重定向

### 3. OAuth成功页面
- 成功确认信息
- 用户信息预览
- 功能介绍
- 快捷操作按钮

### 4. OAuth状态组件
- 多种显示模式
- 实时状态更新
- 交互操作支持

## 🔐 安全特性

### 1. 令牌管理
- 自动令牌刷新
- 安全存储
- 过期检测

### 2. 状态验证
- 实时状态检查
- 错误处理
- 自动重试

### 3. 权限控制
- 基于权限的UI显示
- 用户组验证
- 操作权限检查

## 📱 响应式支持

所有OAuth相关页面和组件都支持响应式设计：
- 桌面端优化
- 平板适配
- 移动端友好

## 🐛 故障排除

### 1. 常见问题

#### OAuth认证失败
- 检查后端API是否正常
- 验证MediaWiki OAuth配置
- 查看浏览器控制台错误

#### 状态不更新
- 检查网络连接
- 验证API响应
- 重新加载页面

#### 权限显示异常
- 确认用户权限配置
- 检查MediaWiki用户组
- 验证API返回数据

### 2. 调试方法

1. 打开浏览器开发者工具
2. 查看Console日志
3. 检查Network请求
4. 使用测试页面验证功能

## 🚀 下一步

1. **配置MediaWiki OAuth应用**
   - 按照之前的配置指南设置
   - 获取真实的Client ID和Secret
   - 更新后端配置

2. **测试完整流程**
   - 访问OAuth管理页面
   - 执行认证流程
   - 验证功能正常

3. **生产部署**
   - 配置HTTPS
   - 更新生产环境配置
   - 监控和日志设置

## 📞 技术支持

如有问题或需要帮助：
1. 查看本指南的故障排除部分
2. 检查浏览器控制台错误信息
3. 使用测试页面诊断问题
4. 联系开发团队获取支持

---

🎊 **恭喜！OAuth认证功能已成功集成到gfast中台管理后台中！**
