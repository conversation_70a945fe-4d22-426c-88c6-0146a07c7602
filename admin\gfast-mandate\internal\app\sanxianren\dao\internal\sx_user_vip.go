// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SxUserVipDao is the data access object for table sx_user_vip.
type SxUserVipDao struct {
	table   string           // table is the underlying table name of the DAO.
	group   string           // group is the database configuration group name of current DAO.
	columns SxUserVipColumns // columns contains all the column names of Table for convenient usage.
}

// SxUserVipColumns defines and stores column names for table sx_user_vip.
type SxUserVipColumns struct {
	Id            string // VIP记录ID
	UserId        string // 用户ID
	VipType       string // VIP类型：monthly-月度，yearly-年度，lifetime-终身
	StartDate     string // VIP开始时间
	EndDate       string // VIP结束时间
	IsActive      string // 是否激活：1-激活，0-未激活
	PaymentAmount string // 支付金额
	PaymentMethod string // 支付方式
	OrderNo       string // 订单号
	CreatedAt     string // 创建时间
	UpdatedAt     string // 更新时间
}

// sxUserVipColumns holds the columns for table sx_user_vip.
var sxUserVipColumns = SxUserVipColumns{
	Id:            "id",
	UserId:        "user_id",
	VipType:       "vip_type",
	StartDate:     "start_date",
	EndDate:       "end_date",
	IsActive:      "is_active",
	PaymentAmount: "payment_amount",
	PaymentMethod: "payment_method",
	OrderNo:       "order_no",
	CreatedAt:     "created_at",
	UpdatedAt:     "updated_at",
}

// NewSxUserVipDao creates and returns a new DAO object for table data access.
func NewSxUserVipDao() *SxUserVipDao {
	return &SxUserVipDao{
		group:   "default",
		table:   "sx_user_vip",
		columns: sxUserVipColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *SxUserVipDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *SxUserVipDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *SxUserVipDao) Columns() SxUserVipColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *SxUserVipDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *SxUserVipDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}
