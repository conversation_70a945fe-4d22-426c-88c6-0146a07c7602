<?php
/**
 * 天南地北三线人 - 现代化首页
 * 
 * 这个文件提供现代化的Wikipedia首页体验
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-22
 * @website www.sanxianren.com
 */

// 设置页面信息
$pageTitle = "天南地北三线人 - 三线建设历史百科";
$pageDescription = "记录三线建设历史，传承三线精神。探索三线人故事、三线厂历史、口述历史和历史遗址。";
$siteName = "天南地北三线人";
// 使用相对路径或自动检测当前主机名
$baseUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https://" : "http://") . $_SERVER['HTTP_HOST'];

// 获取当前分类和搜索参数
$currentCategory = $_GET['category'] ?? 'all';
$searchQuery = $_GET['search'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$errorCode = $_GET['error'] ?? '';

// 处理错误页面
if ($errorCode) {
    switch ($errorCode) {
        case '404':
            http_response_code(404);
            $pageTitle = "页面未找到 - " . $siteName;
            $pageDescription = "抱歉，您访问的页面不存在。";
            break;
        case '403':
            http_response_code(403);
            $pageTitle = "访问被拒绝 - " . $siteName;
            $pageDescription = "抱歉，您没有权限访问此页面。";
            break;
        case '500':
            http_response_code(500);
            $pageTitle = "服务器错误 - " . $siteName;
            $pageDescription = "抱歉，服务器遇到了问题。";
            break;
    }
}

// 检查是否是API请求
if (isset($_GET['api'])) {
    header('Content-Type: application/json; charset=utf-8');
    require_once __DIR__ . '/api/sanxianren.php';
    exit;
}

// 设置缓存头
header('Cache-Control: public, max-age=300');
header('Vary: Accept-Encoding');

// 启用Gzip压缩
if (extension_loaded('zlib') && !ob_get_level()) {
    ob_start('ob_gzhandler');
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?></title>
    <meta name="description" content="<?php echo htmlspecialchars($pageDescription); ?>">
    <meta name="keywords" content="三线建设,三线人,三线厂,口述历史,历史遗址,故事馆">
    <meta name="author" content="天南地北三线人">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph -->
    <meta property="og:title" content="<?php echo htmlspecialchars($pageTitle); ?>">
    <meta property="og:description" content="<?php echo htmlspecialchars($pageDescription); ?>">
    <meta property="og:url" content="<?php echo htmlspecialchars($baseUrl); ?>">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="<?php echo htmlspecialchars($siteName); ?>">
    <meta property="og:image" content="<?php echo htmlspecialchars($baseUrl); ?>/frontend/assets/images/og-image.jpg">
    
    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo htmlspecialchars($pageTitle); ?>">
    <meta name="twitter:description" content="<?php echo htmlspecialchars($pageDescription); ?>">
    <meta name="twitter:image" content="<?php echo htmlspecialchars($baseUrl); ?>/frontend/assets/images/og-image.jpg">
    
    <!-- Favicon -->
    <link rel="icon" href="./favicon.ico" type="image/x-icon">
    <link rel="apple-touch-icon" href="./favicon.ico">
    <link rel="canonical" href="<?php echo htmlspecialchars($baseUrl); ?>">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="https://unpkg.com/vue@3.4.15/dist/vue.global.js" as="script">
    <link rel="preload" href="https://unpkg.com/element-plus@2.4.4/dist/index.css" as="style">
    
    <!-- CSS -->
    <link rel="stylesheet" href="./frontend/assets/css/main.css?v=<?php echo filemtime(__DIR__ . '/frontend/assets/css/main.css'); ?>">
    <link rel="stylesheet" href="./frontend/assets/css/components.css?v=<?php echo filemtime(__DIR__ . '/frontend/assets/css/components.css'); ?>">
    <link rel="stylesheet" href="./frontend/assets/css/responsive.css?v=<?php echo filemtime(__DIR__ . '/frontend/assets/css/responsive.css'); ?>">
    
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus@2.4.4/dist/index.css">
    
    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3.4.15/dist/vue.global.js"></script>
    
    <!-- Element Plus -->
    <script src="https://unpkg.com/element-plus@2.4.4/dist/index.full.js"></script>
    
    <!-- Element Plus Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue@2.3.1/dist/index.iife.js"></script>
    
    <!-- 结构化数据 -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "<?php echo htmlspecialchars($siteName); ?>",
        "url": "<?php echo htmlspecialchars($baseUrl); ?>",
        "description": "<?php echo htmlspecialchars($pageDescription); ?>",
        "potentialAction": {
            "@type": "SearchAction",
            "target": "<?php echo htmlspecialchars($baseUrl); ?>?search={search_term_string}",
            "query-input": "required name=search_term_string"
        },
        "publisher": {
            "@type": "Organization",
            "name": "<?php echo htmlspecialchars($siteName); ?>",
            "url": "<?php echo htmlspecialchars($baseUrl); ?>"
        }
    }
    </script>
    
    <!-- Google Analytics (如果需要) -->
    <?php if (defined('GA_TRACKING_ID') && GA_TRACKING_ID): ?>
    <script async src="https://www.googletagmanager.com/gtag/js?id=<?php echo GA_TRACKING_ID; ?>"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', '<?php echo GA_TRACKING_ID; ?>');
    </script>
    <?php endif; ?>
</head>
<body>
    <div id="app">
        <!-- 顶部导航栏 -->
        <nav class="navbar">
            <div class="navbar-container">
                <!-- Logo和标题 -->
                <div class="navbar-brand" @click="goHome">
                    <div class="logo">
                        <i class="el-icon-reading"></i>
                        <span class="brand-text"><?php echo htmlspecialchars($siteName); ?></span>
                    </div>
                    <div class="subtitle">记录三线建设历史，传承三线精神</div>
                </div>

                <!-- 主导航菜单 -->
                <div class="navbar-nav">
                    <div 
                        v-for="nav in navigationItems"
                        :key="nav.key"
                        class="nav-item"
                        :class="{ active: currentNav === nav.key }"
                        @click="handleNavClick(nav)"
                    >
                        <i :class="nav.icon"></i>
                        <span>{{ nav.label }}</span>
                    </div>
                </div>

                <!-- 搜索栏 -->
                <div class="navbar-search">
                    <el-input
                        v-model="searchQuery"
                        placeholder="搜索三线人、三线厂、故事..."
                        class="search-input"
                        size="large"
                        clearable
                        @keyup.enter="handleSearch"
                    >
                        <template #prefix>
                            <i class="el-icon-search"></i>
                        </template>
                        <template #suffix>
                            <el-button 
                                type="primary" 
                                @click="handleSearch"
                                class="search-button"
                            >
                                搜索
                            </el-button>
                        </template>
                    </el-input>
                </div>

                <!-- 用户菜单 -->
                <div class="navbar-user">
                    <el-dropdown @command="handleUserCommand">
                        <el-button text>
                            <i class="el-icon-user"></i>
                            <span>用户中心</span>
                            <i class="el-icon-arrow-down"></i>
                        </el-button>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item command="login">
                                    <i class="el-icon-key"></i>
                                    登录
                                </el-dropdown-item>
                                <el-dropdown-item command="register">
                                    <i class="el-icon-user-filled"></i>
                                    注册
                                </el-dropdown-item>
                                <el-dropdown-item command="admin" divided>
                                    <i class="el-icon-setting"></i>
                                    管理后台
                                </el-dropdown-item>
                                <el-dropdown-item command="wiki">
                                    <i class="el-icon-document"></i>
                                    传统Wiki
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </div>

                <!-- 移动端菜单按钮 -->
                <el-button 
                    text 
                    @click="toggleMobileMenu"
                    class="mobile-menu-btn"
                >
                    <i class="el-icon-menu"></i>
                </el-button>
            </div>
        </nav>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 搜索结果提示 -->
            <div v-if="searchQuery" class="search-results-header">
                <h2>搜索结果: "{{ searchQuery }}"</h2>
                <p>找到 {{ totalCount }} 个相关词条</p>
            </div>

            <!-- 分类筛选器 -->
            <div class="category-filters">
                <div class="filter-tags">
                    <el-tag
                        v-for="category in categories"
                        :key="category.id"
                        :type="selectedCategory === category.id ? 'primary' : ''"
                        :effect="selectedCategory === category.id ? 'dark' : 'plain'"
                        class="category-tag"
                        @click="selectCategory(category.id)"
                    >
                        <i :class="category.icon"></i>
                        {{ category.name }}
                        <span class="count">({{ category.count }})</span>
                    </el-tag>
                </div>
            </div>

            <!-- 瀑布流内容区域 -->
            <div 
                ref="waterfallContainer" 
                class="waterfall-container"
                v-loading="initialLoading"
                element-loading-text="正在加载精彩内容..."
            >
                <!-- 瀑布流列 -->
                <div 
                    v-for="(column, index) in waterfallColumns" 
                    :key="index"
                    class="waterfall-column"
                    :style="{ width: columnWidth }"
                >
                    <div
                        v-for="article in column"
                        :key="article.id"
                        class="article-card"
                        @click="openArticle(article)"
                    >
                        <!-- 特色标识 -->
                        <div v-if="article.featured" class="featured-badge">
                            <i class="el-icon-star-filled"></i>
                            <span>精选</span>
                        </div>

                        <!-- 图片区域 -->
                        <div v-if="article.image" class="card-image">
                            <img :src="article.image" :alt="article.title" loading="lazy">
                            <div class="image-overlay">
                                <div class="overlay-actions">
                                    <el-button 
                                        circle 
                                        size="small"
                                        @click.stop="toggleFavorite(article)"
                                        :type="article.isFavorite ? 'danger' : 'default'"
                                    >
                                        <i :class="article.isFavorite ? 'el-icon-star-filled' : 'el-icon-star-off'"></i>
                                    </el-button>
                                    <el-button 
                                        circle 
                                        size="small"
                                        @click.stop="shareArticle(article)"
                                    >
                                        <i class="el-icon-share"></i>
                                    </el-button>
                                </div>
                            </div>
                        </div>

                        <!-- 内容区域 -->
                        <div class="card-content">
                            <!-- 分类标签 -->
                            <div class="card-category">
                                <el-tag 
                                    :type="getCategoryType(article.category)"
                                    size="small"
                                    effect="light"
                                >
                                    <i :class="getCategoryIcon(article.category)"></i>
                                    {{ getCategoryName(article.category) }}
                                </el-tag>
                            </div>

                            <!-- 标题 -->
                            <h3 class="card-title">{{ article.title }}</h3>

                            <!-- 摘要 -->
                            <p class="card-summary">{{ article.summary }}</p>

                            <!-- 标签 -->
                            <div v-if="article.tags && article.tags.length" class="card-tags">
                                <el-tag
                                    v-for="tag in article.tags.slice(0, 3)"
                                    :key="tag"
                                    size="small"
                                    type="info"
                                    effect="plain"
                                    class="tag-item"
                                >
                                    {{ tag }}
                                </el-tag>
                                <span v-if="article.tags.length > 3" class="more-tags">
                                    +{{ article.tags.length - 3 }}
                                </span>
                            </div>

                            <!-- 统计信息 -->
                            <div class="card-stats">
                                <div class="stat-item">
                                    <i class="el-icon-view"></i>
                                    <span>{{ formatNumber(article.views) }}</span>
                                </div>
                                <div class="stat-item">
                                    <i class="el-icon-edit"></i>
                                    <span>{{ formatNumber(article.edits) }}</span>
                                </div>
                                <div class="stat-item">
                                    <i class="el-icon-star-off"></i>
                                    <span>{{ formatNumber(article.favorites) }}</span>
                                </div>
                            </div>

                            <!-- 作者和时间 -->
                            <div class="card-meta">
                                <div class="author-info">
                                    <el-avatar :size="20" :src="article.author?.avatar">
                                        <i class="el-icon-user"></i>
                                    </el-avatar>
                                    <span class="author-name">{{ article.author?.name || '匿名' }}</span>
                                </div>
                                <div class="time-info">
                                    <i class="el-icon-time"></i>
                                    <span>{{ formatTime(article.updatedAt) }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 加载更多指示器 -->
            <div v-if="loadingMore" class="loading-more">
                <i class="el-icon-loading loading-icon"></i>
                <span>正在加载更多内容...</span>
            </div>

            <!-- 无更多内容提示 -->
            <div v-if="noMoreData && displayArticles.length > 0" class="no-more-data">
                <i class="el-icon-check"></i>
                <span>已加载全部内容</span>
            </div>

            <!-- 空状态 -->
            <div v-if="!initialLoading && displayArticles.length === 0" class="empty-state">
                <i class="el-icon-document-remove empty-icon"></i>
                <h3>暂无内容</h3>
                <p>{{ searchQuery ? '没有找到相关词条，请尝试其他关键词' : '暂时没有可显示的词条' }}</p>
                <el-button type="primary" @click="refreshData">
                    <i class="el-icon-refresh"></i>
                    刷新页面
                </el-button>
            </div>
        </main>

        <!-- 页脚 -->
        <footer class="site-footer">
            <div class="footer-container">
                <div class="footer-content">
                    <div class="footer-section">
                        <h4>关于我们</h4>
                        <p>记录三线建设历史，传承三线精神</p>
                        <div class="footer-links">
                            <a href="/about">关于网站</a>
                            <a href="/contact">联系我们</a>
                            <a href="/privacy">隐私政策</a>
                        </div>
                    </div>
                    <div class="footer-section">
                        <h4>快速链接</h4>
                        <div class="footer-links">
                            <a href="?category=sanxianren">三线人</a>
                            <a href="?category=sanxianchang">三线厂</a>
                            <a href="?category=storyhall">故事馆</a>
                            <a href="?category=oralhistory">口述历史</a>
                            <a href="?category=heritage">遗址馆</a>
                        </div>
                    </div>
                    <div class="footer-section">
                        <h4>技术支持</h4>
                        <div class="footer-links">
                            <a href="/help">使用帮助</a>
                            <a href="/api">API文档</a>
                            <a href="./index.php">传统Wiki</a>
                        </div>
                    </div>
                </div>
                <div class="footer-bottom">
                    <p>&copy; <?php echo date('Y'); ?> <?php echo htmlspecialchars($siteName); ?>. 保留所有权利.</p>
                    <p>基于 <a href="https://www.mediawiki.org/" target="_blank">MediaWiki</a> 构建</p>
                </div>
            </div>
        </footer>

        <!-- 返回顶部按钮 -->
        <el-backtop :right="40" :bottom="40" :visibility-height="300">
            <div class="backtop-button">
                <i class="el-icon-top"></i>
            </div>
        </el-backtop>

        <!-- 移动端抽屉菜单 -->
        <el-drawer
            v-model="mobileMenuVisible"
            direction="rtl"
            size="280px"
            :show-close="false"
            class="mobile-menu-drawer"
        >
            <template #header>
                <div class="mobile-menu-header">
                    <div class="logo">
                        <i class="el-icon-reading"></i>
                        <span><?php echo htmlspecialchars($siteName); ?></span>
                    </div>
                </div>
            </template>

            <div class="mobile-menu-content">
                <!-- 主导航菜单 -->
                <div class="mobile-nav-section">
                    <div class="section-title">主要栏目</div>
                    <div class="mobile-nav-items">
                        <div 
                            v-for="nav in navigationItems"
                            :key="nav.key"
                            class="nav-item"
                            :class="{ active: currentNav === nav.key }"
                            @click="handleNavClick(nav)"
                        >
                            <i :class="nav.icon"></i>
                            <div class="nav-content">
                                <span class="nav-title">{{ nav.label }}</span>
                                <span class="nav-desc">{{ nav.description }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </el-drawer>
    </div>

    <!-- JavaScript -->
    <script>
        // 设置全局配置
        window.SITE_CONFIG = {
            siteName: '<?php echo addslashes($siteName); ?>',
            baseUrl: '<?php echo addslashes($baseUrl); ?>',
            currentCategory: '<?php echo addslashes($currentCategory); ?>',
            searchQuery: '<?php echo addslashes($searchQuery); ?>',
            apiUrl: './api/sanxianren.php'
        };
    </script>
    <script src="./frontend/assets/js/data.js?v=<?php echo filemtime(__DIR__ . '/frontend/assets/js/data.js'); ?>"></script>
    <script src="./frontend/assets/js/utils.js?v=<?php echo filemtime(__DIR__ . '/frontend/assets/js/utils.js'); ?>"></script>
    <script src="./frontend/assets/js/main.js?v=<?php echo filemtime(__DIR__ . '/frontend/assets/js/main.js'); ?>"></script>
</body>
</html>
