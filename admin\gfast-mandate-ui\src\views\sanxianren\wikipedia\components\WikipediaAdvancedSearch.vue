<template>
  <div class="advanced-search">
    <el-form 
      ref="formRef"
      :model="searchForm"
      :rules="rules"
      label-width="100px"
      size="default"
    >
      <!-- 基础搜索 -->
      <div class="search-section">
        <h4 class="section-title">
          <SvgIcon name="ele-Search" :size="16" />
          基础搜索
        </h4>
        
        <el-form-item label="关键词" prop="keywords">
          <el-input
            v-model="searchForm.keywords"
            placeholder="输入搜索关键词"
            clearable
          >
            <template #prefix>
              <SvgIcon name="ele-Search" :size="16" />
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="标题">
          <el-input
            v-model="searchForm.title"
            placeholder="搜索标题中包含的词语"
            clearable
          />
        </el-form-item>

        <el-form-item label="内容">
          <el-input
            v-model="searchForm.content"
            type="textarea"
            :rows="3"
            placeholder="搜索内容中包含的词语"
            clearable
          />
        </el-form-item>
      </div>

      <!-- 分类筛选 -->
      <div class="search-section">
        <h4 class="section-title">
          <SvgIcon name="ele-Grid" :size="16" />
          分类筛选
        </h4>

        <el-form-item label="主分类">
          <el-select
            v-model="searchForm.category"
            placeholder="选择分类"
            clearable
            multiple
            collapse-tags
            collapse-tags-tooltip
          >
            <el-option
              v-for="category in categories"
              :key="category.value"
              :label="category.label"
              :value="category.value"
            >
              <SvgIcon :name="category.icon" :size="16" class="mr8" />
              {{ category.label }}
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="标签">
          <el-select
            v-model="searchForm.tags"
            placeholder="选择标签"
            clearable
            multiple
            filterable
            allow-create
            collapse-tags
            collapse-tags-tooltip
          >
            <el-option
              v-for="tag in popularTags"
              :key="tag"
              :label="tag"
              :value="tag"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="质量等级">
          <el-checkbox-group v-model="searchForm.quality">
            <el-checkbox label="fa">特色文章</el-checkbox>
            <el-checkbox label="ga">优良文章</el-checkbox>
            <el-checkbox label="b">乙级文章</el-checkbox>
            <el-checkbox label="c">丙级文章</el-checkbox>
            <el-checkbox label="start">初级文章</el-checkbox>
            <el-checkbox label="stub">小作品</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </div>

      <!-- 作者和时间 -->
      <div class="search-section">
        <h4 class="section-title">
          <SvgIcon name="ele-User" :size="16" />
          作者和时间
        </h4>

        <el-form-item label="作者">
          <el-input
            v-model="searchForm.author"
            placeholder="输入作者用户名"
            clearable
          >
            <template #prefix>
              <SvgIcon name="ele-User" :size="16" />
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="创建时间">
          <el-date-picker
            v-model="searchForm.createdDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>

        <el-form-item label="更新时间">
          <el-date-picker
            v-model="searchForm.updatedDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
      </div>

      <!-- 统计筛选 -->
      <div class="search-section">
        <h4 class="section-title">
          <SvgIcon name="ele-DataAnalysis" :size="16" />
          统计筛选
        </h4>

        <el-form-item label="浏览量">
          <div class="range-input">
            <el-input-number
              v-model="searchForm.minViews"
              :min="0"
              placeholder="最小值"
              controls-position="right"
            />
            <span class="range-separator">至</span>
            <el-input-number
              v-model="searchForm.maxViews"
              :min="0"
              placeholder="最大值"
              controls-position="right"
            />
          </div>
        </el-form-item>

        <el-form-item label="编辑次数">
          <div class="range-input">
            <el-input-number
              v-model="searchForm.minEdits"
              :min="0"
              placeholder="最小值"
              controls-position="right"
            />
            <span class="range-separator">至</span>
            <el-input-number
              v-model="searchForm.maxEdits"
              :min="0"
              placeholder="最大值"
              controls-position="right"
            />
          </div>
        </el-form-item>

        <el-form-item label="收藏数">
          <div class="range-input">
            <el-input-number
              v-model="searchForm.minFavorites"
              :min="0"
              placeholder="最小值"
              controls-position="right"
            />
            <span class="range-separator">至</span>
            <el-input-number
              v-model="searchForm.maxFavorites"
              :min="0"
              placeholder="最大值"
              controls-position="right"
            />
          </div>
        </el-form-item>
      </div>

      <!-- 排序选项 -->
      <div class="search-section">
        <h4 class="section-title">
          <SvgIcon name="ele-Sort" :size="16" />
          排序选项
        </h4>

        <el-form-item label="排序方式">
          <el-select v-model="searchForm.sortBy" placeholder="选择排序字段">
            <el-option label="相关性" value="relevance" />
            <el-option label="创建时间" value="created_at" />
            <el-option label="更新时间" value="updated_at" />
            <el-option label="浏览量" value="views" />
            <el-option label="编辑次数" value="edits" />
            <el-option label="收藏数" value="favorites" />
            <el-option label="标题" value="title" />
          </el-select>
        </el-form-item>

        <el-form-item label="排序顺序">
          <el-radio-group v-model="searchForm.sortOrder">
            <el-radio label="desc">降序</el-radio>
            <el-radio label="asc">升序</el-radio>
          </el-radio-group>
        </el-form-item>
      </div>

      <!-- 其他选项 -->
      <div class="search-section">
        <h4 class="section-title">
          <SvgIcon name="ele-Setting" :size="16" />
          其他选项
        </h4>

        <el-form-item label="语言">
          <el-select v-model="searchForm.language" placeholder="选择语言">
            <el-option label="中文" value="zh" />
            <el-option label="English" value="en" />
            <el-option label="日本語" value="ja" />
            <el-option label="한국어" value="ko" />
          </el-select>
        </el-form-item>

        <el-form-item label="特殊筛选">
          <el-checkbox-group v-model="searchForm.special">
            <el-checkbox label="featured">仅显示特色内容</el-checkbox>
            <el-checkbox label="hasImage">包含图片</el-checkbox>
            <el-checkbox label="recentlyUpdated">最近更新</el-checkbox>
            <el-checkbox label="highQuality">高质量文章</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="每页显示">
          <el-select v-model="searchForm.pageSize">
            <el-option label="10条" :value="10" />
            <el-option label="20条" :value="20" />
            <el-option label="50条" :value="50" />
            <el-option label="100条" :value="100" />
          </el-select>
        </el-form-item>
      </div>
    </el-form>

    <!-- 操作按钮 -->
    <div class="search-actions">
      <el-button @click="resetForm">
        <SvgIcon name="ele-RefreshLeft" :size="16" />
        重置
      </el-button>
      <el-button @click="saveAsTemplate">
        <SvgIcon name="ele-DocumentAdd" :size="16" />
        保存为模板
      </el-button>
      <el-button type="primary" @click="handleSearch">
        <SvgIcon name="ele-Search" :size="16" />
        搜索
      </el-button>
    </div>

    <!-- 搜索模板 -->
    <div v-if="searchTemplates.length > 0" class="search-templates">
      <h4>搜索模板</h4>
      <div class="template-list">
        <el-tag
          v-for="template in searchTemplates"
          :key="template.id"
          closable
          @click="loadTemplate(template)"
          @close="deleteTemplate(template.id)"
          class="template-tag"
        >
          {{ template.name }}
        </el-tag>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="WikipediaAdvancedSearch">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import SvgIcon from '/@/components/svgIcon/index.vue';

// Emits
const emit = defineEmits<{
  search: [params: any];
  close: [];
}>();

// 响应式数据
const formRef = ref();

const searchForm = reactive({
  keywords: '',
  title: '',
  content: '',
  category: [],
  tags: [],
  quality: [],
  author: '',
  createdDateRange: null,
  updatedDateRange: null,
  minViews: null,
  maxViews: null,
  minEdits: null,
  maxEdits: null,
  minFavorites: null,
  maxFavorites: null,
  sortBy: 'relevance',
  sortOrder: 'desc',
  language: 'zh',
  special: [],
  pageSize: 20,
});

// 分类选项
const categories = ref([
  { label: '历史', value: 'history', icon: 'ele-Clock' },
  { label: '科学', value: 'science', icon: 'ele-Experiment' },
  { label: '技术', value: 'technology', icon: 'ele-Cpu' },
  { label: '文化', value: 'culture', icon: 'ele-Picture' },
  { label: '地理', value: 'geography', icon: 'ele-Location' },
  { label: '人物', value: 'biography', icon: 'ele-User' },
  { label: '艺术', value: 'arts', icon: 'ele-Brush' },
]);

// 热门标签
const popularTags = ref([
  '中国历史', '世界历史', '科学技术', '文学艺术', '地理环境',
  '人物传记', '社会文化', '政治经济', '军事战争', '宗教哲学',
]);

// 搜索模板
const searchTemplates = ref([]);

// 表单验证规则
const rules = {
  keywords: [
    { min: 1, max: 100, message: '关键词长度在1到100个字符', trigger: 'blur' }
  ],
};

// 方法
const handleSearch = async () => {
  try {
    await formRef.value?.validate();
    
    // 构建搜索参数
    const params = {
      ...searchForm,
      query: searchForm.keywords,
    };
    
    // 移除空值
    Object.keys(params).forEach(key => {
      if (params[key] === null || params[key] === '' || 
          (Array.isArray(params[key]) && params[key].length === 0)) {
        delete params[key];
      }
    });
    
    emit('search', params);
    ElMessage.success('开始搜索...');
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};

const resetForm = () => {
  formRef.value?.resetFields();
  Object.assign(searchForm, {
    keywords: '',
    title: '',
    content: '',
    category: [],
    tags: [],
    quality: [],
    author: '',
    createdDateRange: null,
    updatedDateRange: null,
    minViews: null,
    maxViews: null,
    minEdits: null,
    maxEdits: null,
    minFavorites: null,
    maxFavorites: null,
    sortBy: 'relevance',
    sortOrder: 'desc',
    language: 'zh',
    special: [],
    pageSize: 20,
  });
};

const saveAsTemplate = async () => {
  try {
    const { value: templateName } = await ElMessageBox.prompt(
      '请输入模板名称',
      '保存搜索模板',
      {
        confirmButtonText: '保存',
        cancelButtonText: '取消',
        inputPattern: /^.{1,20}$/,
        inputErrorMessage: '模板名称长度在1到20个字符',
      }
    );

    const template = {
      id: Date.now().toString(),
      name: templateName,
      params: { ...searchForm },
      createdAt: new Date().toISOString(),
    };

    searchTemplates.value.push(template);
    saveTemplatesToStorage();
    
    ElMessage.success('搜索模板保存成功');
  } catch {
    // 用户取消
  }
};

const loadTemplate = (template: any) => {
  Object.assign(searchForm, template.params);
  ElMessage.success(`已加载模板: ${template.name}`);
};

const deleteTemplate = (templateId: string) => {
  searchTemplates.value = searchTemplates.value.filter(t => t.id !== templateId);
  saveTemplatesToStorage();
  ElMessage.success('模板删除成功');
};

const saveTemplatesToStorage = () => {
  try {
    localStorage.setItem('wikipedia_search_templates', JSON.stringify(searchTemplates.value));
  } catch (error) {
    console.error('保存搜索模板失败:', error);
  }
};

const loadTemplatesFromStorage = () => {
  try {
    const stored = localStorage.getItem('wikipedia_search_templates');
    if (stored) {
      searchTemplates.value = JSON.parse(stored);
    }
  } catch (error) {
    console.error('加载搜索模板失败:', error);
  }
};

// 生命周期
onMounted(() => {
  loadTemplatesFromStorage();
});
</script>

<style scoped lang="scss">
.advanced-search {
  .search-section {
    margin-bottom: 24px;
    padding: 16px;
    background: var(--el-fill-color-lighter);
    border-radius: 8px;

    .section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0 0 16px 0;
      font-size: 14px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }

  .range-input {
    display: flex;
    align-items: center;
    gap: 8px;

    .range-separator {
      color: var(--el-text-color-secondary);
      font-size: 14px;
    }
  }

  .search-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px 0;
    border-top: 1px solid var(--el-border-color-light);
  }

  .search-templates {
    margin-top: 20px;
    padding: 16px;
    background: var(--el-fill-color-lighter);
    border-radius: 8px;

    h4 {
      margin: 0 0 12px 0;
      font-size: 14px;
      color: var(--el-text-color-primary);
    }

    .template-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .template-tag {
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
      }
    }
  }

  .mr8 {
    margin-right: 8px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .advanced-search {
    .search-section {
      padding: 12px;
    }

    .range-input {
      flex-direction: column;
      align-items: stretch;

      .range-separator {
        text-align: center;
      }
    }

    .search-actions {
      flex-direction: column;

      .el-button {
        width: 100%;
      }
    }
  }
}
</style>
