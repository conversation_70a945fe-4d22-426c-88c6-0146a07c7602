# MediaWiki OAuth应用配置指南

## 前提条件

1. 确保MediaWiki已安装OAuth扩展
2. 具有管理员权限的MediaWiki账户
3. MediaWiki版本支持OAuth 2.0

## 配置步骤

### 1. 启用OAuth扩展

在MediaWiki的`LocalSettings.php`文件中添加：

```php
# OAuth扩展配置
wfLoadExtension( 'OAuth' );

# 启用OAuth 2.0支持
$wgOAuth2EnabledGrantTypes = [
    'authorization_code',
    'refresh_token'
];

# OAuth配置
$wgOAuth2PrivateKey = '/path/to/oauth2-private.key';
$wgOAuth2PublicKey = '/path/to/oauth2-public.key';
$wgOAuth2RequireCodeChallengeForPublicClients = false;
```

### 2. 生成RSA密钥对（如果使用OAuth 2.0）

```bash
# 生成私钥
openssl genrsa -out oauth2-private.key 2048

# 生成公钥
openssl rsa -in oauth2-private.key -pubout -out oauth2-public.key

# 设置权限
chmod 600 oauth2-private.key
chmod 644 oauth2-public.key
```

### 3. 创建OAuth应用

#### 方法一：通过Web界面创建

1. 登录MediaWiki管理员账户
2. 访问：`https://www.sanxianren.com/wiki/Special:OAuthConsumerRegistration`
3. 点击"Propose a new consumer"
4. 填写应用信息：

**基本信息：**
- Consumer name: `gfast-mediawiki-integration`
- Consumer version: `1.0`
- Description: `gfast中台与MediaWiki集成的OAuth应用`

**技术信息：**
- OAuth protocol version: `OAuth 2.0`
- Allowed grants: `authorization_code`, `refresh_token`
- Redirect URL: `http://localhost:8808/api/v1/sanxianren/oauth/callback`

**权限设置：**
- ✅ Basic rights (查看页面、用户信息)
- ✅ Edit existing pages (编辑现有页面)
- ✅ Create, edit, and move pages (创建、编辑和移动页面)
- ✅ Upload new files (上传新文件)
- ✅ Upload, replace, and move files (上传、替换和移动文件)

5. 提交申请并等待管理员批准

#### 方法二：通过数据库直接创建（管理员）

```sql
-- 插入OAuth消费者记录
INSERT INTO oauth2_consumers (
    name,
    description,
    client_id,
    client_secret,
    redirect_uris,
    grants,
    scopes,
    user_id,
    stage,
    oauth_version
) VALUES (
    'gfast-mediawiki-integration',
    'gfast中台与MediaWiki集成的OAuth应用',
    'gfast_client_' || UNIX_TIMESTAMP(),
    SHA2(CONCAT('gfast_secret_', UNIX_TIMESTAMP(), RAND()), 256),
    'http://localhost:8808/api/v1/sanxianren/oauth/callback',
    'authorization_code,refresh_token',
    'basic,editpage,createpage,uploadfile',
    1,  -- 管理员用户ID
    1,  -- 已批准状态
    2   -- OAuth 2.0
);
```

### 4. 获取客户端凭据

创建成功后，您将获得：
- **Client ID**: 类似 `1234567890abcdef`
- **Client Secret**: 类似 `abcdef1234567890...`

### 5. 测试OAuth端点

验证OAuth端点是否正常工作：

```bash
# 测试授权端点
curl "https://www.sanxianren.com/wiki/Special:OAuth/authorize?response_type=code&client_id=YOUR_CLIENT_ID&redirect_uri=http://localhost:8808/api/v1/sanxianren/oauth/callback&scope=basic"

# 测试令牌端点（需要有效的授权码）
curl -X POST "https://www.sanxianren.com/wiki/Special:OAuth/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=authorization_code&code=YOUR_AUTH_CODE&redirect_uri=http://localhost:8808/api/v1/sanxianren/oauth/callback&client_id=YOUR_CLIENT_ID&client_secret=YOUR_CLIENT_SECRET"
```

## 常见问题解决

### 1. OAuth扩展未安装

```bash
# 下载OAuth扩展
cd /path/to/mediawiki/extensions
git clone https://github.com/wikimedia/mediawiki-extensions-OAuth.git OAuth

# 或者使用Composer
composer require mediawiki/oauth
```

### 2. 权限不足

确保用户具有以下权限：
- `mwoauthproposeconsumer` - 提议OAuth消费者
- `mwoauthmanageconsumer` - 管理OAuth消费者
- `mwoauthviewprivate` - 查看私有OAuth信息

### 3. SSL证书问题

如果使用HTTPS，确保SSL证书有效：

```php
# 在LocalSettings.php中添加
$wgOAuthSecureTokenTransfer = true;
```

### 4. 跨域问题

如果遇到CORS问题，在LocalSettings.php中添加：

```php
# 允许跨域请求
$wgCrossSiteAJAXdomains = array(
    'http://localhost:8808',
    'http://localhost:5173'
);
```

## 安全建议

1. **使用HTTPS**: 生产环境必须使用HTTPS
2. **限制重定向URI**: 只允许必要的重定向URI
3. **定期轮换密钥**: 定期更新Client Secret
4. **监控使用情况**: 定期检查OAuth使用日志
5. **最小权限原则**: 只授予必要的权限

## 验证配置

配置完成后，可以通过以下方式验证：

1. 访问 `Special:OAuthListConsumers` 查看已创建的应用
2. 检查应用状态是否为"已批准"
3. 确认重定向URI配置正确
4. 测试授权流程是否正常

## 下一步

配置完成后，请：
1. 记录Client ID和Client Secret
2. 更新gfast配置文件
3. 执行数据库迁移
4. 运行集成测试
